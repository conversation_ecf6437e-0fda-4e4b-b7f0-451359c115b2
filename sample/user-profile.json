{"id": 1, "user_id": 3057171, "email": "<EMAIL>", "display_name": "Cường Đặng Ngọc", "phone": "19001560", "gender": "Male", "birthday": "2020-10-12", "position": "<PERSON>", "years_of_exp": 3, "status": "new_start", "address": "X6173646173646173", "province_id": 10958, "province_name": "<PERSON><PERSON> <PERSON>", "linkedin_link": "https://www.linkedin.com/", "github_link": "https://github.com/", "summary": "<p><strong>Lorem Ipsum</strong>&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>", "skills": {"soft_skills": ["Team building", "<PERSON><PERSON><PERSON> to"], "technical_skills": [{"skill_id": 1, "skill_name": "PHP"}, {"skill_id": 5, "skill_name": "Laravel 6"}]}, "experiences": [{"to": null, "from": "2023-01-25", "skills": [{"skill_id": 1, "skill_name": "PHP"}], "company": "TopDop", "position": "CEO", "projects": [{"project_time": "12 tháng", "description": "<p><strong>Lorem Ipsum</strong>&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>", "project_name": "TopDop.Com"}], "description": "<p><strong>Lorem Ipsum</strong>&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>", "is_working_here": true}], "educations": [{"to": "2022-10-20", "from": "2020-10-20", "degree": "Công nghệ thông tin", "description": "<p><strong>Lorem Ipsum</strong>&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>", "school_name": "<PERSON><PERSON><PERSON> h<PERSON> mông cổ", "is_studying_here": false}], "projects": [{"project": "<PERSON> an fake", "description": "<p><strong>Lorem Ipsum</strong>&nbsp;is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.</p>", "project_time": "12 thang"}], "languages": [{"fluency": "Proficient", "language": "English"}, {"fluency": "Native", "language": "Chinese"}], "interests": ["thich du thu", "Thich tum lum"], "references": [{"refer_name": "<PERSON>an ta la van", "refer_email": "<EMAIL>", "refer_phone": "0968000000", "refer_profession": "CEO"}], "activities": [{"to": "2023-06-26", "activity": "<PERSON> du lich", "from": "2023-04-26", "achievement": "<p>fdsfsdfd</p>", "is_working_here": false}, {"to": null, "activity": "<PERSON> ngu", "from": "2023-01-26", "achievement": "<p>dsffsdf</p>", "is_working_here": true}], "certificates": [{"name": "<PERSON> lai xe", "description": "<p><PERSON><PERSON> bang lai xe may</p>", "date_completed": "2019-02-26"}], "additionals": [{"additional": "<PERSON>hong tin ne", "description": "<p><PERSON> di</p>"}], "completed_sections": ["userid", "email", "display_name", "phone", "gender", "birthday", "position", "years_of_exp", "status", "address", "province_id", "skills.technical_skills"]}