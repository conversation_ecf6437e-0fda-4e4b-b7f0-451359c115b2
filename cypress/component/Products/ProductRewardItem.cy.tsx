import ProductRewardItem from "@/components/Product/ProductRewardItem";
import { formatPriceVND } from "@/utils";

describe("ProductRewardItem.cy.tsx", () => {
  it("Render correct information title & price (formated price)", () => {
    cy.mount(
      <div className="m-auto mt-4 max-w-sm">
        <ProductRewardItem
          name="Gói KM 8"
          id={1}
          price={3000000}
          activeProduct={{ id_product: 0, media_url: "", description: "" }}
          setActiveProduct={() => {}}
        />
      </div>,
    );

    cy.get("[data-cy='Product-Reward']").within(() => {
      cy.get("h3").should("have.text", "Gói KM 8");
      cy.get(".products-price").should("have.text", formatPriceVND(3000000));
    });
  });

  it("Render correct state with isActive false", () => {
    cy.mount(
      <div id="section-topDev-rewards" className="m-auto mt-4 max-w-sm">
        <ProductRewardItem
          name="Gói KM 8"
          id={1}
          price={3000000}
          activeProduct={{ id_product: 0, media_url: "", description: "" }}
          setActiveProduct={() => {}}
        />
      </div>,
    );
  });

  it("Render correct state with isActive true", () => {
    cy.mount(
      <div id="section-topDev-rewards" className="m-auto mt-4 max-w-sm">
        <ProductRewardItem
          name="Gói KM 8"
          id={1}
          price={3000000}
          activeProduct={{ id_product: 0, media_url: "", description: "" }}
          setActiveProduct={() => {}}
        />
      </div>,
    );
  });

  it("Call onClick handle when click button", () => {
    cy.mount(
      <div id="section-topDev-rewards" className="m-auto mt-4 max-w-sm">
        <ProductRewardItem
          name="Gói KM 8"
          id={1}
          price={3000000}
          activeProduct={{ id_product: 0, media_url: "", description: "" }}
          setActiveProduct={() => {}}
        />
      </div>,
    );

    cy.get('[data-cy="Product-Reward"]')
      .should("have.attr", "data-cy", "Product-Reward")
      .click();

    cy.get('[data-cy="Product-Reward"]')
      .should("have.attr", "data-cy", "Product-Reward")
      .click()
      .invoke("addClass", "active");

    cy.get('[data-cy="Product-Reward"]').should("have.class", "active");
  });
});
