import QuickNavigationButton from "@/components/Product/QuickNavigationButton";

describe("QuickNavigationButton.cy.tsx", () => {
  it("Render Quick navigation item for url, title, image", () => {
    cy.mount(
      <QuickNavigationButton
        title={"Đăng tin tuyển dụng"}
        url="/products/#section-candidate-cv"
        image="/v4/assets/images/online-payment/published-recruitment.svg"
      />,
    );

    cy.get("[data-cy='item-quick-navigation-button']").within(() => {
      cy.get("img").should(
        "have.attr",
        "src",
        "/v4/assets/images/online-payment/published-recruitment.svg",
      );
      cy.get("h3").should("have.text", "Đăng tin tuyển dụng");
    });
  });
});
