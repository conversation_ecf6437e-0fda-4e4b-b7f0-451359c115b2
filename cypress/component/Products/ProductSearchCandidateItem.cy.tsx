import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import ProductSearchCandidateItem from "@/components/Product/ProductSearchCandidateItem";
import { BenefitsProductProp } from "@/types/products";

describe("ProductSearchCandidateItem.cy.tsx", () => {
  const listBenifits: Omit<BenefitsProductProp, "translables">[]= [
    {
      id: 1,
      name: "Hiển thị 03 Phúc lợi công ty trên trang tìm kiếm",
      order: 1,
    },
    {
      id: 2,
      name: "Logo công ty được hiển thị tại mục Popular Companies trên trang chủ",
      order: 2,
    },
    {
      id: 3,
      name: "Xuất hiện ở vị trí hàng đầu trong kết quả tìm kiếm liên quan",
      order: 3,
    },
    {
      id: 4,
      name: "Email marketing đến 9.500 data đúng đối tượng",
      order: 4,
    },
    {
      id: 5,
      name: "Tin đăng được tô đỏ và nền nổi bật",
      order: 5,
    },
  ];

  it("Render correct information", () => {
    cy.mount(
      <NextIntlClientProvider
        locale={"vi"}
        messages={{
          products_buy_now: "Mua ngay",
          products_view_more: "Xem thêm",
          products_collapse: "Thu gọn",
        }}
      >
        <StoreProvider>
          <ProductSearchCandidateItem
            id={1}
            benefits={listBenifits as []}
            name="Gói Distinction"
            price={7689000}
            description="Phù hợp vị trí chuyên môn cao/ khó, tuyển gấp/ số lượng lớn, đẩy mạnh thương hiệu công ty"
            anchoring_price={6999000}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Product-Search-Candidate"]').within(() => {
      cy.get("h3").should("have.text", "Gói Distinction");
      cy.get(".products-price").should("have.text", "7.689.000 ₫6.999.000 ₫");
      cy.get(".products-description").should(
        "have.text",
        "Phù hợp vị trí chuyên môn cao/ khó, tuyển gấp/ số lượng lớn, đẩy mạnh thương hiệu công ty",
      );
      cy.get(".box-benefits").within(() => {
        cy.get("li").should("have.length", listBenifits.length);
      });
    });
  });

  it("Check button then on click add to cart and buy bow", () => {
    cy.mount(
      <NextIntlClientProvider
        locale={"vi"}
        messages={{
          products_buy_now: "Mua ngay",
          products_view_more: "Xem thêm",
          products_collapse: "Thu gọn",
          products_add_cart_fail:
            "Rất tiếc, đã xảy ra sự cố khi thêm sản phẩm vào giỏ hàng. Vui lòng thử lại.",
        }}
      >
        <StoreProvider>
          <ProductSearchCandidateItem
            id={1}
            benefits={listBenifits as []}
            name="Gói Distinction"
            price={7689000}
            description="Phù hợp vị trí chuyên môn cao/ khó, tuyển gấp/ số lượng lớn, đẩy mạnh thương hiệu công ty"
            anchoring_price={6999000}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Product-Search-Candidate"]').within(() => {
      cy.get('[data-cy="buy-now"]')
        .should("have.attr", "data-cy", "buy-now");
      cy.get('[data-cy="add-to-cart"]')
        .should("have.attr", "data-cy", "add-to-cart");
    });
  });

  it("Show tooltip when hover description", () => {
    cy.mount(
      <NextIntlClientProvider
        locale={"vi"}
        messages={{
          products_buy_now: "Mua ngay",
          products_view_more: "Xem thêm",
          products_collapse: "Thu gọn",
        }}
      >
        <StoreProvider>
          <ProductSearchCandidateItem
            id={1}
            benefits={listBenifits as []}
            name="Gói Distinction"
            price={7689000}
            description="Phù hợp vị trí chuyên môn cao/ khó, tuyển gấp/ số lượng lớn, đẩy mạnh thương hiệu công ty"
            anchoring_price={6999000}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Product-Search-Candidate"]').within(() => {
      cy.get('.products-description')
        .click();
    });
  });

  it("Render Product Job Posting when benifit max height, Check show view more or Collapse", () => {
    const listBenifitsMax = [
      {
        id: 1,
        name: "Hiển thị 03 Phúc lợi công ty trên trang tìm kiếm",
        order: 1,
      },
      {
        id: 2,
        name: "Logo công ty được hiển thị tại mục Popular Companies trên trang chủ",
        order: 2,
      },
      {
        id: 3,
        name: "Xuất hiện ở vị trí hàng đầu trong kết quả tìm kiếm liên quan",
        order: 3,
      },
      {
        id: 4,
        name: "Email marketing đến 9.500 data đúng đối tượng",
        order: 4,
      },
      {
        id: 5,
        name: "Tin đăng được tô đỏ và nền nổi bật",
        order: 5,
      },
      {
        id: 6,
        name: "Tin đăng được tô đỏ và nền nổi bật",
        order: 6,
      },
      {
        id: 7,
        name: "Tin đăng được tô đỏ và nền nổi bật",
        order: 7,
      },
    ];

    cy.mount(
      <NextIntlClientProvider
        locale={"vi"}
        messages={{
          products_buy_now: "Mua ngay",
          products_view_more: "Xem thêm",
          products_collapse: "Thu gọn",
        }}
      >
        <StoreProvider>
          <ProductSearchCandidateItem
            id={1}
            benefits={listBenifitsMax as []}
            category_code="package"
            name="Gói Distinction"
            price={7689000}
            description="Phù hợp vị trí chuyên môn cao/ khó, tuyển gấp/ số lượng lớn, đẩy mạnh thương hiệu công ty"
            anchoring_price={6999000}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Product-Search-Candidate"]').within(() => {
      cy.get('[data-cy="read-more"]').should("have.text", "Xem thêm").click();
      cy.get('[data-cy="read-more"]').should("have.text", "Thu gọn").click();
    });
  });
});
