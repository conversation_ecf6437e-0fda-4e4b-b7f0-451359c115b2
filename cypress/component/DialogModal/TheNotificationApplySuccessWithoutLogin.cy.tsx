import TheNotificationApplySuccessWithoutLogin from "@/components/DialogModal/NotificationSuccess/TheNotificationApplySuccessWithoutLogin";
import StoreProvider from "@/store/provider";
import { showDialogNotification } from "@/store/slices/settingSlice";
import { NextIntlClientProvider } from "next-intl";

describe("TheNotificationApplySuccessWithoutLogin.cy.tsx", () => {
  it("Render Component The Notification Apply Success Without Login", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={locales}>
        <StoreProvider>
          <TheNotificationApplySuccessWithoutLogin handleClose={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
    cy.window()
      .its("Cypress")
      .its("store")
      .invoke("dispatch", showDialogNotification(true));
    cy.get('[data-cy="TheNotificationApplySuccessWithoutLogin"]').should(
      "exist",
    );
  });
});

const locales = {
  detail_job_page_title_notification_success_popup: "Xin chúc mừng",
  common_error: "Rất tiếc",
  detail_job_page_description_notification_success_popup:
    "Bạn đã ứng tuyển thành công!",
  detail_job_page_cv_applied:
    "Bạn đã ứng tuyển vị trí này. Hãy thử một công việc khác.",
  detail_job_page_content_email_notification_success_popup:
    "Bạn sẽ nhận được email xác nhận ứng tuyển tại địa chỉ email",
  detail_job_page_applay_faster_notification_success_popup:
    "Ứng tuyển nhanh hơn với tài khoản TopDev",
  detail_job_page_sign_up_now: "Đăng ký ngay",
  detail_job_page_title_list_benifit: "Lợi ích khi tạo tài khoản TopDev",
  detail_job_page_benifit1: "Ứng tuyển nhanh chóng hơn với hồ sơ đã được lưu.",
  detail_job_page_benifit2: "Lưu lại công việc để ứng tuyển sau.",
  detail_job_page_benifit3: "Xem được mức lương cho mỗi vị trí",
};
