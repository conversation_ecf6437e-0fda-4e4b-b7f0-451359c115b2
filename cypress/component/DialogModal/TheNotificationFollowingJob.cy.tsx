import TheNotificationFollowingJob from "@/components/DialogModal/NotificationSuccess/TheNotificationFollowingJob";
import StoreProvider from "@/store/provider";
import { showDialogNotification } from "@/store/slices/settingSlice";
import { NextIntlClientProvider } from "next-intl";

describe("TheNotificationFollowingJob.cy.tsx", () => {
  it("Render Component The Notification Following Job", () => {
    cy.mount(
      <NextIntlClientProvider
        locale="vi"
        messages={{
          detail_job_page_save_jobs_succesed: "Lưu việc làm thành công",
          detail_job_page_content_job_information_following:
            "Thông tin việc làm bạn theo dõi sẽ được lưu tại ",
          detail_job_page_explore_more: "Khám phá thêm",
        }}
      >
        <StoreProvider>
          <TheNotificationFollowingJob handleClose={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
    cy.window()
      .its("Cypress")
      .its("store")
      .invoke("dispatch", showDialogNotification(true));
    cy.get('[data-cy="TheNotificationFollowingJob"]').should("exist");
  });
});
