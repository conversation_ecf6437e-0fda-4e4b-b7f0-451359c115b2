import TheNotificationApplySuccessLogin from "@/components/DialogModal/NotificationSuccess/TheNotificationApplySuccessLogin";
import StoreProvider from "@/store/provider";
import { showDialogNotification } from "@/store/slices/settingSlice";
import { NextIntlClientProvider } from "next-intl";

describe("TheNotificationApplySuccessLogin.cy.tsx", () => {
  it("Render Component The Notification Apply Success Login", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={locales}>
        <StoreProvider>
          <TheNotificationApplySuccessLogin handleClose={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
    cy.window()
      .its("Cypress")
      .its("store")
      .invoke("dispatch", showDialogNotification(true));
    cy.get('[data-cy="TheNotificationApplySuccessLogin"]').should("exist");
  });
});

const locales = {
  notification_title_popup_success_login: "Bạn đã ứng tuyển thành công!",
  notification_desc_popup_success_login:
    "Hãy tiếp tục ứng tuyển để tìm thêm nhiều cơ hội mới nhé!",
  notification_title_explore_more_popup_success_login:
    "Khám phá thêm cơ hội việc làm khác phù hợp",
  notification_info_explore_more_popup_success_login_1:
    "Tìm kiếm và ứng tuyển việc làm IT phù hợp theo kỹ năng chuyên môn.",
  notification_info_explore_more_popup_success_login_2:
    "Lưu lại công việc phù hợp để ứng tuyển sau.",
  notification_title_constitutive_popup_success_login:
    "Bật trạng thái 'Đang tìm việc' để tiếp cận nhiều Nhà tuyển dụng",
  notification_info_constitutive_popup_success_login_1:
    "Cho Nhà tuyển dụng biết bạn đang tích cực tìm kiếm một vị trí",
  notification_info_constitutive_popup_success_login_2:
    "Khi có vị trí phù hợp, Nhà tuyển dụng có thể chủ động liên hệ trao đổi với bạn",
  notification_button_explore_more: "Khám phá thêm",
  notification_button_constitutive: "Thiết lập 'Đang tìm việc'",
};
