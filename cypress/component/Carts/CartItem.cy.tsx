import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import CartItem from "@/components/Cart/CartItem";
import { formatPriceVND } from "@/utils";
const messages = {
  carts_toast_delete_cart_confirm: "<PERSON>ó<PERSON>",
  carts_toast_delete_cart_cancel: "Hủy",
  carts_toast_delete_cart_title: "Bạn có muốn xóa ?",
};
describe("CartItem.cy.tsx", () => {
  it("Render Cart Item type job posting full data", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <CartItem
            name="Gói basic"
            price={1000000}
            product_id={1}
            quantity={20}
            total_price={1200000}
            type="package"
            anchoring_price={900000}
            total_anchoring_price={1000000 * 20}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Cart-Item"]').within(() => {
      cy.get('[data-cy="name"]').should("have.text", "Gói basic");
      cy.get('[data-cy="discount-price"]').should(
        "have.text",
        formatPriceVND(900000),
      );
      cy.get('[data-cy="price"]').should("have.text", formatPriceVND(1000000));
      cy.get('[data-cy="total-price"]').should(
        "have.text",
        formatPriceVND(1200000),
      );
    });
  });

  it("Render Cart Item type search candaite full data", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <CartItem
            name="Gói basic"
            price={1000000}
            product_id={1}
            quantity={20}
            total_price={1200000}
            type="credit"
            anchoring_price={900000}
            total_anchoring_price={900000 * 20}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Cart-Item"]').within(() => {
      cy.get('[data-cy="name"]').should("have.text", "Gói basic");
      cy.get('[data-cy="discount-price"]').should(
        "have.text",
        formatPriceVND(900000),
      );
      cy.get('[data-cy="price"]').should("have.text", formatPriceVND(1000000));
      cy.get('[data-cy="total-price"]').should(
        "have.text",
        formatPriceVND(1200000),
      );
    });
  });

  it("Render Cart Item when anchoring_price exist", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <CartItem
            name="Gói basic"
            price={1000000}
            product_id={1}
            quantity={20}
            total_price={1200000}
            type="package"
            anchoring_price={900000}
            total_anchoring_price={900000 * 20}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
    cy.get('[data-cy="Cart-Item"]').within(() => {
      cy.get('[data-cy="name"]').should("have.text", "Gói basic");
      cy.get('[data-cy="discount-price"]').should(
        "have.text",
        formatPriceVND(900000),
      );
      cy.get('[data-cy="price"]').should("have.text", formatPriceVND(1000000));
      cy.get('[data-cy="total-price"]').should(
        "have.text",
        formatPriceVND(1200000),
      );
    });
  });

  it("Render Cart Item when anchoring_price not exist", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <CartItem
            name="Gói basic"
            price={1000000}
            product_id={1}
            quantity={20}
            total_price={1000000 * 20}
            type="package"
            anchoring_price={0}
            total_anchoring_price={0}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Cart-Item"]').within(() => {
      cy.get('[data-cy="name"]').should("have.text", "Gói basic");
      cy.get('[data-cy="price"]').should("have.text", formatPriceVND(1000000));
      cy.get('[data-cy="total-price"]').should(
        "have.text",
        formatPriceVND(1000000 * 20),
      );
    });
  });

  it("Cart Item onClick decrement quality order", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={{}}>
        <StoreProvider>
          <CartItem
            name="Gói basic"
            price={1000000}
            product_id={1}
            quantity={20}
            total_price={1000000 * 20}
            type="package"
            anchoring_price={0}
            total_anchoring_price={0}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Cart-Item"]').within(() => {
      cy.get('[data-cy="onclick-decrement"]').should("exist");
    });
    cy.log("decrement successfully");
  });

  it("Cart Item onClick increment quality order", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={{}}>
        <StoreProvider>
          <CartItem
            name="Gói basic"
            price={1000000}
            product_id={1}
            quantity={20}
            total_price={1000000 * 20}
            type="package"
            anchoring_price={0}
            total_anchoring_price={0}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Cart-Item"]').within(() => {
      cy.get('[data-cy="onclick-increment"]').should("exist");
    });
    cy.log("increment successfully");
  });

  it("Cart Item On click delete", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={{}}>
        <StoreProvider>
          <CartItem
            name="Gói basic"
            price={1000000}
            product_id={1}
            quantity={20}
            total_price={1000000 * 20}
            type="package"
            anchoring_price={0}
            total_anchoring_price={0}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Cart-Item"]').within(() => {
      cy.get('[data-cy="delete-cart"]').should("exist");
    });
  });
});
