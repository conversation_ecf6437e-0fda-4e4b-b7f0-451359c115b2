import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import TheCartPageHeading from "@/components/Cart/TheCartPageHeading";
const messages = {
  carts_return_to_cart: "Quay lại Giỏ hàng",
  carts_return_to_infomation: "Quay lại Thông tin",
  carts_back_to_product: "Quay lại Cửa hàng",
};

describe("TheCartPageHeading.cy.tsx", () => {
  it("Render heading cart page when step 1", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <TheCartPageHeading step={1} setStepCart={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
  });

  it("Render heading cart page when step 2", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <TheCartPageHeading step={2} setStepCart={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
  });

  it("Render heading cart page when step 3", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <TheCartPageHeading step={3} setStepCart={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
  });
});
