import StoreProvider from "@/store/provider";
import StepItem from "@/components/Cart/StepItem";

describe("StepItem.cy.tsx", () => {
  const defineCart = {
    uuid: "",
    id: 0,
    items: [],
    promotions: [],
    quantity: 0,
    price_total: 0,
    order_total: 0,
    discount_total: 0,
    tax_name: "",
    total_tax: 0,
    items_count: 0,
  };

  it("Render button navigation when step 1 and active", () => {
    cy.mount(
      <StoreProvider>
        <div className="flex justify-center p-10">
          <StepItem
            step={0}
            stepValue={1}
            title="Kiểm tra giỏ hàng"
          />
        </div>
      </StoreProvider>,
    );

    cy.log("When active step");
    cy.mount(
      <StoreProvider>
        <div className="flex justify-center p-10">
          <StepItem
            step={1}
            stepValue={1}
            title="Kiểm tra giỏ hàng"
          />
        </div>
      </StoreProvider>,
    );
  });

  it("Render button navigation when step 2 and active", () => {
    cy.mount(
      <StoreProvider>
        <div className="flex justify-center p-10">
          <StepItem
            step={0}
            stepValue={2}
            title="Thông tin đơn hàng"
          />
        </div>
      </StoreProvider>,
    );

    cy.log("When active step");
    cy.mount(
      <StoreProvider>
        <div className="flex justify-center p-10">
          <StepItem
            step={2}
            stepValue={2}
            title="Thông tin đơn hàng"
          />
        </div>
      </StoreProvider>,
    );
  });

  it("Render button navigation when step 3 and active", () => {
    cy.mount(
      <StoreProvider>
        <div className="flex justify-center p-10">
          <StepItem
            step={0}
            stepValue={3}
            title="Thực hiện thanh toán"
          />
        </div>
      </StoreProvider>,
    );

    cy.log("When active step");
    cy.mount(
      <StoreProvider>
        <div className="flex justify-center p-10">
          <StepItem
            step={3}
            stepValue={3}
            title="Thực hiện thanh toán"
          />
        </div>
      </StoreProvider>,
    );
  });
});
