import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import TheCheckoutPageInformationOrder from "@/components/Cart/TheCheckoutPageInformationOrder";
const messages = {
  carts_select_promotional_code: "Chọn mã ưu đãi",
  carts_total_order_value: "Tổng giá trị đơn hàng",
  carts_discount_order: "Giảm giá",
  carts_title_tax: "Giảm",
  carts_total_payment: "Tổng thanh tốn",
  carts_vat_included: "đã bao gồm VAT",
  carts_order: "Đơn hàng",
  carts_terms_policy:
    "Bằng việc mua hàng, tôi đồng ý với <terms>Điều khoản sử dụng</terms> và <policy>Chính sách bảo mật</policy> của TopDev.",
  carts_order_confirmation: "Xác nhận đơn hàng",
};

describe("TheCheckoutPageInformationOrder.cy.tsx", () => {
  it("Render infomation order", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={messages}>
        <StoreProvider>
          <TheCheckoutPageInformationOrder setFieldValue={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
  });

  it("Render infomation order and checked Tnc", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={messages}>
        <StoreProvider>
          <TheCheckoutPageInformationOrder setFieldValue={() => {}} />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="The-Checkout-Page-Information-Order"]').within(() => {
      cy.get("#term_of_use").check();
    });
  });
});
