import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import TotalOrder from "@/components/Cart/TotalOrder";
import { formatPriceVND } from "@/utils";

const messages = {
  carts_select_promotional_code: "Chọn mã ưu đãi",
  carts_total_order_value: "Tổng giá trị đơn hàng",
  carts_discount_order: "Giảm giá",
  carts_title_tax: "Giảm",
  carts_total_payment: "Tổng thanh tốn",
  carts_vat_included: "đã bao gồm VAT",
};

describe("TotalOrder.cy.tsx", () => {
  it("Render Total order when discount", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={messages}>
        <StoreProvider>
          <TotalOrder
            discount_total={100000}
            order_total={800000}
            price_total={1000000}
            tax_name="<PERSON><PERSON><PERSON><PERSON> (10%)"
            total_tax={100000}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Total-Order"]').within(() => {
      cy.get('[data-cy="price-total"]').should(
        "have.text",
        formatPriceVND(1000000),
      );
      cy.get('[data-cy="discount-total"]').should(
        "have.text",
        formatPriceVND(100000),
      );
      cy.get('[data-cy="tax-total"]').should(
        "have.text",
        formatPriceVND(100000),
      );
      cy.get('[data-cy="order-total"]').should(
        "have.text",
        formatPriceVND(800000),
      );
    });
  });

  it("Render Total order when not discount", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={messages}>
        <StoreProvider>
          <TotalOrder
            discount_total={0}
            order_total={1000000}
            price_total={1000000}
            tax_name="Giảm"
            total_tax={0}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="Total-Order"]').within(() => {
      cy.get('[data-cy="price-total"]').should(
        "have.text",
        formatPriceVND(1000000),
      );
      cy.get('[data-cy="discount-total"]').should(
        "have.text",
        formatPriceVND(0),
      );
      cy.get('[data-cy="tax-total"]').should(
        "have.text",
        formatPriceVND(0),
      );
      cy.get('[data-cy="order-total"]').should(
        "have.text",
        formatPriceVND(1000000),
      );
    });
  });
});
