import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import TheCartPageEmpty from "@/components/Cart/TheCartPageEmpty";

describe("TheCartPageEmpty.cy.tsx", () => {
  it("Render UI when empty", () => {
    cy.mount(
      <NextIntlClientProvider
        locale={"vi"}
        messages={{
          carts_title_cart_empty: "Giỏ hàng trống rỗng và buồn bã",
          carts_description_cart_empty:
            "Bạn ơi, nhanh chóng khám phá và thêm các sản phẩm của TopDev để làm đầy giỏ hàng ngay nào.",
          carts_go_to_product_page: "Đi đến trang Sản phẩm",
        }}
      >
        <StoreProvider>
          <TheCartPageEmpty />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="The-Cart-Page-Empty"]').within(() => {
      cy.get("button").should("have.text","Đi đến trang Sản phẩm");
    });
  });
});
