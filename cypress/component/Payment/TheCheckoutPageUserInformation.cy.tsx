import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import TheCheckoutPageUserInformation from "@/components/Cart/TheCheckoutPageUserInformation";

describe("TheCheckoutPageUserInformation.cy.tsx", () => {
  it("Render UI Checkout User Information and validate", () => {
    cy.mount(
      <NextIntlClientProvider
        locale="vi"
        messages={{
          carts_payment_buyer_information: "Thông tin người mua",
          carts_payment_fullname: "Họ và tên",
          carts_payment_phone_number: "Số điện thoại",
          carts_payment_placeholder_fullname: "Ví dụ: Nguyễn <PERSON>ăn <PERSON>",
          carts_payment_placeholder_phone: "Ví dụ: **********",
        }}
      >
        <StoreProvider>
          <TheCheckoutPageUserInformation
            setFieldValue={() => {}}
            errors={{
              buyer_name: "Họ và tên bắt buộc nhập",
              buyer_phone: "<PERSON><PERSON><PERSON><PERSON> thoại bắt buộc nhập",
            }}
            touched={{
              buyer_name: true,
              buyer_phone: true,
            }}
            values={{
              buyer_name: "",
              buyer_phone: "",
              crm_company_id: 0,
              company_business_name: "",
              company_phone: "",
              payment_method_id: 0,
              item_total: 0,
              order_total: 0,
              discount_total: 0,
              quantity: 0,
              term_of_use: true,
              company_tax_number: "",
            }}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get(".checkout-user-information").within(() => {
      cy.get("#email").should("have.attr", "disabled");
    });
  });

  it("Render UI Checkout User Information and fill data", () => {
    cy.mount(
      <NextIntlClientProvider
        locale="vi"
        messages={{
          carts_payment_buyer_information: "Thông tin người mua",
          carts_payment_fullname: "Họ và tên",
          carts_payment_phone_number: "Số điện thoại",
          carts_payment_placeholder_fullname: "Ví dụ: Nguyễn Văn A",
          carts_payment_placeholder_phone: "Ví dụ: **********",
        }}
      >
        <StoreProvider>
          <TheCheckoutPageUserInformation
            setFieldValue={() => {}}
            errors={{}}
            touched={{}}
            values={{
              buyer_name: "Run test",
              buyer_phone: "**********",
              crm_company_id: 0,
              company_business_name: "",
              company_phone: "",
              payment_method_id: 0,
              item_total: 0,
              order_total: 0,
              discount_total: 0,
              quantity: 0,
              term_of_use: true,
              company_tax_number: "",
            }}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get(".checkout-user-information").within(() => {
      cy.get("#email").should("be.disabled");
      cy.get("#buyer_name").type("Run test");
      cy.get("#buyer_phone").type("**********");
      cy.get("#buyer_name").should("have.value", "Run test");
      cy.get("#buyer_phone").should("have.value", "**********");
    });
  });
});
