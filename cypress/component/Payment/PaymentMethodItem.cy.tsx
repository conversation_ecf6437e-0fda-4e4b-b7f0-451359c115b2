import PaymentMethodItem from "@/components/Cart/PaymentMethodItem";
describe("PaymentMethodItem.cy.tsx", () => {
  it("Render UI Payment Method Item", () => {
    cy.mount(
      <PaymentMethodItem
        id={1}
        setFieldValue={() => {}}
        type="credit_card"
        valueCheck={1}
        name="Thẻ ATM nội địa"
        description="Thanh toán bằng thẻ ghi nợ nội địa (NAPAS)"
      />,
    );

    cy.get(".method-item")
      .should("have.class", "bg-gray-100")
      .within(() => {
        cy.get("input").should("have.value", 1);
        cy.get("h3").should("have.text", "Thẻ ATM nội địa");
        cy.get("p").should(
          "have.text",
          "Thanh toán bằng thẻ ghi nợ nội địa (NAPAS)",
        );
      });
  });
});
