import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import { PaymentProps } from "@/types/cart";
import TheCheckoutPageCompanyInformation from "@/components/Cart/TheCheckoutPageCompanyInformation";

const values: PaymentProps = {
  buyer_name: "",
  buyer_phone: "",
  crm_company_id: 0,
  company_business_name: "",
  company_phone: "",
  payment_method_id: 0,
  item_total: 0,
  order_total: 0,
  discount_total: 0,
  quantity: 0,
  term_of_use: false,
  company_tax_number: "",
};

describe("TheCheckoutPageCompanyInformation.cy.tsx", () => {
  it("Render UI Checkout Company Information and validate", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={message}>
        <StoreProvider>
          <TheCheckoutPageCompanyInformation
            setFieldValue={() => {}}
            errors={{
              company_business_name:
                "Tên công ty đăng ký kinh doanh bắt buộc nhập",
              company_phone: "Vui lòng nhập Số điện thoại",
            }}
            touched={{
              company_business_name: true,
              company_phone: true,
            }}
            values={values}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get(".checkout-company-informationtion").within(() => {
      cy.get("#crm_company_id").should("have.id", "crm_company_id");
    });
  });

  it("Render UI Checkout Company Information and fill datas", () => {
    cy.mount(
      <NextIntlClientProvider locale="vi" messages={message}>
        <StoreProvider>
          <TheCheckoutPageCompanyInformation
            setFieldValue={() => {}}
            errors={{
              company_business_name:
                "Tên công ty đăng ký kinh doanh bắt buộc nhập",
              company_phone: "Vui lòng nhập Số điện thoại",
            }}
            touched={{
              company_business_name: true,
              company_phone: true,
            }}
            values={values}
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get(".checkout-company-informationtion").within(() => {
      cy.get("#crm_company_id").should("have.id", "crm_company_id");
      cy.get("#company_business_name").type("Công ty Topdev");
      cy.get("#company_phone").type("**********");
    });
  });
});

const message = {
  carts_payment_company_information: "Thông tin công ty",
  carts_payment_unable_to_query_company_information:
    "Không truy vấn được thông tin công ty",
  carts_payment_alert_content:
    "Rấc tiếc, TopDev đã kiểm tra mã số thuế công ty của bạn và nhận thấy nó không hợp lệ. Vui lòng liên hệ bộ phận CSKH qua số hotline <phone>0888 1555 00</phone> hoặc gửi qua email <email><EMAIL></email> để được hỗ trợ.",
  carts_payment_tax_code: "Mã số thuế",
  carts_payment_name_of_business_registration_company:
    "Tên công ty đăng ký kinh doanh",
  carts_payment_required_company_business_name:
    "Tên công ty đăng ký kinh doanh bắt buộc nhập",
  carts_payment_phone_number: "Số điện thoại",
  carts_payment_required_phone_number: "Vui lòng nhập Số điện thoại",
  carts_payment_placeholder_company_business_name:
    "Ví dụ: Công ty Cổ phần Applancer",
  carts_payment_placeholder_phone: "Ví dụ: 0888155500",
};
