import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import ResultStatusPayment from "@/components/ResultPayment/ResultStatusPayment";

describe("ResultStatusPayment.cy.tsx", () => {
  it("Render Result success Payment", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <ResultStatusPayment
            amount={10000000}
            code="code_order"
            created_at={"13:22:16 - 20/11/2023"}
            id={1}
            paid_at={"13:22:16 - 20/11/2023"}
            payment_method={"Thanh toán momo"}
            status="success"
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="status"]').within(() => {
      cy.get("img").should(
        "have.attr",
        "src",
        "/_next/image?url=%2Fv4%2Fassets%2Fimages%2Fonline-payment%2Fsuccess-icon.png&w=256&q=75",
      );
      cy.get(".text-xl.font-bold").should(
        "have.text",
        messages.carts_status_success_title,
      );
      cy.get(".desc-status-payment").should(
        "have.text",
        "Cảm ơn bạn lựa chọn sử dụng dịch vụ tại TopDev. Chúng tôi sẽ gửi thông tin xác nhận đơn hàng tới địa chỉ email <EMAIL>",
      );

      cy.get(".flex.items-center.justify-between").should("have.length", 4);

      cy.get("button")
        .eq(0)
        .should("have.text", messages.carts_status_go_to_products_page);

      // cy.get("button")
      // .eq(0)
      // .should("have.text", messages.carts_status_order_view_cart_history);
      // cy.get("button")
      //   .eq(1)
      //   .should("have.text", messages.carts_status_go_to_products_page);

        cy.get('[data-cy="footer"]').should("exist");
    });
  });

  it("Render Result failed Payment", () => {
    cy.mount(
      <NextIntlClientProvider locale={"vi"} messages={messages}>
        <StoreProvider>
          <ResultStatusPayment
            amount={10000000}
            code="code_order"
            created_at={"13:22:16 - 20/11/2023"}
            id={1}
            paid_at={""}
            payment_method={"Thanh toán momo"}
            status="failed"
          />
        </StoreProvider>
      </NextIntlClientProvider>,
    );

    cy.get('[data-cy="status"]').within(() => {
      cy.get("img").should(
        "have.attr",
        "src",
        "/_next/image?url=%2Fv4%2Fassets%2Fimages%2Fonline-payment%2Ferror-icon.png&w=256&q=75",
      );
      cy.get(".text-xl.font-bold").should(
        "have.text",
        messages.carts_status_failed_title,
      );
      cy.get(".desc-status-payment").should(
        "have.text",
        messages.carts_status_failed_description,
      );

      cy.get(".flex.items-center.justify-between").should("have.length", 4);

      cy.get("button")
      .eq(0)
      .should("have.text", messages.carts_status_go_to_products_page);

      // cy.get("button")
      //   .eq(0)
      //   .should("have.text", messages.carts_status_order_view_cart_history);

      // cy.get("button")
      //   .eq(1)
      //   .should("have.text", messages.carts_status_go_to_products_page);

        cy.get('[data-cy="footer"]').should("exist");
    });
  });
});

const messages = {
  carts_status_success_title: "Mua hàng thành công!",
  carts_status_failed_title: "Mua hàng không thành công!",
  carts_status_success_description:
    "Cảm ơn bạn lựa chọn sử dụng dịch vụ tại TopDev. Chúng tôi sẽ gửi thông tin xác nhận đơn hàng tới địa chỉ email <b><EMAIL></b>",
  carts_status_failed_description:
    "Cảm ơn bạn lựa chọn sử dụng dịch vụ tại TopDev. Rất tiếc đơn hàng của bạn thanh toán chưa thành công, xin vui lòng kiểm tra lại.",
  carts_status_order_code: "Mã đơn hàng",
  carts_status_time_order: "Thời gian đặt hàng",
  carts_status_payment_time: "Thời gian thanh toán",
  carts_status_payment_methods: "Phương thức thanh toán",
  carts_status_order_value: "Giá trị đơn hàng",
  carts_status_order_view_cart_history: "Xem lịch sử giỏ hàng",
  carts_status_go_to_products_page: "Về trang mua hàng",
  carts_status_for_support:
    "Để được hỗ trợ vui lòng gửi qua email <a><EMAIL></a>",
  carts_status_call_hotline: "hoặc gọi vào số hotline <a>0888 1555 00</a>",
};
