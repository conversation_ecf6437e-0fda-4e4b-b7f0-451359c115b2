import { NextIntlClientProvider } from "next-intl";
import StoreProvider from "@/store/provider";
import ResultNotFoundPayment from "@/components/ResultPayment/ResultNotFoundPayment";

describe("ResultNotFoundPayment.cy.tsx", () => {
  it("Render Result NotFound Payment", () => {
    cy.mount(
      <NextIntlClientProvider
        locale={"vi"}
        messages={{
          carts_status_for_support:
            "Để được hỗ trợ vui lòng gửi qua email <a><EMAIL></a>",
          carts_status_call_hotline:
            "hoặc gọi vào số hotline <a>0888 1555 00</a>",
          carts_status_order_view_cart_history: "Xem lịch sử giỏ hàng",
          carts_status_status_title_notfound: "Opps! Không tìm thấy đơn hàng",
          carts_status_description_notfound:
            "Đơn hàng này không tồn tại trên TopDev. <PERSON><PERSON> thể đường dẫn URL đã bị thay đổi hoặc do hệ thống gặp sự cố. <PERSON><PERSON><PERSON> lo lắng, bạn có thể tìm kiếm các đơn hàng của mình trong Lịch sử đơn hàng.",
        }}
      >
        <StoreProvider>
          <ResultNotFoundPayment />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
  });
});
