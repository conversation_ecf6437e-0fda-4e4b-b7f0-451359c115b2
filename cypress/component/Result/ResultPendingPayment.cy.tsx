import StoreProvider from "@/store/provider";
import { NextIntlClientProvider } from "next-intl";
import ResultPendingPayment from "@/components/ResultPayment/ResultPendingPayment";

describe("ResultPendingPayment.cy.tsx", () => {
  it("Render Result Pending Payment", () => {
    cy.mount(
      <NextIntlClientProvider
        locale={"vi"}
        messages={{
          carts_status_pending_title: "Giao dịch đang được xử lý",
          carts_status_peding_description:
            "Cổng thanh toán đang tiến hành xử lý thanh toán của bạn. quá trình có thể mất một ít thời gian, bạn chờ chút nhé.",
        }}
      >
        <StoreProvider>
          <ResultPendingPayment />
        </StoreProvider>
      </NextIntlClientProvider>,
    );
  });
});
