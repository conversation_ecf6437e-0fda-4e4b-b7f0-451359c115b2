<template>
  <div class="container-fluid bg-white">
    <div v-if="isCandidatesLoading" class="py-4 pt-5">
      <TheCandidatesSearchWidget isAllCandidates :isDetail="true"/>

      <!-- Tabs -->
      <div class="box-nav line-tabs-candidates">
        <div class="d-flex pt-5 align-items-center justify-content-between">
          <div class="d-flex align-items-center nav-tabs flex-1">
            <button
              class="pb-5 btn-tab mw-125px"
              :class="{ active: candidatesStore.candidatesParams.filter.matching_status == 1 }"
              type="button"
              @click="() => activeTab('bestMatch')"
              :title="translate('candidate_tab_best_match')"
            >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_best_match") }}
          </span>
            </button>
            <button
              class="pb-5 btn-tab mw-125px"
              :class="{ active: candidatesStore.candidatesParams.filter.matching_status == 0 }"
              type="button"
              @click="() => activeTab('other')"
              :title="translate('candidate_tab_other')"
            >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_other") }}
          </span>
            </button>
            <button
              class="pb-5 btn-tab mw-125px"
              :class="{ active: candidatesStore.candidatesParams.filter.matching_status == null }"
              type="button"
              @click="() => activeTab('all')"
              :title="translate('candidate_tab_all')"
            >
          <span class="line-clamp-1">
            {{ translate("candidate_tab_all") }}
          </span>
            </button>
          </div>
        </div>
      </div>

      <!-- List candidates  -->
      <div class="candidate-table-container">
        <table class="candidate-table">
          <thead>
          <tr class="table-header">
            <th class="header-no">{{ translate("candidate_list_no") }}</th>
            <th class="header-candidate">{{ translate("candidate_list_candidate_information") }}</th>
            <th class="header-experience">{{ translate("search_resumes_candidate_experience") }}</th>
            <th class="header-job">{{ translate("candidate_list_job_title") }}</th>
            <th class="header-action">{{ translate("candidate_list_action") }}</th>
          </tr>
          </thead>
          <tbody v-if="candidates.length > 0" id="table-body">
          <AppLoader />
          <tr
            class="candidate-row"
            v-for="(candidate, index) in candidates"
            :key="index"
          >
            <!-- Number Column -->
            <td class="cell-no">
              <div class="number-chip">{{ index + noPrefix }}</div>
            </td>
            <!-- Candidate Information Column -->
            <td class="cell-candidate" @click="toDetailCandidates(candidate.id)">
              <div v-if="candidate.is_resume_exists" class="candidate-info">
                <div class="candidate-header">
                  <div class="avatar-circle">
                    {{ candidate.full_name ? candidate.full_name.charAt(0).toUpperCase() : 'U' }}
                  </div>
                  <div class="candidate-details">
                    <div class="name-section">
                      <h6 class="candidate-name">{{ candidate.full_name }}</h6>
                      <div v-if="candidate.matching_status === 1" class="best-match-badge">
                        <span class="badge-text">Best Match</span>
                        <inline-svg width="12" height="12" src="assets/icons/candidates/sparkles.svg" />
                      </div>
                    </div>

                    <div class="contact-info">
                      <div class="contact-item">
                        <span class="contact-icon">📧</span>
                        <span class="contact-text">{{ candidate.email }}</span>
                      </div>
                      <div class="contact-item">
                        <span class="contact-icon">📞</span>
                        <span class="contact-text">{{ candidate.phone }}</span>
                      </div>
                      <div class="contact-item">
                        <span class="contact-icon">📍</span>
                        <span class="contact-text">{{ candidate.location }}</span>
                      </div>
                    </div>

                    <div v-if="candidate.cover_letter" class="cover-letter-info">
                      <span class="cover-letter-icon">📄</span>
                      <span class="cover-letter-text">{{ translate("candidate_list_cover_letter") }}</span>
                    </div>

                    <div class="applied-date">
                      <span class="applied-icon">📅</span>
                      <span class="applied-text">{{ translate("candidate_list_applied_date") }}: </span>
                      <span v-html="candidate.applied_at"></span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="disabled-candidate">
                <span>{{ translate("candidate_detail_user_has_been_disabled") }}</span>
              </div>
            </td>
            <!-- Experience Column -->
            <td class="cell-experience">
              <div class="experience-content">
                <p class="experience-description">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor.
                </p>

                <div class="skills-section">
                  <span class="skills-label">Skill(s):</span>
                  <div class="skills-tags">
                    <span v-for="(skill, skillIndex) in (candidate.skills || ['PHP', 'JavaScript', 'CSS']).slice(0, 3)"
                          :key="skillIndex"
                          class="skill-tag">
                      {{ typeof skill === 'object' ? skill.skill_name : skill }}
                    </span>
                  </div>
                </div>

                <div class="experience-details">
                  <div class="experience-label">
                    <span>Experience:</span>
                    <span class="experience-badge">6 YOE</span>
                  </div>
                  <div class="experience-list">
                    <div class="experience-item">
                      <span class="experience-date">06/2018 - 04/2022</span>
                      <span class="experience-separator">|</span>
                      <span class="experience-position">Graphic Designer - abcxyz</span>
                    </div>
                    <div class="experience-item">
                      <span class="experience-date">06/2018 - 04/2022</span>
                      <span class="experience-separator">|</span>
                      <span class="experience-position">Graphic Designer - abcxyz</span>
                    </div>
                    <div class="experience-item">
                      <span class="experience-date">06/2018 - 04/2022</span>
                      <span class="experience-separator">|</span>
                      <span class="experience-position">Graphic Designer - abcxyz</span>
                    </div>
                    <div class="experience-item">
                      <span class="experience-date">06/2018 - 04/2022</span>
                      <span class="experience-separator">|</span>
                      <span class="experience-position">Graphic Designer - abcxyz</span>
                    </div>
                  </div>
                </div>

                <div class="education-section">
                  <span class="education-label">Education:</span>
                  <span class="education-text">Posts and Telecommunication Institute of Technology - Ho Chi Minh City Campus</span>
                </div>
              </div>
            </td>

            <!-- Job Title Column -->
            <td class="cell-job">
              <a @click="navigationToJobDetailPage(candidate.job_detail_url, $event)"
                 class="job-title-link">
                {{ candidate.job_title }}
              </a>
            </td>
            <!-- Action Column -->
            <td class="cell-action">
              <div class="action-buttons">
                <button class="action-btn view-btn" @click="toDetailCandidates(candidate.id)">
                  👁️
                </button>
                <button class="action-btn note-btn">
                  📝
                </button>
                <button class="action-btn download-btn" @click="downloadCV(candidate.id, $event)">
                  ⬇️
                </button>
              </div>

              <div class="recruitment-process">
                <select class="process-select">
                  <option>Recruitment Process</option>
                  <option>Matched</option>
                  <option>Interview Passed</option>
                  <option>Interview Failed</option>
                  <option>Offer</option>
                  <option>Hired</option>
                  <option>Failed</option>
                </select>
              </div>
            </td>
          </tr>
          </tbody>

          <!-- If jobs empty -->
          <tbody v-else id="table-body">
          <tr>
            <td colspan="6" class="text-center">
              <inline-svg
                src="/assets/icons/candidates/no-search-result.svg"
                class="empty-icon"
              ></inline-svg>
              <p style="font-size: 16px">
                {{
                  translate("candidate_list_you_currently_have_no_candidates")
                }}
              </p>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
      <!-- End list candidates  -->
    </div>
    <!-- Skeletor  -->
    <div v-else class="py-5">
      <Skeletor />
      <Skeletor />
      <Skeletor />
    </div>
  </div>

  <!-- Pagination  -->
  <div
    class="container-fluid bg-white"
    v-if="isCandidatesLoading && candidates.length"
  >
    <div class="pb-5 border-gray-500">
      <AppPagination :meta="candidatesMeta" @setPagination="setPagination" />
    </div>
  </div>
</template>

<style scoped>
/* Candidate Table Styles based on Figma Design */
.candidate-table-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  padding: 0px;
  width: 100%;
}

.candidate-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* Header Styles */
.table-header {
  background: #F0F5FE;
  border: 0.5px solid #C2D5FB;
}

.header-no {
  width: 65px;
  height: 32px;
  padding: 10px 16px;
  text-align: center;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #4876EF;
  border-right: 0.5px solid #C2D5FB;
}

.header-candidate {
  width: 292px;
  height: 32px;
  padding: 10px 16px;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #4876EF;
  border-right: 0.5px solid #C2D5FB;
}

.header-experience {
  width: 714px;
  height: 32px;
  padding: 10px 16px;
  text-align: center;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #4876EF;
  border-right: 0.5px solid #C2D5FB;
}

.header-job {
  width: 200px;
  height: 32px;
  padding: 10px 16px;
  text-align: center;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #4876EF;
  border-right: 0.5px solid #C2D5FB;
}

.header-action {
  width: 150px;
  height: 32px;
  padding: 10px 16px;
  text-align: center;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #4876EF;
}

/* Row Styles */
.candidate-row {
  background: #FFFFFF;
  border-bottom: 0.5px solid #C2D5FB;
}

.candidate-row:hover {
  background: #F8FBFF;
}

/* Cell Styles */
.cell-no {
  width: 65px;
  padding: 12px 16px;
  text-align: center;
  border-right: 0.5px solid #C2D5FB;
  vertical-align: top;
}

.number-chip {
  background: #F0F5FE;
  border: 0.5px solid #C2D5FB;
  border-radius: 4px;
  padding: 10px 16px;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  color: #4876EF;
  display: inline-block;
}

.cell-candidate {
  width: 292px;
  padding: 12px 20px;
  border-right: 0.5px solid #C2D5FB;
  vertical-align: top;
  cursor: pointer;
}

.candidate-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.avatar-circle {
  width: 43px;
  height: 44px;
  background: #DCE7FD;
  border-radius: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 16px;
  color: #4876EF;
  flex-shrink: 0;
}

.candidate-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.name-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.candidate-name {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #4F4F4F;
  margin: 0;
}

.best-match-badge {
  background: #D7FFE3;
  padding: 2px 6px 2px 8px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 2px;
  width: fit-content;
}

.badge-text {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 12px;
  color: #049132;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.contact-icon {
  width: 10px;
  height: 10px;
  font-size: 10px;
}

.contact-text {
  font-family: 'Inter';
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #6D6D6D;
}

.cover-letter-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.cover-letter-icon {
  font-size: 10px;
}

.cover-letter-text {
  font-family: 'Inter';
  font-weight: 400;
  font-size: 12px;
  color: #FF821E;
}

.applied-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.applied-icon {
  font-size: 10px;
}

.applied-text {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 12px;
  color: #6D6D6D;
}

.cell-experience {
  width: 714px;
  padding: 12px 16px 16px;
  border-right: 0.5px solid #C2D5FB;
  vertical-align: top;
}

.experience-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.experience-description {
  font-family: 'Inter';
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #4F4F4F;
  margin: 0;
}

.skills-section {
  display: flex;
  align-items: flex-start;
  gap: 65px;
}

.skills-label {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  color: #4F4F4F;
  width: 51px;
}

.skills-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.skill-tag {
  background: #F0F5FE;
  border: 0.5px solid #97BBF9;
  border-radius: 2px;
  padding: 4px 8px;
  font-family: 'Inter';
  font-weight: 400;
  font-size: 12px;
  color: #4876EF;
}

.experience-details {
  display: flex;
  gap: 36px;
}

.experience-label {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 80px;
}

.experience-label span:first-child {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  color: #4F4F4F;
}

.experience-badge {
  background: #FFF8ED;
  border-radius: 2px;
  padding: 2px 6px 2px 8px;
  font-family: 'Inter';
  font-weight: 600;
  font-size: 12px;
  color: #FF821E;
  width: fit-content;
}

.experience-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 332px;
}

.experience-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.experience-date {
  font-family: 'Inter';
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  width: 127px;
}

.experience-separator {
  font-family: 'Inter';
  font-weight: 400;
  font-size: 14px;
  color: #888888;
  width: 5px;
}

.experience-position {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  color: #4F4F4F;
  width: 180px;
}

.education-section {
  display: flex;
  gap: 42px;
}

.education-label {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  color: #4F4F4F;
  width: 73px;
}

.education-text {
  font-family: 'Inter';
  font-weight: 400;
  font-size: 14px;
  color: #4F4F4F;
  flex: 1;
}

.cell-job {
  width: 200px;
  padding: 12px 16px;
  border-right: 0.5px solid #C2D5FB;
  vertical-align: top;
  text-align: center;
}

.job-title-link {
  font-family: 'Inter';
  font-weight: 600;
  font-size: 14px;
  color: #4876EF;
  text-decoration: none;
  cursor: pointer;
}

.job-title-link:hover {
  text-decoration: underline;
}

.cell-action {
  width: 150px;
  padding: 12px 16px;
  vertical-align: top;
  text-align: center;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #C2D5FB;
  border-radius: 4px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
}

.action-btn:hover {
  background: #F0F5FE;
}

.recruitment-process {
  margin-top: 8px;
}

.process-select {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #C2D5FB;
  border-radius: 4px;
  font-family: 'Inter';
  font-size: 11px;
  color: #6D6D6D;
  background: white;
}

.disabled-candidate {
  color: #888888;
  font-style: italic;
}
</style>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";

// import TheSelectBoxStatusCandidates from "@/components/TheSelectBoxStatusCandidates.vue";

import { fetchCandidateCV, fetchCandidates } from "@/api/candidate";
import { useCandidatesStore, useLayoutStore } from "@/stores";

import { fetchCompanyInfo } from "@/api/company";
import AppLoader from "@/components/AppLoader.vue";
// import AppPagination from "@/components/AppPagination.vue";
// import TheCandidateExperienceInformation from "@/components/TheCandidateExperienceInformation.vue";
// import TheCandidatesSearchWidget from "@/components/TheCandidatesSearchWidget.vue";
import { scrollToTop, showWarningToast, translate } from "@/helpers";
import { Candidate } from "@/models/candidates";
import { Meta } from "@/models/jobs";
import { InlineSvg } from "@/plugins";
import { COMPANY_STATUS_REVIEW } from "@/schemas/company-profile-form";

//Define store
const layoutStore = useLayoutStore();
const candidatesStore = useCandidatesStore();

//Define router
const router = useRouter();
const route = useRoute();

//Define data
const isCandidatesLoading = ref(false);
const candidates = ref<Candidate[]>([]);
const candidatesMeta = ref<Meta>();
const idChange = ref(0);
const getDataStatus = ref([]);

const breakLine = (text: string) => {
  if (!text) {
    return "";
  }

  return text.replace(" ", "<br>");
};
const toDetailCandidates = (id: number) => {
  const validTabs = ['bestMatch', 'other', 'all'];
  const tabParam = validTabs.includes(route.query.tab as string) ? route.query.tab : 'bestMatch';
  router.push(`/candidates/${id}?tab=${tabParam}`);
};

const downloadCV = (id: number, event: any) => {
  event.stopPropagation();
  fetchCandidateCV(id)
    .then((response) => {
      window.location.href = response.data.download_url;
    })
    .catch(() => {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
    });
};

const getAndSetSeeMore = async () => {
  const items = (await document.getElementsByClassName(
    "box_search_resumes_work_experience_education"
  )) as HTMLCollectionOf<HTMLElement>;
  for (let i = 0; i < items.length; i++) {
    let element = items[i];
    let height = element.clientHeight;
    if (height > 160) {
      element.classList.add("fix");
      element.lastElementChild.classList.add("d-inline-flex");
    } else {
      element.classList.add("no-fix");
      element.lastElementChild.classList.add("d-none");
    }
  }
};

const navigationToJobDetailPage = (detailJobUrl: any, event: any) => {
  event.stopPropagation();
  window.open(detailJobUrl);
};

const setPagination = (value: any) => {
  candidatesStore.setCandidatesParams(value);
};

const openDropList = (id: number, event: any) => {
  event.stopPropagation();
  idChange.value = id;
};
const closeDropList = () => {
  idChange.value = 0;
};
const initializeTabState = (tabActive: string = null) => {
  let tab = route.query.tab as string;
  let page = candidatesStore.candidatesParams.page;
  let page_size = candidatesStore.candidatesParams.page_size;
  if (tabActive) {
    tab = tabActive;
    page = 1;
    page_size = 30;
  }
  let matching_status: number | null; // Explicitly type matching_status

  switch (tab) {
    case 'bestMatch':
      matching_status = 1;
      break;
    case 'other':
      matching_status = 0;
      break;
    case 'all':
      matching_status = null;
      break;
    default:
      matching_status = 1;
  }

  candidatesStore.setCandidatesParams({
    filter: { ...candidatesStore.candidatesParams.filter, matching_status},
    page: page,
    page_size: page_size
  });
}
const addQueryTab = (tabActive: string) => {
  const { path, query } = route;
  router.replace({
    path,
    query: { ...query, tab: tabActive }
  });
}
const activeTab = (tabActive: string) => {
  addQueryTab(tabActive)
  initializeTabState(tabActive);
}
//Life cycle
onMounted(async () => {
  layoutStore.setPageTitle("layout_manage_candidates");

  fetchCompanyInfo()
    .then(({data}) => {
      if (data.status == COMPANY_STATUS_REVIEW) {
          showWarningToast(
            "",
            "You are not granted access to this feature. Please complete your company profile"
          )

          router.push('/company/profile');
        }
      });

  //Fetch Candidates
  try {
    const { data, meta }: any = await fetchCandidates(
      candidatesStore.candidatesParamsRequest
    );
    candidates.value = data;
    candidatesMeta.value = meta;
    initializeTabState()
  } catch (err) {
    showWarningToast(
      translate("toast_sorry"),
      translate("toast_save_failed_message")
    );
  } finally {
    isCandidatesLoading.value = true;
    await getAndSetSeeMore();
  }

  window.onclick = function() {
    if (idChange.value > 0) closeDropList();
  };
});
// Watch for changes to candidatesParams
watch(
  () => candidatesStore.candidatesParams,
  async () => {
    const tableBody = document.getElementById("table-body");
    tableBody.className = "page-loading";
    try {
      const { data, meta }: any = await fetchCandidates(
        candidatesStore.candidatesParamsRequest
      );
      candidates.value = data;
      candidatesMeta.value = meta;
    } catch (err) {
      showWarningToast(
        translate("toast_sorry"),
        translate("toast_save_failed_message")
      );
      throw err;
    } finally {
      tableBody.classList.remove("page-loading");
      await getAndSetSeeMore();
      scrollToTop();
    }
  },
  { deep: true }
);

const noPrefix = computed(() => {
  return (
    1 +
    (candidatesMeta.value.current_page - 1) *
    parseInt(candidatesMeta.value.per_page)
  );
});

watch(
  () => candidatesStore.getValueStatusCandidates,
  (data) => {
    if (
      JSON.stringify(getDataStatus.value) ===
      JSON.stringify(candidatesStore.getValueStatusCandidates)
    )
      return;
    getDataStatus.value = data;
  },
  { deep: true }
);
</script>
