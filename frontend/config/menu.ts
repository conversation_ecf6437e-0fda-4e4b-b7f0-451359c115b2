

interface MenuItemsType {
  heading: string;
  route: string;
  activeRoutes?: string[];
  svgIcon?: string;
}

interface MainMenuType extends MenuItemsType {
  childItems?: MenuItemsType[];
}

const MainMenuConfig: MainMenuType[] = [
  {
    heading: "top_bar_dashboard",
    route: "/dashboard",
    svgIcon: "/assets/icons/aside/dashboard.svg",
  },
  {
    heading: "layout_account_setting",
    route: null,
    activeRoutes: ['company-profile', 'company-contact'],
    svgIcon: "/assets/icons/aside/account-setting.svg",
    childItems: [
      {
        heading: "top_bar_company_profile",
        route: "/company",
        activeRoutes: ['company-profile']
      },
      {
        heading: "top_bar_contact_information",
        route: "/company/contact",
        activeRoutes: ['company-contact']
      }
    ]
  },
  {
    heading: "layout_manage_jobs",
    route: "/jobs",
    svgIcon: "/assets/icons/aside/manage-job.svg",
  },
  {
    heading: "layout_manage_candidates",
    route: "/candidates",
    svgIcon: "/assets/icons/aside/manage-candidates.svg",
  },
  {
    heading: "layout_search_candidates",
    route: "/search-candidates",
    svgIcon: "/assets/icons/aside/search-candidates.svg",
  },
  {
    heading: "layout_usage_history",
    route: null,
    activeRoutes: ['unlock-candidate-management'],
    svgIcon: "/assets/icons/aside/my-products.svg",
    childItems: [
      {
        heading: "layout_credit_management",
        route: "/unlock-candidate-management",
        activeRoutes: ['unlock-candidate-management']
      }
    ]
  },
];

export default MainMenuConfig;

interface TabMenuConfigItem {
  name: string;
  title: string;
}
const CompanyProfileMenuConfig: TabMenuConfigItem[] = [
  {
    name: "company-profile",
    title: "top_bar_company_profile",
  },
  {
    name: "company-contact",
    title: "top_bar_contact_information",
  },
];

const ManageJobMenuConfig: TabMenuConfigItem[] = [
  {
    name: "jobs",
    title: "top_bar_all_jobs",
  },
  {
    name: "post-job",
    title: "top_bar_post_job",
  },
];

const MyProductMenuConfig: TabMenuConfigItem[] = [
  {
    name: "my-products-job-postings",
    title: "layout_job_posting_management",
  }
];

export { CompanyProfileMenuConfig, ManageJobMenuConfig, MyProductMenuConfig };
