<template>
  <div class="bg-white px-8 py-2 flex gap-7 mb-4">
    <TopSide />
  </div>
  <div class="bg-white px-[32px] py-3">
    <div v-if="!loading" class="flex gap-3">
      <div v-for="(stat, index) in formattedStats" :key="index" class="w-[20%] px-8 py-4 rounded-[4px]"
        :style="{ color: stat?.textColor, backgroundColor: stat?.bgColor }">
        <span class="block font-medium text-[32px]/[40px]">
          {{ stat?.value ?? 0 }}
        </span>
        <span class="text-sm">{{ translate(stat?.label) ?? 0 }}</span>
      </div>
    </div>
    <div v-show="loading">
      <Skeletor width="100%" height="95" />
    </div>
  </div>
  <MiddleSide />
  <div class="flex gap-[10px] mt-[10px]">
    <PackageBalance />
    <PremiumJobs />
  </div>
</template>

<script lang="ts" setup>
import { getApplicationScoreCards } from '@/api/dashboard';
import MiddleSide from '@/components/HrDashBoard/MiddleSide.vue';
import PackageBalance from '@/components/HrDashBoard/PackageBalance.vue';
import PremiumJobs from '@/components/HrDashBoard/PremiumJobs.vue';
import TopSide from '@/components/HrDashBoard/TopSide.vue';
import { translate } from '@/helpers';
import { ScoreCardStats } from '@/models/dashboard';
import { useDashBoardStore } from "@/stores";
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';
import { Skeletor } from "vue-skeletor";

const dashBoardStore = useDashBoardStore();
const { from_date,to_date,job_id } = storeToRefs(dashBoardStore)

const loading = ref(false);
const stats = ref()
const formattedStats = ref([])
const error = ref<string | null>(null);


const fetchScoreCardStats = async (params?: {
  jobId?: number[] | null;
  fromDate?: string;
  toDate?: string;
}) => {
  const allState = {from_date,to_date,job_id }

  const requestParams = params
    ? { ...allState, job_id: params.jobId, from_date: params.fromDate, to_date: params.toDate }
    : undefined;

  const response = await getApplicationScoreCards(requestParams);
  stats.value = response.data as ScoreCardStats;
  const valueMap = response?.data ?? {
    "applications_today": 0,
    "total_application": 0,
    "not_viewed_application": 0,
    "viewed_application": 0,
    "view_job": 0,
  }
  const desiredOrder = [
    "applications_today",
    "total_application",
    "not_viewed_application",
    "viewed_application",
    "view_job"
  ];

  const formatData = desiredOrder.map((key) => ({
    value: valueMap[key] ?? null,
    label: statsMapping[key]?.label,
    bgColor: statsMapping[key]?.bgColor,
    textColor: statsMapping[key]?.textColor
  }));

  formattedStats.value = formatData;

};

const loadApplicationScoreCards = async (jobId?: number[] | null, fromDate?: string, toDate?: string) => {
  try {
    loading.value = true;
    if (fromDate && toDate) {
      await fetchScoreCardStats({ jobId, fromDate, toDate });
    } else {
      await fetchScoreCardStats();
    }
  } catch (err) {
    error.value = 'Failed to load premium jobs';
  } finally {
    loading.value = false;
  }
};


watch(
  () => ({
    jobId: dashBoardStore.job_id,
    toDate: dashBoardStore.to_date,
    fromDate: dashBoardStore.from_date
  }),
  (newValues) => {
    const { jobId, toDate, fromDate } = newValues;
    loadApplicationScoreCards(jobId, fromDate, toDate);
  }
);
const statsMapping = {
  applications_today: {
    label: 'hr_dashboard_application_today',
    bgColor: '#F2F9FF',
    textColor: '#4876EF'
  },
  total_application: {
    label:'hr_dashboard_total_applications',
    bgColor: '#F2FFF7',
    textColor: '#16A34A'
  },
  not_viewed_application: {
    label: 'hr_dashboard_not_viewed_applications',
    bgColor: '#E9E9E9',
    textColor: '#4F4F4F'
  },
  viewed_application: {
    label: 'hr_dashboard_viewed_applications',
    bgColor: '#FFF9ED',
    textColor: '#F59E0B'
  },
  view_job: {
    label: 'hr_dashboard_total_views',
    bgColor: '#FFF6F5',
    textColor: '#4876EF'
  },
};

</script>
