<template>
    <div class="w-full flex my-4 gap-4">
        <div class="bg-white p-6 w-[60%]">
            <div class="mb-4">
                <span class="text-[#4F4F4F] font-semibold">{{ translate('hr_dashboard_application_time_series') }}

                </span>
            </div>
            <div v-if="!loading" class="border bg-[#FAFAFA]">
                <ChartBar :categories="chartCategories" :series="chartSeries" />
            </div>
            <div class="border border-[#E3E3E3]" v-show="loading">
                <Skeletor width="100%" height="472" />
            </div>
        </div>
        <div class="bg-white p-6 w-[40%]">
            <div class="mb-4">
                <span class="text-[#4F4F4F] font-semibold">{{ translate('hr_dashboard_application_by_status') }} </span>
            </div>
            <div v-if="!loadingStatus" class="bg-[#FAFAFA] px-8 py-6 h-[92%] flex items-center">
                <ChartBarHorizontal :data="chartData" />
            </div>
            <div v-show="loadingStatus">
                <Skeletor width="100%" height="472" />
            </div>
        </div>
    </div>
</template> 

<script lang="ts" setup>
import { getApplicationByStatus, getApplicationTimeSeries } from '@/api/dashboard';
import ChartBar from '@/components/ChartBar.vue';
import ChartBarHorizontal from '@/components/ChartBarHorizontal.vue';
import { ChartDataItem, SeriesItem } from '@/models/dashboard';
import { useDashBoardStore } from '@/stores';
import { storeToRefs } from 'pinia';
import { ref, watch } from 'vue';
import { Skeletor } from "vue-skeletor";
import { translate } from "@/helpers";
import { useI18n } from 'vue-i18n';
const { locale } = useI18n()


const loading = ref(false);
const loadingStatus = ref(false);
const chartData = ref<ChartDataItem[]>();
const error = ref<string | null>(null);
const dashBoardStore = useDashBoardStore();
const allState = dashBoardStore.$state;
const { from_date,to_date,job_id } = storeToRefs(dashBoardStore)

const mapToChartData = (apiData) => {
    const totalApplications = apiData.find(item => item.status === "all")?.total || 0;
    const statusMapping = [
        {
            apiStatus: "all",
            label: "hr_dashboard_cv_applied",
            color: "#4876EF",
            bgColor: "#F0FDF5",
            textColor: "#4876EF"
        },
        {
            apiStatus: "matched",
            label: "hr_dashboard_pass_cv_screen",
            color: "#22C55E",
            bgColor: "#F0FDF5",
            textColor: "#22C55E"
        },
        {
            apiStatus: "not_matching",
            label: "hr_dashboard_cv_not_matched",
            color: "#454545",
            bgColor: "#E7E7E7",
            textColor: "#454545"
        },
        {
            apiStatus: "interviewed",
            label: "hr_dashboard_interview_appointment",
            color: "#4876EF",
            bgColor: "#EEF8FF",
            textColor: "#4876EF"
        }
    ];

    const chartData = statusMapping.map(mapping => {
        const apiItem = apiData.find(item => item.status === mapping.apiStatus);
        const count = apiItem ? apiItem.total : 0;
        const percent = totalApplications > 0 ? Math.round((count / totalApplications) * 100) : 0;

        return {
            label: mapping.label,
            count: count,
            percent: percent,
            color: mapping.color,
            bgColor: mapping.bgColor,
            textColor: mapping.textColor
        };
    });

    return chartData;
};

const fetchApplicationChartData = async (params?: {
    jobId?: number[] | null;
    fromDate?: string;
    toDate?: string;
}) => {
    const allState = { from_date,to_date,job_id }; 

    const requestParams = params?.fromDate && params?.toDate || params?.jobId
        ? { ...allState, job_id: params.jobId, from_date: params.fromDate, to_date: params.toDate }
        : undefined;

    const response = await getApplicationByStatus(requestParams);
    chartData.value = mapToChartData(response.data);
};

const loadApplicationByStatus = async (jobId?: number[] | null, fromDate?: string, toDate?: string) => {
    try {
        loadingStatus.value = true;
        if (fromDate && toDate) {
            await fetchApplicationChartData({ jobId, fromDate, toDate });
        } else {
            await fetchApplicationChartData();
        }
    } catch (err) {
        error.value = 'Failed to load premium jobs';
        console.error(err);
    } finally {
        loadingStatus.value = false;
    }
};

const defaultXaxis = [
    "Jan.", "Feb.", "Mar.", "Apr.", "May", "June", "July", "Aug.", "Sept.", "Oct.", "Nov.", "Dec."
];

const chartCategories = ref<string[]>([
]);

const chartSeries = ref<SeriesItem[]>([

]);
const fetchChartData = async (params?: {
    jobId?: number[] | null;
    fromDate?: string;
    toDate?: string;
}) => {
    const allState = { from_date,to_date,job_id }; 
    const requestParams = params?.fromDate && params?.toDate || params?.jobId
        ? { ...allState, job_id: params.jobId, from_date: params.fromDate, to_date: params.toDate }
        : undefined;

    const response = await getApplicationTimeSeries(requestParams);
    const dateMap = response.data.map(x => x.date);

    chartCategories.value = dateMap.length > 0 ? dateMap : defaultXaxis;
    chartSeries.value = [
        { name: translate('hr_dashboard_unviewed'), data: response.data.map(x => x.not_viewed), color: "#C40000" },
        { name: translate('hr_dashboard_viewed'), data: response.data.map(x => x.viewed), color: "#0C5DE9" }
    ];
};
const loadApplicationTimeSeries = async (jobId?: number[] | null, fromDate?: string, toDate?: string) => {
    try {
        loading.value = true;
        if (fromDate && toDate) {
            await fetchChartData({ ...allState, jobId, fromDate, toDate });
        } else {
            await fetchChartData();
        }
    } catch (err) {
        error.value = 'Failed to load premium jobs';
        console.error(err);
    } finally {
        loading.value = false;
    }
};




watch(
    () => ({
        jobId: dashBoardStore.job_id,
        toDate: dashBoardStore.to_date,
        fromDate: dashBoardStore.from_date
    }),
    (newValues) => {
        const { toDate, fromDate, jobId } = newValues;
        loadApplicationByStatus(jobId, fromDate, toDate);
        loadApplicationTimeSeries(jobId, fromDate, toDate);
    }
);
watch(locale, () => {
    chartSeries.value = [
        {...chartSeries.value[0], name: translate('hr_dashboard_unviewed') },
        { ...chartSeries.value[1], name: translate('hr_dashboard_viewed') }
    ];
})
</script>

<style>
.active {
    border-color: #F98470;
    background-color: #FEE6E2;
    color: #BA321B;
}

.border-gray {
    border: 0.5px solid #888888;
}

.border-orange {
    border: 0.5px solid #F98470;
}
</style>