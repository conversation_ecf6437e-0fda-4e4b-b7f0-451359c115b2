<template>
  <section id="company-erc-container" class="w-100">
    <input
      type="file"
      id="company-erc__uploader-input"
      @change="uploadERC($event.target.files)"
      class="d-none"
      ref="ercFileInputRef"
    />
    <div class="d-flex" id="company-erc">
      <div id="company-erc__intro">
        <h2 id="company-erc__intro-title" class="text-extra-large-bold">
          {{ translate('about_company_upload_enterprise_registration_certificate') }} <span>*</span>
        </h2>
        <p id="company-erc__intro-helper" class="text-small-regular">
          {{ translate('about_company_upload_this_to_verify_your_business_legitimacy') }}
        </p>
      </div>
      <div id="company-erc__uploader" v-if="ercFile == null" class="upload-section-blue">
        <div id="company-erc__uploader-icon">
          <img
            src="/assets/icons/erc-upload.svg"
            alt="upload icon"
            width="49"
            height="49"
          />
        </div>
        <div id="company-erc__uploader-container" :class="{ 'is-invalid': ercFileError }">
          <p
            id="company-erc__uploader-drag-drop-text"
            class="text-medium-regular"
          >
            <span class="text-medium-bold">Drag and drop your file</span>
            or
            <button
              type="button"
              id="company-erc__uploader-button"
              class="linked-button-large text-primary"
              @click="handleUploadButtonClick"
            >
              {{ translate('about_company_browse_folder') }}
            </button>
          </p>
          <p id="company-erc__uploader-helper-text" class="text-small-regular">
            {{ translate('about_company_support_pdf_and_file_ls_5mb') }}
          </p>
        </div>

        <AppErrorMessage name="erc_file" />

      </div>
      <div id="company-erc__uploaded" v-if="ercFile != null">
        <!-- #company-erc__filecontainer -->
        <div id="company-erc__filecontainer">
          <!-- #company-erc__file -->
          <div id="company-erc__file">
            <div id="company-erc__fileicon">
              <img
                src="/assets/icons/erc-fileicon.svg"
                alt="pdf icon"
                width="24"
                height="25"
              />
            </div>
            <div id="company-erc__filename" class="text-medium-bold">
              {{ ercFilename }}
            </div>
          </div>
          <!-- end #company-erc__file -->

          <div id="company-erc__status" class="mt-2" :class="{
            'company-erc__status--active': status == COMPANY_STATUS_ACTIVE,
            'company-erc__status--waiting': status == COMPANY_STATUS_WAITING
          }"
             v-if="status !== COMPANY_STATUS_WAITING"
          >
            <div id="company-erc__statusicon">
              <img
                src="/assets/icons/check-green.svg"
                alt="cross icon"
                width="24"
                height="25"
                v-if="status == COMPANY_STATUS_ACTIVE"
              />
              <img
                src="/assets/icons/cross-red.svg"
                alt="cross icon"
                width="24"
                height="25"
                v-else
              />
            </div>
            <div id="company-erc__statustext" class="text-small-semi-bold">
              <template v-if="status == COMPANY_STATUS_WAITING">
              </template>
              <template v-else-if="status == COMPANY_STATUS_REVIEW">
                {{ translate('about_company_your_erc_is_being_checked') }}
              </template>
              <template v-else-if="status == COMPANY_STATUS_INACTIVE">
                {{ translate('about_company_your_erc_is_not_approved') }}
              </template>
              <template v-else>
                {{ translate('about_company_your_erc_is_approved') }}
              </template>
            </div>
          </div>
        </div>
        <!-- end #company-erc__filecontainer -->

        <!-- #company-erc__replacer -->
        <div id="company-erc__replacer" v-if="status != COMPANY_STATUS_ACTIVE">
          <div id="company-erc__replacer-icon">
            <img
              src="/assets/icons/erc-upload.svg"
              alt="upload icon"
              width="21"
              height="21"
            />
          </div>
          <div id="company-erc__replacer-content">
            <div id="company-erc__replacer-content__text">
              <button
                class="linked-button-large"
                type="button"
                @click="handleUploadButtonClick"
              >
                {{ translate('about_company_replace_file') }}
              </button>
            </div>
            <div
              id="company-erc__replacer-content__support"
              class="text-small-regular"
            >
            {{ translate('about_company_support_pdf_and_file_ls_5mb') }}
            </div>
          </div>
        </div>
        <!-- end #company-erc__replacer -->
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { uploadErc } from "@/api/files";
import { showWarningToast, translate } from "@/helpers";
import { ref } from "vue";
import { useField } from "vee-validate";
import { COMPANY_STATUS_ACTIVE, COMPANY_STATUS_INACTIVE, COMPANY_STATUS_REVIEW, COMPANY_STATUS_WAITING } from "@/schemas/company-profile-form";
import AppErrorMessage from "@/components/AppErrorMessage.vue";

/**
 * Define data
 */
const ercFileInputRef = ref<HTMLInputElement | null>(null);

interface Emit {
  (e: "uploaded", value: string): void;
  (e: "uploading"): void;
  (e: "error", value: any): void;
}
const emit = defineEmits<Emit>();

const {
  value: ercFile,
  errorMessage: ercFileError,
  meta: ercFileMeta,
  handleChange: changeErcFile,
} = useField("erc_file");
const {
  value: ercFilename,
  handleChange: changeErcFilename,
} = useField("erc_filename");
const {
  value: status,
} = useField("status");

/**
 * Define Function
 */
const MAX_FILE_SIZE = 1024 * 1024 * 5;

const uploadERC = async (files: FileList) => {
  if (files.length === 0) {
    return;
  }

  if (files[0].size > MAX_FILE_SIZE) {
    showWarningToast(
      translate("toast_upload_failed"),
      translate("toast_image_not_exceed_5mb")
    );
    return;
  }

  if (files[0].type !== "application/pdf") {
    showWarningToast(
      translate("toast_upload_failed"),
      translate("toast_just_accept_pdf_files")
    );
    return;
  }

  const response: any = await uploadErc(files[0]);

  changeErcFile(response.url);
  changeErcFilename(files[0].name);
};

const handleUploadButtonClick = () => ercFileInputRef.value?.click();
</script>

<style scoped>
.upload-section-blue {
  background-color: #e3f2fd !important;
  border: 2px dashed #2196f3 !important;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
}

.upload-section-blue:hover {
  background-color: #bbdefb !important;
  border-color: #1976d2 !important;
}

#company-erc__uploader-container {
  background: transparent !important;
}

.linked-button-large.text-primary {
  color: #2196f3 !important;
  text-decoration: underline;
  font-weight: 600;
}

.linked-button-large.text-primary:hover {
  color: #1976d2 !important;
}
</style>
