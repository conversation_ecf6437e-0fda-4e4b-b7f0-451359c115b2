@import "./variables.scss";

.aside {
  background-color: #ffffff;
  border-right: 1px solid #979797;
}

.aside-menu {
  height: calc(100dvh - 5rem);
  overflow-y: auto;
  [data-kt-aside-minimize="on"] & {
    overflow: unset;
  }
  #kt_aside_menu_wrapper {
    padding-left: 0;
    padding-right: 10px;

    #block-side-banner {
      padding-left: 10px;
      img {
        border-radius: 5px;
        width: 100%;
      }
    }

    [data-kt-aside-minimize="on"] & {
      padding-right: 0;
      overflow-y: inherit;

      .non-collapse-information {
        display: none;
      }

      .collapse-information,
      .icon-post-job {
        display: block !important;
      }
     .menu-new-badge{
        font-size: 0;
        background: $topdev-color-1;
        width: 10px;
        height: 10px;
        padding: 0;
        top: 8px;
        right: 3px;
      }
      #block-side-banner {
        padding-left: 0;
        img {
          width: auto;
        }

        .this-icon-mobile-side-banner {
          display: block !important;
        }
      }

      .icon-post-job {
        margin-right: 0;
      }
      #block-side-banner {
        .this-side-banner {
          border-radius: 4px;
          left: 80%;
          white-space: nowrap;
          top: 0;
          position: absolute;
          opacity: 0;
          visibility: hidden;
          text-align: left;
          transition: get($aside-config, transition-speed) ease;
        }

        &:hover {
          .this-side-banner {
            opacity: 1;
            visibility: visible;
          }
        }
      }
      .item-collapse-information {
        .collapse-information-info {
          border-radius: 4px;
          left: 80%;
          white-space: nowrap;
          top: get($aside-config, menu-padding-x);
          padding: 7px 22px;
          background: $white;
          box-shadow: 0px 0px 10px 0px #0000001a;
          opacity: 0;
          visibility: hidden;
          text-align: left;
          transition: get($aside-config, transition-speed) ease;
        }

        &:hover {
          .collapse-information-info {
            opacity: 1;
            visibility: visible;
          }
        }
      }
    }
  }

  // Main menu container
  .menu {
    // Item
    .menu-item {
      padding: 0;

      @include menu-link-active-state(
        $title-color: $topdev-color-1,
        $icon-color: $topdev-color-1,
        $bullet-color: $white,
        $arrow-color: $white,
        $bg-color: $topdev-color-5,
        $all-links: true
      );

      .menu-link {
        font-size: 16px;
        line-height: 23px;
        padding-top: 15px;
        padding-bottom: 15px;
        padding-left: 20px;
        margin-bottom: 10px;
        border-radius: 0 4px 4px 0;
        border-left: 4px solid transparent;

        [data-kt-aside-minimize="on"] & {
          border-radius: 0;

          &:hover {
            .menu-title {
              opacity: 1;
              visibility: visible;
            }
          }
        }

        .menu-title {
          color: $topdev-color-3;

          [data-kt-aside-minimize="on"] & {
            border-radius: 4px;
            left: 100%;
            display: inline-block;
            white-space: nowrap;
            top: get($aside-config, menu-padding-x);
            padding: 7px 22px;
            background: $white;
            box-shadow: 0px 0px 10px 0px #0000001a;
            opacity: 0;
            visibility: hidden;
            text-align: left;
            transition: get($aside-config, transition-speed) ease;
            position: absolute;
            min-width: 186px;

            &:hover {
              opacity: 1;
              visibility: visible;
            }
          }
        }

        .menu-icon {
          [data-kt-aside-minimize="on"] & {
            margin-right: 0;
            transition: right get($aside-config, transition-speed) ease;
            justify-content: center;
          }
        }

        &:hover {
          .menu-title {
            color: $topdev-color-1 !important;
          }

          .menu-icon {
            path {
              fill: $topdev-color-1;
            }

            [data-kt-aside-minimize="on"] & {
              margin-right: 0;
            }
          }
        }

        .menu-expanable {
          @extend .menu-arrow;
          &:after {
            display: block;
            width: 100%;
            content: " ";
            @include svg-bg-icon(arrow-start, $text-muted);
            /*rtl:begin:remove*/
            @include svg-bg-icon(arrow-end, $text-muted);
            /*rtl:end:remove*/
          }
        }
        [data-kt-aside-minimize="on"] & {
          .menu-expanable {
            display: none;
          }
          .expanded {
            position: relative;
          }
        }
      }

      .menu-link {
        &:hover, &.active {
          .menu-expanable {
            &:after {
              @include svg-bg-icon(arrow-start, $topdev-color-1);
              /*rtl:begin:remove*/
              @include svg-bg-icon(arrow-end, $topdev-color-1);
              /*rtl:end:remove*/
            }
          }
        }
      }

      .menu-link.active {
        border-left: 4px solid $topdev-color-1;
        font-weight: bold;

        .menu-icon {
          path {
            fill: $topdev-color-1;
          }
        }
      }

      &.expanded {
        .menu-sub {
          display: flex;
        }
        .menu-link {
          .menu-expanable:after {
            backface-visibility: hidden;
            transition: get($menu, accordion, arrow-transition);
            @include menu-link-arrow-rotate(-90deg, 90deg);
          }
        }
      }
    
      .menu-sub {
        .menu-link {
          &.active {
            border: none;
            border-radius: 0px;
            background-color: #F5F5F5;
            border-left: 2px $topdev-color-1 solid;
          }
          .menu-title {
            padding-left: 12px;
          }
        }
      }

      [data-kt-aside-minimize="on"] & {
        .menu-sub {
          display: none;
        }

        &.has-sub-menu {
          .menu-link {
            &:hover {
              .menu-title {
                display: none;
              }
            }
          }
          .menu-sub {
            .menu-link {
              &:hover {
                .menu-title {
                  display: inline-block;
                }
              }
            }
          }
        }

        &:hover {
          .menu-sub {
            border-radius: 4px;
            left: 100%;
            display: inline-block;
            white-space: nowrap;
            top: -50%;
            padding: 7px;
            background: #ffffff;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1019607843);
            text-align: left;
            transition: 0.3s ease;
            position: absolute;
            min-width: 186px;
            opacity: 1;
            visibility: visible;

            .menu-link {
              padding: 0.1rem;
            }

            .menu-title {
              position: unset;
              opacity: 1;
              visibility: visible;
              display: inline-block;
              border: none;
              padding: none;
              background: transparent;
              box-shadow: none;
            }
          }
        }
      }
    }
  }

  // Post job container
  .menu-post-job-container {
    padding: 0;

    [data-kt-aside-minimize="on"] & {
      padding: 0;
      position: relative;

      .title {
        border-radius: 4px;
        left: 100%;
        white-space: nowrap;
        top: get($aside-config, menu-padding-x);
        padding: 7px 22px;
        background: $white;
        box-shadow: 0px 0px 10px 0px #0000001a;
        opacity: 0;
        visibility: hidden;
        text-align: left;
        transition: get($aside-config, transition-speed) ease;
        position: absolute;
        min-width: 186px;
      }

      &:hover {
        .title {
          opacity: 1;
          visibility: visible;
        }
      }
    }

    a {
      display: block;
      font-size: 17px;
      padding: 5px !important;
      border-radius: 4px;

      [data-kt-aside-minimize="on"] & {
        padding-top: 13px !important;
        padding-bottom: 13px !important;

        &:hover {
          .icon-post-job {
            path {
              fill: $topdev-color-1;
            }
          }
        }
      }
    }
  }
}
// Tablet & mobile modes
@include media-breakpoint-down(lg) {
  .aside:not(.drawer.drawer-on) {
    .aside-menu {
      .menu-item {
        .menu-link {
          padding: 15px;
          .menu-icon {
            justify-content: center;
            margin-right: 0;
          }
        }
      }

      #kt_aside_menu_wrapper {
        padding-right: 0;

      }

      .menu-post-job-container {
        // padding: 0 10px;
        .svg-icon {
          margin-right: 0;
        }
        a {
          padding: 10px 5px !important;
        }
      }
    }
  }
}

.aside-menu {
  .menu-sidebar {
    padding: 0 1rem;
    .menu-item {
      .menu-link {
        padding: 0.75rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 0.5rem;
        [data-kt-aside-minimize="on"] & {
          justify-content: center;
        }
        &.active {
          border: none;
          border-radius: 0.25rem;
          background-color: $topdev-primary-100;
        }
        .menu-icon {
          margin: 0;
          width: 1.5rem;
          justify-content: center;
        }
        .menu-new-badge{
          width: 38px;
          height: 20px;
          background-color: $topdev-primary-100;
          border: 1px solid $topdev-color-1;
          border-radius: 6px;
          font-weight: 700;
          font-size: 10px;
          line-height: 14px;
          color: $topdev-color-1;
          padding: 3px 8px 2px 8px;
          letter-spacing: 0.5px !important;
          font-family: $font-family-sans-serif;
          position: absolute;
          right: 3px !important;
         
        }
       
      }
    }
  }
}

.aside  {
  &.aside-custom {
    position: fixed;
    top: 74px;
    left: 0;
    width: 265px;
    z-index: 101;
    bottom: 0;
    @include media-breakpoint-down(lg){
      top: 60px;
    }

    [data-kt-aside-minimize="on"] & {
      width: 82px;
    }
  }
}
