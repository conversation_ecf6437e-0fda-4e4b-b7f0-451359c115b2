i {
  color: inherit;
}

//Text
.text-topdev-1 {
  color: $topdev-color-1;
}

.text-topdev-2 {
  color: $topdev-color-2;
}

.text-topdev-3 {
  color: $topdev-color-3;

  &.active::before {
    content: "";
    background-color: $topdev-color-1;
    width: 10px;
    height: 10px;
    position: absolute;
    border-radius: 50%;
    top: -2px;
    right: -2px;
  }
}

.text-green {
  color: #46bf44;
}

.text-disabled {
  color: #b5b2b2;
  text-decoration: underline;
}

.text-gray-300 {
  color: $topdev-gray-300 !important;
}

// Table
.table {
  th {
    font-size: 14px;
    padding: 1rem 0.5rem;
  }

  td {
    font-size: 14px;

    p {
      margin-bottom: 0.25rem;
    }
  }

  tbody {
    .tr-active {
      background-color: rgba(72, 118, 239, 0.05);

      td:first-child {
        border-left: 2px solid $topdev-color-1;
      }
    }

    .candidate-active {
      background-color: rgba(72, 118, 239, 0.05);
    }
  }
}

//Tagify
.tagify__input {
  line-height: normal;
}

// Tool tip
.tooltip-wrapper {
  display: inline-block;
  width: 100%;
}

.tooltip-wrapper .btn-list-action[disabled] {
  pointer-events: none;
}

.tooltip-wrapper.disabled {
  cursor: not-allowed;
}

//popover
.popover .popover-header {
  font-weight: lighter;
  width: 400px;
}

//tag
.tag-skill-container {
  overflow: hidden;
  padding: 4px 0px;
  margin-left: 0;
  gap: 0.5rem;
}

.tag {
  padding: 4px 8px;
  background-color: $topdev-color-4;
  font-size: 12px;
  border: 1px solid $topdev-gray-300;
  border-radius: 4px;
  display: inline;
  word-break: break-all;
  white-space: nowrap;
  color: $topdev-gray-600;
  width: fit-content;
}

/**
 * Dropdown
 */
.dropdown-menu {
  padding: 5px 10px;
  border-radius: $border-radius;

  button {
    font-size: 16px;

    &:hover {
      background-color: $topdev-color-5;
      color: $topdev-color-1;
    }
  }
}

//User Menu
.text-overflow-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/*
 * Company profile
 */
.company-profile-container {
  padding-bottom: 20px;
  min-height: 75vh;

  .form-control.form-control-solid:disabled {
    background-color: #eff2f5;
    cursor: not-allowed;
  }
}

/*
 * About company
 */
.about-company-footer {
  margin-top: 50px;
  padding-bottom: 50px;
}

/*
 * Topbar
 */
.topbar-container {
  height: 75px;
  position: sticky;
  top: 74px;
  z-index: 100;

  p {
    font-size: 13px;
  }
}

.topbar-container.show-announcement-bar {
  top: 104px !important;
}

/**
 * TinyMCE
 */
.tox {
  &.tox-tinymce {
    border: 1px solid $topdev-color-2 !important;
  }
}

/*
 * Address
 */
.address-list {
  margin-top: 15px;

  li {
    font-size: 16px;
    color: $topdev-color-3;
    margin-bottom: 15px;
  }
}

/*
 * Benefits
 */
.benefit-list {
  margin-top: 26px;

  li {
    margin-bottom: 15px;
  }

  .benefit-select-icon {
    .input-group-text {
      padding: 0 10px !important;
      border-color: $topdev-color-2;
      background-color: unset;
      border-radius: $border-radius;
    }
  }
}

/*
 * Gallery upload
 */
.img-cover {
  height: 240px;
  object-fit: cover;
  width: 100%;
}

.gallery-upload-container {
  padding: 31px 20px;
  border-radius: $border-radius;

  .upload-drop-zone {
    height: 250px;
    min-width: 270px;
    border: 2px dashed #cccccc;
    margin-right: 20px;
    background: $topdev-color-4;
    border-radius: $border-radius;
  }
}

/*
 * Top concern
 */
.top-conern-list {
  margin-top: 20px;
}

/*
 * Company product
 */
.company-product-list {
  margin-top: 20px;

  .company-product {
    border: 1px solid $topdev-color-2;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    & > div {
      padding: 15px;
    }

    .link-container {
      padding-top: 8px;
      padding-bottom: 8px;
      border-top: 1px solid $topdev-color-2;
    }
  }
}

/*
 * Contact information
 */
#contact-information-container {
  margin-top: 20px;

  h2 {
    margin-bottom: 20px;
  }
}

#employer-account-container {
  margin-top: 10px;

  h2 {
    margin-bottom: 4px;
  }

  th,
  td {
    font-size: 14px !important;
  }
}

#modal-account-settings {
  .tab-content {
    margin-top: 25px;
  }
}

#modal-notification-new-feature {
  a {
    color: black;

    :hover {
      color: black;
    }
  }

  .modal-header {
    padding: 0px;
    margin-top: 0px;
    width: 529px;
    height: 100%;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    background: rgb(255, 232, 216);
    background: linear-gradient(48deg, rgba(255, 232, 216, 1) 59%, rgba(255, 199, 189, 1) 100%);

    .btn-close {
      margin-top: 12px !important
    }

    .modal-header-content {
      margin-top: 18px;

      img {
        border-bottom-left-radius: 20%;

      }

      .modal-header-content-right {
        margin-left: 24px;
      }

      .highligt-new-feature {
        background-color: #ffffff;
        color: $topdev-color-1;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        margin-bottom: 8px;
        border: 1px solid $topdev-color-1;
        border-radius: 2px;
        font-family: Roboto;
        font-style: normal;
        line-height: 157%;
        letter-spacing: 0.1px;
        text-transform: uppercase;
        padding: var(--spacing-xxs, 2px) var(--spacing-sm, 6px) var(--spacing-none, 0px) var(--spacing-sm, 6px);

      }

      .modal-title-banner {
        font-weight: 600;
        font-size: 24px;
        line-height: 30px;
      }

      .modal-description-banner {
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
      }
    }

  }

  .modal-body {
    .benefit-list {
      :nth-child(1) {
        margin-right: 8px;
      }

      .benefit-item {
        margin-top: 16px;

        :nth-child(1) {
          margin-right: 8px;
        }

        a {
          color: #5C5B5B;

          :hover {
            color: #5C5B5B;
          }
        }
      }

      a {
        width: 100%;
        height: 100%;
        background-color: $topdev-color-1;
        color: #ffffff;
        border-radius: 4px;
        margin-top: 16px;
      }

    }


  }

  .modal-footer {
    padding-left: 24px;
    padding-right: 24px;

    .modal-footer-content--text {
      color: #757575;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      letter-spacing: 0.16px;
    }

    .product-topdev--text {
      text-decoration: none;
      color: #424242;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      letter-spacing: 0.16px;
    }
  }
}

#modal-notification-new-feature-not-approve {
  border-radius: 8px;

  a {
    color: black;

    :hover {
      color: black;
    }
  }

  .modal-header {
    padding: 0px;
    margin-top: 0px;
    width: 529px;
    height: 100%;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    background: rgb(255, 232, 216);
    background: linear-gradient(48deg, rgba(255, 232, 216, 1) 59%, rgba(255, 199, 189, 1) 100%);

    .btn-close-modal {
      top: 8px;
      right: 0;
      font-size: 24px;
      margin-right: 8px;
    }

    .modal-header-content {
      margin-top: 18px;

      img {
        border-bottom-left-radius: 10px;

      }

      .modal-header-content-right {
        margin-left: 24px;
      }

      .highligt-new-feature {
        background-color: #ffffff;
        color: $topdev-color-1;
        font-size: 10px;
        font-weight: 500;
        text-align: center;
        margin-bottom: 8px;
        border: 1px solid $topdev-color-1;
        border-radius: 2px;
        font-family: Roboto;
        font-style: normal;
        line-height: 157%;
        letter-spacing: 0.1px;
        text-transform: uppercase;
        padding: var(--spacing-xxs, 2px) var(--spacing-sm, 6px) var(--spacing-none, 0px) var(--spacing-sm, 6px);

      }

      .modal-title-banner {
        font-weight: 600;
        font-size: 24px;
        line-height: 30px;
      }

      .modal-description-banner {
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
      }
    }

  }

  .modal-body {
    .benefit-list {
      :nth-child(1) {
        margin-right: 8px;
      }

      .benefit-item {
        margin-top: 16px;

        :nth-child(1) {
          margin-right: 8px;
        }

        a {
          color: #5C5B5B;

          :hover {
            color: #5C5B5B;
          }
        }
      }
    }

    a {
      width: 100%;
      height: 100%;
      background-color: $topdev-color-1;
      color: #ffffff;
      border-radius: 4px;
      margin-top: 16px;
    }

  }

  .modal-footer {
    padding-left: 24px;
    padding-right: 24px;

    .modal-footer-content--text {
      color: #757575;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      letter-spacing: 0.16px;
    }

    .product-topdev--text {
      text-decoration: none;
      color: #424242;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      letter-spacing: 0.16px;
    }
  }
}

/*
 * Block ui
 */
#main-blockui {
  height: 100vh;
  width: 100%;
  z-index: 999999;
  position: fixed;
}

/*
 * Media upload
 */
.media-upload-container {
  height: 100%;
  width: 100%;

  .img-container {
    height: 100%;
    width: 100%;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .change-backdrop {
    transition: 0.5s ease-in-out;

    &:hover {
      opacity: 1 !important;
    }
  }
}

#logo-upload-container {
  height: 90px;
  overflow: hidden;
  border: 0.5px solid $topdev-color-2;
  border-radius: $border-radius;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

#cover-photo-container {
  height: 250px;
  background: $topdev-color-4;
  border: 2px dashed #cccccc;
  margin-top: 39px;
  border-radius: $border-radius;
}

#gallery-upload-container {
  .upload-drop-zone {
    background: $topdev-color-4;
    border: 2px dashed #cccccc;
  }
}

//List Jobs
.list-jobs {
  margin-top: 1.5rem;
  min-height: 70vh;
}
.text-green-600 {
  color: #16A34A !important;
}

//View job
.job-info-block {
  padding: 12px 0;
  border-top: 1px solid $topdev-color-2;
  font-size: 16px;

  p {
    color: $topdev-color-3;
    margin-bottom: 4px;
  }
}

//recruiment
.process-list-container {
  padding: 5px 0 0;
  margin: 0 0 0 10px;
  border-left: 2px solid $topdev-color-1;

  li {
    list-style-type: none;
    padding-left: 25px;
    margin-bottom: 5px;
    margin-left: -11px;
    background-image: url("/assets/images/red-circle.fabc2a1.png");
    background-repeat: no-repeat;
    background-size: 15px;
    background-position-x: 3px;

    p {
      margin: 0;
    }
  }
}

//Pagination
.select-pagination {
  width: 90px;
  margin-left: 10px;
}

.cursor {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.grab {
  cursor: -webkit-grab;
  cursor: grab;

  &:active {
    cursor: -webkit-grabbing;
    cursor: grabbing;
  }
}

//Candidates
.filter-select {
  min-width: 250px;
}

.btn-best-match.active{
  background: #FEE6E2;
}

.form-switch .form-check-input:checked {
  background-color: $topdev-color-1;
  border-color: $topdev-color-1;
}

.form-check-input:focus {
  box-shadow: 0 0 0 .2rem rgba(72, 118, 239, 0.5);
}

.show-best-matched {
  height: 3rem;
  background-color: #F5F5F5;
  margin: 0rem;
  font-weight: bold;
  margin-bottom: -2px;
  margin-top: 5px;
}
.application-status {
  font-weight: 500;
  text-transform: uppercase;

  &.ready {
    color: #46bf44;
  }

  &.not-match {
    color: #f68407;
  }
}

//Process
.process-list-container {
  padding: 5px 0;
  margin: 0 0 0 10px;
  border-left: 2px solid $topdev-color-1;

  li {
    list-style-type: none;
    padding-left: 25px;
    margin-bottom: 5px;
    margin-left: -11px;
    background-image: url("/assets/images/red-circle.fabc2a1.png");
    background-repeat: no-repeat;
    background-size: 16px;
    background-position-x: 2px;
    background-position-y: 2px;

    p {
      margin: 0;
    }
  }
}

//Custom procedure status
.procedue-status-detail {
  .procedure-status-box {
    max-width: 190px;
    order: 2;

    .sub-procedure-status {
      right: 0;
    }
  }

  .process-procedue-status-detail {
    & > h3 {
      font-size: 16px;
    }

    ul li {
      font-size: 14px;
    }
  }
}

.procedure-status-box {
  position: relative;
  width: 100%;
  background: #fff;
  border: 0.5px solid #979797;
  border-radius: 4px;
  transition: all 0.4s;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .title-procedure-status {
    font-size: 13px;
    color: #a1a1a1;
    display: block;
    padding: 6px 10px 6px 10px;
    cursor: pointer;
  }

  .sub-procedure-status {
    position: absolute;
    top: calc(100% + 8px);
    min-width: 220px;
    z-index: 2;
    background: #fff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    max-height: 260px;
    padding-right: 2px;
    overflow-y: auto;
    overflow-x: hidden;
    right: 0;
    text-align: left;

    .item-groups-procedure-status {
      font-size: 14px;
      color: #393e46;

      .title-groups {
        text-transform: capitalize;
        line-height: 23px;
        padding: 8px 20px;
        display: block;
        color: #979797;
        background-color: #fff;
        transition: all 0.4s;
      }

      .child-groups {
        padding: 10px;
        margin-bottom: 0;
        list-style: none;

        & > li {
          padding: 10px 10px 10px 40px;
          border-radius: 4px;
          background: #fff url(/assets/icons/checkbox.svg) no-repeat left 10px center;
          background-size: 17.5px 17.5px;
          transition: all 0.4s;

          &.disible {
            cursor: default;
            pointer-events: none;
            opacity: 0.5 !important;
          }
        }

        & > li:not(:last-child) {
          margin-bottom: 10px;
        }

        & > li.active {
          background: #f5f5f5 url(/assets/icons/checkbox-selected.svg) no-repeat left 10px center;
          background-size: 17.5px 17.5px;
        }
      }

      &.disible {
        background-color: #e9ecef;
        cursor: default;
        pointer-events: none;

        .title-groups {
          background-color: #e9ecef;
          cursor: default;
          pointer-events: none;
        }

        .child-groups {
          & > li {
            opacity: 0.5;
          }
        }
      }

      &:hover {
        .title-groups {
          color: #393e46;
          background-color: #f5f5f5;
        }

        .child-groups {
          & > li:hover {
            background: #f5f5f5 url(/assets/icons/checkbox-selected.svg) no-repeat left 10px center;
            background-size: 17.5px 17.5px;
          }
        }
      }

      &.open,
      &.active {
        background-color: #fff;
        cursor: pointer;
        pointer-events: inherit;

        .title-groups {
          color: #393e46;
          background-color: #f5f5f5;
          position: relative;
        }

        .child-groups {
          & > li {
            opacity: 1;
          }
        }
      }

      &.active {
        .title-groups::before {
          content: "✓";
          display: inline-block;
          color: #46bf44;
          font-size: 18px;
          position: absolute;
          right: 15px;
          top: calc(50% - 11.5px);
        }
      }

      &.open {
        &:hover {
          .title-groups {
            color: $topdev-color-1;
            background-color: #fff;

            &.no-hover-title-groups {
              color: #393e46;
              background-color: #f5f5f5;
            }
          }
        }
      }
    }
  }

  .arrow-procedure-status {
    background-image: url("/assets/images/arrow-procedure-status.png");
    background-repeat: no-repeat;
    width: 16px;
    height: 9px;
    position: absolute;
    right: 10px;
    top: calc(50% - 4.5px);
    cursor: pointer;
  }

  &:hover {
    border-color: #46bf44;
  }
}

//Show view candidates
.procedue-status {
  border-color: #979797 !important;
}

.line-tabs-candidates {
  position: relative;
}

.line-tabs-candidates::before {
  content: "";
  width: calc(100% + 60px);
  height: 1px;
  background-color: #eff2f5;
  position: absolute;
  left: -30px;
  bottom: -1px;
  z-index: 1;
}

.detail-search-resumes .line-tabs-candidates::before {
  width: 100%;
  left: 0;
}

.title-search-candidates-resumes {
  background-color: $topdev-gray;
  position: relative;
  font-weight: 500;

  & > p {
    z-index: 1;
    color: #393e46;
  }
}

.title-search-candidates-resumes::after {
  content: "";
  background-color: $topdev-gray;
  width: calc(100% + 60px);
  left: -30px;
  position: absolute;
  height: 100%;
  z-index: 0;
}

.inline-svg-eye {
  color: #ccc;

  path {
    fill: #ccc;
  }
}

.btn-see-more {
  font-size: 14px;
  color: $topdev-primary !important;
  font-weight: 600;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 2;
  gap: 0.25rem;
  padding: 10px 10px 0 0 !important;
  background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 1) 30%,
      rgba(255, 255, 255, 0.9) 100%
  );
  width: 100%;
  text-align: left;
  text-decoration: underline;
  display: inline-flex;
  align-items: center;
}

.btn-view-less {
  font-size: 14px;
  color: $topdev-primary !important;
  font-weight: 600;
  left: 0;
  bottom: 0;
  z-index: 2;
  gap: 0.25rem;
  padding: 10px 10px 0 0 !important;
  background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 1) 30%,
      rgba(255, 255, 255, 0.9) 100%
  );
  width: 100%;
  text-align: left;
  text-decoration: underline;
  display: inline-flex;
  align-items: center;
}

.box_search_resumes_work_experience_education {
  overflow-y: hidden;
  position: relative;

  &.fix {
    height: 175px;
  }

  &.no-fix {
    height: auto;
  }
}

//Preview CV
.preview-cv-container {
  position: relative;
  box-shadow: rgb(0 0 0 / 35%) 0px 5px 10px;
  max-width: 895px;
  margin: 0 auto;

  .page-loading {
    min-height: 4rem;
  }
}

//Loading CV Text
.loading-cv-text {
  font-size: 13px;
}

.loading-cv-text::after {
  display: inline-block;
  animation: dotty steps(1, end) 2s infinite;
  content: "";
}

@keyframes dotty {
  0% {
    content: "";
  }
  25% {
    content: ".";
  }
  50% {
    content: "..";
  }
  75% {
    content: "...";
  }
  100% {
    content: "";
  }
}

//Filter bar
.container-filter {
  &.detail-candidate {
    padding-left: 0;

    .container-filter-box {
      overflow-x: auto;
    }

    .item-filter {
      display: none;

      &.active {
        display: block;
      }
    }
  }

  .btn-reset {
    font-size: 14px !important;
    color: $topdev-color-3;
    margin-top: 2px;
    white-space: nowrap;
  }

  .title-filter {
    font-size: 18px;
    color: $topdev-color-3;
    margin-top: 2px;
    white-space: nowrap;
  }

  .item-filter {
    font-size: 13px;

    .multiselect-option,
    .multiselect-placeholder,
    .multiselect-single-label-text,
    .multiselect-multiple-label {
      font-size: 1rem;
    }

    .multiselect-multiple-label,
    .multiselect-placeholder,
    .multiselect-single-label {
      white-space: nowrap;
      position: relative;
      padding: 0 10px;
    }

    .multiselect {
      min-height: 40px;
      height: 40px;
      min-width: 7.125rem;
      border-radius: 4px;
      justify-content: space-between;
      font-size: 16px;

      .multiselect-tags .multiselect-tag {
        font-size: 11px;
        height: 17px;
        padding: 5px;

        .multiselect-tag-remove {
          display: none;
        }
      }

      .multiselect-dropdown {
        min-width: 256px;
        border-radius: 4px;
      }

      .multiselect-clear {
        padding-right: 5px;
      }

      &.is-active-custum {
        .multiselect-caret.is-open {
          position: absolute;
          right: 0;
        }
      }
    }

    &.active {
      .multiselect {
        border-color: $topdev-color-1;
        background-color: $topdev-color-5;

        .multiselect-search {
          background-color: transparent;
        }

        .multiselect-clear .multiselect-clear-icon {
          background-color: $topdev-color-3;
        }
      }

      .multiselect-placeholder,
      .multiselect-single-label-text,
      .multiselect-multiple-label {
        color: $topdev-color-1;

        span {
          overflow: hidden;
          display: block;
          -webkit-line-clamp: 1;
          display: box;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
          white-space: normal;
        }
      }

      .form-control.form-control-solid {
        border-color: $topdev-color-1;
        background-color: $topdev-color-5;
        color: $topdev-color-1;
      }
    }

    .form-control.form-control-solid,
    .form-select.form-control-solid {
      width: 102px !important;
      height: 40px;
      font-size: 16px;

      ::placeholder {
        font-size: 13px;
      }
    }
  }

  .btn-reset {
    cursor: pointer;
    line-height: 1;
  }

  .btn-reset.svg-icon svg {
    width: 12px;
    margin-top: -3px;
  }
}

// Tablet & mobile modes
@include media-breakpoint-down(lg) {
  .topbar-container {
    top: 60px;
  }
  .container-filter {
    padding-left: 0;

    .container-filter-box {
      flex-wrap: wrap;
      width: calc(100% - 136px);

      .item-filter {
        margin-bottom: 5px;
      }
    }
  }
}

.left-5 {
  left: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.h-6 {
  height: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.top-1\/2 {
  top: 50%;
}

.text-danger {
  color: $topdev-primary !important;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

// Text
.text-gray-black {
  color: $topdev-gray-black;
}

.text-yellow-dark {
  color: $topdev-yellow-dark;
}

.text-gray-400 {
  color: $topdev-gray-400;
}

.text-gray-500 {
  color: $topdev-gray-500 !important;
}

.text-gray-600 {
  color: $topdev-gray-600 !important;
}

.text-base {
  font-size: 16px;
}

.text-sm {
  font-size: 0.875rem;
}

.capitalize {
  text-transform: capitalize;
}

.bg-gray {
  background-color: $topdev-gray;
}

.mt-8 {
  margin-top: 32px;
}

.text-2xl {
  font-size: 24px;
}

.mb-6 {
  margin-bottom: 24px;
}

#credit-package {
  .table {
    color: inherit;
    user-select: none;
  }

  .credit-package-header {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    overflow: hidden;
    border-bottom: solid 1px $topdev-gray;
  }

  .credit-package-body {
    overflow: hidden;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;

    .credit-available {
      min-width: 244px;
      text-align: center;
      background-color: $topdev-primary-100;
      border-radius: 8px;
      padding: 24px 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      &-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0;
      }

      .num-of-credit {
        margin-top: 10px;
        font-size: 64px;
        font-weight: 600;
        letter-spacing: -1px;
        line-height: 1;
      }

      .credit-icon {
        font-size: 20px;
      }
    }

    .flex-1 {
      flex: 1;
    }

    .package-table {
      thead {
        width: 100%;

        tr {
          border-bottom: solid 1px $topdev-gray;
          display: flex;
          width: 100%;

          th {
            border: none;
            padding-left: 0;
            font-weight: 600;
            align-self: center;
          }
        }

        .btn-sort {
          cursor: pointer;
        }

        .sort-icon {
          color: $topdev-gray-600;
          line-height: 0.5;

          &.active {
            color: $topdev-primary;
          }
        }
      }

      tbody {
        width: 100%;
        height: 150px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        align-items: center;

        tr {
          width: 100%;
          display: flex;

          &:hover {
            background-color: $topdev-gray;
          }

          td {
            height: unset;

            &:first-child {
              font-weight: 600;
            }
          }
        }
      }
    }

    .package-expired {
      color: $topdev-primary;
    }

    .package-active {
      color: $topdev-green-dark;
    }

    .package-inactive {
      color: $topdev-gray-500;
    }
  }
}

#usage-log {
  .usage-log-body {
    height: 320px;
    overflow-y: auto;
    color: $topdev-gray-400;

    .credit-plus,
    .credit-subtract {
      font-weight: 600;
    }

    .credit-plus {
      color: #0f5e1c;
    }

    .credit-subtract {
      color: #ce8800;
    }

    .table {
      color: inherit;
    }

    .highlight-text {
      color: $topdev-gray-600;
    }

    .usage-table {
      width: 100%;

      tr {
        border-bottom: solid 1px $topdev-gray-200;
      }
    }
  }
}

tbody::-webkit-scrollbar {
  width: 0.4rem;
  height: 0.4rem;
}

tbody::-webkit-scrollbar-thumb {
  background-color: #eff2f5;
}

.employer-name {
  color: $topdev-gray-600;
}

.target-name {
  color: $topdev-blue-dark;
  font-weight: 600;
}

.select-none {
  user-select: none;
}

.left-2 {
  left: 0.5rem;
}

.flex-1 {
  flex: 1;
}

.table-candidates {
  thead {
    th {
      &:nth-child(1) {
        width: 3.5rem;
      }

      &:nth-child(2) {
        width: 17.75rem;
      }

      &:nth-child(4) {
        width: 15rem;
      }
    }
  }
}

.max-w-full {
  max-width: 100%;
}

.object-contain {
  object-fit: contain;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.the-search-resume-candidate-information-container {
  .img-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 9999px;
    object-fit: cover;
    object-position: center center;
  }

  .list-reset-custom {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .candidate-fullname {
    color: $topdev-blue-dark;

    &:hover {
      color: $topdev-primary;
      text-decoration: underline;
    }
  }

  .candidate-current-job {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: $topdev-gray-500;
    font-weight: bold;
    color: white;
    border-radius: 0.25rem;
    font-size: 0.75rem;
  }

  .candidate-list-info > *:not(:last-child) {
    margin-bottom: 2px;
  }
}

.candidate-avatar {
  width: calc(2.5rem + 4px);
  height: calc(2.5rem + 4px);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  position: relative;
  justify-content: center;
  padding: 2px;

  &.status-actively-seeking {
    background: linear-gradient(281.14deg, #00fff0 3.49%, #0029ff 117.34%);

    &::after {
      position: absolute;
      bottom: 0;
      right: 0;
      border: solid white 2px;
      content: "";
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
      background: linear-gradient(281.14deg, #00fff0 3.49%, #0029ff 117.34%);
    }
  }

  &.status-open-to-work {
    background: linear-gradient(281.14deg, #00a79d 3.49%, #60e90c 117.34%);

    &::after {
      position: absolute;
      bottom: 0;
      right: 0;
      border: solid white 2px;
      content: "";
      width: 0.75rem;
      height: 0.75rem;
      border-radius: 50%;
      background: linear-gradient(281.14deg, #00a79d 3.49%, #60e90c 117.34%);
    }
  }
}

.candidate-experience-container {
  .container-title {
    min-width: 6rem;
  }
}

.best-match {
  background: #16A34A;
  border-radius: 50px;
  position: relative;
  display: inline-block;
  z-index: 2;

  .text-match {
    font-weight: 600;
    color: white;
  }

  > i {
    margin-left: 4px;
  }

  .badge {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .tooltip {
    visibility: hidden;
    width: 260px;
    border-radius: 6px;
    font-size: 12px;
    border: 1px solid #C2C2C2;
    color: #6D6D6D;
    background-color: white;
    padding: 10px 16px;
    position: absolute;
    z-index: 1;
    top: 125%;
    right: -50%;
    opacity: 0;
    transition: opacity 0.5s;

    .bold {
      font-weight: 700;
    }
  }

  .popover-caret {
    color: #6D6D6D;
    position: absolute;
    left: 50%;
    top: 20px;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.5s;
  }

  &.detail {
    .tooltip {
      right: -70%;
    }
  }

  &:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }

  &:hover .popover-caret {
    visibility: visible;
    opacity: 1;
  }
}

.custom-application-status {
  margin-right: 8px;
  font-weight: 700;
  text-transform: uppercase;

  &.ready {
    background: #DCFCE8;
    color: #16A34A;
  }

  &.not-match {
    background: #FFEEDC;
    color: #F68407;
  }
}


.line-clamp-1 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.candidate-experience-education-list {
  padding-left: 1.5rem;
  margin: 0;

  p {
    margin: 0;
    padding: 0;
  }

  & > li {
    &::marker {
      color: $topdev-gray-400;
    }
  }

  .space-line {
    display: inline-block;
    width: 1px;
    height: 0.75rem;
    margin: 0 0.5rem;
    background-color: $topdev-gray-200;

    &.management {
      margin: 3px 0.5rem;
    }
  }

  .experience-time {
    display: inline-block;
    min-width: 10rem;

    &.management {
      min-width: 7.5rem;
    }
  }
}

.space-y-2 {
  > *:not(:last-child) {
    margin-bottom: 0.5rem;
  }
}

.the-search-resume-popover-unlock {

  .popover-active-unlock-package {
    position: absolute;
    top: calc(100% + 0.625rem);
    right: 3rem;
    z-index: 100;
  }

  .popover-active-unlock-package-content {
    position: fixed;
    right: 7rem;
    background-color: white;
    width: 20rem;
    border-radius: 0.25rem;
    box-shadow: 0px 16px 24px 0px #5f2f2626;
    border: 1px solid $topdev-gray-300;
    padding: 1rem;
  }

  .candidate-popover-unlock {
    position: absolute;
    top: calc(100% + 0.625rem);
    right: -1rem;
    z-index: 100;
  }

  .candidate-popover-unlock-content {
    position: relative;
    background-color: white;
    width: 25rem;
    border-radius: 0.25rem;
    box-shadow: 0px 16px 24px 0px #5f2f2626;
    border: 1px solid $topdev-gray-300;
    padding: 1rem;
  }

  .popover-caret {
    position: absolute;
    top: -15px;
    right: 2rem;
  }

  .btn-cancel {
    &:hover {
      background-color: $topdev-primary-100;
      color: $topdev-primary;
    }
  }
}

// Candidate status
.candidate-status {
  $borderWidth: 1px;
  position: relative;
  padding: 0.25rem 0.5rem;
  border-radius: 3rem;
  display: inline-flex;
  border: solid $borderWidth transparent;
  background-clip: padding-box;
  background-color: white;
  align-items: center;
  gap: 0.5rem;

  &::after {
    content: "";
    z-index: -1;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: -$borderWidth;
    border-radius: inherit;
  }

  &.status-actively-seeking {
    &::after {
      background: linear-gradient(281.14deg, #00a79d 3.49%, #60e90c 117.34%);
    }

    .candidate-status-title {
      background: linear-gradient(270.79deg, #00a79d 81.59%, #60e90c 122.17%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  &.status-open-to-work {
    &::after {
      background: linear-gradient(281.14deg, #00fff0 3.49%, #0029ff 117.34%);
    }

    .candidate-status-title {
      // background: linear-gradient(281.14deg, #00fff0 3.49%, #0029ff 117.34%);
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
      color: $topdev-blue-dark;
    }
  }

  &.status-actively-seeking {
    .candidate-status-icon {
      background: linear-gradient(281.14deg, #00a79d 3.49%, #60e90c 117.34%);
    }
  }

  &.status-open-to-work {
    .candidate-status-icon {
      background: linear-gradient(281.14deg, #00fff0 3.49%, #0029ff 117.34%);
    }
  }

  .candidate-status-title {
    margin: 0;
    padding: 0;
    font-weight: 700;
  }

  .candidate-status-icon {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
  }
}

#request-refund {
  #note-box {
    background-color: $topdev-primary-100;
  }
}

.flex {
  display: flex;
}

.text-gray-black {
  color: $topdev-gray-black;
}

.table-detail-candidate {
  thead {
    tr > th {
      color: $topdev-gray-black;
    }
  }
}

.candidate-detail-container {
  .candidate-detail-content {
    box-shadow: 0px 8px 16px 0px #5f2f2626;
  }
}

.bg-gray-400 {
  background-color: $topdev-gray-400;
}

.text-blue-dark {
  color: $topdev-blue-dark;
}

.group-button-choose-type-of-cv {
  background-color: $topdev-gray-200;
  border-radius: 0.25rem;
}

.z-10 {
  z-index: 10;
}

.inline-block {
  display: inline-block;
}

#search-resumes-detail-table {
  scroll-behavior: smooth;
}

.preview-cv-container * {
  user-select: none;
  pointer-events: none;
}

html:has(#search-resumes-index) {
  user-select: none;
}

html:has(#search-resumes-show) {
  user-select: none;
}

// Notification bar
.notification-bar {
  position: fixed;
  top: 73px;
  left: 0;
  right: 0;
  z-index: 102;
  height: 72px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url("/assets/images/congat-banner.svg");
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.1);

  &__content {
    display: flex;
    align-items: center;
    height: 80px;
    justify-content: space-between;
    width: 100%;
  }

  &__text {
    width: 100%;
    text-align: center;
  }

  &__close {
    background: none;
    border: none;
    cursor: pointer;
    text-align: center;
    margin-right: 20px;

    svg {
      width: 12px;
    }
  }
}

#notification-bar-not-approve {
  position: fixed;
  top: 73px;
  left: 0;
  right: 0;
  z-index: 102;
  height: 114px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // background-repeat: no-repeat;
  // background-size: cover;
  // background-image: url("/assets/images/congat-banner.svg");
  background: rgb(255, 232, 216);

  .notification-bar__content--not-approve {
    display: flex;
    align-items: center;
    height: 80px;
    width: 100%;
    flex-direction: column;

    .notification-bar__header {
      width: 100%;
      text-align: center;
      font-size: 24px;
      font-weight: 600;
      color: #292929;
      line-height: 30px;
      font-family: $font-family-sans-serif;

      .notification-bar__header_img_right {
        right: 0;
        margin-right: 54px;
        top: -8px;
      }

      .notification-bar__close {
        background: none;
        border: none;
        cursor: pointer;
        text-align: center;
      }
    }

    .notification-bar__description {
      font-size: 16px;
      color: #5C5B5B;
      font-weight: 400;
      letter-spacing: 1%;
      text-align: center;
      align-items: center;
    }

    .contact-information {
      .contact-email {
        margin-left: 16px;
      }

      .contact-information-text {
        color: #5C5B5B;
        font-size: 16px;
        line-height: 22px;
        font-weight: 400;

      }
    }
  }
}

 //Start Handle style checkbox in filter
.custom-checkbox-filter {
  .custom-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    pointer-events: none; /* Disable direct interaction with the checkbox */

    input[type='checkbox'] {
      pointer-events: none; /* Disable direct interaction with the checkbox */
      width: 20px;
      height: 20px;
      appearance: none; // Unset style default checkbox
      border: 1px solid #5c5b5b;
      border-radius: 0.25rem;
      outline: none;
      text-align: center;
      position: relative;
      background-color: white;
      cursor: pointer;

      &:checked {
        background-color: $topdev-color-1;
        border-color: $topdev-color-1;
        border-width: 1px;
        border-style: solid;

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 16px; // Change size of SVG
          height: 16px;
          transform: translate(-50%, -50%);
          background-image: url('data:image/svg+xml,%3Csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3E%3Cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3E%3C/svg%3E');
          background-size: cover; // Cover always SVG in center checkbox
          background-repeat: no-repeat;
        }
      }
    }
  }
}
// End Handle style checkbox in filter


.usage-history {
  .available-package {
    background-color: #FEEEEB;
  }

  .job-posting-usage {
    td.job-package-title {
      color: #F59E0B;
      font-weight: 600;
    }
  }

  .job-packages, .job-posting-usage {
    .package-table {
      thead {
        width: 100%;
  
        tr {
          border-bottom: solid 1px $topdev-gray;
          display: flex;
          width: 100%;
  
          th {
            border: none;
            padding-left: 0;
            font-weight: 600;
            align-self: center;
          }
        }
  
        .btn-sort {
          cursor: pointer;
        }
  
        .sort-icon {
          color: $topdev-gray-600;
          line-height: 0.5;
  
          &.active {
            color: $topdev-primary;
          }
        }
      }
  
      tbody {
        width: 100%;
        height: 150px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        scrollbar-width: thin;
        scrollbar-color: $topdev-color-1 transparent;

        &.table-data-empty {
          height: unset;
          tr {
            td {
              flex: 1;
            }
          }
        }

        tr {
          width: 100%;
          display: flex;
  
          &:hover {
            background-color: #f5f8fa;
          }

          &.odd {
            background-color: #E7E7E7;
            &:hover {
              background-color: #f5f8fa;
            }
          }
  
          td {
            height: unset;
  
            &:first-child(:not(.unset)) {
              font-weight: 600;
            }
            &.font-bold {
              font-weight: 700;
            }
          }
        }
      }
    }
  }

}