"use client";

import Image from "next/image";
import Link from "@/components/Link/Link";

// Render the default Next.js 404 page when a route
// is requested that doesn't match the middleware and
// therefore doesn't have a locale associated with it.

export default function NotFound() {
  return (
    <html lang="en">
      <body
        style={{
          textAlign: "center",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: "97vh",
        }}
      >
        <section>
          <Image
            width="520"
            height="520"
            src="https://cdn.topdev.vn/v4/assets/images/common/400-error.png"
            alt="Topdev"
            priority
          />
          <br />
          <Link
            style={{
              backgroundColor: "rgb(221 63 36 )",
              color: "white",
              textTransform: "uppercase",
              fontSize: "18px",
              fontWeight: "bold",
              textDecoration: "none",
              display: "inline-block",
              padding: "0.75rem 2rem",
              fontFamily: "tahoma",
            }}
            href="/"
          >
            Go to home
          </Link>
        </section>
      </body>
    </html>
  );
}
