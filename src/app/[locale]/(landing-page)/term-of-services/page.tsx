import EnglishTerm from "@/components/TermService/EnglishTerm";
import VietNameseTerm from "@/components/TermService/VietNameseTerm";
import { Lang } from "@/types/page";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import { getLocale } from "@/utils/localeServer";

// get datameta for SEO
export async function generateMetadata({
  params,
}: {
  params: { slug: string; locale: Lang };
}): Promise<Metadata> {
  const locale = params.locale ?? "vi";
  const translate = await getTranslations();

  // set title metadata
  const title = translate("term_of_services_metadata_title");

  // set description metadata
  const description = translate("term_of_services_metadata_description");

  // set robots metadata
  let robots_meta = { index: true, follow: true };

  // set slug
  const slug_vi = process.env.NEXT_PUBLIC_BASE_URL + "/term-of-services";
  const slug_en = process.env.NEXT_PUBLIC_BASE_URL + "/term-of-services";

  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
    alternates: {
      canonical: slug_en,
      languages: {
        vi: slug_vi,
        en: slug_en,
        "x-default": slug_vi,
      },
    },
    title: title,
    openGraph: {
      url: slug_en,
      title: title,
      description: description,
      type: "website",
      locale: locale === "vi" ? "vi_VN" : "en_US",
      siteName: "TopDev",
    },
    twitter: {
      site: "TopDev",
      title: title,
      description: description,
      creator: "@Topdevvn",
    },
    robots: robots_meta,
    verification: {
      google: "XxqADEgloBElQyOQ9ePm7EG3XO01_vcTMre2KQgD9K8",
    },
    other: {
      "twitter:domain": "topdev.vn",
    },
  };
}

const TermOfServices = ({
  params,
}: {
  params: { slug: string; locale: Lang };
}) => {
  return <>{getLocale() === "vi" ? <VietNameseTerm /> : <EnglishTerm />}</>;
};

export default TermOfServices;
