import React from "react";
import "@/assets/styles/pages/create-cv-online.scss";
import dynamic from "next/dynamic";
const BannerSection = dynamic(
  () => import("@/components/LandingPage/CreateCVOnline/BannerSection"),
  { ssr: true },
);
import CreateNowSection from "@/components/LandingPage/CreateCVOnline/CreateNowSection";
import CreateStandardCVSection from "@/components/LandingPage/CreateCVOnline/CreateStandardCVSection";
import CreatingCVTipsSection from "@/components/LandingPage/CreateCVOnline/CreatingCVTipsSection";
import PreviewCVSection from "@/components/LandingPage/CreateCVOnline/PreviewCVSection";
import TextLinkSection from "@/components/LandingPage/CreateCVOnline/TextLinkSection";
import { Metadata } from "next";

const metadata = {
  title: "Tạo CV Online miễn phí, mẫu CV IT chuẩn nhất 2024 | TopDev",
  keywords:
    "cv online, cv, tạo cv, cv developer, cv it, cv cntt, cv xin việc, mẫu cv",
  description:
    "Tạo CV online miễn phí và nhanh chóng, tổng hợp mẫu CV IT mới và chuẩn nhất năm 2024.  Khởi sắc sự nghiệp IT của bạn cùng TopDev ngay hôm nay.",
  image: "https://topdev.vn/users/static/assets/images/home-background.jpeg",
};

// Get dataMeta for SEO
export async function generateMetadata(): Promise<Metadata> {
  // set title metadata
  const title = metadata.title;

  // set keywords metadata
  const keywords = metadata.keywords;

  // set description metadata
  const description = metadata.description;

  // image
  const image = metadata.image;

  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
    alternates: {
      canonical: process.env.NEXT_PUBLIC_BASE_URL + "/tao-cv-online",
      languages: {
        vi: process.env.NEXT_PUBLIC_BASE_URL + "/tao-cv-online",
        en: process.env.NEXT_PUBLIC_BASE_URL + "/tao-cv-online",
        "x-default": process.env.NEXT_PUBLIC_BASE_URL + "/tao-cv-online",
      },
    },
    title: title,
    keywords: keywords,
    description: description,
    openGraph: {
      url: process.env.NEXT_PUBLIC_BASE_URL + "/tao-cv-online",
      title: title,
      description: description,
      type: "website",
      locale: "en_US",
      siteName: "TopDev",
      images: image,
    },
    twitter: {
      site: "TopDev",
      title: title,
      description: description,
      creator: "@Topdevvn",
    },
    other: {
      "twitter:domain": "topdev.vn",
      "twitter:image:src": image ?? "",
    },
  };
}

const CreateCVOnline = () => {
  return (
    <>
      <BannerSection />
      <CreateNowSection />
      <CreateStandardCVSection />
      <CreatingCVTipsSection />
      <PreviewCVSection />
      <TextLinkSection />
    </>
  );
};

export default CreateCVOnline;
