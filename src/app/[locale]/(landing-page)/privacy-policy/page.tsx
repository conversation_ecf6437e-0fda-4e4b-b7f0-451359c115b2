import English from "@/components/PrivacyPolicy/English";
import { VietNamese } from "@/components/PrivacyPolicy/VietNamese";
import { getLocale } from "@/utils/localeServer";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
  const translate = await getTranslations();

  return {
    title:
      translate("privacy_policy_title") + " - " + translate("abouts_us_title"),
    description:
      translate("privacy_policy_title") +
      " - " +
      translate("abouts_us_title") +
      " | " +
      translate("home_meta_description"),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`,
      languages: {
        vi: `${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`,
        en: `${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`,
        "x-default": `${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`,
      },
    },
  };
}

export default function PrivacyPolicyPage() {
  return <>{getLocale() === "vi" ? <VietNamese /> : <English />}</>;
}
