import "@/assets/styles/pages/techcombank.scss";
import dynamic from "next/dynamic";
import { Metadata } from "next/types";
import { getLocaleForParams } from "@/utils/localeServer";

const SectionHeader = dynamic(
  () => import("@/components/Group/Techcombank/SectionHeader"),
);
const SectionBanner = dynamic(
  () => import("@/components/Group/Techcombank/SectionBanner"),
);

const SectionJob = dynamic(
  () => import("@/components/Group/Techcombank/SectionJob"),
);

const SelectionProcess = dynamic(
  () => import("@/components/Group/Techcombank/SelectionProcess"),
);

const SectionMagazineBlogs = dynamic(
  () => import("@/components/Group/Techcombank/SectionMagazineBlogs"),
);

const SectionAbout = dynamic(
  () => import("@/components/Group/Techcombank/SectionAbout"),
);

const SectionMedia = dynamic(
  () => import("@/components/Group/Techcombank/SectionMedia"),
);

const SectionFooter = dynamic(
  () => import("@/components/Group/Techcombank/SectionFooter"),
);

const ModalViewBlog = dynamic(
  () => import("@/components/Group/Techcombank/ModalViewBlog"),
);

export default async function TechcombankPage() {
  return (
    <div id="page-techcombank">
      <SectionHeader />
      <SectionBanner />
      <SectionJob />
      <SelectionProcess />
      <SectionMagazineBlogs />
      <div className="box-effect relative overflow-hidden">
        <SectionAbout />
        <SectionMedia />
      </div>
      <SectionFooter />
      <ModalViewBlog />
    </div>
  );
}

export async function generateMetadata(): Promise<Metadata> {
  let metaData: Metadata = {};
  metaData = {
    title: "TopDev | Techcombank",
    description: "topdev,techcombank",
    alternates: {
      canonical: process.env.NEXT_PUBLIC_BASE_URL + "/group/techcombank",
    },
    keywords: "topdev,techcombank",
    openGraph: {
      url: process.env.NEXT_PUBLIC_BASE_URL + "/group/techcombank",
      title: "TopDev | Techcombank",
      description: "TopDev | Techcombank",
      siteName: "TopDev",
      type: "article",
      locale: getLocaleForParams(),
    },
    twitter: {
      title: "TopDev | Techcombank",
      description: "TopDev | Techcombank",
      site: "TopDev",
      creator: "@Topdevvn",
    },
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
  };
  return {
    ...metaData,
  };
}
