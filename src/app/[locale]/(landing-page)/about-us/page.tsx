import Link from "@/components/Link/Link";
import Image from "next/image";
import { useTranslations } from "next-intl";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
  const translate = await getTranslations();

  return {
    title: translate("abouts_us_header") + " - " + translate("abouts_us_title"),
    description: translate("abouts_us_header") + " - " + translate("abouts_us_title") + " - " + translate("home_meta_description"),
    alternates: {
      canonical: `${process.env.NEXT_PUBLIC_BASE_URL}/` + translate("abouts_us_slug"),
      languages: {
        vi: `${process.env.NEXT_PUBLIC_BASE_URL}/ve-chung-toi`,
        en: `${process.env.NEXT_PUBLIC_BASE_URL}/about-us`,
        "x-default": `${process.env.NEXT_PUBLIC_BASE_URL}/ve-chung-toi`,
      },
    },
    };
}

export default function AboutUsPage() {
  const t = useTranslations();

  return (
    <main>
      <div id="about-us">
        <div className="bg-gray-200">
          <div className="container max-w-7xl px-5 py-10">
            <div className="rounded-md bg-white p-5 lg:p-10">
              <h1 className="mb-5 text-3xl font-bold text-primary lg:mb-8 lg:text-5xl">
                {t("abouts_us_header")}
              </h1>
              <p className="text-xl font-bold">{t("abouts_us_title")}</p>
              <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}`}>https://topdev.vn</Link>
              <hr className="my-4 border-t border-solid border-gray-300" />
              <p className="mb-4">
                {t.rich("abouts_us_summary", {
                  strong: (chunk) => <strong>{chunk}</strong>,
                  link: (chunks) => (
                    <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}`}>{chunks}</Link>
                  ),
                })}
              </p>
              <p className="mb-4">
                {t.rich("abouts_us_content", {
                  strong: (chunk) => <strong>{chunk}</strong>,
                  link: (chunks) => (
                    <Link href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/bao-cao-it-viet-nam`}>
                      {chunks}
                    </Link>
                  ),
                })}
              </p>
              <hr className="my-4" />
              <p>{t("abouts_us_mission")}</p>
              <h2 className="text-3xl font-bold">MAKE IT VIETNAM BETTER</h2>
              <ul className="list-disc pl-4">
                <li>
                  {t.rich("abouts_us_connect", {
                    strong: (chunk) => <strong>{chunk}</strong>,
                  })}
                </li>
                <li>
                  {t.rich("abouts_us_supply", {
                    strong: (chunk) => <strong>{chunk}</strong>,
                  })}
                </li>
                <li>
                  {t.rich("abouts_us_accelerate", {
                    strong: (chunk) => <strong>{chunk}</strong>,
                  })}
                </li>
              </ul>
              <Image
                className="min-w-1142 min-h-542 mx-auto"
                src={t("abouts_us_image")}
                alt="TopDev - Top IT Jobs For Developers - MAKE IT VIETNAM BETTER"
                height="542"
                width="1142"
              />
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
