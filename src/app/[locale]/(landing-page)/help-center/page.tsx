import Image from "next/image";
import { FaExclamationCircle, FaExclamationTriangle } from "react-icons/fa";
import SadFaceImg from "@/assets/images/help-center/sad-face.png";
import DeleteAccountImg from "@/assets/images/help-center/delete-account.png";

export default function HelpCenterPage() {
  return (
    <main>
      <div id="resolve-complaints" className="bg-gray-200 py-10">
        <div className="container max-w-7xl lg:px-5">
          <div className="bg-white px-6 py-4">
            <h4 className="text-xl font-bold text-primary lg:text-center lg:text-2xl">
              Hướng dẫn quản lý hồ sơ của bạn
            </h4>
          </div>
          <div
            className="border-t border-solid border-yellow-400 p-6 lg:border-b"
            style={{ background: "#FDF1D8" }}
          >
            <div
              className="flex text-lg font-medium lg:items-center lg:text-xl"
              style={{ color: "#CE8800" }}
            >
              <span>
                <FaExclamationCircle />
              </span>
              <span className="ml-1">Lưu ý:</span>
            </div>
            <div className="ml-12">
              <ul className="list-disc" style={{ color: "#CE8800" }}>
                <li>Vui lòng gửi email bằng email bạn đăng ký tài khoản</li>
                <li>Khi bạn gửi email yêu cầu đến cho TopDev:</li>
              </ul>
              <div>
                <p>
                  - Nếu yêu cầu của bạn hợp lệ, TopDev sẽ xử lý trong vòng 72
                  giờ
                </p>
                <p>
                  - Trong trường hợp yêu cầu của bạn được thực hiện thành công,
                  thông tin của bạn có thể đã được nhà tuyển dụng lưu trữ trước
                  khi bạn gửi yêu cầu, TopDev không thể kiểm soát việc lưu trữ,
                  sử dụng thông tin từ các bên này.
                </p>
              </div>
            </div>
          </div>
          <div className="max-w-7xl lg:pt-5">
            <div className="lg:rounded-md bg-white p-6">
              <h2 className="text-lg font-medium lg:text-xl">
                Rút đơn ứng tuyển của bạn
              </h2>
              <div className="my-6 flex items-center rounded bg-brand-100 p-2 text-primary lg:text-sm">
                <span className="mr-2 border-r border-solid border-red-400 p-2 text-2xl">
                  <FaExclamationTriangle />
                </span>
                <span className="text- font-semibold tracking-[1%]">
                  Lưu ý: Khi rút đơn ứng tuyển, bạn sẽ không được tiếp tục quy
                  trình ứng tuyển tại vị trí công việc đã rút, cũng như không
                  thể ứng tuyển lại vào công việc trên
                </span>
              </div>
              <ul className="ml-4 list-decimal">
                <li className="my-6 text-sm font-medium lg:text-lg">
                  Bạn cần gửi một email tới cho
                  <span className="text-primary"> <EMAIL> </span>
                  với tiêu đề và nội dung như sau:
                  <div className="my-3 -ml-4 bg-gray-100 p-3">
                    <div className="grid grid-cols-12 gap-2">
                      <div className="col-span-12 text-base font-normal text-gray-500 lg:col-span-1">
                        Tiêu đề:
                      </div>
                      <div className="col-span-12 text-base lg:col-span-11">
                        <span className="font-bold"> [Rút đơn ứng tuyển]</span>
                      </div>
                      <div className="col-span-12 text-base font-normal text-gray-500 lg:col-span-1">
                        Nội dung:
                      </div>
                      <div className="col-span-12 text-base font-bold lg:col-span-11">
                        Yêu cầu rút đơn ứng tuyển
                        <div className="ml-6">
                          <ul className="list-disc">
                            <li className="text-base">
                              <span className="mr-1 font-normal text-gray-500">
                                Họ tên:
                              </span>
                              <span className="font-bold">
                                [Họ tên dùng nộp đơn ứng tuyển]
                              </span>
                            </li>
                            <li className="text-base">
                              <span className="mr-1 font-normal text-gray-500">
                                Email:
                              </span>
                              <span className="font-bold">
                                [Địa chỉ email đã dùng để ứng tuyển]
                              </span>
                            </li>
                            <li className="text-base">
                              <span className="mr-1 font-normal text-gray-500">
                                Số điện thoại:
                              </span>
                              <span className="font-bold">
                                [SĐT đã dùng để ứng tuyển]
                              </span>
                            </li>
                            <li className="text-base">
                              <span className="mr-1 font-normal text-gray-500">
                                Vị trí ứng tuyển:
                              </span>
                              <span className="font-bold">
                                [Tên vị trí ứng tuyển]
                              </span>
                            </li>
                            <li className="text-base">
                              <span className="mr-1 font-normal text-gray-500">
                                Công ty ứng tuyển:
                              </span>
                              <span className="font-bold">
                                [Tên công ty ứng tuyển]
                              </span>
                            </li>
                            <li className="text-base">
                              <span className="mr-1 font-normal text-gray-500">
                                Lý do rút đơn ứng tuyển:
                              </span>
                              <span className="font-bold">[Nội dung]</span>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Image
                    src="/v4/assets/images/help-center/rut-don-ung-tuyen.jpg"
                    alt="Rút đơn ứng tuyển"
                    className="mx-auto h-auto max-w-full"
                    width="440"
                    height="280"
                  />
                  <p className="text-center text-xs font-thin italic">
                    Ảnh minh họa
                  </p>
                </li>
                <li className="my-6 text-sm font-medium lg:text-lg">
                  TopDev sẽ gửi cho bạn một email xác nhận rút các đơn ứng tuyển
                  theo yêu cầu của bạn.
                </li>
                <li className="mt-6 text-sm font-medium lg:text-lg">
                  Những vị trí đã được yêu cầu &#8220;Rút đơn ứng tuyển&#8221;sẽ
                  được chuyển sang trạng thái “Đã rút đơn ứng tuyển” tại trang
                  &#8220;Việc làm đã ứng tuyển&#8221; và &#8220;Quản lý
                  CV&#8221;.
                </li>
              </ul>
            </div>
          </div>
          <div className="max-w-7xl pt-5">
            <div className="lg:rounded-md bg-white p-6">
              <h2 className="text-lg font-medium lg:text-xl">Xóa CV của bạn</h2>
              <div className="mt-6">
                <ul className="ml-4 list-disc">
                  <li className="mb-3">
                    <div className="text-sm lg:flex lg:text-lg">
                      <div className="basis-1/10 font-bold lg:mr-3">
                        Trường hợp 1:
                      </div>
                      <div className="basis-9/10">
                        <p>
                          CV chưa gửi đi ứng tuyển bất kỳ vị trí nào trên TopDev
                          <br />
                          Bạn chỉ cần nhấn vào nút “Xóa” trong cột tùy chọn tại
                          trang &#8220;Quản lý CV&#8221;
                        </p>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div className="text-sm lg:flex lg:text-lg">
                      <div className="basis-1/10 font-bold lg:mr-3">
                        Trường hợp 2:
                      </div>
                      <div className="basis-9/10">
                        <p>
                          CV đã được gửi đi ứng tuyển
                          <br />
                          Đối với trường hợp này bạn cần làm theo các bước sau
                          để có thể xóa CV
                        </p>
                      </div>
                    </div>
                  </li>
                </ul>
                <div className="my-6 flex items-center rounded bg-brand-100 p-2 text-xs font-semibold text-primary lg:text-sm">
                  <span className="mr-2 border-r border-solid border-red-400 p-2 text-2xl">
                    <FaExclamationTriangle />
                  </span>{" "}
                  Lưu ý: Khi xóa CV, đồng nghĩa với việc rút lại tất cả hồ sơ
                  ứng tuyển tại các công việc mà bạn đã ứng tuyển với CV này.
                  Bạn sẽ không được tiếp tục quy trình ứng tuyển tại vị trí công
                  việc đã rút, cũng như không thể ứng tuyển lại vào công việc
                  trên
                </div>
                <ul className="ml-4 list-decimal">
                  <li className="my-6 text-sm font-medium lg:text-lg">
                    Bạn cần gửi một email tới cho
                    <span className="text-primary"> <EMAIL> </span>
                    với tiêu đề và nội dung như sau:
                    <div className="my-3 -ml-4 bg-gray-100 p-3">
                      <div className="grid grid-cols-12 gap-2">
                        <div className="col-span-12 text-base font-normal text-gray-500 lg:col-span-1">
                          Tiêu đề:
                        </div>
                        <div className="col-span-12 lg:col-span-11">
                          <span className="font-bold"> [Xóa CV ứng tuyển]</span>
                        </div>
                        <div className="col-span-12 text-base font-normal text-gray-500 lg:col-span-1">
                          Nội dung:
                        </div>
                        <div className="col-span-12 lg:col-span-11">
                          Yêu cầu xóa CV ứng tuyển
                          <div className="ml-6">
                            <ul className="list-disc">
                              <li className="text-base">
                                <span className="mr-1 font-normal text-gray-500">
                                  Họ tên:
                                </span>
                                <span className="font-bold">
                                  [Họ tên dùng nộp đơn ứng tuyển]
                                </span>
                              </li>
                              <li className="text-base">
                                <span className="mr-1 font-normal text-gray-500">
                                  Email:
                                </span>
                                <span className="font-bold">
                                  [Địa chỉ email đã dùng để ứng tuyển]
                                </span>
                              </li>
                              <li className="text-base">
                                <span className="mr-1 font-normal text-gray-500">
                                  Số điện thoại:
                                </span>
                                <span className="font-bold">
                                  [SĐT đã dùng để ứng tuyển]
                                </span>
                              </li>
                              <li className="text-base">
                                <span className="mr-1 font-normal text-gray-500">
                                  Tên CV:
                                </span>
                                <span className="font-bold">
                                  [Tên file CV đầy đủ được lưu tại trang Quản lý
                                  CV]
                                </span>
                              </li>

                              <li className="text-base">
                                <span className="mr-1 font-normal text-gray-500">
                                  Vị trí ứng tuyển:
                                </span>
                                <span className="font-bold">
                                  [Lấy ví dụ 1 vị trí mà bạn đã ứng tuyển với CV trên]
                                </span>
                              </li>
                              <li className="text-base">
                                <span className="mr-1 font-normal text-gray-500">
                                  Công ty ứng tuyển:
                                </span>
                                <span className="font-bold">
                                  [Tên công ty của vị trí ứng tuyển mà bạn đề cập phía trên]
                                </span>
                              </li>
                              <li className="text-base">
                                <span className="mr-1 font-normal text-gray-500">
                                  Lí do xóa CV:
                                </span>
                                <span className="font-bold">[Nội dung]</span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Image
                      src="/v4/assets/images/help-center/xoa-cv-ung-tuyen.jpg"
                      alt="Rút đơn ứng tuyển"
                      className="mx-auto h-auto max-w-full"
                      width="440"
                      height="280"
                    />
                    <p className="text-center text-xs font-thin italic">
                      Ảnh minh họa
                    </p>
                  </li>
                  <li className="my-6 text-sm font-medium lg:text-lg">
                    TopDev sẽ gửi cho bạn một email xác nhận xóa CV theo yêu cầu
                    của bạn.
                  </li>
                  <li className="mt-6 text-sm font-medium lg:text-lg">
                    Những CV đã yêu cầu xóa sẽ bị biến mất khỏi trang Quản lý CV
                    của bạn và tất cả các vị trí bạn dùng CV đó để ứng tuyển
                    chuyển sang trạng thái &#8220;Đã rút đơn ứng tuyển&#8221;
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div className="max-w-7xl pt-5">
            <div className="lg:rounded-md bg-white p-6">
              <h2 className="text-lg font-medium lg:text-xl">
                Xóa tài khoản TopDev của bạn
              </h2>
              <div className="mt-6">
                <div
                  className="mt-6 flex rounded p-3 text-sm lg:items-center lg:text-lg"
                  style={{ background: "#FDF1D8" }}
                >
                  <span className="mr-2 p-2 text-2xl">
                    <Image
                      className="w-64 md:w-32 lg:w-24"
                      src={SadFaceImg}
                      alt="Sad face"
                      width="65"
                      height="66"
                    />
                  </span>
                  <div>
                    Chúng tôi sẽ rất tiếc khi bạn không muốn giữ tài khoản của
                    TopDev. Trong trường hợp bạn chỉ muốn ngừng nhận các email
                    từ TopDev, bên dưới mỗi email từ TopDev có gắn một liên kết
                    để bạn có thể:
                    <ul className="ml-6 list-disc font-semibold">
                      <li>Ngưng nhận bản tin</li>
                      <li>Ngưng nhận thông báo việc làm</li>
                    </ul>
                  </div>
                </div>
                <div
                  className="mb-6 mt-3 flex items-center rounded
                bg-red-100 p-2 text-xs font-medium text-primary lg:text-sm"
                >
                  <span className="mr-2 border-r border-solid border-red-400 p-2 text-2xl">
                    <FaExclamationTriangle />
                  </span>{" "}
                  Đóng tài khoản của bạn có nghĩa là xóa vĩnh viễn hồ sơ của bạn
                  bao gồm sơ yếu lý lịch, công việc đã ứng tuyển. <br />
                  Vui lòng cân nhắc trước khi xóa tài khoản TopDev.
                </div>
                <h3 className="text-lg font-medium">
                  Sau khi cân nhắc kỹ, nếu bạn vẫn muốn xóa vĩnh viễn tài khoản
                  của mình trên TopDev vui lòng:
                </h3>
                <ul className="ml-4 list-decimal">
                  <li className="my-6 text-sm font-medium lg:text-lg">
                    Gửi một email tới cho
                    <span className="text-primary"> <EMAIL> </span>
                    với tiêu đề và nội dung như sau:
                    <div className="my-3 -ml-4 bg-gray-100 p-3">
                      <div className="grid grid-cols-12 gap-2">
                        <div className="col-span-12 text-base font-normal text-gray-500 lg:col-span-1">
                          Tiêu đề:
                        </div>
                        <div className="col-span-12 lg:col-span-11">
                          <span className="font-bold">
                            [Xóa tài khoản TopDev]
                          </span>
                        </div>
                        <div className="col-span-12 text-base font-normal text-gray-500 lg:col-span-1">
                          Nội dung:
                        </div>
                        <div className="col-span-12 text-base font-normal lg:col-span-11">
                          Yêu cầu xóa tài khoản TopDev
                          <div className="ml-6">
                            <ul className="list-disc">
                              <li>
                                <span className="mr-1 text-base font-normal text-gray-500">
                                  Họ tên:
                                </span>
                                <span className="font-bold">
                                  [Họ tên đăng ký tài khoản]
                                </span>
                              </li>
                              <li>
                                <span className="mr-1 text-base font-normal text-gray-500">
                                  Email:
                                </span>
                                <span className="font-bold">
                                  [Địa chỉ email đăng ký tài khoản]
                                </span>
                              </li>
                              <li>
                                <span className="mr-1 text-base font-normal text-gray-500">
                                  Lý do yêu cầu xóa tài khoản TopDev:
                                </span>
                                <span className="font-bold"> [Nội dung]</span>
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <Image
                      src="/v4/assets/images/help-center/xoa-tai-khoan-topdev.jpg"
                      alt="Rút đơn ứng tuyển"
                      className="mx-auto h-auto max-w-full"
                      width="440"
                      height="246"
                    />
                    <p className="text-center text-xs font-thin italic">
                      Ảnh minh họa
                    </p>
                  </li>
                  <li className="my-6 text-sm font-medium lg:text-lg">
                    TopDev sẽ gửi cho bạn một email xác nhận yêu cầu và hướng
                    dẫn xóa tài khoản cho bạn.
                    <Image
                      src={DeleteAccountImg}
                      alt="Rút đơn ứng tuyển"
                      className="mx-auto h-auto max-w-full"
                      width="300"
                      height="200"
                    />
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
