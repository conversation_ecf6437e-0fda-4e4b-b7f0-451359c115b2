export type ErrorsProps = {
    fullname: string[],
    company: string[],
    phone: string[],
    email: string[],
    message: string[]
}

export type PostContactResultType = {
    error: boolean | ErrorsProps,
    data: PostContactData,
    message: string
}

export type PostContactData = {
    fullname: string,
    company: string,
    email: string,
    phone: string,
    message: string,
    subject?: string,
    token?: string
}