import "@/assets/styles/base.scss";
import { Lang } from '@/types/page';
import { useTranslations } from 'next-intl';
import ContactForm from '@/components/LandingPage/Contact/ContactForm';
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import Link from "next/link";
// Get dataMeta for SEO
export async function generateMetadata({params}: {
    params: { slug: string; locale: Lang }
}): Promise<Metadata> {
    const locale = params.locale ?? 'vi';
    const translate = await getTranslations();

    // set title metadata
    const title = translate("contact_metadata_title");

    // set description metadata
    const description = translate("contact_metadata_description");

    // set robots metadata
    let robots_meta = { index: true, follow: true };

    // set slug
    const slug_vi = process.env.NEXT_PUBLIC_BASE_URL + "/contact";
    const slug_en = process.env.NEXT_PUBLIC_BASE_URL + "/contact";

    return {
        metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
        alternates: {
            canonical: slug_en,
            languages: {
                vi: slug_vi,
                en: slug_en
            }
        },
        title: title,
        openGraph: {
            url: slug_en,
            title: title,
            description: description,
            type: "website",
            locale: locale === "vi" ? "vi_VN" : "en_US",
            siteName: "TopDev"
        },
        twitter: {
          site: "TopDev",
          title: title,
          description: description,
          creator: "@Topdevvn",
        },
        robots: robots_meta,
        verification: {
          google: "XxqADEgloBElQyOQ9ePm7EG3XO01_vcTMre2KQgD9K8",
        },
        other: {
          "twitter:domain": "topdev.vn",
        },
    }
}

const Contact = ({params}: {
    params: { slug: string; locale: Lang }
}) => {
    const t = useTranslations();
    
    return (
        <div id="contact" className="bg-gray-200 py-10">
            <div className="container max-w-7xl px-5">
                <div className="bg-white p-5 lg:rounded-md">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-5">
                        <div className="col-span-1">
                            <h2 className="text-3xl text-gray-800">{t("contact_our_office")}</h2>
                            <h4 className="mt-2">{t("contact_head_office")}</h4>
                            <ul className="mt-2">
                                <li>
                                    <span className="font-bold">{t("contact_address")} </span>{t("footer_address")}
                                </li>
                                <li>
                                    <span className="font-bold">{t("contact_email")} </span>
                                    <Link href="mailto:<EMAIL>">
                                        <span className="transition-all hover:text-primary"><EMAIL></span>
                                    </Link>
                                </li>
                                <li>
                                    <span className="font-bold">{t("contact_tel")} </span>
                                    <Link href="tel:0888155500">
                                        <span className="text-primary">0888 1555 00</span>
                                    </Link>
                                </li>
                            </ul>
                            <div className="border border-solid border-gray-600 mt-5 overflow-hidden">
                                <iframe
                                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3919.138240221817!2d106.71240821519642!3d10.800722561697185!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x317527bca86da247%3A0xb9d37fcd00009796!2zQ8OUTkcgVFkgQ-G7lCBQSOG6pk4gWMOCWSBE4buwTkcgQU4gUEhPTkc!5e0!3m2!1svi!2s!4v1664966848908!5m2!1svi!2s"
                                    width="600" height="450" style={{border:0}} allowFullScreen loading="lazy"
                                    referrerPolicy="no-referrer-when-downgrade"></iframe>
                            </div>
                        </div>
                        <div className="col-span-1">
                            <h1 className="text-3xl text-gray-800">{t("contact_contact_now")}</h1>
                            <ContactForm />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Contact;
