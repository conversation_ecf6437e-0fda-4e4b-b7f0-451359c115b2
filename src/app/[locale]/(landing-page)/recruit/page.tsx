import CardRecruitInformation from "@/components/Recruit/CardRecruitInformation";
import { getTranslations } from "next-intl/server";
import { Lang } from "@/types/page";
import { Metadata } from "next";
import Link from "next/link";
import Image from "next/image";
import { useTranslations } from "next-intl";
import CompanyLogoSlider from "@/components/Recruit/CompanyLogoSlider";
import FormRecruit from "@/components/Recruit/FormRecruit";

export async function generateMetadata({
  params,
}: {
  params: { slug: string; locale: Lang };
}): Promise<Metadata> {
  const t = await getTranslations();
  const locale = params.locale ?? "vi";
  const slug = process.env.NEXT_PUBLIC_BASE_URL + "/recruit";
  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
    alternates: {
      canonical: slug,
      languages: {
        vi: slug,
        en: slug,
        "x-default": slug,
      },
    },
    title: t("recruit_meta_title"),
    description: t("recruit_meta_description"),
    keywords: "",
    robots: "index, follow",
    creator: "@Topdevvn",
    other: {
      type: "website",
      site_name: "TopDev",
      site: "TopDev",
      "twitter:domain": "topdev.vn",
      "og:site_name": "TopDev",
    },
    openGraph: {
      url: slug,
      title: t("recruit_meta_open_graph_title"),
      description: t("recruit_meta_open_graph_description"),
      type: "website",
      locale: locale === "vi" ? "vi_VN" : "en_US",
    },
    twitter: {
      site: "TopDev",
      title: t("recruit_meta_twitter_title"),
      description: t("recruit_meta_twitter_description"),
      creator: "@Topdevvn",
      card: undefined,
    },
  };
}

const RecruitPage = () => {
  const t = useTranslations();

  return (
    <div className="container mb-10 mt-14 w-auto bg-white">
      <div className="grid-cols-12 gap-14 lg:grid">
        <div className="col-span-6 max-w-full text-center">
          <Link href={"/"} className="block">
            <Image
              className="m-auto"
              src="https://c.topdevvn.com/v4/assets/images/td-logo.png"
              alt=""
              width="225"
              height="44"
            />
          </Link>
          <h4 className="mb-8 mt-4 text-xl font-semibold text-gray-600">
            {t("recruit_infomation_title")}
          </h4>
          <div className="grid w-full grid-cols-12 flex-row gap-3 lg:grid">
            <div className="col-span-4 rounded bg-gray-100">
              <CardRecruitInformation
                image="/v4/assets/images/recruit/recruit_benefit_1.webp"
                title={t("recruit_infomation_benefit_title_1")}
                description={t("recruit_infomation_benefit_description_1")}
              />
            </div>
            <div className="col-span-4 rounded bg-gray-100">
              <CardRecruitInformation
                image="/v4/assets/images/recruit/recruit_benefit_2.webp"
                title={t("recruit_infomation_benefit_title_2")}
                description={t("recruit_infomation_benefit_description_2")}
              />
            </div>
            <div className="col-span-4 rounded-lg bg-gray-100">
              <CardRecruitInformation
                image="/v4/assets/images/recruit/recruit_benefit_3.webp"
                title={t("recruit_infomation_benefit_title_3")}
                description={t("recruit_infomation_benefit_description_3")}
              />
            </div>
          </div>
          <div className="mx-auto flex-col">
            <h4 className="mt-8 text-2xl font-semibold text-primary">
              {t("recruit_customer_trust")}
            </h4>
            <p className="mt-2 text-sm font-normal text-gray-400">
              {t("recruit_customer_trust_description")}
            </p>
          </div>
          <div className="my-8">
            <CompanyLogoSlider />
          </div>
          <div className="mt-8 h-[auto] w-[auto] rounded-xl bg-gray-100 p-4">
            <p className="text-left text-base font-bold">
              {t("recruit_title_new_feature")}
            </p>
            <ul className="mt-1 flex flex-col gap-1 overflow-hidden text-gray-500">
              <li className="flex flex-row text-2xl">
                <span className="p-1">
                  <Image
                    src="/v4/assets/images/online-payment/icon-check-list.png"
                    alt="icon--checklist"
                    width="16"
                    height="16"
                  />
                </span>
                <span className="ml-2 text-base">
                  {t("recruit_payment_information")}
                </span>
              </li>
              <li className="mt-1 flex flex-row  text-2xl">
                <span className="p-1">
                  <Image
                    src="/v4/assets/images/online-payment/icon-check-list.png"
                    alt="icon--checklist"
                    width="16"
                    height="16"
                  />
                </span>
                <span className="ml-2 text-base">
                  {t("recruit_search_cadidates")}
                </span>
              </li>
              <li className="mt-1 text-left text-gray-500">
                {t.rich("recruit_call_hotline", {
                  red: (chunk) => (
                    <span className={"font-bold text-primary  underline"}>
                      {chunk}
                    </span>
                  ),
                })}
              </li>
            </ul>
          </div>
        </div>

        <div className="col-span-6">
          <FormRecruit />
        </div>
      </div>
    </div>
  );
};

export default RecruitPage;
