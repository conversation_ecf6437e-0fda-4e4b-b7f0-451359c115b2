import BlogITSection from "@/components/LandingPage/SalaryCalculatorTool/BlogITSection/BlogITSection";
import SectionCalculateCard from "@/components/LandingPage/SalaryCalculatorTool/SectionCalculateCard";
import SectionCalculatorDescripton from "@/components/LandingPage/SalaryCalculatorTool/SectionCalculatorDescripton";
import { getTranslations } from "next-intl/server";

export async function generateMetadata() {
  const translate = await getTranslations();

  return {
    title: translate("salary_calculate_title") + " - " + translate("abouts_us_title"),
    description: translate("salary_calculate_title") + " - " + translate("abouts_us_title") + " - " + translate("home_meta_description"),
    alternates: {
      canonical: process.env.NEXT_PUBLIC_BASE_URL + "/tools/tinh-luong-gross-net",
      languages: {
        vi: process.env.NEXT_PUBLIC_BASE_URL + "/tools/tinh-luong-gross-net",
        en: process.env.NEXT_PUBLIC_BASE_URL + "/tools/tinh-luong-gross-net",
        "x-default": process.env.NEXT_PUBLIC_BASE_URL + "/tools/tinh-luong-gross-net",
      },
    },
  };
}

const SalaryConvertPage = () => {
  return (
    <div className="mb-5 bg-[#efefef] py-3">
      <div className="container grid max-w-7xl grid-cols-1 flex-row gap-10 px-5 lg:grid-cols-12">
        <div className="lg:col-span-8">
          <SectionCalculateCard />
          <SectionCalculatorDescripton />
        </div>
        <div className="lg:col-span-4">
          <BlogITSection />
        </div>
      </div>
    </div>
  );
};

export default SalaryConvertPage;
