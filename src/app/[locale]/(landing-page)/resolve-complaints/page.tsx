import { Lang } from "@/types/page";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import Link from "next/link";

// get datameta for SEO
export async function generateMetadata({params}: {
    params: { slug: string; locale: Lang }
}): Promise<Metadata> {
    const locale = params.locale ?? 'vi';
    const translate = await getTranslations();

    // set title metadata
    const title = translate("resolve_complaints_metadata_title");

    // set description metadata
    const description = translate("resolve_complaints_metadata_description");

    // set robots metadata
    let robots_meta = { index: true, follow: true };

    // set slug
    const slug_vi = process.env.NEXT_PUBLIC_BASE_URL + "/resolve-complaints";
    const slug_en = process.env.NEXT_PUBLIC_BASE_URL + "/resolve-complaints";

    return {
        metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
        alternates: {
            canonical: slug_en,
            languages: {
                vi: slug_vi,
                en: slug_en,
                "x-default": slug_vi
            }
        },
        title: title,
        openGraph: {
            url: slug_en,
            title: title,
            description: description,
            type: "website",
            locale: locale === "vi" ? "vi_VN" : "en_US",
            siteName: "TopDev"
        },
        twitter: {
          site: "TopDev",
          title: title,
          description: description,
          creator: "@Topdevvn",
        },
        robots: robots_meta,
        verification: {
          google: "XxqADEgloBElQyOQ9ePm7EG3XO01_vcTMre2KQgD9K8",
        },
        other: {
          "twitter:domain": "topdev.vn",
        },
    }
}

const ResolveComplaints = ({params}: {
    params: { slug: string; locale: Lang }
}) => {
    return (
        <div id="resolve-complaints" className="bg-gray-200 py-10">
            <div className="container max-w-7xl px-5">
                <div className="bg-white p-10 lg:rounded-md">
                    <h1 className="text-5xl text-primary">Giải quyết khiếu nại</h1>
                    <hr className="border-t border-solid border-gray-300 my-5" />
                    <p>Khi xảy ra tranh chấp, Công ty đề cao giải pháp thương lượng hòa giải giữa các bên nhằm duy trì sự tin
                        cậy và chất lượng dịch vụ của topdev.vn theo các bước sau:</p>
                    <ul className="pl-4 list-disc">
                        <li>Bước 1: Thành viên gửi khiếu nại qua email <Link href="mailto:<EMAIL>" className="transition-all hover:text-primary"><EMAIL></Link></li>
                        <li>Bước 2: Bộ phận chăm sóc khách hàng của topdev.vn sẽ tiếp nhận khiếu nại và phản hồi với thành viên
                            trong vòng 24 giờ. Tùy theo tính chất và mức độ khiếu nại thì topdev.vn sẽ có biện pháp cụ thể để
                            giải quyết tranh chấp đó không quá 10 ngày làm việc.</li>
                        <li>Bước 3: Trong trường hợp nằm ngoài khả năng và thẩm quyền của topdev.vn, thì Ban Quản Trị sẽ yêu cầu
                            thành viên đưa vụ việc ra cơ quan Nhà Nước có thẩm quyền để giải quyết theo pháp luật.</li>
                    </ul>
                    <div className="mt-3">
                        <p className="font-bold">Thành viên gửi khiếu nại tại địa chỉ:</p>
                        <ul className="list-none pl-10">
                            <li>- Công Ty Cổ Phần Applancer</li>
                            <li>- Địa chỉ: Tầng 12A, Toà nhà AP Tower, 518B Điện Biên Phủ, Phường 21, Quận Bình Thạnh, Thành phố
                                Hồ Chí Minh</li>
                            <li>- Điện thoại: <Link href="tel:0888155500" className="transition-all hover:text-primary">028 62733496</Link> | <Link href="tel:02862733497" className="transition-all hover:text-primary">028 62733497</Link></li>
                            <li>- Email: <Link href="mailto:<EMAIL>" className="transition-all hover:text-primary"><EMAIL></Link></li>
                        </ul>
                    </div>
                    <div>
                        <ul className="list-disc pl-4">
                            <li><Link href="https://topdev.vn" className="transition-all hover:text-primary">https://topdev.vn</Link> tôn trọng và nghiêm túc thực hiện các quy định của pháp luật về bảo vệ quyền
                                lợi của ứng viên. Vì vậy, đề nghị các thành viên đăng tin tuyển dụng trên sàn cung cấp đầy đủ,
                                chính xác, trung thực và chi tiết các thông tin liên quan đến nội dung công việc. Mọi hành vi
                                lừa đảo, gian lận đều bị lên án và phải chịu hoàn toàn trách nhiệm trước pháp luật.</li>
                            <li>Các bên bao gồm ứng viên, người tuyển dụng sẽ phải có vai trò trách nhiệm trong việc tích cực
                                giải quyết vấn đề. Đối với người tuyển dụng cần có trách nhiệm cung cấp văn bản giấy tờ chứng
                                thực thông tin liên quan đến sự việc đang gây mâu thuẫu với ứng viên. Đối với topdev.vn sẽ có
                                trách cung cấp những thông tin liên quan đến ứng viên và người tuyển dụng nếu được một trong hai
                                bên (liên quan đến tranh chấp đó) yêu cầu.</li>
                            <li>Sau khi ứng viên, người tuyển dụng đã giải quyết xong tranh chấp phải có trách nhiệm báo lại cho
                                ban quản trị topdev.vn. Trong trường hợp giao dịch phát sinh mâu thuẫn mà lỗi thuộc về người
                                tuyển dụng: topdev.vn sẽ có biện pháp cảnh cáo, khóa tài khoản hoặc chuyển cho cơ quan pháp luật
                                có thẩm quyền tùy theo mức độ của sai phạm. topdev.vn sẽ chấm dứt và gỡ bỏ toàn bộ tin bài về
                                nội dung công việc của người tuyển dụng đó trên topdev.vn.</li>
                            <li>Nếu thông qua hình thức thỏa thuận mà vẫn không thể giải quyết được mâu thuẫn phát sinh từ giao
                                dịch giữa 2 bên ứng viên, người tuyển dụng, thì một trong hai bên sẽ có quyền nhờ đến cơ quan
                                pháp luật có thẩm quyền can thiệp nhằm đảm bảo lợi ích hợp pháp của các bên.</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default ResolveComplaints;
