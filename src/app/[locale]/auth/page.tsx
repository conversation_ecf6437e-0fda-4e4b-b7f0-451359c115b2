"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";

export default function AuthPage() {
  const searchParams = useSearchParams();

  useEffect(() => {
    let showTabEmployer = "";
    let tracking = "";
    if (searchParams.get("isEmployer")) showTabEmployer = "?#tab-employer";
    if (searchParams.get('referring_name')) tracking = `referring_name=${searchParams.get('referring_name')}&`
    if (searchParams.get('referring_from')) tracking += `referring_from=${searchParams.get('referring_from')}&`

    window.location.href =
      process.env.NEXT_PUBLIC_OAUTH2_URL_ACCOUNT +
      `?${tracking}redirect_uri=` +
      encodeURIComponent(process.env.NEXT_PUBLIC_BASE_URL + "/auth/success") +
      showTabEmployer;
  }, [searchParams]);

  return (
    <div role="status" className="m-5 max-w-sm animate-pulse">
      <div className="mb-4 h-2.5 w-48 rounded-full bg-gray-200 dark:bg-gray-700"></div>
      <div className="mb-2.5 h-2 max-w-[360px] rounded-full bg-gray-200 dark:bg-gray-700"></div>
      <div className="mb-2.5 h-2 rounded-full bg-gray-200 dark:bg-gray-700"></div>
      <div className="mb-2.5 h-2 max-w-[330px] rounded-full bg-gray-200 dark:bg-gray-700"></div>
      <div className="mb-2.5 h-2 max-w-[300px] rounded-full bg-gray-200 dark:bg-gray-700"></div>
      <div className="h-2 max-w-[360px] rounded-full bg-gray-200 dark:bg-gray-700"></div>
      <span className="sr-only">Loading...</span>
    </div>
  );
}
