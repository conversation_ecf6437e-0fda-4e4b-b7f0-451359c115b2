import "@/assets/styles/pages/home.scss";

import HomePageV1 from "@/components/pages/page";
import HomePageV2 from "@/components/v2/pages/page";

export function generateMetadata() {
  return {
    alternates: {
      canonical: "https://topdev.vn/",
      languages: {
        vi: "https://topdev.vn/",
        en: "https://topdev.vn/",
        "x-default": "https://topdev.vn/",
      },
    },
  };
}

export default function HomePage() {
  if (process.env.RENEW_TOPDEV_2025 === "true") {
    return <HomePageV2 />;
  }

  return <HomePageV1 />;
}
