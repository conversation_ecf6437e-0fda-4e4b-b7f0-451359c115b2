import "@/assets/styles/base.scss";
import Footer from "@/components/Common/Footer";
import Header from "@/components/Common/Header";
import BottomBar from "@/components/Common/mobiles/BottomBar";
import HeaderMobile from "@/components/Common/mobiles/Header.mobile";
import GTM from "@/components/Google/GTM";
import GoogleAdsManager from "@/components/Google/GoogleAdsManager";
import LayoutProvider from "@/components/Providers/LayoutProvider";
import UserProvider from "@/components/Providers/UserProvider";
import { Toaster } from "@/components/v2/ui/sonner";
import { FacebookSDKProvider } from "@/context/FacebookSDKProvider";
import StoreProvider from "@/store/provider";
import { classNames } from "@/utils";
import { isMobile } from "@/utils/device";
import type { Viewport } from "next";
import { NextIntlClientProvider, useMessages } from "next-intl";
import { getTranslations } from "next-intl/server";
import { Inter } from "next/font/google";
import Script from "next/script";
import NextTopLoader from "nextjs-toploader";
import React from "react";
import ReactDOM from "react-dom";

// Font
const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter", // nếu bạn dùng Tailwind custom font
  display: "swap",
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export async function generateMetadata() {
  const t = await getTranslations();

  return {
    title: t("home_meta_title"),
    description: t("home_meta_description"),
    keywords: t("home_meta_keywords"),
    robots: "index, follow",
    creator: "@Topdevvn",
    other: {
      type: "website",
      site_name: "TopDev",
      site: "TopDev",
      "apple-itunes-app": isMobile() ? "app-id=**********" : null,
      "google-play-app": isMobile() ? "app-id=it.jobs.topdev" : null,
    },
    icons: [
      {
        rel: "icon",
        type: "image/x-icon",
        url: "https://cdn.topdev.vn/v4/assets/images/favicon/favicon.ico",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "16x16",
        url: "https://cdn.topdev.vn/v4/assets/images/favicon/favicon-16x16.png",
      },
      {
        rel: "icon",
        type: "image/png",
        sizes: "32x32",
        url: "https://cdn.topdev.vn/v4/assets/images/favicon/favicon-32x32.png",
      },
      {
        rel: "apple-touch-icon",
        sizes: "180x180",
        url: "https://topdev.vn/assets/promote_app/app_icon.png",
      },
      {
        rel: "android-touch-icon",
        sizes: "180x180",
        url: "https://topdev.vn/assets/promote_app/app_icon.png",
      },
      {
        rel: "manifest",
        url: "../v4/assets/images/favicon/site.webmanifest",
      },
    ],
  };
}

// define schema json
const schemaTypeOrganization = {
  "@context": "https://schema.org",
  "@type": "EmploymentAgency",
  "@id": "https://topdev.vn/#employmentagency",
  additionalType: [
    "https://en.wikipedia.org/wiki/Employment_website",
    "https://www.wikidata.org/wiki/Q580148",
    "https://www.wikidata.org/wiki/Q17175443",
    "https://www.google.com/search?kgmid=/m/0dzb89",
  ],
  name: "TopDev",
  alternateName: [
    "TopDev",
    "TopDev Tuyển dụng IT",
    "TopDev Việt Nam",
    "TopDev Business",
  ],
  legalName: "CÔNG TY CỔ PHẦN APPLANCER",
  url: "https://topdev.vn/",
  foundingDate: "2015-10",
  slogan: "MAKE IT VIETNAM BETTER",
  vatID: "0313032338",
  keywords: [
    "TopDev",
    "tuyển dụng IT",
    "tìm việc làm IT",
    "việc làm IT",
    "IT jobs",
  ],
  description:
    "1000+ việc làm IT, tin tuyển dụng IT tại các công ty tập đoàn hàng đầu với mức lương thưởng hấp dẫn dành cho bạn. Xem và ứng tuyển ngay tại Topdev.vn",
  disambiguatingDescription:
    "Được thành lập vào năm 2015 bởi Applancer JSC, TopDev đã trở thành nền tảng Tuyển dụng IT hàng đầu tại Việt Nam với trang web có hàng triệu lượt truy cập hàng tháng tập trung vào IT https://topdev.vn & Ứng dụng tìm kiếm việc làm IT trên thiết bị di động, hơn 380.000 hồ sơ Lập trình viên & quản lý Cộng đồng Lập trình viên lớn nhất Việt Nam với hơn 550.000 người theo dõi trên mạng xã hội. Từ năm 2020, TopDev nhận đầu tư từ Saramin (https://saramin.co.kr - KOSDAQ 143240) - Nền tảng tuyển dụng số 1 tại Hàn Quốc.",
  logo: "https://cdn.topdev.vn/v4/assets/images/td-logo.png",
  image: "https://cdn.topdev.vn/v4/assets/images/td-logo.png",
  knowsAbout: [
    {
      "@type": "Thing",
      name: "Tuyển dụng",
      url: "https://www.wikidata.org/wiki/Q899277",
    },
    {
      "@type": "Thing",
      name: "Quản trị nhân sự",
      url: "https://www.wikidata.org/wiki/Q1056396",
    },
    {
      "@type": "Thing",
      name: "Phát triển sự nghiệp",
      url: "https://www.wikidata.org/wiki/Q5038939",
    },
    {
      "@type": "Thing",
      name: "Tìm việc",
      url: "https://www.wikidata.org/wiki/Q629194",
    },
  ],
  knowsLanguage: ["vi", "en"],
  telephone: "0888 1555 00",
  email: "<EMAIL>",
  contactPoint: {
    "@type": "ContactPoint",
    telephone: "0888 1555 00",
    email: "<EMAIL>",
    contactType: "customer service",
    areaServed: {
      "@type": "AdministrativeArea",
      name: "Việt Nam",
      "@id": "kg:/m/01crd5",
      url: "https://vi.wikipedia.org/wiki/Vi%E1%BB%87t_Nam",
      hasMap: "https://www.google.com/maps?cid=12698937955444482750",
    },
    availableLanguage: "Vietnamese",
  },
  hasMap: "https://maps.app.goo.gl/qEtcoDTTfpqWM476A",
  address: {
    "@type": "PostalAddress",
    streetAddress: "Toà nhà AP Tower, 518B Đ. Điện Biên Phủ, Phường 21",
    addressLocality: "Bình Thạnh",
    addressRegion: "Hồ Chí Minh",
    postalCode: "700000",
    addressCountry: "VN",
  },
  openingHoursSpecification: {
    "@type": "OpeningHoursSpecification",
    dayOfWeek: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
    opens: "08:00",
    closes: "17:30",
  },
  sameAs: [
    "https://www.youtube.com/channel/UCZedbcmUtab8Y7DEt4ZZv7Q",
    "https://www.linkedin.com/company/topdev-vn/",
    "https://www.facebook.com/topdevvietnam",
  ],
  award: [
    "Top 10 nền tảng tuyển dụng trực tuyến tại Việt Nam",
    "Top 1 và 2 từ khóa phổ biến tièm việc IT trên Google",
  ],
  founder: {
    "@type": "Person",
    name: "PARK JONGHO",
    gender: "https://schema.org/Male",
    nationality: {
      "@type": "Country",
      "@id": "kg:/m/2F06qd3",
      url: "https://vi.wikipedia.org/wiki/H%C3%A0n_Qu%E1%BB%91c",
      name: "Hàn Quốc",
      sameAs: "https://en.wikipedia.org/wiki/Korea",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/0/09/Flag_of_South_Korea.svg/1920px-Flag_of_South_Korea.svg.png",
      hasMap: "https://maps.app.goo.gl/2umUNUWeQxpzE6dd7",
    },
    image:
      "https://media.licdn.com/dms/image/C5603AQHEbFPUO3U-Cw/profile-displayphoto-shrink_400_400/0/1516456310549?e=2147483647&v=beta&t=0uoh3X9D1yNAIju9jvrbqjTnvrZa7jyc44Si5rzySDI",
    url: "https://topdev.vn/blog/ceo-topdev-nhan-manh-ve-yeu-to-con-nguoi-trong-thoi-dai-so-tat-ca-cho-su-phat-trien-kinh-te-lay-nhan-tai-lam-trung-tam/",
    jobTitle: "Co-Founder & CEO",
    worksFor: {
      "@type": "EmploymentAgency",
      name: "TopDev",
      alternateName: [
        "TopDev",
        "TopDev Tuyển dụng IT",
        "TopDev Việt Nam",
        "TopDev Business",
      ],
      legalName: "CÔNG TY CỔ PHẦN APPLANCER",
      url: "https://topdev.vn/",
      logo: "https://cdn.topdev.vn/v4/assets/images/td-logo.png",
      image: "https://cdn.topdev.vn/v4/assets/images/td-logo.png",
      foundingDate: "2015-10",
      slogan: "MAKE IT VIETNAM BETTER",
      vatID: "0313032338",
      telephone: "0888 1555 00",
      email: "<EMAIL>",
      contactPoint: {
        "@type": "ContactPoint",
        telephone: "0888 1555 00",
        email: "<EMAIL>",
      },
      address: {
        "@type": "PostalAddress",
        streetAddress: "Toà nhà AP Tower, 518B Đ. Điện Biên Phủ, Phường 21",
        addressLocality: "Bình Thạnh",
        addressRegion: "Hồ Chí Minh",
        postalCode: "700000",
        addressCountry: "VN",
      },
      hasMap: "https://maps.app.goo.gl/qEtcoDTTfpqWM476A",
    },
    description:
      "Ông PARK JONGHO là Nhà đồng sáng lập kiêm Tổng giám đốc điều hành (Co-Founder & CEO) của CÔNG TY CỔ PHẦN APPLANCER. Ông là một trong những người tiên phong trong lĩnh vực HR Tech,, hướng tới cung cấp các giải pháp công nghệ kết nối và phát triển nguồn nhân lực chất lượng cao tại Hàn Quốc và Việt Nam",
    sameAs: "https://vn.linkedin.com/in/park-jongho-b8483430",
  },
};

const schemaTypeWebsite = {
  "@context": "https://schema.org/",
  "@type": "WebSite",
  name: "TopDev - Việc Làm IT Hàng Đầu",
  alternateName: "TopDev - Việc Làm IT Hàng Đầu",
  url: "https://topdev.vn/",
  sameAs: [
    "https://www.youtube.com/channel/UCZedbcmUtab8Y7DEt4ZZv7Q",
    "https://www.linkedin.com/company/topdev-vn/",
    "https://www.facebook.com/topdevvietnam",
  ],
  potentialAction: {
    "@type": "SearchAction",
    target: "https://topdev.vn/viec-lam-it/{search_term}",
    "query-input": "required name=search_term",
  },
};

export default function AppLayout({
  children,
  params: { locale },
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const messages = useMessages();

  /** @ts-ignore */
  ReactDOM.preconnect("https://api.topdev.vn");

  /** @ts-ignore */
  ReactDOM.preconnect("https://cdnjs.cloudflare.com");

  const taScript = `${process.env.NEXT_PUBLIC_TOPDEV_ANALYTICS_SCRIPT}`;
  return (
    <html lang={locale} className={inter.variable}>
      <GTM />
      <GoogleAdsManager />
      <Script src={taScript} id="modal-scripts" />

      <body
        id={"frontend-v4"}
        className={classNames(isMobile() ? "pb-[66px]" : "")}
      >
        <NextIntlClientProvider locale={locale} messages={messages}>
          <NextTopLoader showSpinner={false} color="#DD3F24" />
          <StoreProvider>
            <LayoutProvider>
              <UserProvider>
                <FacebookSDKProvider>
                  {isMobile() ? <HeaderMobile /> : <Header />}

                  {children}

                  {isMobile() ? (
                    <>
                      {/* <FooterMobile /> */}
                      <BottomBar />
                    </>
                  ) : (
                    <Footer />
                  )}
                </FacebookSDKProvider>
              </UserProvider>
            </LayoutProvider>
          </StoreProvider>
          <Toaster />
        </NextIntlClientProvider>

        {process.env.NEXT_PUBLIC_TOPDEV_SCRIPT_ENABLE == "true" && (
          <>
            <Script
              src={process.env.NEXT_PUBLIC_TOPDEV_SCRIPT_URL}
              id="modal-scripts"
            />
            <link
              href={process.env.NEXT_PUBLIC_TOPDEV_STYLE_URL}
              rel="stylesheet"
            />
          </>
        )}

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schemaTypeOrganization),
          }}
        ></script>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schemaTypeWebsite),
          }}
        ></script>
      </body>
    </html>
  );
}
