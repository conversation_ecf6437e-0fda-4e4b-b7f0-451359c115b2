import "@/assets/styles/pages/detail-job.scss";
import {
  Apply<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CardDetailJobHeaderNew,
  CardDetailJobNewHeaderMobile,
  CardInfo,
  CardMoreJob,
  InitTracking,
  NotificationSuccess,
} from "@/components/DetailJob";
import CardNewContent from "@/components/DetailJob/NewVersion/CardNewContent";
import TailwindCDNInjector from "@/components/TailwindCDNInjector";
import DetailPage from "@/components/v2/pages/detail-jobs/page";
import { getJobDetail, getSuggestJobs } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { classNames, getIdFromSlug } from "@/utils";
import { isDesktop, isMobile } from "@/utils/device";
import {
  JOB_DISPLAY_STATUS_CLOSED,
  JOB_DISPLAY_STATUS_IN_REVIE<PERSON>,
  J<PERSON><PERSON>_DISPLAY_STATUS_OPEN,
} from "@/utils/enums";
import { getLocaleForParams } from "@/utils/localeServer";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import dynamic from "next/dynamic";
import { notFound } from "next/navigation";

const SectionRecommendJob = dynamic(
  () => import("@/components/DetailJob/SectionRecommendJob"),
);
const PopupRemindSignIn = dynamic(
  () => import("@/components/DetailJob/PopupRemindSignIn"),
);

//Get dataMeta for SEO
export async function generateMetadata({
  params,
}: {
  params: { slug: string; locale: Lang };
}): Promise<Metadata> {
  let job!: JobType;
  const locale = params.locale ?? "vi";
  const tranlate = await getTranslations();

  const id = getIdFromSlug(params.slug);

  if (!id) {
    notFound();
  }

  const skillName = () => {
    const skillArrLower = job.skills_arr.map((skill) => skill.toLowerCase());
    const titleArr = job.title.toLowerCase().split(" ");
    const titleArrLower = titleArr.map((title) => title.toLowerCase());
    const skillName = skillArrLower.filter(
      (skill) => !titleArrLower.includes(skill),
    );

    if (locale !== "vi") {
      return skillName.map((skill) => skill);
    }

    return skillName;
  };

  const levelKeyword = () => {
    if (job.job_levels_str) {
      return job.job_levels_str.split(",").map((level) => {
        return (
          tranlate("SEO_hiring_text") +
          " " +
          tranlate("SEO_hiring_level_text") +
          ` ${level}`
        );
      });
    }
    return [];
  };

  const locationKeyword = () => {
    if (job.addresses?.address_region_array) {
      return job.addresses?.address_region_array.map((location) => {
        return (
          tranlate("SEO_hiring_text") +
          ` ${titleKeyWord()} ` +
          tranlate("SEO_hiring_at") +
          ` ${location}`
        );
      });
    }
    return [];
  };

  const titleKeyWord = () => {
    return job.title.replace(/,/g, "");
  };

  const experienceKeyword = () => {
    let experience = job.experiences_str;
    experience = experience ? experience.split(",")[0] : "";

    // add from text if have any number in experience string
    if (/\d/.test(experience)) {
      experience = tranlate("SEO_experience_from") + " " + experience;
    }

    return experience;
  };

  // fetch data
  try {
    const data = await getJobDetail(id, getLocaleForParams());
    if (data) {
      job = data;
    } else {
      throw new Error("Job not found");
    }
  } catch (error) {
    notFound();
  }

  if (!job) {
    notFound();
  }

  const title = job.meta_title
    ? job.meta_title
    : tranlate("SEO_title_job", {
        job: job.title,
        company: job.company?.display_name,
        expired:
          job.status_display === JOB_DISPLAY_STATUS_CLOSED
            ? " - " + tranlate("SEO_job_expired")
            : "",
      });
  const keywords = job.meta_keywords
    ? job.meta_keywords
    : tranlate("SEO_keywords_job", {
        job: titleKeyWord(),
        company: job.company?.display_name,
        expired:
          job.status_display === JOB_DISPLAY_STATUS_CLOSED
            ? " - " + tranlate("SEO_job_expired")
            : "",
        skills: skillName().join(", " + tranlate("SEO_hiring_text") + " "),
        yoe: experienceKeyword(),
        location: locationKeyword().join(", "),
        level: levelKeyword().join(", "),
      });
  const description = job.meta_description
    ? job.meta_description
    : tranlate("SEO_description_job", {
        job: job.title,
        company: job.company?.display_name,
        expired:
          job.status_display === JOB_DISPLAY_STATUS_CLOSED
            ? " - " + tranlate("SEO_job_expired")
            : "",
        skills: skillName().join(", "),
        location: job.addresses?.address_region_array.join(", ") ?? "",
      });

  // Set robots metadata
  let robots_meta = {
    index: true,
    follow: true,
  };

  if (
    job.status_display &&
    job.status_display === JOB_DISPLAY_STATUS_IN_REVIEW
  ) {
    robots_meta = {
      ...robots_meta,
      ...{
        index: false,
        follow: false,
      },
    };
  }

  if (job.status_display && job.status_display === JOB_DISPLAY_STATUS_CLOSED) {
    robots_meta = {
      ...robots_meta,
      ...{
        index: false,
        follow: true,
      },
    };
  }
  //End set robots metadata

  const imageGalleries = job.company?.image_galleries;
  let image = job.image_thumbnail;
  if (!image && imageGalleries.length > 0) {
    image = imageGalleries[0].url;
  }

  //Set Slug
  const slug_vi =
    process.env.NEXT_PUBLIC_BASE_URL + "/viec-lam/" + job?.slug + "-" + job?.id;
  const slug_en =
    process.env.NEXT_PUBLIC_BASE_URL +
    "/detail-jobs/" +
    job?.slug +
    "-" +
    job?.id;
  //End set Slug

  const alternates = {
    canonical: job.detail_url,
    languages: {
      vi: slug_vi,
      en: slug_en,
      "x-default": slug_vi,
    },
  };
  // if expired, remove language alternate
  if (job.status_display === JOB_DISPLAY_STATUS_CLOSED) {
    if (alternates && alternates.languages) {
      // @ts-ignore
      delete alternates.languages;
    }
  }

  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
    alternates: alternates,
    title: title,
    description: description,
    keywords: keywords,
    openGraph: {
      url: job.detail_url,
      title: title,
      description: description,
      type: "article",
      locale: locale === "vi" ? "vi_VN" : "en_US",
      siteName: "TopDev",
      images: image,
    },
    twitter: {
      site: "TopDev",
      title: title,
      description: description,
      creator: "@Topdevvn",
    },
    robots: robots_meta,
    verification: {
      google: "XxqADEgloBElQyOQ9ePm7EG3XO01_vcTMre2KQgD9K8",
    },
    other: {
      "twitter:domain": "topdev.vn",
      "twitter:image:src": image ?? "",
    },
  };
}

export default async function DetailJobPage({
  params: { slug, locale },
  searchParams,
}: {
  params: { slug: string; locale: Lang };
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  const t = await getTranslations();
  const id = getIdFromSlug(slug);

  const { view } = searchParams as any;

  if (!id) {
    notFound();
  }

  const device = isMobile() ? "mobile" : "desktop";

  const job = await getJobDetail(id, getLocaleForParams(), device);

  if (!job) {
    notFound();
  }

  const suggestJobs = await getSuggestJobs(
    String(id),
    job.skills_arr.join(","),
    1,
    4,
    getLocaleForParams(),
  )
    .then((response) => response?.data)
    .catch(() => []);

  const jobs = [job];
  if (suggestJobs && suggestJobs.length > 0) {
    jobs.push(...suggestJobs);
  }

  const ampUrl = (slug: string) => {
    return slug
      .replace("/viec-lam/", "/amp/viec-lam/")
      .replace("/detail-jobs/", "/amp/detail-jobs/");
  };
  if (process.env.RENEW_TOPDEV_2025 === "true") {
    return <DetailPage job={job} />;
  }
  return (
    <>
      {job?.content_html && <TailwindCDNInjector enabled={true} />}

      <section
        id="detailJobPage"
        className="mt-[1px] bg-gray-100 pb-10 last:pb-0 md:mt-0 md:py-6 md:pb-6"
        data-slug={job?.slug}
        data-id={job?.id}
      >
        <div
          id={`card-job-${job?.id}`}
          className={`${
            job?.content_html ? "mx-auto max-w-[80rem]" : "container"
          } flex flex-wrap items-start gap-6 px-0 md:px-4`}
        >
          <section
            className={classNames(
              isDesktop() ? "flex w-full flex-col gap-4 md:w-[100%]" : "w-full",
            )}
          >
            {/* Show header detail job */}
            {isDesktop() ? (
              <CardDetailJobHeaderNew
                statusDisplay={job?.status_display}
                jobAddress={job?.addresses?.full_addresses}
                jobTitle={job?.title}
                jobId={job?.id}
                company={job?.company}
                jobSalary={job?.salary?.value}
                jobPosted={job?.refreshed?.since}
                job={job}
              />
            ) : (
              <CardDetailJobNewHeaderMobile
                jobs={jobs}
                jobData={job}
                locale={locale as Lang}
                viewScreen={view}
              />
            )}
            {/* End header detail job */}

            {isDesktop() && (
              <section className="flex w-full flex-wrap gap-4 md:w-[100%]">
                <section className="w-full bg-white">
                  <div className="flex flex-col self-stretch p-[25px] pb-2">
                    {/* Show info detail job */}
                    <CardInfo locale={locale as Lang} job={job} />
                    {/* End info detail job */}
                  </div>
                </section>
              </section>
            )}

            {/* Show content detail job */}
            <CardNewContent
              isMobile={isMobile()}
              jobs={jobs}
              locale={locale as Lang}
              isOpen={job?.status_display === JOB_DISPLAY_STATUS_OPEN}
            />
            {/* End content detail job */}

            {/* <CardMoreJob job={job} /> */}
          </section>
        </div>

        {isDesktop() ? (
          <SectionRecommendJob job={job} />
        ) : (
          <CardMoreJob job={job} />
        )}
        <NotificationSuccess />
        <ApplyAllJobs />
        <ApplyJob job={job} />
        <InitTracking jobData={job} />
        <PopupRemindSignIn job={job} device={device} />
      </section>
    </>
  );
}
