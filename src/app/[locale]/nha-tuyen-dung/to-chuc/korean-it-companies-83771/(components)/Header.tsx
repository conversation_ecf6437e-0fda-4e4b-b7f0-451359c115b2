"use client";

import { useEffect, useState } from "react";

import classnames from "classnames";
import Image from "next/image";
import Link from "next/link";
import { FaUser } from "react-icons/fa6";
import { openLoginPopup } from "@/utils";
import { useAppSelector } from "@/store";
const Header = () => {
  const [isSticky, setIsSticky] = useState(false);
  const user = useAppSelector((state) => state?.user?.user);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setIsSticky(scrollPosition > 1);
    };
    handleScroll();
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  return (
    <header
      className={classnames(
        "z-10 w-full transition-all duration-300 ease-in-out lg:fixed",
        isSticky ? "top-0 w-[100vw]" : "top-[62px]",
      )}
    >
      <div className="relative z-10 flex items-center justify-center py-7  lg:hidden ">
        <Image
          src="https://c.topdevvn.com/uploads/2024/09/14/logo-pc.webp"
          alt="Logo"
          width={206}
          height={28}
          className="object-contain"
        />
      </div>
      <div
        className={classnames(
          "mx-auto flex w-full items-center justify-center bg-[#ECECEC] py-[10px] transition-all duration-300 lg:justify-between lg:bg-white lg:px-[122px] lg:py-[22px]",
          isSticky
            ? " lg:max-w-full lg:shadow-md"
            : "lg:container lg:rounded-[16px]",
        )}
      >
        <div className="relative z-[2] hidden h-[50px] w-[350px] lg:block">
          <Image
            src="https://c.topdevvn.com/uploads/2024/09/14/logo-pc.webp"
            alt="Logo"
            className="object-contain"
            layout="fill"
          />
        </div>
        <div className="flex items-center gap-6 rounded-xl bg-white px-7 py-4 text-[#23417A] lg:gap-[56px] lg:rounded-none lg:bg-transparent lg:p-0">
          <Link href="#jobs" className="text-sm lg:text-base">
            Jobs
          </Link>
          <Link href="#companies" className="text-sm lg:text-base">
            Companies
          </Link>
          <button
            className="flex items-center gap-2 rounded-[56px] bg-[#23417A] p-2 text-sm text-white lg:px-5 lg:py-1 lg:text-base"
            onClick={() =>
              openLoginPopup([{ name: "isEmployer", value: true }])
            }
          >
            <FaUser className="h-3 w-3" />{" "}
            <span className="hidden lg:block">{user?.display_name ?? 'Log in'}
            </span>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
