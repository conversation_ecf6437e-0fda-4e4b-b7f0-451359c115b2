"use client";
import React, { useEffect, useState } from "react";
import NavUsers from "@/components/User/Common/NavUsers";
import EmailManagementRequest from "@/components/User/EmailManagement/EmailManagementRequest";
import { getAllEmail } from "@/services/emaiManagementAPI";
import { useAppSelector } from "@/store";
import { IEmail } from "@/types/email";
import { getCurrentLocaleForParams } from "@/utils/locale";

const EmailManagement = () => {
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const [emailCategories, setEmailCategories] = useState<IEmail>();
  const locale = getCurrentLocaleForParams();

  // Get email categories
  useEffect(() => {
    const fetchDataEmail = async () => {
      try {
        const emailData = await getAllEmail(getCurrentLocaleForParams());
        if (emailData) {
          setEmailCategories(emailData);
        }
      } catch (error) {}
    };
    if (isLoggedIn) {
      fetchDataEmail();
    }
  }, [isLoggedIn]);

  return (
    <div className="scroll-smooth bg-neutral-light pb-48">
      <NavUsers />
      <div>
        <EmailManagementRequest
          locale={locale}
          emailCategories={emailCategories}
        />
      </div>
    </div>
  );
};

export default EmailManagement;
