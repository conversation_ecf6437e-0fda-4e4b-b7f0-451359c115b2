"use client";
import Twitter from "@/components/Icons/Twitter";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaGithub, FaLinkedinIn } from "react-icons/fa";
import { FcGoogle } from "react-icons/fc";
import { RiFacebookCircleFill } from "react-icons/ri";

const Login = () => {
  const t = useTranslations();
  const searchParams = useSearchParams();

  // Get redirect_uri from the URL params
  const redirectUri = searchParams.get("redirect_uri") || "/";

  const buildProviderUrl = (provider: string) =>
    `${
      process.env.NEXT_PUBLIC_OAUTH2_URL_ACCOUNT
    }/login/${provider}?redirect_uri=${encodeURIComponent(redirectUri)}`;
  return (
    <div className="flex px-6 md:p-0">
      <div className="flex w-full items-center justify-center px-6">
        <div className="flex flex-col items-center rounded-[16px] px-20 py-11">
          <Image
            src="https://c.topdevvn.com/uploads/2025/07/23/login.png"
            width={171}
            height={140}
            alt="login"
          />
          <span className="mt-5 block text-center text-lg font-bold text-[#DD3F24] md:text-[32px]/[30px]">
            {t("auth_login_title")}
          </span>
          <span className="mt-4 hidden whitespace-nowrap text-sm text-[#5D5D5D] md:block md:text-lg">
            {t("auth_login_subtitle")}
          </span>
          <Link href={buildProviderUrl("google")} className="mt-5 px-6 md:px-0">
            <span className="flex h-14 w-[90vw] items-center justify-center gap-[10px] rounded-full border border-[#3D3D3D] font-medium md:w-[454px]">
              <FcGoogle className="h-6 w-6" />
              {t("auth_continue_with_google")}
            </span>
          </Link>
          <Link
            href={buildProviderUrl("facebook")}
            className="mt-3 px-6 md:px-0"
          >
            <span className="flex h-14 w-[90vw] items-center justify-center gap-[10px] rounded-full bg-[#176CF0] font-medium text-white md:w-[454px]">
              <RiFacebookCircleFill className="h-6 w-6" />
              {t("auth_continue_with_facebook")}
            </span>
          </Link>
          <div className={`mt-4 flex w-[450px] items-center`}>
            <div className="flex-grow border-t border-gray-300" />
            <span className="px-3 text-sm text-gray-500">
              {t("auth_login_with_other_methods")}
            </span>
            <div className="flex-grow border-t border-gray-300" />
          </div>
          <div className="mt-4 flex items-center gap-3">
            <Link href={buildProviderUrl("linkedin")}>
              <span className="flex h-[56px] w-[56px]  items-center justify-center rounded-full bg-[#176CF0] font-medium text-white">
                <FaLinkedinIn className="h-6 w-6" />
              </span>
            </Link>
            <Link href={buildProviderUrl("twitter")}>
              <span className="flex h-[56px] w-[56px] items-center justify-center rounded-full bg-black font-medium text-white">
                <Twitter />
              </span>
            </Link>
            {/* <Link href={buildProviderUrl("apple")}>
              <span className="flex h-[56px] w-[56px] items-center justify-center rounded-full border-[0.5px] border-[#5D5D5D] font-medium text-[#3D3D3D]">
                <FaApple className="h-6 w-6" />
              </span>
            </Link> */}
            <Link href={buildProviderUrl("github")}>
              <span className="flex h-[56px] w-[56px] items-center justify-center rounded-full bg-[#060742] font-medium text-white">
                <FaGithub className="h-6 w-6" />
              </span>
            </Link>
          </div>
          <span className="mt-3 block w-[90vw] text-center text-sm text-[#3D3D3D] md:w-full">
            {t("auth_terms_agree_text")}{" "}
            <Link
              href={`${process.env.NEXT_PUBLIC_BASE_URL}/term-of-services`}
              className="text-[#DD3F24]"
            >
              {t("detail_job_page_apply_tnc_apply_term_of_services")}
            </Link>{" "}
            {t("auth_and")}{" "}
            <Link
              href={`${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`}
              className="text-[#DD3F24]"
            >
              {t("privacy_policy_title")}
            </Link>{" "}
            {t("auth_topdev")}.
          </span>
          <span className="mt-7 block text-sm text-[#5D5D5D] md:text-lg">
            {t("auth_are_you_employer")}{" "}
            <span className="text-[#DD3F24]">
              {t("auth_please_login_using_this")}
            </span>{" "}
            <Link
              onClick={() => window?.close()}
              href={`${process.env.NEXT_PUBLIC_BASE_URL}/employer/login`}
              className="underline"
              target="_blank"
            >
              {t("auth_link_word")}
            </Link>{" "}
            !
          </span>
        </div>
      </div>
    </div>
  );
};

export default Login;
