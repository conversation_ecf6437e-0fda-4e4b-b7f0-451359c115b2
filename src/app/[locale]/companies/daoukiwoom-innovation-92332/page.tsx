const CompanyNewsSection = dynamic(
  () => import("@/components/DetailCompany/CompanyNewsSection"),
);
const CompanyProductSection = dynamic(
  () => import("@/components/DetailCompany/CompanyProductSection"),
);
const CompanyProfileSection = dynamic(
  () => import("@/components/DetailCompany/CompanyProfileSection"),
);
const CompanyTabSection = dynamic(
  () => import("@/components/DetailCompany/CompanyTabSection"),
);
const ContactInformationSection = dynamic(
  () => import("@/components/DetailCompany/ContactInformationSection"),
);
const GeneralInformationSection = dynamic(
  () => import("@/components/DetailCompany/GeneralInformationSection"),
);
const HeaderSection = dynamic(
  () => import("@/components/DetailCompany/HeaderSection"),
);
const JobOpeningSection = dynamic(
  () => import("@/components/DetailCompany/JobOpeningSection"),
);
const HeaderSectionMobile = dynamic(
  () => import("@/components/DetailCompany/mobile/HeaderSection.mobile"),
);
const HeaderTitleMobile = dynamic(
  () => import("@/components/DetailCompany/mobile/HeaderTitleMobile"),
);
import DetailCompanyWrapper from "@/components/DetailCompany/DetailCompanyContext";
import { getDetailCompany } from "@/services/companyAPI";
import { CompanyType } from "@/types/company";
import { Lang } from "@/types/page";
import isDevice from "@/utils/device";
import { getLocaleForParams } from "@/utils/localeServer";
import dynamic from "next/dynamic";
import { notFound, redirect } from "next/navigation";
import Head from "next/head";
const StickyHeaderCompany = dynamic(
  () => import("@/components/DetailCompany/StickyHeaderCompany"),
);

const DaoukiwoomPage = async ({
  params: { locale },
}: {
  params: {  locale: string };
}) => {
  const slug = 'daoukiwoom-innovation-92332';
  const device = isDevice();
  const regexTest = slug.match(/\d+$/);

  const ampUrl = (slug: string) => {
    return slug
      .replace("/companies/", "/amp/companies/")
      .replace("/nha-tuyen-dung/", "/amp/nha-tuyen-dung/");
  };

  if (regexTest === null || regexTest.length === 0) {
    notFound();
  }

  const companyId = +regexTest[0];
  let companyData: CompanyType;

  try {
    companyData = await getDetailCompany(companyId, getLocaleForParams());
  } catch (error) {
    redirect("/");
  }

  

  if (device === "mobile") {
    return (
      <>
        <Head>
          <link rel="amphtml" href={`${ampUrl(companyData.detail_url)}`} />
        </Head>
        <div className="bg-gray-100 text-sm">
          <DetailCompanyWrapper>
            
            <HeaderTitleMobile companyName={companyData.display_name} />
            <HeaderSectionMobile company={companyData} />
            <div className="mt-5">
              <GeneralInformationSection
                company={companyData}
                locale={locale as Lang}
              />
            </div>
            <div className="mt-6">
              <CompanyTabSection company={companyData} device="mobile" />
              <CompanyProfileSection company={companyData} isDkiProfile/>
              {companyData.products && companyData.products.length > 0 && (
                <CompanyProductSection company={companyData} />
              )}
              {companyData.news && companyData.news.length > 0 && (
                <CompanyNewsSection company={companyData} />
              )}
              {companyData.num_job_openings > 0 && (
                <JobOpeningSection companyId={companyId} device="mobile" />
              )}
            </div>
            <div className="mt-4">
              <ContactInformationSection company={companyData} />
            </div>
          </DetailCompanyWrapper>
        </div>
      </>
    );
  }

  return (
    <>  
      <Head>
        <link rel="amphtml" href={`${ampUrl(companyData.detail_url)}`} />
      </Head>
      <main className="bg-gray-100" id="company-detail-page">
        <div className="container">
          <div className="grid grid-cols-3 gap-6">
            <div className="relative col-span-2">
              <DetailCompanyWrapper>
                <HeaderSection company={companyData} />
                <StickyHeaderCompany company={companyData} />
                <div className="mb-6">
                  <CompanyProfileSection company={companyData} isDkiProfile/>
                  {companyData.products && companyData.products.length > 0 && (
                    <CompanyProductSection company={companyData} />
                  )}
                  {companyData.news && companyData.news.length > 0 && (
                    <CompanyNewsSection company={companyData} />
                  )}
                  {companyData.num_job_openings > 0 && (
                    <JobOpeningSection companyId={companyId} />
                  )}
                </div>
              </DetailCompanyWrapper>
            </div>
            <div className="col-span-1">
              <div className="py-6">
                <GeneralInformationSection
                  company={companyData}
                  locale={locale as Lang}
                />
                <div className="mt-4">
                  <ContactInformationSection company={companyData} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
};

export default DaoukiwoomPage;
