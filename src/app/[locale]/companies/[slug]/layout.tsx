import { getDetailCompany } from "@/services/companyAPI";
import { CompanyType } from "@/types/company";
import isDevice from "@/utils/device";
import { getLocaleForParams } from "@/utils/localeServer";
import { Metadata, ResolvingMetadata } from "next";
import { getTranslations } from "next-intl/server";
import { notFound } from "next/navigation";
import React, { ReactNode } from "react";

interface MetaProps {
  params: { slug: string; locale: string };
}

export const generateMetadata = async (
  { params }: MetaProps,
  parent: ResolvingMetadata,
): Promise<Metadata> => {
  const { slug, locale } = params;
  const translate = await getTranslations();
  const regexTest = slug.match(/\d+$/);
  if (regexTest === null || regexTest.length === 0) {
    notFound();
  }
  let title = "";
  let description = "";
  let keywords = "";
  let metaData: Metadata = {};

  const companyId = +regexTest[0];
  const companyData: CompanyType = await getDetailCompany(
    companyId,
    getLocaleForParams(),
  );

  title = companyData.meta_title
    ? companyData.meta_title
    : translate("company_meta_title", {
        company: companyData.display_name,
        updated_now: new Date().toLocaleDateString('en-GB'),
      });
  keywords = companyData.meta_keywords
    ? companyData.meta_keywords
    : translate("company_meta_keywords", {
        company: companyData.display_name,
      });
  description = companyData.meta_description
    ? companyData.meta_description
    : translate("company_meta_description", {
        company: companyData.display_name,
      });
  let amp_url = companyData.detail_url ? companyData.detail_url : "";
  if (typeof amp_url !== "undefined" || amp_url !== null) {
    amp_url = amp_url.replace("/companies/", "/amp/companies/");
    amp_url = amp_url.replace("/nha-tuyen-dung/", "/amp/nha-tuyen-dung/");
  }
  const image =
    companyData.image_galleries && companyData.image_galleries.length > 0
      ? companyData.image_galleries[0].url
      : companyData.image_logo;

  const slug_vi = process.env.NEXT_PUBLIC_BASE_URL + "/nha-tuyen-dung/" + companyData.slug + "-" + companyData.id;
  const slug_en = process.env.NEXT_PUBLIC_BASE_URL + "/companies/" + companyData.slug + "-" + companyData.id;

  metaData = {
    title,
    description,
    keywords,
    openGraph: {
      description,
      url: companyData.detail_url,
      title,
      type: "article",
      locale: getLocaleForParams(),
      siteName: "TopDev",
      images: image,
    },
    twitter: {
      site: "TopDev",
      title,
      description,
      creator: "@Topdevvn",
      images: image,
    },
    robots: "index,follow",
    alternates: {
      canonical: companyData.detail_url,
      languages: {
        vi: slug_vi,
        en: slug_en,
        "x-default": slug_vi,
      }
    },
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string)
  };

  return {
    ...metaData,
  };
};

const CompanyDetailLayout = ({ children }: { children: ReactNode }) => {
  return <>{children}</>;
};

export default CompanyDetailLayout;
