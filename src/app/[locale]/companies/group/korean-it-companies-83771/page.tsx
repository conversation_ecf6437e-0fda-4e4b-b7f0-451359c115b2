"use client";

import { useEffect, useLayoutEffect, useState } from "react";

// Import Swiper modules
import { getKiccCompaniesSelect } from "@/services/jobAPI";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import Header from "./(components)/Header";
import SwiperCustomKorean from "./(components)/SwiperCustomKorean";
const SwiperCustom = dynamic(
  () =>
    import(
      "@/app/[locale]/nha-tuyen-dung/to-chuc/korean-it-companies-83771/(components)/SwiperCustom"
    ),
);

export interface CompanyShort {
  id: number;
  display_name: string;
  image_logo: string;
  slug: string;
  detail_url: string;
  is_followed: boolean;
}
const LogoSlider = ({ companies }: { companies: CompanyShort[] }) => {
  const [visibleLogos, setVisibleLogos] = useState<CompanyShort[]>([]);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const itemsPerPage = 6;

  useLayoutEffect(() => {
    if (!companies.length) return;

    const rotateLogos = async () => {
      // Start fade out
      setIsTransitioning(true);
      
      // Wait for fade out to complete (800ms)
      await new Promise(resolve => setTimeout(resolve, 800));
      
      setVisibleLogos(prevLogos => {
        const firstIndex = companies.findIndex(c => c.id === prevLogos[0]?.id);
        const nextIndex = (firstIndex + itemsPerPage) % companies.length;
        const nextLogos = [...companies.slice(nextIndex)];
        
        if (nextLogos.length < itemsPerPage) {
          nextLogos.push(...companies.slice(0, itemsPerPage - nextLogos.length));
        }
        
        return nextLogos.slice(0, itemsPerPage);
      });

      // Wait before starting fade in (200ms)
      await new Promise(resolve => setTimeout(resolve, 200));
      setIsTransitioning(false);
    };

    // Set initial logos
    setVisibleLogos(companies.slice(0, itemsPerPage));

    // Rotate every 5 seconds (5000ms)
    const interval = setInterval(rotateLogos, 5000);
    return () => clearInterval(interval);
  }, [companies]);

  return (
    <div className="flex no-scrollbar overflow-x-auto items-center justify-center gap-5 lg:gap-[100px] py-6">
      {visibleLogos.map((company) => (
        <div
          key={company.id}
          className={`
            relative block w-[72px] h-[32px] lg:h-[80px] lg:w-[10%] shrink-0
            transition-all duration-700 ease-in-out
            ${isTransitioning ? 'opacity-0 scale-95 translate-y-2' : 'opacity-100 scale-100 translate-y-0'}
          `}
        >
          <Image
            src={company.image_logo}
            alt={company.display_name}
            className="object-contain"
            layout="fill"
          />
        </div>
      ))}
    </div>
  );
};

export default function LandingPage() {
  const [companies, setCompanies] = useState<CompanyShort[]>([]);

  useEffect(() => {
    const fetchCompanies = async () => {
      const data = await getKiccCompaniesSelect();
      setCompanies(data);
    };
    fetchCompanies();
  }, []);

  

  

  return (
    <>
      <Header/>
      <Image
        src="https://c.topdevvn.com/uploads/2025/04/24/bg.png"
        alt="Banner"
        className="relative object-contain object-top"
        width={0}
        height={0}
        layout="responsive"
      />
      <main>
        <LogoSlider companies={companies} />
        <div className="bg-[#ECECEC] pb-[90px]">
          <div className="container">
            <div className="pt-8 lg:pt-[74px]">
              <h1 className="text-base lg:text-[36px] font-bold text-[#23417A]">
                Job Openings - Korean IT Companies
                <div className="relative -bottom-1 lg:-bottom-[20px] block h-[2px] lg:h-[6px] w-64 bg-[#A93A28] after:absolute after:inset-0 after:h-full after:w-[128px] after:bg-[#23417A] after:content-[''] lg:w-[750px] after:lg:w-[657px]"></div>
              </h1>
              <h2 id="jobs" className="mx-auto mt-6 lg:mt-16 flex w-fit items-center gap-2 rounded-[40px] border border-[#A93A28] bg-[#FFEFEC] px-4 py-2 lg:px-11 lg:py-4 font-semibold text-sm lg:text-[24px]/[20px] text-[#A93A28]">
                <svg
                  width="26"
                  height="28"
                  viewBox="0 0 26 28"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="lg:w-[26px] lg:h-[28px] w-[14px] h-[14px]"
                >
                  <path
                    d="M12.4437 1.02052L10.7095 0L10.3791 1.95858C9.85337 5.05122 7.7021 7.92489 5.32923 9.94429C0.683968 13.8993 -0.333173 18.1976 1.10514 21.7458C2.48151 25.1412 5.97475 27.4661 9.52029 27.854L10.3406 27.9432C8.31184 26.7254 7.00978 23.8801 7.46399 21.7539C7.91269 19.6642 9.44322 17.707 12.3832 15.9011L13.8655 14.9928L14.4188 16.6216C14.745 17.584 15.3093 18.3571 15.8833 19.1425C16.1585 19.5209 16.438 19.9035 16.694 20.3103C17.579 21.7228 17.8129 23.2935 17.2418 24.8519C16.7215 26.2685 15.864 27.3823 14.6817 28L16.0168 27.854C19.3449 27.4904 21.7879 26.3726 23.3749 24.5032C24.9481 22.6501 25.5 20.2643 25.5 17.7245C25.5 15.3591 24.5104 12.9207 23.3432 10.8918C21.9751 8.51557 20.1899 6.54212 18.241 4.6295C17.9038 5.29182 17.9299 5.55945 17.2362 6.62322C16.3354 4.27395 14.6446 2.29728 12.4437 1.02052Z"
                    fill="#A93A28"
                  />
                </svg>
                IT Jobs from Korean IT Companies
              </h2>
              <SwiperCustom companies={companies}/>
              <h2 id="companies" className="mx-auto mt-6 lg:mt-16 flex w-fit items-center gap-2 rounded-[40px] border border-[#23417A] bg-[#E0EBFF] px-4 py-2 lg:px-11 lg:py-4 font-semibold text-sm lg:text-[24px]/[20px] text-[#23417A]">
                <svg
                  width="30"
                  height="25"
                  viewBox="0 0 30 25"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="lg:w-[26px] lg:h-[28px] w-[14px] h-[14px]"
                >
                  <path
                    d="M26.4302 22.2778H29.0552V25H0.180176V22.2778H2.80518V1.86111C2.80518 1.50012 2.94346 1.15392 3.1896 0.89866C3.43574 0.643402 3.76958 0.5 4.11768 0.5H17.2427C17.5908 0.5 17.9246 0.643402 18.1708 0.89866C18.4169 1.15392 18.5552 1.50012 18.5552 1.86111V22.2778H21.1802V8.66667H25.1177C25.4658 8.66667 25.7996 8.81007 26.0458 9.06533C26.2919 9.32059 26.4302 9.66679 26.4302 10.0278V22.2778ZM8.05518 11.3889V14.1111H13.3052V11.3889H8.05518ZM8.05518 5.94444V8.66667H13.3052V5.94444H8.05518Z"
                    fill="#23417A"
                  />
                </svg>
                List of Korean IT Companies
              </h2>
              <SwiperCustomKorean />
              <h1 className="mt-[75px] text-base lg:text-[36px]/[36px] font-bold text-[#23417A]">
                About Korea IT
                <br />
                Cooperation Center in HCMC
                <div className="relative -bottom-1 lg:-bottom-[10px] block h-[2px] lg:h-[6px] w-64 bg-[#A93A28] after:absolute after:inset-0 after:h-full after:w-[128px] after:bg-[#23417A] after:content-[''] lg:w-[600px] after:lg:w-[522px]"></div>
              </h1>
              <div className="mt-6 lg:mt-16  rounded-[16px] border-[2px] border-[#23417A] bg-white p-4 lg:p-8">
                <div className="flex flex-col lg:flex-row gap-8">
                  <div className="lg:w-[55%]">
                    <span className="text-2xl block text-center lg:text-left font-bold text-[#23417A]">
                      About KICC HCMC
                    </span>
                    <span className="mt-4 lg:mt-6 text-xs lg:text-base block text-[#3D3D3D]">
                      Korea IT Cooperation Center in HCMC is the Vietnam office
                      of NIPA (National IT Industry Promotion Agency) under the
                      Ministry of Science and ICT of Korea, dedicated to
                      fostering the Korea-Vietnam tech startup ecosystem.
                      <br /> We promote startup exchanges, joint projects,
                      investment, and networking, connecting technology and
                      business opportunities to bring value.
                      <br /> We also provide market research, legal consulting,
                      and infrastructure support to create an optimal
                      environment for sustainable startup growth.
                      <br /> By offering tailored support and growth
                      opportunities, we aim to build a dynamic and collaborative
                      global innovation ecosystem.
                    </span>
                    <div className="flex no-scrollbar overflow-x-auto lg:overflow-hidden gap-[18px]">
                      <div className="relative mt-6 h-[160px] w-[225px] shrink-0 lg:shrink">
                        <Image
                          alt="Logo"
                          className="object-contain"
                          layout="fill"
                          src="https://c.topdevvn.com/uploads/2025/04/24/nipa.png"
                        />
                      </div>
                      <div className="relative mt-6 h-[160px] w-[225px] shrink-0 lg:shrink">
                        <Image
                          alt="Logo"
                          className="object-contain"
                          layout="fill"
                          src="https://c.topdevvn.com/uploads/2025/04/24/office.png"
                        />
                      </div>
                      <div className="relative mt-6 h-[160px] w-[225px] shrink-0 lg:shrink">
                        <Image
                          alt="Logo"
                          className="object-contain"
                          layout="fill"
                          src="https://c.topdevvn.com/uploads/2025/04/24/bantry.png"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="lg:w-[45%]">
                    <span className="text-base lg:text-2xl font-bold text-[#23417A]">
                      Contact Us
                    </span>
                    <div className="mt-2 lg:mt-6 flex gap-1">
                      <span className="block w-[77px] lg:w-[98px] text-xs lg:text-xl font-semibold">
                        Hotline:
                      </span>
                      <div className="flex flex-col gap-2">
                        <a
                          href="tel:+1234567890"
                          className="text-xs lg:text-xl font-semibold text-[#23417A]"
                        >
                          (+84) 28 730 68681 (Vietnamese)
                        </a>
                        <a
                          href="tel:+1234567890"
                          className="text-xs lg:text-xl font-semibold text-[#23417A]"
                        >
                          (+84) 28 730 68682 (Korean)
                        </a>
                      </div>
                    </div>
                    <div className="mt-4 flex gap-1">
                      <span className="block w-[77px] lg:w-[98px] text-xs lg:text-xl font-semibold">
                        E-mail:
                      </span>
                      <div className="flex flex-col gap-2">
                        <a
                          href="mailto:<EMAIL>"
                          className="text-xs lg:text-xl font-semibold text-[#23417A]"
                        >
                          <EMAIL>
                        </a>
                      </div>
                    </div>
                    <div className="mt-4 flex gap-1">
                      <span className="block w-[77px] lg:w-[98px] text-xs lg:text-xl font-semibold">
                        Address:
                      </span>
                      <span className="block w-[239px] lg:w-[296px] text-xs lg:text-xl font-semibold text-[#23417A]">
                        Room 1504, Room 1505, Level 15, mPlaza Saigon Building,
                        39 Le Duan, Ben Nghe Ward, District 1, Ho Chi Minh City
                      </span>
                    </div>
                    <Link
                      href={"https://www.kicchcmc.vn/"}
                      className="mt-6 text-sm lg:text-base mx-auto max-w-[231px] lg:max-w-full lg:mt-[72px] block w-full rounded-xl bg-[#1C4587] py-2 lg:py-[22px] text-center font-semibold text-white"
                    >
                      Visit our website
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </>
  );
}
