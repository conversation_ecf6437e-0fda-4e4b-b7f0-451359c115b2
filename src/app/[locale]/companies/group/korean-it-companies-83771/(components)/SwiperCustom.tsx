"use client";

import { FC, useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper modules
import { Button } from "@/components/Button";
import { getKiccJobs } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { classNames } from "@/utils";
import dynamic from "next/dynamic";
import { useCallback, useRef } from "react";
import { isDesktop } from "react-device-detect";
import { FaCaretDown } from "react-icons/fa";
import { HiCheck } from "react-icons/hi2";
import { Grid, Navigation, Pagination } from "swiper/modules";

import { useDebounce } from "usehooks-ts";
import { CompanyShort } from "../page";
const CardInformativeGrid = dynamic(
  () => import("@/components/Card/Job/CardInformativeGrid"),
);
type TaxonomyType = { value: string; label: string };

export interface ParamsSearch {
  region_ids: string;
  "member_group_company_ids[]": number | null;
  keyword: string;
  job_types_ids?: string;
}
const LOCATIONS = [
  { value: "all", label: "All Location" },
  { value: "79", label: "Ho Chi Minh" },
  { value: "01", label: "Ha Noi" },
  { value: "49", label: "Da Nang" },
];
const JOB_TYPES = [
  { value: "8792,8642,1623,1625", label: "All job types" },
  { value: "8792", label: "In Office" },
  { value: "8642", label: "Hybrid" },
  { value: "1623", label: "Remote" },
  { value: "1625", label: "Oversea" },
];

type LabelValue = {
  value: string;
  label: string;
};
interface SelectSingleLocationType {
  value: string | null;
  placeholder?: string;
  onSelect: (value: string) => void;
  options?: LabelValue[];
  device?: "mobile" | "desktop";
}
interface SelectSingleCompanyType {
  value: number | null;
  placeholder?: string;
  onSelect: (value: number) => void;
  options: CompanyShort[];
  device?: "mobile" | "desktop";
}

const SelectSingleCompany: FC<SelectSingleCompanyType> = (props) => {
  const { value, onSelect, options, device = "desktop" } = props;
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener("click", handleClickContext);
    return () => {
      window.removeEventListener("click", handleClickContext);
    };
  }, []);

  const handleClickContext = (event: globalThis.MouseEvent) => {
    if (!dropdownRef.current?.contains(event.target as Element)) {
      setIsOpen(false);
    }
  };

  const handleToggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const handleSelectItem = (selectedItem: CompanyShort) => {
    setIsOpen(false);
    onSelect(selectedItem?.id);
  };
  const optionsAdditional = [
    {
      id: null,
      display_name: "All company",
      image_logo: "string",
      slug: "",
      detail_url: "string",
      is_followed: false,
    },
    ...options,
  ] as CompanyShort[];
  const getValue = () => {
    try {
      if (!value) {
        return "Data is invalid!";
      }
      const valueFiltered = options.find((item) => item?.id === value);
      if (!valueFiltered) {
        return "Data is invalid!";
      }
      return valueFiltered.display_name;
    } catch (error) {
      return "Data is invalid!";
    }
  };
  return (
    <div ref={dropdownRef} className="relative w-full select-none lg:w-[170px]">
      <div
        className={classNames(
          "relative flex cursor-pointer items-center justify-between rounded border border-solid px-4 py-2 transition-all",
          "border-white bg-white",
          device === "desktop" ? "h-12" : "h-9 text-sm",
        )}
        onClick={handleToggleDropdown}
      >
        <span className="line-clamp-1 text-xs text-[#3D3D3D66] lg:flex-1 lg:text-base">
          {value === null ? "All companies" : getValue()}
        </span>
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </div>
      <div
        className={classNames(
          "absolute left-0 top-full z-[2] h-[250px] w-full translate-y-1 overflow-y-auto rounded bg-white py-4 shadow-md transition-all ease-out lg:h-[400px]",
          isOpen ? "visible opacity-100" : "invisible opacity-0",
        )}
      >
        <ul>
          {optionsAdditional.map((optionItem) => {
            return (
              <li
                key={optionItem?.id}
                onClick={() => handleSelectItem(optionItem)}
                className="cursor-pointer"
              >
                <div
                  className={classNames(
                    "flex items-center justify-between gap-2 p-4 transition-all hover:bg-gray-100",
                    value === optionItem?.id ? "text-primary" : "",
                  )}
                >
                  <span className="text-wrap max-w-[135px] text-xs lg:flex-1 lg:text-base">
                    {optionItem?.display_name}
                  </span>
                  {value === optionItem?.id && (
                    <span className="inline-flex h-5 w-5 items-center justify-center">
                      <HiCheck />
                    </span>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};
const SelectSingleLocation: FC<SelectSingleLocationType> = (props) => {
  const {
    value,
    onSelect,
    options = LOCATIONS,
    placeholder,
    device = "desktop",
  } = props;
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener("click", handleClickContext);
    return () => {
      window.removeEventListener("click", handleClickContext);
    };
  }, []);

  const handleClickContext = (event: globalThis.MouseEvent) => {
    if (!dropdownRef.current?.contains(event.target as Element)) {
      setIsOpen(false);
    }
  };

  const handleToggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const getValue = () => {
    try {
      if (!value) {
        return "Data is invalid!";
      }
      const valueFiltered = options.find((item) => item?.value === value);
      if (!valueFiltered) {
        return "Data is invalid!";
      }
      return valueFiltered.label;
    } catch (error) {
      return "Data is invalid!";
    }
  };

  const handleSelectItem = (selectedItem: string) => {
    setIsOpen(false);
    onSelect(selectedItem);
  };

  return (
    <div
      ref={dropdownRef}
      className="relative w-full select-none lg:w-[170px] lg:whitespace-nowrap"
    >
      <div
        className={classNames(
          "relative flex cursor-pointer items-center justify-between rounded border border-solid px-4 py-2 transition-all",
          "border-white bg-white",
          device === "desktop" ? "h-12" : "h-9 text-sm",
        )}
        onClick={handleToggleDropdown}
      >
        <span className="text-xs text-[#3D3D3D66] lg:flex-1 lg:text-base">
          {value !== null ? getValue() : placeholder}
        </span>
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </div>
      <div
        className={classNames(
          "absolute left-0 top-full z-[2] w-full translate-y-1 rounded bg-white py-4 shadow-md transition-all ease-out",
          isOpen ? "visible opacity-100" : "invisible opacity-0",
        )}
      >
        <ul>
          {options.map((optionItem) => {
            return (
              <li
                key={optionItem?.value}
                onClick={() => handleSelectItem(optionItem?.value)}
                className="cursor-pointer"
              >
                <div
                  className={classNames(
                    "flex items-center justify-between gap-2 p-4 transition-all hover:bg-gray-100",
                    value === optionItem?.value ? "text-primary" : "",
                  )}
                >
                  <span className="line-clamp-1 text-xs lg:flex-1 lg:text-base">
                    {optionItem?.label}
                  </span>
                  {value === optionItem?.value && (
                    <span className="inline-flex h-5 w-5 items-center justify-center">
                      <HiCheck />
                    </span>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};
const SwiperCustom = ({ companies }: { companies: CompanyShort[] }) => {
  const [jobs, setJobs] = useState<JobType[]>([]);
  const [location, setLocation] = useState(LOCATIONS[0]?.value);
  const [company, setCompany] = useState<number | null>(null);
  const [contractType, setContractType] = useState<string>(
    "8792,8642,1623,1625",
  );
  const [paramsSearch, setParamsSearch] = useState<ParamsSearch>({
    region_ids: "",
    "member_group_company_ids[]": null,
    keyword: "",
    job_types_ids: "",
  });

  const [keyword, setKeyword] = useState<string>("");
  const debouncedSuggest = useDebounce<string>(keyword, 300);
  const [activeFilter, setActiveFilter] = useState<null | "1617">(null);

  interface ChangeEvent {
    target: {
      value: string;
    };
  }

  const handleChangeValue = (e: ChangeEvent): void => {
    const { value } = e.target;
    setKeyword(value);
  };
  const fetchJobs = useCallback(async (paramsSearch: ParamsSearch) => {
    const data = await getKiccJobs(paramsSearch);
    setJobs(data);
  }, []);

  useEffect(() => {
      setParamsSearch(() => ({
        region_ids: "all",
        "member_group_company_ids[]": null,
        keyword: debouncedSuggest,
        job_types_ids: "8792,8642,1623,1625",
      }));
  }, [debouncedSuggest]);

  const handleLocation = (location: string) => {
    setLocation(location);
    setParamsSearch((params) => ({ ...params, region_ids: location }));
  };
  const handleJobType = (jobType: string) => {
    setContractType(jobType);
    setParamsSearch((params) => ({ ...params, job_types_ids: jobType }));
  };
  const handleCompany = (company: number) => {
    setCompany(company);
    setParamsSearch((params) => ({
      ...params,
      "member_group_company_ids[]": company,
    }));
  };
  const handleFresher = (level: null | "1617") => {
    setActiveFilter(level);
    setParamsSearch((params) => ({ ...params, job_levels_ids: level }));
  };
  useEffect(() => {
    if(paramsSearch?.region_ids && paramsSearch?.job_types_ids)
     fetchJobs(paramsSearch);
  }, [paramsSearch, fetchJobs]);

  return (
    <section
      id={"popular-companies-container"}
      className="mt-8 rounded-[16px] border-[2px] border-[#A93A28] bg-white p-4 lg:p-8"
    >
      <div className="mb-8 grid grid-cols-2 gap-3 rounded-[16px] bg-[#FFE9E5] p-5 lg:flex lg:gap-4 lg:px-8 lg:py-6">
        <input
          type="text"
          placeholder="Type your keyword..."
          className="w-full rounded border-none px-4 py-3 text-xs text-[#3D3D3D66] focus:outline-none focus:ring-0 lg:w-[271px] lg:text-base"
          onChange={handleChangeValue}
        />
        <SelectSingleLocation
          value={location}
          placeholder={"Location"}
          onSelect={(item) => handleLocation(item)}
        />
        <SelectSingleCompany
          value={company}
          placeholder={"Company"}
          onSelect={(item) => handleCompany(item)}
          options={companies}
        />

        <SelectSingleLocation
          value={contractType}
          placeholder={"Job Type"}
          options={JOB_TYPES}
          onSelect={(item) => handleJobType(item)}
        />
        <Button
          className={`rounded-xl px-6 py-3 transition-colors ${
            activeFilter === null
              ? "bg-[#A93A28] text-white"
              : "border border-[#A93A28] bg-transparent text-[#A93A28]"
          }`}
          onClick={() => {
            handleFresher(null);
            // Add your filter logic here
          }}
        >
          All
        </Button>
        <Button
          className={`rounded-xl px-6 py-3 transition-colors ${
            activeFilter === "1617"
              ? "bg-[#A93A28] text-white"
              : "border border-[#A93A28] bg-transparent text-[#A93A28]"
          }`}
          onClick={() => {
            handleFresher("1617");
          }}
        >
          Fresher
        </Button>
      </div>
      <div className={"flex flex-col gap-4 lg:gap-6"}>
        <div className={"overflow-x-hidden"}>
          {jobs.length > 0 ? (
            <div className="md:min-w-[750px]">
              <Swiper
                grabCursor={true}
                slidesPerView={isDesktop ? 3 : 1}
                slidesPerGroup={isDesktop ? 3 : 1}
                spaceBetween={isDesktop ? 24 : 12}
                grid={{
                  rows: isDesktop ? 3 : 2,
                  fill: "row",
                }}
                className="w-full"
                modules={
                  isDesktop
                    ? [Grid, Pagination, Navigation]
                    : [Grid, Pagination]
                }
                pagination={{ clickable: true, el: ".pagination" }}
                navigation={
                  isDesktop
                    ? {
                        nextEl: ".nextEl",
                        prevEl: ".preEl",
                      }
                    : false
                }
              >
                {jobs.map((job, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <CardInformativeGrid
                        index={index}
                        outline={true}
                        job={job}
                        display={isDesktop ? "horizontal" : "vertical"}
                        highlight={isDesktop ? true : false}
                        home={true}
                        className="h-full"
                        srcPage={"home"}
                        mediumPage={"popularcompanies"}
                      />
                    </SwiperSlide>
                  );
                })}

                <div
                  className={
                    "swiper-footer !mt-6 flex items-center justify-center lg:mt-0"
                  }
                >
                  <div className={"pagination pagination__custom"}></div>
                </div>
              </Swiper>
            </div>
          ) : (
            ""
          )}
        </div>
      </div>
    </section>
  );
};
export default SwiperCustom;
