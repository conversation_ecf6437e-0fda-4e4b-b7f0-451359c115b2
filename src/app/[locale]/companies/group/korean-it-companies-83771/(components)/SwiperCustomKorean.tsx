"use client";

import { useEffect, useState } from "react";
import { IoIosArrowRoundForward } from "react-icons/io";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper modules
import { getKiccCompanies } from "@/services/jobAPI";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { isDesktop } from "react-device-detect";
import { Grid, Navigation, Pagination } from "swiper/modules";

interface CompaniesType {
  id: number;
  display_name: string;
  image_logo: string;
  slug: string;
  detail_url: string;
  description_str: string;
  tagline: string;
  company_size: string;
  num_job_openings: number;
  image_cover: string;
  industries_str: string;
  is_followed: boolean;
  addresses: {
    address_region_ids: string[];
    address_region_list: string;
    address_region_array: string[];
    full_addresses: string[];
    sort_addresses: string;
    collection_addresses: {
      id: number;
      ward: {
        id: string;
        value: string;
      };
      province: {
        id: string;
        value: string;
      };
      district: {
        id: string;
        value: string;
      };
      postal_code: string;
      full_address: string;
      latitude: number | null;
      longitude: number | null;
      street: string;
    }[];
    address_short_region_list: string;
  };
}

const KoreanITCompaniesMobile = ({ job }: { job: CompaniesType }) => {
  return (
    <div className="flex h-[221px] w-[158px] flex-col gap-1">
      <Link href={job?.detail_url} className="relative mb-1 block h-[32px] w-[100px]">
        <Image
          src={job?.image_logo}
          alt="image_logo"
          className="object-contain object-left"
          layout="fill"
        />
      </Link>
      <span className="line-clamp-2 h-8  text-sm/[16px] font-bold text-[#292929] ">
        {job?.display_name}
      </span>
      <span className="line-clamp-3 h-[51px] text-xs font-bold text-[#6D6D6D]">
        {job?.tagline}
      </span>
      <span className="line-clamp-2 text-xs text-[#5D5D5D]">
        {job?.description_str}
      </span>
      {job?.num_job_openings ? (
        <Link
          href={job?.detail_url}
          className="flex w-fit items-center gap-1 text-sm font-semibold text-[#23417A]"
        >
          <span className="underline">
            {job?.num_job_openings} {job?.num_job_openings > 1 ? "jobs" : "job"}
          </span>
          <IoIosArrowRoundForward className="h-4 w-4" />
        </Link>
      ) : (
        <></>
      )}
    </div>
  );
};

const KoreanITCompanies = ({ job }: { job: CompaniesType }) => {
  return (
    <div className="h-[221px] w-[158px] rounded-xl border border-[#23417A] p-6 md:h-[350px] lg:h-[423px] md:w-[280px]">
      <div className="flex flex-col gap-2">
        <Link href={job?.detail_url} className="h-[40px] lg:h-[56px] text-base lg:text-xl font-bold text-[#23417A]">
          {job?.display_name}
        </Link>
        <span className="block h-[2px] w-8 bg-[#23417A]"></span>
        <span className="line-clamp-2 h-[40px] lg:h-[56px] text-base lg:text-xl/[26px] font-bold text-[#292929]">
          {job?.tagline}
        </span>
        <span className="line-clamp-4 block h-[80px] lg:h-[88px] text-[#5D5D5D] text-sm lg:text-base">
          {job?.description_str}
        </span>
        <span className="text-xs text-[#B0B0B0]">
          <span className="line-clamp-1">{job?.addresses?.sort_addresses}</span>
          <span className="line-clamp-1">{job?.industries_str}</span>
          <span className="line-clamp-1">{job?.company_size}</span>
        </span>
        <div className="flex items-center justify-end">
          <Link href={job?.detail_url} className="relative block h-[40px] w-[155px]">
            <Image
              src={job?.image_logo}
              alt="Logo"
              className="object-contain object-right"
              layout="fill"
            />
          </Link>
        </div>
        {job?.num_job_openings ? (
          <Link
            href={job?.detail_url}
            className="mt-2 flex w-fit items-center gap-1 rounded-xl bg-[#23417A] p-2 text-sm text-white"
          >
            {job?.num_job_openings} {job?.num_job_openings > 1 ? "jobs" : "job"}
            <IoIosArrowRoundForward className="h-4 w-4" />
          </Link>
        ) : (
          <></>
        )}
      </div>
    </div>
  );
};

const SwiperCustomKorean = () => {
  const t = useTranslations();
  const [jobs, setJobs] = useState<CompaniesType[]>([]);
  useEffect(() => {
    getKiccCompanies().then((data) => {
      setJobs(data);
    });
  }, []);

  return (
    <section
      id={"popular-companies-container"}
      className="mt-8 rounded-[16px] border-[2px] border-[#23417A] bg-white p-4 lg:p-8"
    >
      <div className={"flex flex-col gap-4 lg:gap-6"}>
        <div className={"overflow-x-hidden"}>
          {jobs?.length > 0 ? (
            <div className="md:min-w-[750px]">
              <Swiper
                grabCursor={true}
                slidesPerView={isDesktop ? 4 : 2}
                slidesPerGroup={isDesktop ? 4 : 1}
                spaceBetween={isDesktop ? 24 : 12}
                breakpoints={{
                  1024: {
                    slidesPerView: 3,
                    slidesPerGroup: 1,
                  },
                  1280: {
                    slidesPerView: 4,
                    slidesPerGroup: 4,
                  },
                }}
                grid={{
                  rows: 2,
                  fill: "row",
                }}
                className="w-full"
                modules={
                  isDesktop
                    ? [Grid, Pagination, Navigation]
                    : [Grid, Pagination]
                }
                pagination={{ clickable: true, el: ".pagination" }}
                navigation={
                  isDesktop
                    ? {
                        nextEl: ".nextEl",
                        prevEl: ".preEl",
                      }
                    : false
                }
              >
                {jobs.map((job, index) => {
                  return (
                    <SwiperSlide key={index}>
                      {isDesktop ? (
                        <KoreanITCompanies job={job} />
                      ) : (
                        <KoreanITCompaniesMobile job={job} />
                      )}
                    </SwiperSlide>
                  );
                })}

                <div
                  className={"swiper-footer flex items-center md:mt-4 justify-center"}
                >
                  <div
                    className={
                      "pagination pagination__custom__blue justify-center"
                    }
                  ></div>
                </div>
              </Swiper>
            </div>
          ) : (
            ""
          )}
        </div>
      </div>
    </section>
  );
};
export default dynamic(() => Promise.resolve(SwiperCustomKorean), {
  ssr: false,
});
