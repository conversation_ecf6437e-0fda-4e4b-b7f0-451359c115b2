import { getSearchData } from "@/services/searchAPI";
import { Metadata, ResolvingMetadata } from "next";
import { getTranslations } from "next-intl/server";
import { notFound } from "next/navigation";
import { ReactNode } from "react";
interface MetaProps {
  params: { slug: string; locale: string };
}

export const generateMetadata = async (
  { params }: MetaProps,
  parent: ResolvingMetadata,
): Promise<Metadata> => {
  const { slug, locale } = params;
  const translate = await getTranslations();
  const keywords = decodeURIComponent(slug);
  let metaTitle = "TopDev - Việc làm IT Hàng Đầu";
  let metaDescription = "";
  let metaKeywords = "";
  let metaImage = "";
  let metaType = "";

  let metaData: Metadata = {};

  if (!slug) {
    notFound();
  }

  const { units, meta_tag, filters } = await getSearchData(keywords);
  metaTitle = meta_tag ? meta_tag.title : "";
  metaDescription = meta_tag ? meta_tag.description : "";
  metaKeywords = meta_tag ? meta_tag.keywords : "";
  metaImage = meta_tag ? meta_tag.image : "";
  metaType = meta_tag ? meta_tag.type : "";

  if (units && units.data.jobs.length === 0 && filters.keyword) {
    metaTitle = translate("search_page_job_not_found");
    metaDescription = translate("search_page_coudnt_find_your_job");
    metaKeywords = "";
    metaImage = "";
    metaType = "";
  }

  return {
    ...metaData,
    title: metaTitle,
    description: metaDescription,
    keywords: metaKeywords,
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      images: {
        url: metaImage,
        alt: metaTitle,
      },
    },
    twitter: {
      title: metaTitle,
      description: metaDescription,
    },
    alternates: {
      canonical:
        process.env.NEXT_PUBLIC_BASE_URL +
        "/" +
        translate("slug_it_jobs") +
        "/" +
        slug,
      languages: {
        vi:
          process.env.NEXT_PUBLIC_BASE_URL +
          "/viec-lam-it/" + slug,
        en:
          process.env.NEXT_PUBLIC_BASE_URL +
          "/it-jobs/" + slug,
        "x-default": process.env.NEXT_PUBLIC_BASE_URL + "/viec-lam-it/" + slug,
      }
    },
    robots: "index,follow",
  };
};

const SearchPageLayout = ({ children }: { children: ReactNode }) => {
  return children;
};

export default SearchPageLayout;
