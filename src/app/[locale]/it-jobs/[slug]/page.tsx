import { Card<PERSON>ompanyList } from "@/components/Card/Company";
import CardInformativeList from "@/components/Card/Job/CardInformativeList";
import CardInformativeListMobile from "@/components/Card/Job/mobile/CardInformativeList.mobile";
import { <PERSON>er<PERSON><PERSON>ider } from "@/components/Common/mobiles/HeaderSearchContext";
import JobBySkillSection from "@/components/Job/JobBySkillSection";
import JobBySkillSectionMobile from "@/components/Job/mobile/JobBySkillSectionMobile";
const FilterBarMobile = dynamic(
  () => import("@/components/Search/mobile/FilterBarMobile"),
  { ssr: false },
);
const FilterMobileContainer = dynamic(
  () => import("@/components/Search/mobile/FilterMobileContainer"),
  { ssr: false },
);
import AdsSlideSidebar from "@/components/SearchPage/AdsSlideSidebar";
import HighlightCompaniesSection from "@/components/SearchPage/HighlightCompaniesSection";
import HighlightJobsSection from "@/components/SearchPage/HighlightJobsSection";
import JobsFitForYouSection from "@/components/SearchPage/JobsFitForYouSection";
import LoadMoreResultJobs from "@/components/SearchPage/LoadMoreData/LoadMoreResultJobs";
import SearchNotFoundSection from "@/components/SearchPage/SearchNotFoundSection";
import SearchResultBlogsSection from "@/components/SearchPage/SearchResultBlogsSection";
import SearchResultTitle from "@/components/SearchPage/SearchResultTitle";
import SearchResultCompaniesSectionMobile from "@/components/SearchPage/mobile/SearchResultCompaniesSectionMobile.mobile";
import TabGroupProvider from "@/components/Tab/TabGroup";
import TabContent from "@/components/Tab/TabGroup/TabContent";
import { getSearchData } from "@/services/searchAPI";
import { getAllTaxonomies } from "@/services/taxonomyAPI";
import { Lang } from "@/types/page";
import { TabType } from "@/types/search";
import { TaxonomiesType } from "@/types/taxonomy";
import isDevice from "@/utils/device";
import { getTranslations } from "next-intl/server";
import dynamic from "next/dynamic";
import Script from "next/script";
import ReactDOM from "react-dom";
import SearchNotFound from "@/components/Search/SearchNotFound";
import CardInformativeFreeList from "@/components/Card/Job/CardInformativeFreeList";
import { JobType } from "@/types/job";

/** @ts-ignore */
ReactDOM.preload("https://c.topdevvn.com/v4/assets/images/bg-search.jpg", {as: "image",});

const LoadMoreResultCompanies = dynamic(
  () => import("@/components/SearchPage/LoadMoreData/LoadMoreResultCompanies"),
);
const HeaderSearchMobile = dynamic(
  () => import("@/components/Common/mobiles/HeaderSearch.mobile"),
);
const JobAlertForSearch = dynamic(
  () => import("@/components/Job/JobAlertForSearch"),
);
const MainSearch = dynamic(() => import("@/components/Search/MainSearch"));
const SearchTitleAnimation = dynamic(
  () => import("@/components/Search/SearchTitleAnimation"),
);
const TabResultSearch = dynamic(
  () => import("@/components/Tab/TabResultSearch"),
);
interface SearchResultParams {
  params: {
    slug: string;
    locale: Lang;
  };
}

const SearchResultPage = async ({
  params: { slug, locale },
}: SearchResultParams) => {
  const tranlate = await getTranslations();
  const parseSlug = decodeURIComponent(slug);
  const fullUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/${tranlate(
    "slug_it_jobs",
  )}/${parseSlug}`;

  const [taxonomies, { filters, meta, units, queries }, translate] =
    await Promise.all([
      getAllTaxonomies(),
      getSearchData(parseSlug, fullUrl),
      getTranslations(),
    ]);

  const getExceptSkills = (skillId: number): string => {
    return meta.skills
      .map((item) => item.id)
      .filter((item) => item !== skillId)
      .join(",");
  };

  const isExistedJobs = () => {
    return units && units.data.jobs.length > 0;
  };

  const isExistedCompanies = () => {
    return units && units.data.companies.length > 0;
  };

  const isExistedResult = () => {
    return isExistedJobs() || isExistedCompanies();
  };

  if (isDevice() === "mobile") {
    return (
      <div>
        <HeaderProvider>
          <>
            <HeaderSearchMobile
              taxonomies={taxonomies as any}
              filters={filters}
              meta={meta}
            />
            <div className="container">
              <FilterBarMobile />
            </div>
            <FilterMobileContainer taxonomies={taxonomies as TaxonomiesType} />
          </>
        </HeaderProvider>
        <main className="bg-gray-light">
          <TabGroupProvider>
            <>
              {isExistedResult() && (
                <TabResultSearch
                  hasJob={isExistedJobs()}
                  hasCompany={isExistedCompanies()}
                />
              )}
              {isExistedResult() ? (
                <>
                  {isExistedJobs() && (
                    <JobAlertForSearch
                      keyword={meta.keyword_display}
                      skillId={queries.skills_id}
                    />
                  )}
                  <div className="container py-6">
                    {/* Job information */}
                    {isExistedJobs() ? (
                      <TabContent type={TabType.TAB_JOB}>
                        <section id="tab-job" data-tab="tab-job">
                          <div>
                            <SearchResultTitle meta={meta} units={units} />
                            <ul className="mt-2">
                              {units.data.jobs &&
                                isExistedJobs() &&
                                units.data.jobs.map((jobItem) => {
                                  return (
                                    <li
                                      key={jobItem.id}
                                      className="mb-4 last:mb-0"
                                    >
                                      {jobItem.level == "free" ? (
                                        <CardInformativeFreeList
                                          job={jobItem}
                                          hideImg={true}
                                        />
                                      ) : (
                                        <CardInformativeListMobile
                                          job={jobItem}
                                        />
                                      )}
                                    </li>
                                  );
                                })}
                            </ul>
                            {units.meta.jobs.last_page >
                              units.meta.jobs.current_page && (
                              <LoadMoreResultJobs
                                queries={queries}
                                currentPage={units.meta.jobs.current_page}
                                totalPage={units.meta.jobs.last_page}
                                device={isDevice()}
                              />
                            )}
                          </div>
                          {meta.skills && meta.skills.length > 1 && (
                            <>
                              {meta.skills.map((skillItem, index) => {
                                return (
                                  <div className="mt-8" key={index}>
                                    <JobBySkillSectionMobile
                                      title={skillItem.text}
                                      exceptSkills={getExceptSkills(
                                        skillItem.id,
                                      )}
                                      skillId={skillItem.id}
                                    />
                                  </div>
                                );
                              })}
                            </>
                          )}
                        </section>
                      </TabContent>
                    ) : (
                      <div className="container py-6">
                        <SearchNotFound meta={meta} />
                      </div>
                    )}
                    {/* Companies information */}
                    {isExistedCompanies() && (
                      <TabContent type={TabType.TAB_COMPANY}>
                        <SearchResultCompaniesSectionMobile
                          companies={units.data.companies}
                          units={units}
                          filters={filters}
                          locale={locale as Lang}
                        />
                      </TabContent>
                    )}
                    {/* Related posts */}
                    {units.data.blogs.length > 0 && (
                      <SearchResultBlogsSection
                        blogs={units.data.blogs}
                        meta={meta}
                      />
                    )}
                  </div>
                </>
              ) : (
                <div className="container py-6">
                  <SearchNotFoundSection meta={meta} />
                </div>
              )}
            </>
          </TabGroupProvider>
        </main>

        <div id="footer-banner-mobile"></div>
      </div>
    );
  }

  return (
    <main>
      <div className="bg-cover bg-main-search bg-center bg-no-repeat py-8">
        <div className="container mb-4">
          <SearchTitleAnimation />
        </div>
        <MainSearch
          taxonomies={taxonomies as any}
          key="main-search"
          filters={filters}
          meta={meta}
        />
      </div>
      <TabGroupProvider>
        <>
          <div className="container bg-white">
            {isExistedResult() && (
              <TabResultSearch
                hasJob={isExistedJobs()}
                hasCompany={isExistedCompanies()}
              />
            )}
          </div>
          <div className="bg-gray-light py-8">
            <div className="container">
              <div className="grid grid-cols-3 gap-6">
                <div className="col-span-2">
                  {isExistedResult() ? (
                    <div>
                      {isExistedJobs() && (
                        <JobAlertForSearch
                          keyword={meta.keyword_display}
                          skillId={queries.skills_id}
                        />
                      )}
                      {/* Job information */}
                      {isExistedJobs() ? (
                        <TabContent type={TabType.TAB_JOB}>
                          <section data-tab="tab-job" id="tab-job">
                            {/* Jobs by search */}
                            <div className="mt-8">
                              <SearchResultTitle units={units} meta={meta} />
                              <ul className="mt-4">
                                {(() => {
                                  let pendingFreeJob: null | JobType = null;
                                  return units.data.jobs.map(
                                    (jobItem, index) => {
                                      if (index === 5) {
                                        return (
                                          <>
                                            <li className="mb-4 last:mb-0">
                                              <div
                                                id="div-gpt-ad-1698056646871-0"
                                                className="min-h-[102px] min-w-[832px]"
                                              >
                                                <Script id="ggad-search-middle">
                                                  {`googletag.cmd.push(function() { googletag.display('div-gpt-ad-1698056646871-0'); });`}
                                                </Script>
                                              </div>
                                            </li>
                                            {renderJob(jobItem, index)}
                                          </>
                                        );
                                      } else {
                                        return renderJob(jobItem, index);
                                      }
                                    },
                                  );

                                  function renderJob(
                                    jobItem: JobType,
                                    index: number,
                                  ) {
                                    if (jobItem.level === "free") {
                                      if (pendingFreeJob) {
                                        const pairedJob = pendingFreeJob;
                                        pendingFreeJob = null;
                                        return (
                                          <li
                                            key={`group-${pairedJob.id}-${jobItem.id}`}
                                            className="free-job mb-4 flex gap-3 last:mb-0"
                                          >
                                            <CardInformativeFreeList
                                              job={pairedJob}
                                              hideImg={true}
                                              isDesktop={true}
                                            />
                                            <CardInformativeFreeList
                                              job={jobItem}
                                              hideImg={true}
                                              isDesktop={true}
                                            />
                                          </li>
                                        );
                                      } else if (
                                        index ===
                                        units.data.jobs.length - 1
                                      ) {
                                        return (
                                          <li
                                            key={jobItem.id}
                                            className="free-job mb-4 flex gap-3 last:mb-0"
                                          >
                                            <CardInformativeFreeList
                                              job={jobItem}
                                              hideImg={true}
                                              isDesktop={true}
                                            />
                                          </li>
                                        );
                                      } else {
                                        pendingFreeJob = jobItem;
                                        return null;
                                      }
                                    } else {
                                      const singleFreeJob = pendingFreeJob;
                                      pendingFreeJob = null;

                                      return (
                                        <>
                                          {singleFreeJob && (
                                            <li
                                              key={singleFreeJob.id}
                                              className="free-job mb-4 flex gap-3 last:mb-0"
                                            >
                                              <CardInformativeFreeList
                                                job={singleFreeJob}
                                                hideImg={true}
                                                isDesktop={true}
                                              />
                                            </li>
                                          )}
                                          <li
                                            key={jobItem.id}
                                            className="mb-4 last:mb-0"
                                          >
                                            <CardInformativeList
                                              job={jobItem}
                                            />
                                          </li>
                                        </>
                                      );
                                    }
                                  }
                                })()}
                              </ul>

                              {units.meta.jobs.last_page >
                                units.meta.jobs.current_page && (
                                <LoadMoreResultJobs
                                  queries={queries}
                                  currentPage={units.meta.jobs.current_page}
                                  totalPage={units.meta.jobs.last_page}
                                  device={isDevice()}
                                />
                              )}
                            </div>
                            {/* Jobs by skill */}
                            {meta.skills && meta.skills.length > 1 && (
                              <>
                                {meta.skills.map((skillItem, index) => {
                                  return (
                                    <div className="mt-8" key={index}>
                                      <JobBySkillSection
                                        title={skillItem.text}
                                        exceptSkills={getExceptSkills(
                                          skillItem.id,
                                        )}
                                        skillId={skillItem.id}
                                      />
                                    </div>
                                  );
                                })}
                              </>
                            )}
                          </section>
                        </TabContent>
                      ) : (
                        <SearchNotFound meta={meta} />
                      )}
                      {/* Company information */}
                      {isExistedCompanies() && (
                        <TabContent type={TabType.TAB_COMPANY}>
                          <section data-tab="tab-company" id="tab-company">
                            <div className="mt-8">
                              <h2 className="text-3xl font-bold">
                                {translate("company_company_information")}
                              </h2>
                              <ul className="mt-4">
                                {units.data.companies.map(
                                  (companyItem, index) => {
                                    return (
                                      <li
                                        key={index}
                                        className="mt-4 first:mt-0"
                                      >
                                        <CardCompanyList
                                          companyData={companyItem}
                                          locale={locale as Lang}
                                        />
                                      </li>
                                    );
                                  },
                                )}
                              </ul>
                              {units.meta.companies.current_page <
                                units.meta.companies.last_page && (
                                <LoadMoreResultCompanies
                                  currentPage={
                                    units.meta.companies.current_page
                                  }
                                  totalPage={units.meta.companies.last_page}
                                  filters={filters}
                                  locale={locale as Lang}
                                />
                              )}
                            </div>
                          </section>
                        </TabContent>
                      )}
                      {/* Related post */}
                      {units.data.blogs.length > 0 && (
                        <SearchResultBlogsSection
                          blogs={units.data.blogs}
                          meta={meta}
                        />
                      )}
                    </div>
                  ) : (
                    <SearchNotFoundSection meta={meta} />
                  )}
                </div>
                <div className="col-span-1">
                  <div>
                    {/* Highlight company */}
                    <HighlightCompaniesSection locale={locale as Lang} />
                    {/* Highlight jobs */}
                    <HighlightJobsSection />
                    {/* Ads banner section */}
                    <AdsSlideSidebar />
                    {/* Jobs fit for you */}
                    <JobsFitForYouSection />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      </TabGroupProvider>
    </main>
  );
};

export default SearchResultPage;
