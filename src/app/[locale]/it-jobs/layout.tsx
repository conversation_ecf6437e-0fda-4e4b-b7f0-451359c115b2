const SmartBannerEvent = dynamic(
  () => import("@/components/Events/SmartBanner"),
  { ssr: false },
);
import { getAllJobs } from "@/services/jobAPI";
import isDevice from "@/utils/device";
import { getLocaleForParams } from "@/utils/localeServer";
import { Metadata, ResolvingMetadata } from "next";
import { getTranslations } from "next-intl/server";
import dynamic from "next/dynamic";
import React, { ReactNode } from "react";

type MetaProps = {
  params: { id: string; locale: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

export async function generateMetadata(): Promise<Metadata> {
  const translate = await getTranslations();
  const allJobs = await getAllJobs(getLocaleForParams());
  let metaData: Metadata = {};
  metaData = {
    title: translate("search_page_title_without_search"),
    description: translate("search_page_description_without_search", {
      total: allJobs.meta.total,
    }),
    alternates: {
      canonical:
        process.env.NEXT_PUBLIC_BASE_URL + "/" + translate("slug_it_jobs"),
      languages: {
        vi: process.env.NEXT_PUBLIC_BASE_URL + "/viec-lam-it/",
        en: process.env.NEXT_PUBLIC_BASE_URL + "/it-jobs/",
        "x-default": process.env.NEXT_PUBLIC_BASE_URL + "/viec-lam-it/",
      },
    },
    keywords: translate("search_page_keywords_without_search"),
    openGraph: {
      title: translate("search_page_title_without_search"),
      description: translate("search_page_description_without_search", {
        total: allJobs.meta.total,
      }),
      siteName: "TopDev",
    },
    twitter: {
      title: translate("search_page_title_without_search"),
      description: translate("search_page_description_without_search", {
        total: allJobs.meta.total,
      }),
      site: "TopDev",
      creator: "@Topdevvn",
    },
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
  };
  return {
    ...metaData,
  };
}

const SearchPageLayout = ({ children }: { children: ReactNode }) => {
  const device = isDevice();
  return (
    <>
      {children}
      {/* {device === "mobile" && <SmartBannerEvent />} */}
    </>
  );
};

export default SearchPageLayout;
