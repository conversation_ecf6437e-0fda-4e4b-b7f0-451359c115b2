import ReactDOM from "react-dom";

ReactDOM.preload("https://c.topdevvn.com/v4/assets/images/bg-search.jpg", {
  /** @ts-ignore */
  as: "image",
});

const AdsSlideSidebar = dynamic(
  () => import("@/components/SearchPage/AdsSlideSidebar"),
);
import AllJobsSection from "@/components/SearchPage/AllJobsSection";
const HighlightCompaniesSection = dynamic(
  () => import("@/components/SearchPage/HighlightCompaniesSection"),
);
const HighlightJobsSection = dynamic(
  () => import("@/components/SearchPage/HighlightJobsSection"),
);
const JobsFitForYouSection = dynamic(
  () => import("@/components/SearchPage/JobsFitForYouSection"),
);
const FilterBarMobile = dynamic(
  () => import("@/components/Search/mobile/FilterBarMobile"),
);
const FilterMobileContainer = dynamic(
  () => import("@/components/Search/mobile/FilterMobileContainer"),
  { ssr: false },
);

import HeaderSearchMobile from "@/components/Common/mobiles/HeaderSearch.mobile";
import { HeaderProvider } from "@/components/Common/mobiles/HeaderSearchContext";
import { getAllTaxonomies } from "@/services/taxonomyAPI";
import { TaxonomiesType } from "@/types/taxonomy";
import isDevice from "@/utils/device";
import dynamic from "next/dynamic";
import { Lang } from "@/types/page";
const MainSearch = dynamic(() => import("@/components/Search/MainSearch"));
const SearchTitleAnimation = dynamic(
  () => import("@/components/Search/SearchTitleAnimation"),
);

const ItJobsPageMobile = async ({
  params: { locale },
}: {
  params: {
    locale: Lang;
  };
}) => {
  const taxonomies = await getAllTaxonomies();

  return (
    <>
      <HeaderProvider>
        <>
          <HeaderSearchMobile taxonomies={taxonomies as any} />
          <div className="container">
            <FilterBarMobile />
          </div>
          <FilterMobileContainer taxonomies={taxonomies as TaxonomiesType} />
        </>
      </HeaderProvider>
      <main className="bg-gray-light">
        <div className="container">
          <div className="py-6">
            {/* Highlight jobs */}
            <HighlightJobsSection />
            {/* Jobs section */}
            <AllJobsSection />
          </div>
        </div>
      </main>
      <div id="footer-banner-mobile"></div>
    </>
  );
};

const ItJobsPagePC = async ({
  params: { locale },
}: {
  params: {
    locale: Lang;
  };
}) => {
  const taxonomies = await getAllTaxonomies();

  return (
    <main>
      {/* Search bar section */}
      <div className="bg-cover bg-main-search bg-center bg-no-repeat py-8">
        <div className="container mb-4">
          <SearchTitleAnimation />
        </div>
        <MainSearch taxonomies={taxonomies as any} />
      </div>
      {/* End Search bar section */}

      {/* Jobs section */}
      <div className="bg-gray-light py-8">
        <div className="container">
          <div className="grid grid-cols-3 gap-6">
            {/* Jobs list */}
            <div className="col-span-2">
              <AllJobsSection />
            </div>
            {/* End Jobs list */}

            {/* Sidebar */}
            <div className="col-span-1">
              {/* Highlight company */}
              <HighlightCompaniesSection locale={locale} />
              {/* Highlight jobs */}
              <HighlightJobsSection />
              {/* Ads slide banner */}
              <AdsSlideSidebar />
              {/* Jobs fit for you */}
              <JobsFitForYouSection />
            </div>
            {/* End Sidebar */}
          </div>
        </div>
      </div>
      {/* End Jobs section */}
    </main>
  );
};

const ItJobsPage = async ({
  params: { locale },
}: {
  params: {
    locale: Lang;
  };
}) => {
  if (isDevice() === "mobile") {
    return <ItJobsPageMobile params={{ locale: locale }} />;
  }

  return <ItJobsPagePC params={{ locale: locale }} />;
};

export default ItJobsPage;
