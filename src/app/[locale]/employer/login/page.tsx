import dynamic from "next/dynamic";
import LoginForm from "./(component)/LoginForm";
const SwiperLogin = dynamic(() => import("@/components/SwiperLogin/page"), {
  ssr: false, // disables server-side rendering (important for useState etc.)
  loading: () => <p>Loading modal...</p>, // optional loading fallback
});

const Login = () => {
  return (
    <div className="container flex py-10">
      <LoginForm />
      <SwiperLogin />
    </div>
  );
};

export default Login;
