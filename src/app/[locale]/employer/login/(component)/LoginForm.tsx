"use client";
import React, { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler, UseFormRegisterReturn } from "react-hook-form";
import Image from "next/image";
import { FaUser } from "react-icons/fa";
import { IoIosLock } from "react-icons/io";
import Link from "next/link";
import { loginEmployer } from "@/services/auth";
import { IoEye } from "react-icons/io5";
import { IoEyeOff } from "react-icons/io5";
import { useTranslations } from "next-intl";
import ToastNotification from "@/components/Swal/ToastNotification";

type LoginFormInputs = {
  email: string;
  password: string;
};
type InputWithIconProps = {
  type?: string;
  placeholder?: string;
  icon: React.ReactNode;
  error?: string;
  register: UseFormRegisterReturn;
  labelContext: string;
};
const InputWithIcon: React.FC<InputWithIconProps> = ({
  type = "text",
  placeholder,
  icon,
  error,
  register,
  labelContext = "",
}) => {
  return (
    <div className="mb-3">
      <label className="mb-1 block text-sm font-medium text-[#5D5D5D]">
        {labelContext} <span className="text-[#DD3F24]">*</span>
      </label>
      <div className="flex items-center gap-2 rounded border border-[#C2C2C2] bg-white px-5 py-2">
        <div className="border-r border-r-[#5D5D5D] pr-2 text-[#5D5D5D]">
          {icon}
        </div>
        <input
          type={type}
          placeholder={placeholder}
          className="w-[500px] flex-1 border-none bg-transparent text-sm outline-none focus:border-none focus:ring-0"
          {...register}
        />
      </div>
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};
const LoginForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormInputs>();
  const [showPassword, setShowPassword] = useState(false);
  const t = useTranslations();

  const onSubmit: SubmitHandler<LoginFormInputs> = async (data) => {
    try {
      const loginInfo = await loginEmployer(data?.email, data?.password);
      if (loginInfo?.status === 200) {
        const redirect =
          process.env.NEXT_PUBLIC_OAUTH2_URL_ACCOUNT ??
          "https://accounts.topdev.vn";
        window.location.href = redirect;
      }
    } catch (error: any) {
      const keyMess = error?.response?.data?.message;
      ToastNotification({
        icon: "error",
        title: t("common_sorry"),
        description: keyMess
          ? t(keyMess)
          : t("detail_job_page_apply_notification_apply_failed"),
        timer: 2000,
      });
    }
  };
  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  return (
    <div className="flex w-1/2">
      <div className="flex w-[650px] flex-col items-center rounded-[16px] px-24">
        <Image
          src="https://c.topdevvn.com/uploads/2025/07/23/login.png"
          width={181}
          height={164}
          alt="login"
        />
        <span className="mt-6 block whitespace-nowrap text-[32px] font-bold text-[#DD3F24]">
          {t("auth_welcome_employer")}
        </span>
        <span className="mb-6 mt-4 block whitespace-nowrap text-lg text-[#5D5D5D]">
          {t("auth_employer_subtitle")}
        </span>
        <form className="mb-6" onSubmit={handleSubmit(onSubmit)} noValidate>
          <InputWithIcon
            icon={<FaUser />}
            labelContext={t("bottom_bar_account")}
            placeholder={t("bottom_bar_account")}
            register={register("email", {
              required: t("auth_required"),
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: t("it_report_email_invalid"),
              },
            })}
            error={errors.email?.message}
          />
          <div className="relative">
            <InputWithIcon
              type={showPassword ? "text" : "password"}
              labelContext={t("auth_password")}
              placeholder={t("auth_password")}
              icon={<IoIosLock />}
              register={register("password", {
                required: t("auth_required"),
              })}
              error={errors.password?.message}
            />
            {showPassword ? (
              <IoEyeOff
                onClick={togglePasswordVisibility}
                className="absolute right-4 top-1/2 h-5 w-5 translate-y-[20%]"
              />
            ) : (
              <IoEye
                onClick={togglePasswordVisibility}
                className="absolute right-4 top-1/2 h-5 w-5 translate-y-[20%]"
              />
            )}
          </div>
          {/* Submit */}
          <Link
            href={`${process.env.NEXT_PUBLIC_OAUTH2_URL_ACCOUNT}/reset-password`}
            className="block w-full text-end text-sm text-[#DD3F24]"
          >
            {t("auth_forgot_password")}
          </Link>
          <span className="mb-4 mt-6 block text-center text-xs text-[#3D3D3D]">
            {t("detail_job_page_apply_tnc_apply_with")}{" "}
            <Link
              href={`${process.env.NEXT_PUBLIC_BASE_URL}/term-of-services`}
              className="font-semibold"
            >
              {t("detail_job_page_apply_tnc_apply_term_of_services")}
            </Link>{" "}
            {t("auth_and")}{" "}
            <Link
              href={`${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`}
              className="font-semibold"
            >
              {t("privacy_policy_title")}
            </Link>{" "}
            {t("auth_topdev")}.
          </span>
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full rounded-[64px] bg-[#DD3F24] py-4 font-semibold text-white disabled:bg-[#F6F6F6] disabled:text-[#B0B0B0]"
          >
            {t("header_login")}
          </button>
          <span className="mt-[10px] block text-center text-sm">
            {t("auth_have_no_account")}{" "}
            <Link
              href="/employer/sign-up"
              className="font-semibold text-[#DD3F24]"
            >
              {t("detail_job_page_sign_up_now")}
            </Link>
          </span>
        </form>
      </div>
    </div>
  );
};

export default LoginForm;
