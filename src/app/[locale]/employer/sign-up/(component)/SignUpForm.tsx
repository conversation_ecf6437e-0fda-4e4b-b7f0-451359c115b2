"use client";
import ToastNotification from "@/components/Swal/ToastNotification";
import { signupEmployer } from "@/services/auth";
import { EmployerRegistrationForm } from "@/types/auth";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { SubmitHandler, useForm, UseFormRegisterReturn } from "react-hook-form";

type InputWithIconProps = {
  type?: string;
  placeholder?: string;
  error?: string;
  register: UseFormRegisterReturn;
  labelContext: string;
};

const InputWithIcon: React.FC<InputWithIconProps> = ({
  type = "text",
  placeholder,
  error,
  register,
  labelContext = "",
}) => {
  return (
    <div>
      <label className="mb-1 block text-sm font-semibold text-[#5D5D5D]">
        {labelContext}
      </label>
      <input
        type={type}
        placeholder={placeholder}
        className="w-72 flex-1 rounded border border-[#C2C2C2] bg-transparent bg-white py-4 text-sm outline-none"
        {...register}
      />
      {error && <p className="mt-1 text-xs text-red-500">{error}</p>}
    </div>
  );
};

const SignUpForm: React.FC = () => {
  const [isAgree, setIsAgree] = useState<boolean>(false);
  const t = useTranslations();
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
  } = useForm<EmployerRegistrationForm>({
    defaultValues: {
      employer_display_name: "",
      email: "",
      phone: "",
      password: "",
      password_confirmation: "",
      tax_number: "",
      business_name: "",
      seeking_candidate: 1,
    },
  });

  const onSubmit: SubmitHandler<EmployerRegistrationForm> = async (data) => {
    try {
      const signupData = await signupEmployer(data);
      if ("success" in signupData && signupData.success) {
        router.push(
          `/employer/register-success?email=${encodeURIComponent(data.email)}`,
        );
      } else if ("error" in signupData) {
        const errorMessages = Object.values(signupData.error).flat();
        const errorText = errorMessages.join("\n");

        ToastNotification({
          icon: "error",
          title: t("common_sorry"),
          description: errorText,
          timer: 3000,
        });
      }
    } catch (error) {
      ToastNotification({
        icon: "error",
        title: t("common_sorry"),
        description: t("detail_job_page_apply_notification_apply_failed"),
        timer: 2000,
      });
    }
  };

  const password = watch("password");

  return (
    <div className="flex w-1/2 items-center justify-center rounded-lg">
      <div className="flex w-[663px] flex-col items-center">
        <Image
          src="https://c.topdevvn.com/uploads/2025/07/24/register.png"
          width={170}
          height={144}
          alt="register"
        />
        <span className="mt-6 block text-center text-[32px] font-bold text-[#DD3F24]">
          {t("auth_create_employer_account")}
        </span>
        <span className="mb-6 mt-4 block text-center text-lg text-[#5D5D5D]">
          {t("auth_signup_tagline")}
        </span>

        <form
          className="mb-6 grid grid-cols-2 gap-x-5 gap-y-2"
          onSubmit={handleSubmit(onSubmit)}
          noValidate
        >
          {/* FIRST BLOCK */}
          <span className="col-span-2 block text-sm font-semibold text-[#DD3F24]">
            {t("auth_login_info")}
          </span>
          <InputWithIcon
            labelContext={t("auth_employer_display_name")}
            placeholder={t("auth_employer_display_name")}
            register={register("employer_display_name", {
              required: t("auth_required"),
            })}
            error={errors.employer_display_name?.message}
          />
          <InputWithIcon
            labelContext={t("it_report_email")}
            placeholder={t("it_report_email")}
            register={register("email", {
              required: t("auth_required"),
              pattern: {
                value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                message: t("it_report_email_invalid"),
              },
            })}
            error={errors.email?.message}
          />
          <InputWithIcon
            labelContext={t("it_report_phone_number")}
            placeholder={t("it_report_phone_number")}
            register={register("phone", {
              required: t("auth_required"),
              pattern: {
                value: /^[0-9]{9,11}$/,
                message: "Số điện thoại không hợp lệ",
              },
            })}
            error={errors.phone?.message}
          />
          <InputWithIcon
            type="password"
            labelContext={t("auth_password")}
            placeholder={t("auth_password")}
            register={register("password", {
              required: t("auth_required"),
              minLength: {
                value: 8,
                message: t("auth_password_min_length"), // Add this to your translations
              },
            })}
            error={errors.password?.message}
          />

          <InputWithIcon
            type="password"
            labelContext={t("auth_confirm_password")}
            placeholder={t("auth_confirm_password")}
            register={register("password_confirmation", {
              required: t("auth_required"),
              validate: (value) =>
                value === password || t("auth_confirm_not_matching"),
            })}
            error={errors.password_confirmation?.message}
          />
          <span className="col-span-2 block text-sm font-semibold text-[#DD3F24]">
            {t("auth_company_info")}
          </span>
          {/* SECOND BLOCK */}
          <InputWithIcon
            labelContext={t("auth_tax_code")}
            placeholder={t("auth_tax_code")}
            register={register("tax_number", {
              required: t("auth_required"),
              pattern: {
                value: /^\d{10}(-\d{3})?$/,
                message: t("auth_tax_invalid_format"),
              },
            })}
            error={errors.tax_number?.message}
          />
          <InputWithIcon
            labelContext={t("auth_business_name")}
            placeholder={t("auth_business_name")}
            register={register("business_name", {
              required: t("auth_required"),
            })}
            error={errors.business_name?.message}
          />
          {/* Policies & Submit */}
          <div className="col-span-2 mb-3 mt-5 flex items-center gap-6">
            <label className="block text-sm font-semibold text-[#5D5D5D]">
              {t("auth_check_company_hiring")}
            </label>
            <div className="flex gap-6">
              <label className="flex items-center gap-2 text-sm text-[#3D3D3D]">
                <input
                  type="radio"
                  value="1"
                  defaultChecked
                  {...register("seeking_candidate", {
                    required: "Vui lòng chọn một lựa chọn",
                  })}
                  className="checked:bg-[#DD3F24] focus-within:ring-0 checked:hover:bg-[#DD3F24] checked:focus:bg-[#DD3F24]"
                />
                {t("auth_yes")}
              </label>
              <label className="flex items-center gap-2 text-sm text-[#3D3D3D]">
                <input
                  type="radio"
                  value="0"
                  {...register("seeking_candidate", {
                    required: "Vui lòng chọn một lựa chọn",
                  })}
                  className="checked:bg-[#DD3F24] focus-within:ring-0 checked:hover:bg-[#DD3F24] checked:focus:bg-[#DD3F24]"
                />
                {t("auth_no")}
              </label>
            </div>
            {errors.seeking_candidate && (
              <p className="mt-1 text-xs text-red-500">
                {errors.seeking_candidate.message}
              </p>
            )}
          </div>
          <div className="col-span-2 mb-4">
            <label className="flex items-center gap-2 text-sm text-[#3D3D3D]">
              <input
                type="checkbox"
                checked={isAgree}
                onChange={() => setIsAgree((value) => !value)}
                className="h-4 w-4 rounded border border-[#B0B0B0] checked:bg-[#DD3F24] focus-within:ring-0 checked:hover:bg-[#DD3F24] checked:focus:bg-[#DD3F24]"
              />
              <span>
                {t("detail_job_page_apply_tnc_apply_with")}{" "}
                <Link
                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/term-of-services`}
                  className="text-[#DD3F24]"
                >
                  {t("detail_job_page_apply_tnc_apply_term_of_services")}
                </Link>{" "}
                {t("auth_and")}{" "}
                <Link
                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`}
                  className="text-[#DD3F24]"
                >
                  {t("privacy_policy_title")}
                </Link>{" "}
                {t("auth_topdev")}.
              </span>
            </label>
          </div>
          <div className="col-span-2">
            <button
              type="submit"
              disabled={isSubmitting || !isAgree}
              className="w-full rounded-[64px] bg-[#DD3F24] py-4 font-semibold text-white disabled:bg-[#F6F6F6] disabled:text-[#B0B0B0]"
            >
              {t("auth_register")}
            </button>
            <span className="mt-[10px] block text-center text-sm">
              {t("auth_have_account")}{" "}
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/employer/login`}
                className="font-semibold text-[#DD3F24]"
              >
                {t("header_login")}
              </Link>
            </span>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SignUpForm;
