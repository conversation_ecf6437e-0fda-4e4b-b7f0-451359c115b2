import dynamic from "next/dynamic";
import SignUpForm from "./(component)/SignUpForm";
const SwiperLogin = dynamic(() => import("@/components/SwiperLogin/page"), {
  ssr: false, // disables server-side rendering (important for useState etc.)
  loading: () => <p>Loading modal...</p>, // optional loading fallback
});

const SignIn = () => {
  return (
    <div className="container flex py-10">
      <SignUpForm />
      <SwiperLogin />
    </div>
  );
};

export default SignIn;
