"use client";

import ToastNotification from "@/components/Swal/ToastNotification";
import { resendEmail } from "@/services/auth";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { FaRegCheckCircle } from "react-icons/fa";
import { gtag } from "@/utils";

const COUNTDOWN = 60;
const STORAGE_KEY = "lastResendEmailTime";

const RegisterSuccess = () => {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const email = searchParams.get("email");

  const [isDisabled, setIsDisabled] = useState(false);
  const [countdown, setCountdown] = useState(COUNTDOWN);

  useEffect(() => {
    const lastSentTime = localStorage.getItem(STORAGE_KEY);
    if (lastSentTime) {
      const diff = Math.floor((Date.now() - Number(lastSentTime)) / 1000);
      if (diff < COUNTDOWN) {
        setIsDisabled(true);
        setCountdown(COUNTDOWN - diff);

        const timer = setInterval(() => {
          setCountdown((prev) => {
            if (prev <= 1) {
              clearInterval(timer);
              setIsDisabled(false);
              return COUNTDOWN;
            }
            return prev - 1;
          });
        }, 1000);

        return () => clearInterval(timer);
      }
    }

    gtag({
      event: "employer_register",
    });
  }, []);

  const handleResend = async () => {
    if (email) {
      try {
        const signupData = await resendEmail(email);
        if (signupData?.status === 200) {
          // Save time to localStorage
          localStorage.setItem(STORAGE_KEY, Date.now().toString());

          // Start countdown
          setIsDisabled(true);
          setCountdown(COUNTDOWN);

          const timer = setInterval(() => {
            setCountdown((prev) => {
              if (prev <= 1) {
                clearInterval(timer);
                setIsDisabled(false);
                return COUNTDOWN;
              }
              return prev - 1;
            });
          }, 1000);
        }
      } catch (error) {
        ToastNotification({
          icon: "error",
          title: t("common_sorry"),
          description: t("detail_job_page_apply_notification_apply_failed"),
          timer: 2000,
        });
      }
    }
  };

  return (
    <div className="container flex flex-col items-center gap-4 py-32">
      <FaRegCheckCircle className="h-12 w-12 text-green-600" />
      <span className="block text-2xl font-medium text-[#222831]">
        Đăng Ký Thành Công !
      </span>
      <span className="block">
        Chào <span className="font-semibold">{email}</span>, Vui lòng kiểm tra
        hòm thư của bạn để hoàn tất bước đăng ký.
      </span>
      <span className="block">
        Trong trường hợp bạn không tìm thấy email, chắc chắn rằng bạn đã kiểm
        tra <span className="font-semibold">Hòm Thư Spam</span> hoặc bấm{" "}
        <span className="font-semibold">Gửi Lại</span>.
      </span>
      <button
        onClick={handleResend}
        disabled={isDisabled}
        className={`h-9 rounded px-8 text-white transition ${
          isDisabled ? "cursor-not-allowed bg-gray-400" : "bg-[#d34127]"
        }`}
      >
        {isDisabled ? `${countdown}s` : "Gửi lại"}
      </button>
    </div>
  );
};

export default RegisterSuccess;
