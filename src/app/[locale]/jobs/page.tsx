const AdsSlideSidebar = dynamic(
  () => import("@/components/SearchPage/AdsSlideSidebar"),
);
import AllJobsSection from "@/components/SearchPage/AllJobsSection";
const HighlightCompaniesSection = dynamic(
  () => import("@/components/SearchPage/HighlightCompaniesSection"),
);
const HighlightJobsSection = dynamic(
  () => import("@/components/SearchPage/HighlightJobsSection"),
);
const JobsFitForYouSection = dynamic(
  () => import("@/components/SearchPage/JobsFitForYouSection"),
);
const FilterBarMobile = dynamic(
  () => import("@/components/Search/mobile/FilterBarMobile"),
);
const FilterMobileContainer = dynamic(
  () => import("@/components/Search/mobile/FilterMobileContainer"),
  { ssr: false },
);

import HeaderSearchMobile from "@/components/Common/mobiles/HeaderSearch.mobile";
import { HeaderProvider } from "@/components/Common/mobiles/HeaderSearchContext";
import SearchForm from "@/components/Search/SearchForm";
import Jobs from "@/components/v2/pages/jobs/page";
import { getAllTaxonomies } from "@/services/taxonomyAPI";
import { Lang } from "@/types/page";
import { TaxonomiesType } from "@/types/taxonomy";
import dynamic from "next/dynamic";
const MainSearch = dynamic(() => import("@/components/Search/MainSearch"));
const SearchTitleAnimation = dynamic(
  () => import("@/components/Search/SearchTitleAnimation"),
);

const ItJobsPageMobile = async ({
  params: { locale },
}: {
  params: {
    locale: Lang;
  };
}) => {
  const taxonomies = await getAllTaxonomies();

  return (
    <>
      <HeaderProvider>
        <>
          <HeaderSearchMobile taxonomies={taxonomies as any} />
          <div className="container">
            <FilterBarMobile />
          </div>
          <FilterMobileContainer taxonomies={taxonomies as TaxonomiesType} />
        </>
      </HeaderProvider>
      <main className="bg-gray-light">
        <div className="container">
          <div className="py-6">
            {/* Highlight jobs */}
            <HighlightJobsSection />
            {/* Jobs section */}
            <AllJobsSection />
          </div>
        </div>
      </main>
      <div id="footer-banner-mobile"></div>
    </>
  );
};

const ItJobsPagePC = async ({
  params: { locale },
}: {
  params: {
    locale: Lang;
  };
}) => {
  const taxonomies = await getAllTaxonomies();

  return (
    <main>
      {/* Search bar section */}
      <div className="bg-cover bg-main-search bg-center bg-no-repeat py-8">
        <div className="container">
          <SearchForm />
        </div>
      </div>
      {/* End Search bar section */}

      {/* Jobs section */}
      <div className="bg-gray-light py-8">
        <div className="container">
          <div className="grid grid-cols-3 gap-6">
            {/* Jobs list */}
            <div className="col-span-2">
              <AllJobsSection />
            </div>
            {/* End Jobs list */}

            {/* Sidebar */}
            <div className="col-span-1">
              {/* Highlight company */}
              <HighlightCompaniesSection locale={locale} />
              {/* Highlight jobs */}
              <HighlightJobsSection />
              {/* Ads slide banner */}
              <AdsSlideSidebar />
              {/* Jobs fit for you */}
              <JobsFitForYouSection />
            </div>
            {/* End Sidebar */}
          </div>
        </div>
      </div>
      {/* End Jobs section */}
    </main>
  );
};

const ItJobsPage = async ({
  params: { locale },
  searchParams,
}: {
  params: {
    locale: Lang;
  };
  searchParams: { [key: string]: string | string[] | undefined };
}) => {
  // if (isDevice() === "mobile") {
  //   return <Jobs searchParams={searchParams} />;
  // }
  // return <ItJobsPagePC params={{ locale: locale }} />;
  return <Jobs searchParams={searchParams} />;
};

export default ItJobsPage;
