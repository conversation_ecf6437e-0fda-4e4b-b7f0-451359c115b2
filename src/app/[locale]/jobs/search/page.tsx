import { Card<PERSON><PERSON>panyList } from "@/components/Card/Company";
import CardInformativeFreeList from "@/components/Card/Job/CardInformativeFreeList";
import CardInformativeList from "@/components/Card/Job/CardInformativeList";
import CardInformativeListMobile from "@/components/Card/Job/mobile/CardInformativeList.mobile";
import { HeaderProvider } from "@/components/Common/mobiles/HeaderSearchContext";
import JobBySkillSection from "@/components/Job/JobBySkillSection";
import JobBySkillSectionMobile from "@/components/Job/mobile/JobBySkillSectionMobile";
import SearchFilterExperienceLevel from "@/components/Search/mobile/Filter/SearchFilterExperienceLevel";
import SearchFilterLocation from "@/components/Search/mobile/Filter/SearchFilterLocation";
import SearchAllFilter from "@/components/Search/SearchAllFilter";
import SearchForm from "@/components/Search/SearchForm";
import SearchNotFound from "@/components/Search/SearchNotFound";
import SearchSortBy from "@/components/Search/SearchSortBy";
import AdsSlideSidebar from "@/components/SearchPage/AdsSlideSidebar";
import HighlightCompaniesSection from "@/components/SearchPage/HighlightCompaniesSection";
import HighlightJobsSection from "@/components/SearchPage/HighlightJobsSection";
import JobsFitForYouSection from "@/components/SearchPage/JobsFitForYouSection";
import LoadMoreResultJobs from "@/components/SearchPage/LoadMoreData/LoadMoreResultJobs";
import SearchResultCompaniesSectionMobile from "@/components/SearchPage/mobile/SearchResultCompaniesSectionMobileV2.mobile";
import SearchNotFoundSection from "@/components/SearchPage/SearchNotFoundSection";
import SearchResultBlogsSection from "@/components/SearchPage/SearchResultBlogsSection";
import SearchResultTitleV2 from "@/components/SearchPage/SearchResultTitleV2";
import TabGroupProvider from "@/components/Tab/TabGroup";
import TabContent from "@/components/Tab/TabGroup/TabContent";
import { DEFAULT_SEARCH_PARAMS } from "@/contansts/search";
import { fetchJobCategories } from "@/services/jobAPI";
import {
  getSearchData,
  searchBlog,
  searchCompanyV2,
  searchJobV2,
} from "@/services/searchAPI";
import { getAllJobsLevel, getAllTaxonomies } from "@/services/taxonomyAPI";
import { JobType, Role } from "@/types/job";
import { Lang } from "@/types/page";
import { TabType } from "@/types/search";
import { TaxonomiesType } from "@/types/taxonomy";
import { classNames } from "@/utils";
import isDevice from "@/utils/device";
import { generateSEOMapping, SearchParams } from "@/utils/seoMapping";
import _ from "lodash";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";
import dynamic from "next/dynamic";
import Script from "next/script";
import E from "./E";
import Jobs from "@/components/v2/pages/jobs/page";
const FilterMobileContainer = dynamic(
  () => import("@/components/Search/mobile/FilterMobileContainer"),
  { ssr: false },
);

const LoadMoreResultCompanies = dynamic(
  () => import("@/components/SearchPage/LoadMoreData/LoadMoreResultCompanies"),
);
const HeaderSearchMobile = dynamic(
  () => import("@/components/Common/mobiles/HeaderSearch.mobile"),
);
const JobAlertForSearch = dynamic(
  () => import("@/components/Job/JobAlertForSearch"),
);
const TabResultSearch = dynamic(
  () => import("@/components/Tab/TabResultSearch"),
);

interface Props {
  searchParams: { [key: string]: string | string[] | undefined };
  params: {
    locale: Lang;
  };
}

// Helper function to fetch SEO data consistently
async function fetchSEOData(searchParamsTyped: SearchParams) {
  let totalJobs = 0;
  let levelName = "";
  let roleName = "";
  let imageTag: string = "";

  try {
    const filterParams = {
      keyword: searchParamsTyped.keyword,
      job_categories_ids: searchParamsTyped.job_categories_ids,
      region_ids: searchParamsTyped.region_ids,
      job_levels_ids: searchParamsTyped.job_levels_ids,
      job_types_ids: searchParamsTyped.job_types_ids,
      contract_types_ids: searchParamsTyped.contract_types_ids,
      company_size_ids: searchParamsTyped.company_size_ids,
      company_industry_ids: searchParamsTyped.company_industry_ids,
      benefit_ids: searchParamsTyped.benefit_ids,
      skills_id: searchParamsTyped.skills_id,
      salary_min: searchParamsTyped.salary_min
        ? parseInt(searchParamsTyped.salary_min)
        : undefined,
      salary_max: searchParamsTyped.salary_max
        ? parseInt(searchParamsTyped.salary_max)
        : undefined,
      ordering: searchParamsTyped.ordering,
      page: searchParamsTyped.page
        ? parseInt(searchParamsTyped.page)
        : undefined,
    };

    // Fetch search results first
    const searchResult = await searchJobV2(_.pickBy(filterParams, _.identity));
    totalJobs = searchResult?.meta?.total || 0;
    imageTag = searchResult?.meta_tag?.image || "";

    // Fetch level name if level ID is provided
    if (filterParams?.job_levels_ids && filterParams?.job_levels_ids[0]) {
      const jobLevels = await getAllJobsLevel();
      const level = jobLevels.job_levels.find(
        (item) => item.id === parseInt(filterParams.job_levels_ids as string),
      );
      levelName = level?.text || "";
    }

    // Fetch role name if category ID is provided
    if (
      filterParams?.job_categories_ids &&
      filterParams?.job_categories_ids[0]
    ) {
      const categories = await fetchJobCategories();
      // If job_categories_ids is a comma-separated string, split it and use the first value
      const categoryId = filterParams?.job_categories_ids
        ?.toString()
        .split(",")[0];

      const role = categories
        ?.reduce<Role[]>((prev, item) => [...prev, ...item.roles], [])
        ?.find((item) => item.id === +categoryId);
      roleName = role?.name || "";
    }
  } catch (error) {
    console.log("Could not fetch job count for SEO:", error);
  }

  return { totalJobs, levelName, roleName, imageTag };
}

export const generateMetadata = async ({
  params,
  searchParams,
}: Props): Promise<Metadata> => {
  const searchParamsTyped: SearchParams = {
    keyword: searchParams.keyword as string,
    job_categories_ids: searchParams.job_categories_ids as string,
    region_ids: searchParams.region_ids as string,
    job_levels_ids: searchParams.job_levels_ids as string,
    job_types_ids: searchParams.job_types_ids as string,
    contract_types_ids: searchParams.contract_types_ids as string,
    company_size_ids: searchParams.company_size_ids as string,
    company_industry_ids: searchParams.company_industry_ids as string,
    benefit_ids: searchParams.benefit_ids as string,
    skills_id: searchParams.skills_id as string,
    salary_min: searchParams.salary_min as string,
    salary_max: searchParams.salary_max as string,
    ordering: searchParams.ordering as string,
    page: searchParams.page as string,
  };

  // Fetch SEO data using the helper function
  const { totalJobs, levelName, roleName, imageTag } =
    await fetchSEOData(searchParamsTyped);
  // Generate SEO mapping with the fetched data
  const seoMapping = await generateSEOMapping(
    searchParamsTyped,
    totalJobs,
    levelName,
    roleName,
  );
  const finalTitle =
    params?.locale === "en" ? seoMapping.titleEn : seoMapping.title;
  const finalDescription =
    params?.locale === "en" ? seoMapping.descriptionEn : seoMapping.description;
  const finalKeywords = seoMapping.keywords;

  return {
    title: finalTitle,
    description: finalDescription,
    keywords: finalKeywords,
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
    openGraph: {
      title: finalTitle,
      description: finalDescription,
      url: seoMapping.canonicalUrl,
      siteName: "TopDev",
      type: "website",
      images: {
        url: imageTag,
        alt: finalTitle,
      },
    },
    twitter: {
      card: "summary_large_image",
      title: finalTitle,
      description: finalDescription,
    },
    alternates: {
      canonical: seoMapping.canonicalUrl,
      languages: {
        vi:
          process.env.NEXT_PUBLIC_BASE_URL +
          "/viec-lam/tim-kiem/" +
          seoMapping.slug,
        en:
          process.env.NEXT_PUBLIC_BASE_URL + "/jobs/search/" + seoMapping.slug,
        "x-default":
          process.env.NEXT_PUBLIC_BASE_URL + "/viec-lam-it/" + seoMapping.slug,
      },
    },
    robots: "index,follow",
  };
};

const SearchResultPage = async ({
  searchParams,
  params: { locale },
}: Props) => {
  const filterParams = {
    keyword: searchParams.keyword as string,
    ordering: searchParams.ordering as string,
    locale: locale,
    region_ids: searchParams.region_ids as string,
    job_categories_ids: searchParams.job_categories_ids as string,
    job_roles_ids: searchParams.job_roles_ids as string,
    salary_min: parseInt(searchParams.salary_min as string),
    salary_max: parseInt(searchParams.salary_max as string),
    benefit_ids: searchParams.benefit_ids as string,
    skills_id: searchParams.skills_id as string,
    job_levels_ids: searchParams.job_levels_ids as string,
    job_types_ids: searchParams.job_types_ids as string,
    contract_types_ids: searchParams.contract_types_ids as string,
    company_size_ids: searchParams.company_size_ids as string,
    company_industry_ids: searchParams.company_industry_ids as string,
    page: parseInt(searchParams.page as string),
    "fields[job]": DEFAULT_SEARCH_PARAMS["fields[job]"],
    "fields[company]": DEFAULT_SEARCH_PARAMS["fields[company]"],
  };

  const jobs = await searchJobV2(_.pickBy(filterParams, _.identity));
  const companies = await searchCompanyV2(_.pickBy(filterParams, _.identity));
  const blogs = await searchBlog(
    _.pickBy({ keyword: searchParams.keyword as string }, _.identity),
  );

  // Convert searchParams to typed format for SEO mapping
  const searchParamsTyped = {
    keyword: searchParams.keyword as string,
    job_categories_ids: searchParams.job_categories_ids as string,
    region_ids: searchParams.region_ids as string,
    job_levels_ids: searchParams.job_levels_ids as string,
    job_types_ids: searchParams.job_types_ids as string,
    contract_types_ids: searchParams.contract_types_ids as string,
    company_size_ids: searchParams.company_size_ids as string,
    company_industry_ids: searchParams.company_industry_ids as string,
    benefit_ids: searchParams.benefit_ids as string,
    skills_id: searchParams.skills_id as string,
    salary_min: searchParams.salary_min as string,
    salary_max: searchParams.salary_max as string,
    ordering: searchParams.ordering as string,
    page: searchParams.page as string,
  };

  // Fetch SEO data using the same helper function to ensure consistency
  const { totalJobs, levelName, roleName } =
    await fetchSEOData(searchParamsTyped);

  // Generate SEO mapping with the correct data
  const seoMapping = await generateSEOMapping(
    searchParamsTyped,
    totalJobs,
    levelName,
    roleName,
  );
  const slug = seoMapping.slug;

  const translate = await getTranslations();
  const parseSlug = decodeURIComponent(slug);
  const fullUrl = `${process.env.NEXT_PUBLIC_BASE_URL}/${translate(
    "slug_it_jobs",
  )}/${parseSlug}`;

  const units = {
    data: {
      jobs: jobs.data,
      companies: companies.data,
      blogs: blogs.data,
    },
    meta: {
      jobs: jobs.meta,
      companies: companies.meta,
      blogs: blogs.meta,
    },
  };

  const [taxonomies, { filters, meta, queries }] = await Promise.all([
    getAllTaxonomies(),
    getSearchData(parseSlug, fullUrl),
  ]);

  const getExceptSkills = (skillId: number): string => {
    return meta.skills
      .map((item) => item.id)
      .filter((item) => item !== skillId)
      .join(",");
  };

  const isExistedJobs = () => {
    return jobs && jobs.data && jobs.data.length > 0; // units && units.data.jobs.length > 0;
  };

  const isExistedCompanies = () => {
    return companies && companies.data && companies.data.length > 0;
    // return units && units.data.companies.length > 0;
  };

  const isExistedResult = () => {
    return isExistedJobs() || isExistedCompanies();
  };

  return <Jobs searchParams={searchParams} />;

  // if (isDevice() === "mobile") {
  //   return (
  //     <div>
  //       <E searchParams={filterParams} />

  //       <HeaderProvider>
  //         <>
  //           <HeaderSearchMobile
  //             taxonomies={taxonomies as any}
  //             filters={filters}
  //             meta={meta}
  //           />
  //           <div className="flex gap-2 p-4">
  //             <SearchAllFilter />
  //             <SearchFilterLocation />
  //             <SearchFilterExperienceLevel />
  //           </div>
  //           <FilterMobileContainer taxonomies={taxonomies as TaxonomiesType} />
  //         </>
  //       </HeaderProvider>
  //       <main className="bg-gray-light">
  //         <TabGroupProvider>
  //           <>
  //             {isExistedResult() && (
  //               <TabResultSearch
  //                 hasJob={isExistedJobs()}
  //                 hasCompany={isExistedCompanies()}
  //               />
  //             )}
  //             {isExistedResult() ? (
  //               <>
  //                 {isExistedJobs() && (
  //                   <JobAlertForSearch
  //                     keyword={meta.keyword_display}
  //                     skillId={queries.skills_id}
  //                   />
  //                 )}
  //                 <div className="container py-6">
  //                   {/* Job information */}
  //                   {isExistedJobs() ? (
  //                     <TabContent type={TabType.TAB_JOB}>
  //                       <section id="tab-job" data-tab="tab-job">
  //                         <div>
  //                           <SearchResultTitleV2 totalJob={jobs.meta.total} />

  //                           {/* Sort by section */}
  //                           <div>
  //                             <span className="text-sm text-[#DD3F24]">
  //                               Sort by
  //                             </span>
  //                             <SearchSortBy />
  //                           </div>
  //                           {/* End Sort by section */}

  //                           <ul className="mt-2">
  //                             {jobs.data &&
  //                               isExistedJobs() &&
  //                               jobs.data.map((jobItem) => {
  //                                 return (
  //                                   <li
  //                                     key={jobItem.id}
  //                                     className="mb-4 last:mb-0"
  //                                   >
  //                                     {jobItem.level == "free" ? (
  //                                       <CardInformativeFreeList
  //                                         job={jobItem}
  //                                         hideImg={true}
  //                                       />
  //                                     ) : (
  //                                       <CardInformativeListMobile
  //                                         job={jobItem}
  //                                       />
  //                                     )}
  //                                   </li>
  //                                 );
  //                               })}
  //                           </ul>
  //                           {units.meta.jobs.last_page >
  //                             units.meta.jobs.current_page && (
  //                             <LoadMoreResultJobs
  //                               queries={queries}
  //                               currentPage={units.meta.jobs.current_page}
  //                               totalPage={units.meta.jobs.last_page}
  //                               device={isDevice()}
  //                             />
  //                           )}
  //                         </div>

  //                         {meta.skills && meta.skills.length > 1 && (
  //                           <>
  //                             {meta.skills.map((skillItem, index) => {
  //                               return (
  //                                 <div className="mt-8" key={index}>
  //                                   <JobBySkillSectionMobile
  //                                     title={skillItem.text}
  //                                     exceptSkills={getExceptSkills(
  //                                       skillItem.id,
  //                                     )}
  //                                     skillId={skillItem.id}
  //                                   />
  //                                 </div>
  //                               );
  //                             })}
  //                           </>
  //                         )}
  //                       </section>
  //                     </TabContent>
  //                   ) : (
  //                     <div className="container py-6">
  //                       <SearchNotFound
  //                         meta={meta}
  //                         keyword={searchParams.keyword as string}
  //                       />
  //                     </div>
  //                   )}
  //                   {/* Companies information */}
  //                   {isExistedCompanies() && (
  //                     <TabContent type={TabType.TAB_COMPANY}>
  //                       <SearchResultCompaniesSectionMobile
  //                         companies={units.data.companies}
  //                         // units={units}
  //                         // filters={filters}
  //                         locale={locale as Lang}
  //                       />
  //                     </TabContent>
  //                   )}
  //                   {/* Related posts */}
  //                   {units.data.blogs.length > 0 && (
  //                     <SearchResultBlogsSection
  //                       blogs={units.data.blogs}
  //                       meta={meta}
  //                     />
  //                   )}
  //                 </div>
  //               </>
  //             ) : (
  //               <div className="container py-6">
  //                 <SearchNotFoundSection
  //                   meta={meta}
  //                   keyword={searchParams.keyword as string}
  //                 />
  //               </div>
  //             )}
  //           </>
  //         </TabGroupProvider>
  //       </main>

  //       <div id="footer-banner-mobile"></div>
  //     </div>
  //   );
  // }

  // return (
  //   <main>
  //     <E searchParams={filterParams} />

  //     <div className="bg-cover bg-main-search bg-center bg-no-repeat py-8">
  //       <div className="container">
  //         <SearchForm />
  //       </div>
  //     </div>
  //     <TabGroupProvider>
  //       <>
  //         {Object.keys(searchParams).length > 0 && (
  //           <div className="container bg-white">
  //             {isExistedResult() && (
  //               <TabResultSearch
  //                 hasJob={isExistedJobs()}
  //                 hasCompany={isExistedCompanies()}
  //               />
  //             )}
  //           </div>
  //         )}

  //         <div className={classNames("bg-gray-light py-8")}>
  //           <div className="container">
  //             <div className="grid grid-cols-3 gap-6">
  //               <div className="col-span-2">
  //                 {isExistedResult() ? (
  //                   <div>
  //                     {/* Job alert */}
  //                     {isExistedJobs() &&
  //                       Object.keys(searchParams).length > 0 && (
  //                         <JobAlertForSearch
  //                           keyword={meta.keyword_display}
  //                           skillId={queries.skills_id}
  //                         />
  //                       )}
  //                     {/* End Job alert */}

  //                     {/* Job information */}
  //                     {isExistedJobs() ? (
  //                       <TabContent type={TabType.TAB_JOB}>
  //                         <section data-tab="tab-job" id="tab-job">
  //                           {/* Jobs by search */}
  //                           <div
  //                             className={classNames(
  //                               Object.keys(searchParams).length > 0
  //                                 ? "mt-8"
  //                                 : "mt-0",
  //                             )}
  //                           >
  //                             <SearchResultTitleV2 totalJob={jobs.meta.total} />

  //                             {/* Sort by section */}
  //                             <SearchSortBy />
  //                             {/* End Sort by section */}

  //                             <ul className="mt-4">
  //                               {(() => {
  //                                 let pendingFreeJob: null | JobType = null;
  //                                 return jobs.data.map((jobItem, index) => {
  //                                   if (index === 5) {
  //                                     return (
  //                                       <>
  //                                         <li className="mb-4 last:mb-0">
  //                                           <div
  //                                             id="div-gpt-ad-1698056646871-0"
  //                                             className="min-h-[102px] min-w-[832px]"
  //                                           >
  //                                             <Script id="ggad-search-middle">
  //                                               {`googletag.cmd.push(function() { googletag.display('div-gpt-ad-1698056646871-0'); });`}
  //                                             </Script>
  //                                           </div>
  //                                         </li>
  //                                         {renderJob(jobItem, index)}
  //                                       </>
  //                                     );
  //                                   } else {
  //                                     return renderJob(jobItem, index);
  //                                   }
  //                                 });

  //                                 function renderJob(
  //                                   jobItem: JobType,
  //                                   index: number,
  //                                 ) {
  //                                   if (jobItem.level === "free") {
  //                                     if (pendingFreeJob) {
  //                                       const pairedJob = pendingFreeJob;
  //                                       pendingFreeJob = null;
  //                                       return (
  //                                         <li
  //                                           key={`group-${pairedJob.id}-${jobItem.id}`}
  //                                           className="free-job mb-4 flex gap-3 last:mb-0"
  //                                         >
  //                                           <CardInformativeFreeList
  //                                             job={pairedJob}
  //                                             hideImg={true}
  //                                             isDesktop={true}
  //                                           />
  //                                           <CardInformativeFreeList
  //                                             job={jobItem}
  //                                             hideImg={true}
  //                                             isDesktop={true}
  //                                           />
  //                                         </li>
  //                                       );
  //                                     } else if (
  //                                       index ===
  //                                       units.data.jobs.length - 1
  //                                     ) {
  //                                       return (
  //                                         <li
  //                                           key={jobItem.id}
  //                                           className="free-job mb-4 flex gap-3 last:mb-0"
  //                                         >
  //                                           <CardInformativeFreeList
  //                                             job={jobItem}
  //                                             hideImg={true}
  //                                             isDesktop={true}
  //                                           />
  //                                         </li>
  //                                       );
  //                                     } else {
  //                                       pendingFreeJob = jobItem;
  //                                       return null;
  //                                     }
  //                                   } else {
  //                                     const singleFreeJob = pendingFreeJob;
  //                                     pendingFreeJob = null;

  //                                     return (
  //                                       <>
  //                                         {singleFreeJob && (
  //                                           <li
  //                                             key={singleFreeJob.id}
  //                                             className="free-job mb-4 flex gap-3 last:mb-0"
  //                                           >
  //                                             <CardInformativeFreeList
  //                                               job={singleFreeJob}
  //                                               hideImg={true}
  //                                               isDesktop={true}
  //                                             />
  //                                           </li>
  //                                         )}
  //                                         <li
  //                                           key={jobItem.id}
  //                                           className="mb-4 last:mb-0"
  //                                         >
  //                                           <CardInformativeList
  //                                             job={jobItem}
  //                                           />
  //                                         </li>
  //                                       </>
  //                                     );
  //                                   }
  //                                 }
  //                               })()}
  //                             </ul>

  //                             {jobs.meta.last_page > jobs.meta.current_page && (
  //                               <LoadMoreResultJobs
  //                                 queries={queries}
  //                                 currentPage={units.meta.jobs.current_page}
  //                                 totalPage={units.meta.jobs.last_page}
  //                                 device={isDevice()}
  //                               />
  //                             )}
  //                           </div>

  //                           {/* Jobs by skill */}
  //                           {meta.skills && meta.skills.length > 1 && (
  //                             <>
  //                               {meta.skills.map((skillItem, index) => {
  //                                 return (
  //                                   <div className="mt-8" key={index}>
  //                                     <JobBySkillSection
  //                                       title={skillItem.text}
  //                                       exceptSkills={getExceptSkills(
  //                                         skillItem.id,
  //                                       )}
  //                                       skillId={skillItem.id}
  //                                     />
  //                                   </div>
  //                                 );
  //                               })}
  //                             </>
  //                           )}
  //                         </section>
  //                       </TabContent>
  //                     ) : (
  //                       <SearchNotFound
  //                         meta={meta}
  //                         keyword={searchParams.keyword as string}
  //                       />
  //                     )}
  //                     {/* End Job information */}

  //                     {/* Company information */}
  //                     {isExistedCompanies() && (
  //                       <TabContent type={TabType.TAB_COMPANY}>
  //                         <section data-tab="tab-company" id="tab-company">
  //                           <div className="mt-8">
  //                             <h2 className="text-3xl font-bold">
  //                               {translate("company_company_information")}
  //                             </h2>
  //                             <ul className="mt-4">
  //                               {companies.data.map((companyItem, index) => {
  //                                 return (
  //                                   <li key={index} className="mt-4 first:mt-0">
  //                                     <CardCompanyList
  //                                       companyData={companyItem}
  //                                       locale={locale as Lang}
  //                                     />
  //                                   </li>
  //                                 );
  //                               })}
  //                             </ul>
  //                             {companies.meta.current_page <
  //                               companies.meta.last_page && (
  //                               <LoadMoreResultCompanies
  //                                 currentPage={companies.meta.current_page}
  //                                 totalPage={companies.meta.last_page}
  //                                 filters={filters}
  //                                 locale={locale as Lang}
  //                               />
  //                             )}
  //                           </div>
  //                         </section>
  //                       </TabContent>
  //                     )}
  //                     {/* End Company information */}

  //                     {/* Related post */}
  //                     {blogs.data.length > 0 && (
  //                       <SearchResultBlogsSection
  //                         blogs={blogs.data}
  //                         meta={meta}
  //                       />
  //                     )}
  //                   </div>
  //                 ) : (
  //                   <SearchNotFoundSection
  //                     meta={meta}
  //                     keyword={searchParams.keyword as string}
  //                   />
  //                 )}
  //               </div>

  //               {/* Sidebar */}
  //               <div className="col-span-1">
  //                 <div>
  //                   {/* Highlight company */}
  //                   <HighlightCompaniesSection locale={locale as Lang} />
  //                   {/* Highlight jobs */}
  //                   <HighlightJobsSection />
  //                   {/* Ads banner section */}
  //                   <AdsSlideSidebar />
  //                   {/* Jobs fit for you */}
  //                   <JobsFitForYouSection />
  //                 </div>
  //               </div>
  //               {/* End Sidebar */}
  //             </div>
  //           </div>
  //         </div>
  //       </>
  //     </TabGroupProvider>
  //   </main>
  // );
};

export default SearchResultPage;
