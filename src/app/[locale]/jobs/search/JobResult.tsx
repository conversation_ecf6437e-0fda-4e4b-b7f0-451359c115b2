"use client";

import { CardCompanyList } from "@/components/Card/Company";
import CardInformativeFreeList from "@/components/Card/Job/CardInformativeFreeList";
import CardInformativeList from "@/components/Card/Job/CardInformativeList";
import JobBySkillSection from "@/components/Job/JobBySkillSection";
import SearchNotFound from "@/components/Search/SearchNotFound";
import AdsSlideSidebar from "@/components/SearchPage/AdsSlideSidebar";
import HighlightCompaniesSection from "@/components/SearchPage/HighlightCompaniesSection";
import HighlightJobsSection from "@/components/SearchPage/HighlightJobsSection";
import JobsFitForYouSection from "@/components/SearchPage/JobsFitForYouSection";
import LoadMoreResultJobs from "@/components/SearchPage/LoadMoreData/LoadMoreResultJobs";
import SearchNotFoundSection from "@/components/SearchPage/SearchNotFoundSection";
import SearchResultBlogsSection from "@/components/SearchPage/SearchResultBlogsSection";
import SearchResultTitle from "@/components/SearchPage/SearchResultTitleV2";
import TabGroupProvider from "@/components/Tab/TabGroup";
import TabContent from "@/components/Tab/TabGroup/TabContent";
import { JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { TabType } from "@/types/search";
import { getTranslations } from "next-intl/server";
import Script from "next/script";

import JobAlertForSearch from "@/components/Job/JobAlertForSearch";
import LoadMoreResultCompanies from "@/components/SearchPage/LoadMoreData/LoadMoreResultCompanies";
import TabResultSearch from "@/components/Tab/TabResultSearch";
import { isMobile } from "react-device-detect";

export default async function JobResult({
  jobs,
  companies,
  blogs,
  meta,
  units,
  queries,
  locale,
  filters,
}: any) {
  const translate = await getTranslations();
  /**
   * Return a string of all skill ids except the given skillId
   * @param {number} skillId
   * @returns {string}
   */
  const getExceptSkills = (skillId: number): string => {
    return meta.skills
      .map((item: any) => item.id)
      .filter((item: any) => item !== skillId)
      .join(",");
  };

  const isExistedJobs = () => {
    return units && units.data.jobs.length > 0;
  };

  const isExistedCompanies = () => {
    return units && units.data.companies.length > 0;
  };

  const isExistedResult = () => {
    return isExistedJobs() || isExistedCompanies();
  };

  return (
    <TabGroupProvider>
      <>
        <div className="container bg-white">
          {isExistedResult() && (
            <TabResultSearch
              hasJob={isExistedJobs()}
              hasCompany={isExistedCompanies()}
            />
          )}
        </div>

        <div className="bg-gray-light py-8">
          <div className="container">
            <div className="grid grid-cols-3 gap-6">
              <div className="col-span-2">
                {isExistedResult() ? (
                  <div>
                    {/* Job alert */}
                    {isExistedJobs() && (
                      <JobAlertForSearch
                        keyword={meta.keyword_display}
                        skillId={queries.skills_id}
                      />
                    )}
                    {/* End Job alert */}

                    {/* Job information */}
                    {isExistedJobs() ? (
                      <TabContent type={TabType.TAB_JOB}>
                        <section data-tab="tab-job" id="tab-job">
                          {/* Jobs by search */}
                          <div className="mt-8">
                            <SearchResultTitle totalJob={jobs.total} />

                            {/* Sort by section */}
                            <div className="mt-4 flex items-center gap-4">
                              <span className="text-sm text-[#DD3F24]">
                                Sort by
                              </span>
                              <button className="shadow-xs rounded-full bg-white px-6 py-1 text-sm">
                                Salary (High-Low)
                              </button>
                              <button className="shadow-xs rounded-full bg-white px-6 py-1 text-sm">
                                Salary (low-high)
                              </button>
                              <button className="shadow-xs rounded-full bg-white px-6 py-1 text-sm">
                                Date posted (latest)
                              </button>
                              <button className="shadow-xs rounded-full bg-white px-6 py-1 text-sm">
                                Date posted (oldest)
                              </button>
                            </div>
                            {/* End Sort by section */}

                            <ul className="mt-4">
                              {(() => {
                                let pendingFreeJob: null | JobType = null;
                                return jobs.data.map(
                                  (jobItem: any, index: any) => {
                                    if (index === 5) {
                                      return (
                                        <>
                                          <li className="mb-4 last:mb-0">
                                            <div
                                              id="div-gpt-ad-1698056646871-0"
                                              className="min-h-[102px] min-w-[832px]"
                                            >
                                              <Script id="ggad-search-middle">
                                                {`googletag.cmd.push(function() { googletag.display('div-gpt-ad-1698056646871-0'); });`}
                                              </Script>
                                            </div>
                                          </li>
                                          {renderJob(jobItem, index)}
                                        </>
                                      );
                                    } else {
                                      return renderJob(jobItem, index);
                                    }
                                  },
                                );

                                function renderJob(
                                  jobItem: JobType,
                                  index: number,
                                ) {
                                  if (jobItem.level === "free") {
                                    if (pendingFreeJob) {
                                      const pairedJob = pendingFreeJob;
                                      pendingFreeJob = null;
                                      return (
                                        <li
                                          key={`group-${pairedJob.id}-${jobItem.id}`}
                                          className="free-job mb-4 flex gap-3 last:mb-0"
                                        >
                                          <CardInformativeFreeList
                                            job={pairedJob}
                                            hideImg={true}
                                            isDesktop={true}
                                          />
                                          <CardInformativeFreeList
                                            job={jobItem}
                                            hideImg={true}
                                            isDesktop={true}
                                          />
                                        </li>
                                      );
                                    } else if (
                                      index ===
                                      units.data.jobs.length - 1
                                    ) {
                                      return (
                                        <li
                                          key={jobItem.id}
                                          className="free-job mb-4 flex gap-3 last:mb-0"
                                        >
                                          <CardInformativeFreeList
                                            job={jobItem}
                                            hideImg={true}
                                            isDesktop={true}
                                          />
                                        </li>
                                      );
                                    } else {
                                      pendingFreeJob = jobItem;
                                      return null;
                                    }
                                  } else {
                                    const singleFreeJob = pendingFreeJob;
                                    pendingFreeJob = null;

                                    return (
                                      <>
                                        {singleFreeJob && (
                                          <li
                                            key={singleFreeJob.id}
                                            className="free-job mb-4 flex gap-3 last:mb-0"
                                          >
                                            <CardInformativeFreeList
                                              job={singleFreeJob}
                                              hideImg={true}
                                              isDesktop={true}
                                            />
                                          </li>
                                        )}
                                        <li
                                          key={jobItem.id}
                                          className="mb-4 last:mb-0"
                                        >
                                          <CardInformativeList job={jobItem} />
                                        </li>
                                      </>
                                    );
                                  }
                                }
                              })()}
                            </ul>

                            {jobs.meta.last_page > jobs.meta.current_page && (
                              <LoadMoreResultJobs
                                queries={queries}
                                currentPage={units.meta.jobs.current_page}
                                totalPage={units.meta.jobs.last_page}
                                device={isMobile ? "mobile" : "desktop"}
                              />
                            )}
                          </div>

                          {/* Jobs by skill */}
                          {meta.skills && meta.skills.length > 1 && (
                            <>
                              {meta.skills.map((skillItem: any, index: any) => {
                                return (
                                  <div className="mt-8" key={index}>
                                    <JobBySkillSection
                                      title={skillItem.text}
                                      exceptSkills={getExceptSkills(
                                        skillItem.id,
                                      )}
                                      skillId={skillItem.id}
                                    />
                                  </div>
                                );
                              })}
                            </>
                          )}
                        </section>
                      </TabContent>
                    ) : (
                      <SearchNotFound meta={meta} />
                    )}
                    {/* End Job information */}

                    {/* Company information */}
                    {isExistedCompanies() && (
                      <TabContent type={TabType.TAB_COMPANY}>
                        <section data-tab="tab-company" id="tab-company">
                          <div className="mt-8">
                            <h2 className="text-3xl font-bold">
                              {translate("company_company_information")}
                            </h2>
                            <ul className="mt-4">
                              {companies.data.map(
                                (companyItem: any, index: any) => {
                                  return (
                                    <li key={index} className="mt-4 first:mt-0">
                                      <CardCompanyList
                                        companyData={companyItem}
                                        locale={locale as Lang}
                                      />
                                    </li>
                                  );
                                },
                              )}
                            </ul>
                            {companies.meta.current_page <
                              companies.meta.last_page && (
                              <LoadMoreResultCompanies
                                currentPage={companies.meta.current_page}
                                totalPage={companies.meta.last_page}
                                filters={filters}
                                locale={locale as Lang}
                              />
                            )}
                          </div>
                        </section>
                      </TabContent>
                    )}
                    {/* End Company information */}

                    {/* Related post */}
                    {blogs.data.length > 0 && (
                      <SearchResultBlogsSection
                        blogs={blogs.data}
                        meta={meta}
                      />
                    )}
                  </div>
                ) : (
                  <SearchNotFoundSection meta={meta} />
                )}
              </div>

              {/* Sidebar */}
              <div className="col-span-1">
                <div>
                  {/* Highlight company */}
                  <HighlightCompaniesSection locale={locale as Lang} />
                  {/* Highlight jobs */}
                  <HighlightJobsSection />
                  {/* Ads banner section */}
                  <AdsSlideSidebar />
                  {/* Jobs fit for you */}
                  <JobsFitForYouSection />
                </div>
              </div>
              {/* End Sidebar */}
            </div>
          </div>
        </div>
      </>
    </TabGroupProvider>
  );
}
