"use client";

import { SearchJobParamV2 } from "@/services/searchAPI";
import { getAllTaxonomies } from "@/services/taxonomyAPI";
import { useAppDispatch } from "@/store";
import { setCurrentSearch, setFormSearch } from "@/store/slices/searchSlice";
import { loadTaxonomies } from "@/store/slices/taxonomySlice";
import { useEffect } from "react";
interface Props {
  searchParams: SearchJobParamV2;
}
export default function E({ searchParams }: Props) {
  const dispatch = useAppDispatch();

  useEffect(() => {
    getAllTaxonomies().then((res) => {
      dispatch(loadTaxonomies(res as any)); // sorry men
    });

    // Sync to store
    const searchValues = {
      categories: searchParams.job_categories_ids?.split(",") || [],
      keyword: searchParams.keyword || "",
      locations: searchParams.region_ids?.split(",") || [],
      experience_levels: searchParams.job_levels_ids?.split(",") || [],
      benefits: searchParams.benefit_ids?.split(",") || [],
      company_sizes: searchParams.company_size_ids?.split(",") || [],
      work_types: searchParams.job_types_ids?.split(",") || [],
      salary_min: searchParams.salary_min || null,
      salary_max: searchParams.salary_max || null,
      contract_types: searchParams.contract_types_ids?.split(",") || [],
      company_industries: searchParams.company_industry_ids?.split(",") || [],
      sort_by: searchParams.ordering || "",
    };

    dispatch(setCurrentSearch(searchValues));
    dispatch(setFormSearch(searchValues));
  }, []);

  return <></>;
}
