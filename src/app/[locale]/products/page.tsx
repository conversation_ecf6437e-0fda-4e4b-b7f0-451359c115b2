import "@/assets/styles/pages/products.scss";
import TheProductsPage from "@/components/Product/TheProductsPage";
import { Metadata } from "next";
import { getTranslations } from "next-intl/server";

// Opt out of caching for all data requests in the route segment
export const dynamic = "force-dynamic";

export default function ProductsPage() {
  return <TheProductsPage />;
}

//Get dataMeta for SEO
export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations();
  const slug = process.env.NEXT_PUBLIC_BASE_URL + "/products";
  const title = t("products_title_tag");
  const description = t("products_meta_description");
  const keywords = t("products_meta_keywords");

  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL as string),
    alternates: {
      canonical: slug,
      languages: {
        vi: slug,
        en: slug,
      },
    },
    title: title,
    description: description,
    keywords: keywords,
    robots: "max-image-preview:large",
    other: {
      msapplicationTileImage:
        "https://c.topdevvn.com/v4/assets/images/favicon/favicon.ico",
    },
  };
}
