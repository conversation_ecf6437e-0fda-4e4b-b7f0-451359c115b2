"use client";

import { Button } from "@/components/Button";
import Error from "next/error";
import { useEffect } from "react";

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.log(error);
  }, [error]);

  return (
    <div className="container py-10 text-center">
      <h2 className="text-2xl font-bold mb-5">Something went wrong!</h2>
      <Button accent="primary" onClick={() => reset()}>
        Try again!
      </Button>
    </div>
  );
}
