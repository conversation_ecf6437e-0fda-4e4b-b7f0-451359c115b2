import ButtonDownloadQuestions from "@/components/InterviewPage/ButtonDownloadQuestions";
import ContainerQuestions from "@/components/InterviewPage/ContainerQuestions";
import ContainerSoftSkillQuestions from "@/components/InterviewPage/ContainerSoftSkillQuestions";
import Interview<PERSON>rovider from "@/components/InterviewPage/Context/InterviewContext";
import CreateCvOnlineBanner from "@/components/InterviewPage/CreateCvOnlineBanner";
import FormFilter from "@/components/InterviewPage/FormFilter";
import JobsFitForYouSection from "@/components/InterviewPage/JobsFitForYouSection";
import ListBlogs from "@/components/InterviewPage/ListBlogs";
import PersonalityTestCard from "@/components/InterviewPage/PersonalityTestCard";
import QuestionMenuMobile from "@/components/InterviewPage/QuestionMenuMobile";
import SidebarListQuestions from "@/components/InterviewPage/SidebarListQuestions";
import SidebarListSoftSkills from "@/components/InterviewPage/SidebarListSoftSkills";
import { SOFT_SKILL_DATA } from "@/contansts/it-report";
import {
  fetchJobByParams,
  getAllPositions,
  getAllSkills,
  getListQuestions,
} from "@/services/interviewAPI";
import { BlogTypePicked, InterviewType } from "@/types/interview";
import { JobType } from "@/types/job";
import { isMobile } from "@/utils/device";
import { getLocale, getTranslations } from "next-intl/server";
import Image from "next/image";
import { IoMdBookmarks } from "react-icons/io";

const InterviewPage = async () => {
  const getAllSkillsAndPositions = () => {
    return Promise.all([getAllSkills(), getAllPositions()]).then(
      ([responseSkills, responsePositions]) => {
        return {
          skills: responseSkills.data.data.qa_skills,
          positions: responsePositions.data.data.positions,
        };
      },
    );
  };

  const fetchInitData = async () => {
    try {
      const [response1, response2] = await Promise.all([
        getAllSkillsAndPositions(),
        getListQuestions(undefined, undefined),
      ]);
      const data = response2.data.data;
      const listQuestions = data.questions as InterviewType[];
      const listBlogs = data.blogs as BlogTypePicked[];
      return {
        skills: response1.skills,
        positions: response1.positions,
        listQuestions,
        listBlogs,
      };
    } catch (error) {
      return {
        skills: [],
        positions: [],
        listQuestions: [],
        listBlogs: [],
      };
    }
  };

  const { skills, positions, listBlogs, listQuestions } = await fetchInitData();
  const totalQuestion = listQuestions?.length ?? 0;

  const fetchJobsData = async () => {
    try {
      const response = await fetchJobByParams({
        page_size: 36,
        force_size: 36,
      });
      return response.data.data;
    } catch (error) {
      return [];
    }
  };
  const jobsFitForYouData: JobType[] = await fetchJobsData();

  return (
    <div id="interview-page">
      <InterviewProvider>
        <div className="bg-gradient-to-b from-primary to-[#ffb193] lg:bg-gradient-to-t">
          <div className="container px-4 pb-6">
            <div className="block items-center justify-start gap-5 py-[1.125rem] text-white lg:flex lg:justify-center">
              <p className="text-xl font-semibold lg:text-4xl">
                {isMobile() ? (
                  <>
                    Khám phá bộ câu hỏi phỏng vấn tuyển dụng &quot;BAO TRÚNG
                    TỦ&quot;
                  </>
                ) : (
                  <>
                    Khám phá bộ câu hỏi
                    <br />
                    phỏng vấn ngành IT
                  </>
                )}
              </p>
              <Image
                src="https://topdev.vn/_nuxt/img/cv_passed.c54ff48.svg"
                alt="CV Checked"
                width={177}
                height={120}
                className="hidden h-auto max-w-full lg:inline-block"
              />
            </div>
            <FormFilter skills={skills} positions={positions} />
          </div>
        </div>
        <QuestionMenuMobile
          softSkillQuestions={SOFT_SKILL_DATA}
          technicalQuestions={listQuestions}
        />
        <div className="container">
          <div className="relative mt-5 grid gap-10 lg:grid-cols-9">
            {/* Sidebar */}
            <div className="hidden lg:col-span-2 lg:block">
              <div className="sticky top-24">
                <p className="flex items-center gap-2.5 text-lg font-bold">
                  <span className="text-2xl">
                    <IoMdBookmarks />
                  </span>
                  Bộ câu hỏi
                </p>
                <hr className="my-3" />
                <div>
                  <p className="text-base font-semibold">
                    Câu hỏi kỹ thuật <span>({totalQuestion})</span>
                  </p>
                  <div className="scrollbar-primary h-[20rem] overflow-y-auto lg:h-[15.875rem]">
                    <SidebarListQuestions questions={listQuestions} />
                  </div>
                  <hr className="my-3" />
                  <SidebarListSoftSkills />
                  <hr className="my-3" />
                  <ButtonDownloadQuestions questions={listQuestions} />
                </div>
              </div>
            </div>
            {/* Main content */}
            <div className="lg:col-span-5">
              <h1 className="text-sm font-semibold uppercase lg:text-base">
                Bộ câu hỏi phỏng vấn
              </h1>
              <div className="mt-6">
                <h2 className="text-sm font-semibold uppercase lg:text-base">
                  CÂU HỎI KỸ THUẬT 💻
                  <span className="text-gray-400">({totalQuestion})</span>
                </h2>
                <ContainerQuestions questions={listQuestions} />
                <CreateCvOnlineBanner />
                <ContainerSoftSkillQuestions questions={SOFT_SKILL_DATA} />
              </div>
            </div>
            {/* Sidebar */}
            <div className="lg:col-span-2 lg:block">
              <div className="lg:col-span-2 lg:block">
                <ListBlogs blogs={listBlogs} />
                <PersonalityTestCard />
              </div>
            </div>
          </div>
          <JobsFitForYouSection jobs={jobsFitForYouData} />
        </div>
      </InterviewProvider>
    </div>
  );
};

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const t = await getTranslations({ locale });
  const lang = await getLocale();

  return {
    title: t("interview_meta_title"),
    description: t("interview_meta_description"),
    keywords: t("interview_meta_keywords"),
    alternates: {
      canonical: process.env.NEXT_PUBLIC_BASE_URL + (lang === "vi" ? "/phong-van" : "/interview"),
      languages: {
        vi: process.env.NEXT_PUBLIC_BASE_URL + "/phong-van",
        en: process.env.NEXT_PUBLIC_BASE_URL + "/interview",
        "x-default": process.env.NEXT_PUBLIC_BASE_URL + "/phong-van",
      },
    },
  };
}

export default InterviewPage;
