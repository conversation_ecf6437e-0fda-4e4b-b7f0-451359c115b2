import ButtonDownloadQuestions from "@/components/InterviewPage/ButtonDownloadQuestions";
import ContainerQuestions from "@/components/InterviewPage/ContainerQuestions";
import ContainerSoftSkillQuestions from "@/components/InterviewPage/ContainerSoftSkillQuestions";
import CreateCvOnlineBanner from "@/components/InterviewPage/CreateCvOnlineBanner";
import FormFilter from "@/components/InterviewPage/FormFilter";
import JobsFitForYouSection from "@/components/InterviewPage/JobsFitForYouSection";
import ListBlogs from "@/components/InterviewPage/ListBlogs";
import SidebarListSoftSkills from "@/components/InterviewPage/SidebarListSoftSkills";
import PersonalityTestCard from "@/components/InterviewPage/PersonalityTestCard";
import SidebarListQuestions from "@/components/InterviewPage/SidebarListQuestions";
import {
  fetchJobByParams,
  getAllPositions,
  getAllSkills,
  getListQuestions,
} from "@/services/interviewAPI";
import { JobType } from "@/types/job";
import Image from "next/image";
import React from "react";
import { IoMdBookmarks } from "react-icons/io";
import InterviewProvider from "@/components/InterviewPage/Context/InterviewContext";
import slugify from "slugify";
import { getTranslations } from "next-intl/server";
import { SOFT_SKILL_DATA } from "@/contansts/it-report";
import { Lang } from "@/types/page";
import { InterviewType } from "@/types/interview";
import { TaxonomyType } from "@/types/taxonomy";
import QuestionMenuMobile from "@/components/InterviewPage/QuestionMenuMobile";
import { getLocale } from "@/utils/localeServer";

const InterviewPage = async ({
  params: { slug, locale },
}: {
  params: { slug: string; locale: Lang };
}) => {
  const t = await getTranslations({ locale });
  const parameters = slug?.split("-")?.pop() as string;
  let positionId = parameters.match(/p(\d+)/)?.[1];
  let skillId = parameters.match(/s(\d+)/)?.[1];

  if (!positionId) {
    positionId = undefined;
  }
  if (!skillId) {
    skillId = undefined;
  }

  const getAllSkillsAndPositions = () => {
    return Promise.all([getAllSkills(), getAllPositions()])
      .then(([responseSkills, responsePositions]) => {
        return {
          skills: responseSkills.data.data.qa_skills as TaxonomyType[],
          positions: responsePositions.data.data.positions as TaxonomyType[],
        };
      })
      .catch(() => {
        return {
          skills: [] as TaxonomyType[],
          positions: [] as TaxonomyType[],
        };
      });
  };

  const { skills, positions } = await getAllSkillsAndPositions();

  const fetchQuestionDataWithID = async () => {
    try {
      const result = await Promise.all([
        getListQuestions(positionId, skillId),
      ]).then(([response]) => {
        const data = response.data.data;
        let listQuestions = data.questions;

        const index = listQuestions.findIndex(
          (question: InterviewType) =>
            slugify(question.title, {
              lower: true,
              strict: true,
              locale: "vi",
            }) === slug,
        );

        if (index !== -1) {
          const itemToMove = listQuestions.splice(index, 1)[0];
          listQuestions.unshift(itemToMove);
        }

        const listBlogs = data.blogs;
        return {
          listQuestions,
          listBlogs,
        };
      });
      return result;
    } catch (error) {
      return {
        listBlogs: [],
        listQuestions: [],
      };
    }
  };

  const { listBlogs, listQuestions } = await fetchQuestionDataWithID();

  const fetchJobsData = async () => {
    try {
      const response = await fetchJobByParams({
        page_size: 36,
        force_size: 36,
      });
      return response.data.data;
    } catch (error) {
      return [];
    }
  };
  const jobsFitForYouData: JobType[] = await fetchJobsData();

  const getTitle = () => {
    if (!positionId && !skillId) {
      return `Bộ câu hỏi phỏng vấn kỹ năng lập trình`;
    }
    const position =
      positions.find((item) => +item.id === +(positionId as string))?.text ??
      "";
    const skill =
      skills.find((item) => +item.id === +(skillId as string))?.text ?? "";
    if (positionId && skillId) {
      return `Bộ câu hỏi phỏng vấn kỹ năng lập trình ${skill}
      cho vị trí ${position}`;
    } else if (positionId) {
      return `Bộ câu hỏi phỏng vấn cho vị trí ${position}`;
    } else if (skillId) {
      return `Bộ câu hỏi phỏng vấn kỹ năng lập trình ${skill}`;
    } else {
      return `Bộ câu hỏi phỏng vấn`;
    }
  };

  return (
    <div id="interview-page">
      <InterviewProvider>
        <div className="bg-gradient-to-b from-primary to-[#ffb193] lg:bg-gradient-to-t">
          <div className="container px-4 pb-6">
            <div className="block items-center justify-start gap-5 py-[1.125rem] text-white lg:flex lg:justify-center">
              <p className="text-2xl font-semibold lg:text-4xl">
                Khám phá bộ câu hỏi
                <br />
                phỏng vấn ngành IT
              </p>
              <Image
                src="https://topdev.vn/_nuxt/img/cv_passed.c54ff48.svg"
                alt="CV Checked"
                width={177}
                height={120}
                className="hidden h-auto max-w-full lg:inline-block"
              />
            </div>
            <FormFilter skills={skills} positions={positions} />
          </div>
        </div>
        <QuestionMenuMobile
          softSkillQuestions={SOFT_SKILL_DATA}
          technicalQuestions={listQuestions}
        />
        <div className="container">
          <div className="relative mt-5 grid gap-10 lg:grid-cols-9">
            {/* Sidebar */}
            <div className="hidden lg:col-span-2 lg:block">
              <div className="sticky top-24">
                <p className="flex items-center gap-2.5 text-lg font-bold">
                  <span className="text-2xl">
                    <IoMdBookmarks />
                  </span>
                  Bộ câu hỏi
                </p>
                <hr className="my-3" />
                <div>
                  <p className="text-base font-semibold">
                    Câu hỏi kỹ thuật <span>({listQuestions.length})</span>
                  </p>
                  <div className="scrollbar-primary h-[20rem] overflow-y-auto lg:h-[15.875rem]">
                    <SidebarListQuestions questions={listQuestions} />
                  </div>
                  <hr className="my-3" />
                  <SidebarListSoftSkills />
                  <hr className="my-3" />
                  <ButtonDownloadQuestions questions={listQuestions} />
                </div>
              </div>
            </div>
            {/* Main content */}
            <div className="lg:col-span-5">
              <h1 className="font-semibold uppercase">{getTitle()}</h1>
              <div className="mt-6">
                <h2 className="font-semibold uppercase">
                  CÂU HỎI KỸ THUẬT 💻
                  <span className="text-gray-400">
                    ({listQuestions.length})
                  </span>
                </h2>
                <ContainerQuestions questions={listQuestions} />
                <CreateCvOnlineBanner />
                <ContainerSoftSkillQuestions questions={SOFT_SKILL_DATA} />
              </div>
            </div>
            {/* Sidebar */}
            <div className="lg:col-span-2 lg:block">
              <div className="lg:col-span-2 lg:block">
                <ListBlogs blogs={listBlogs} />
                <PersonalityTestCard />
              </div>
            </div>
          </div>
          <JobsFitForYouSection jobs={jobsFitForYouData} />
        </div>
      </InterviewProvider>
    </div>
  );
};

//SEO home page
export async function generateMetadata({
  params: { slug, locale },
}: {
  params: { slug: string; locale: Lang };
}) {
  const t = await getTranslations({ locale });
  const parameters = slug?.split("-")?.pop() as string;
  let positionId = parameters.match(/p(\d+)/)?.[1];
  let skillId = parameters.match(/s(\d+)/)?.[1];

  if (!positionId) {
    positionId = undefined;
  }
  if (!skillId) {
    skillId = undefined;
  }

  let title = "";
  let description = "";
  let keywords = "";
  try {
    const getAllSkillsAndPositions = () => {
      return Promise.all([getAllSkills(), getAllPositions()])
        .then(([responseSkills, responsePositions]) => {
          return {
            skills: responseSkills.data.data.qa_skills,
            positions: responsePositions.data.data.positions,
          };
        })
        .catch(() => {
          return { skills: [], positions: [] };
        });
    };

    const { skills, positions, questionName } = await Promise.all([
      getAllSkillsAndPositions(),
      getListQuestions(positionId, skillId),
    ])
      .then(async ([response1, response2]) => {
        const data = response2.data.data;
        let listQuestions = data.questions;
        let questionName = "";

        const index = await listQuestions.findIndex(
          (question: InterviewType) =>
            slugify(question.title, {
              lower: true,
              strict: true,
              locale: "vi",
            }) === slug,
        );

        if (index > -1) {
          questionName =
            getLocale() === "vi"
              ? listQuestions[index].title
              : listQuestions[index].title_eng;
        } else {
          questionName = "";
        }
        return {
          skills: response1.skills as TaxonomyType[],
          positions: response1.positions as TaxonomyType[],
          questionName: questionName,
        };
      })
      .catch(() => {
        return {
          skills: [],
          positions: [],
          questionName: "",
        };
      });
    if (positionId !== undefined && skillId !== undefined) {
      const positionName = positions.find(
        (positionObj) => positionObj.id === parseInt(positionId as string, 10),
      )?.text;
      const skillName = skills.find(
        (skillObj) => skillObj.id === parseInt(skillId as string, 10),
      )?.text;
      title = t("interview_meta_title_position_skill", {
        positionName: positionName,
        skillName: skillName,
      });
      description = t("interview_meta_description_position_skill", {
        positionName: positionName,
        skillName: skillName,
      });
      keywords = t("interview_meta_keyword_position_skill", {
        positionName: positionName,
        skillName: skillName,
      });
    } else if (skillId !== undefined) {
      const skillName = skills.find(
        (skillObj) => skillObj.id === parseInt(skillId as string, 10),
      )?.text;
      title = t("interview_meta_title_skill", { skillName: skillName });
      description = t("interview_meta_description_skill", {
        skillName: skillName,
      });
      keywords = t("interview_meta_keyword_skill", { skillName: skillName });
    } else if (positionId !== undefined) {
      const positionName = positions.find(
        (positionObj) => positionObj.id === parseInt(positionId as string, 10),
      )?.text;
      title = t("interview_meta_title_position", {
        positionName: positionName,
      });
      description = t("interview_meta_description_position", {
        positionName: positionName,
      });
      keywords = t("interview_meta_keyword_position", {
        positionName: positionName,
      });
    } else {
      title = t("interview_meta_title_question", {
        questionName: questionName,
      });
      description = t("interview_meta_description_question", {
        questionName: questionName,
      });
      keywords = t("interview_meta_keyword_question", {
        questionName: questionName,
      });
    }

    const slug_vi = process.env.NEXT_PUBLIC_BASE_URL + "/phong-van/" + slug;
    const slug_en = process.env.NEXT_PUBLIC_BASE_URL + "/interview/" + slug;

    return {
      title: title,
      description: description,
      keywords: keywords,
      alternates: {
        canonical: getLocale() === "vi" ? slug_vi : slug_en,
        languages: {
          vi: slug_vi,
          en: slug_en,
          "x-default": slug_vi,
        }
      },
    };
  } catch (error) {
    return {
      title: t("home_meta_title"),
      description: t("home_meta_description"),
      keywords: t("home_meta_keywords"),
    };
  }
}

export default InterviewPage;
