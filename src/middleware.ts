import createIntlMiddleware from "next-intl/middleware";
import { type NextRequest, NextResponse, userAgent } from "next/server";
import type { <PERSON> } from "@/types/page";
import { pathnames, locales } from "@/navigation";

// Types
type Middleware = (request: NextRequest, response: NextResponse) => NextResponse | Promise<NextResponse>;
type MiddlewareHandler = (request: NextRequest) => Promise<NextResponse>;

// Constants
const COOKIE_NAME = "topdev_locale" as const;
const DEFAULT_LOCALE: Lang = "vi" as const;
const EN_FORCED_ROUTES = [
  "/jobs",
  "/detail-jobs",
  "/companies",
  "/about-us",
] as const;
const PUBLIC_FILE = /\.(.*)$/;

/**
 * Creates a middleware chain that processes requests through multiple middleware functions
 */
function createMiddlewareChain(...middlewares: Middleware[]): MiddlewareHandler {
  return async (request: NextRequest) => {
    try {
      // Create initial response
      let response = NextResponse.next();

      // Process each middleware in sequence
      for (const middleware of middlewares) {
        response = await middleware(request, response);
      }

      return response;
    } catch (error) {
      console.error('Middleware chain error:', error);
      return NextResponse.next();
    }
  };
}

/**
 * Determines the appropriate locale based on the URL path and cookie
 */
const getLocaleByUrl = (url: string, defaultLocale: string): Lang => {
  const shouldForceEnglish = EN_FORCED_ROUTES.some((route) => 
    url.startsWith(route)
  );
  
  return shouldForceEnglish ? "en" : (defaultLocale as Lang);
};

/**
 * Middleware for handling internationalization
 */
const withI18n: Middleware = (request, response) => {
  // Skip for static files and API routes
  if (
    request.nextUrl.pathname.startsWith('/api') ||
    PUBLIC_FILE.test(request.nextUrl.pathname)
  ) {
    return response;
  }

  const cookieLocale = request.cookies.get(COOKIE_NAME)?.value ?? DEFAULT_LOCALE;
  const locale = getLocaleByUrl(request.nextUrl.pathname, cookieLocale);

  const intlMiddleware = createIntlMiddleware({
    defaultLocale: locale,
    localePrefix: "never",
    localeDetection: false,
    alternateLinks: false,
    locales,
    pathnames,
  });

  const intlResponse = intlMiddleware(request) as NextResponse;
  
  // Copy headers from intl response to our response
  intlResponse.headers.forEach((value, key) => {
    response.headers.set(key, value);
  });
  
  // Set locale cookie with secure defaults
  response.cookies.set({
    name: COOKIE_NAME,
    value: locale,
    path: "/",
    httpOnly: true,
    sameSite: "lax",
    secure: process.env.NODE_ENV === "production",
    ...(process.env.NEXT_PUBLIC_COOKIE_DOMAIN && {
      domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
    }),
  });

  return response;
};

/**
 * Middleware for device detection
 */
const withDeviceDetection: Middleware = (request, response) => {
  try {
    const { device } = userAgent(request);
    const deviceType = device.type === 'mobile' ? 'mobile' : 'desktop';
    
    response.headers.set("x-device-type", deviceType);
  } catch (error) {
    console.error('Device detection failed:', error);
    response.headers.set("x-device-type", "desktop");
  }
  
  return response;
};

// Create the middleware chain with all middleware functions
const middlewareChain = createMiddlewareChain(
  withI18n,
  withDeviceDetection,
  // Add more middleware functions here as needed
);

/**
 * Main middleware handler
 */
export default async function middleware(request: NextRequest) {
  return middlewareChain(request);
}

// Middleware configuration
export const config = {
  matcher: [
    '/((?!_next|api|_vercel|.*\\..*).*)',
  ],
};
