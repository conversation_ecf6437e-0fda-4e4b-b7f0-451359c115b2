"use client";

import Image from "next/image";
import Link from "@/components/Link/Link";
import { useSearchParams, usePathname, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { HiMiniChevronRight } from "react-icons/hi2";
import TheConvertCVModal from "@/components/User/Profile/TheConvertCVModal";
import { isMobile } from "react-device-detect";
import { openLoginPopup } from "@/utils";
import { getCookie, setCookie } from "@/utils/cookies";
import { IS_SHOW_CONVERT_CV_MODAL } from "@/contansts/userProfiles";
import { useAppSelector } from "@/store";

const BannerSection = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);
  const [isFormConvertCvModalOpen, setIsFormConvertCvModalOpen] =
    useState<boolean>(false);
  const [isClient, setIsClient] = useState<boolean>(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!isMobile || !searchParams.has(IS_SHOW_CONVERT_CV_MODAL)) return;

    // TODO check implement logged in here
    if (!isLoggedIn) {
      openLoginPopup([
        {
          name: IS_SHOW_CONVERT_CV_MODAL,
          value: 1,
        },
      ]);

      //set Cookie for show popup convertCV the first time , after login
      setCookie("isShowFillModal", "true", 0.2);
      return;
    }

    if (
      !!searchParams.has(IS_SHOW_CONVERT_CV_MODAL) &&
      !!searchParams.get(IS_SHOW_CONVERT_CV_MODAL)
    ) {
      setIsFormConvertCvModalOpen(
        Number(searchParams.get(IS_SHOW_CONVERT_CV_MODAL)) === 1,
      );
    }
  }, [searchParams, isMobile]);

  const handleConvertCvMobile = () => {
    if (!isLoggedIn) {
      openLoginPopup();

      //set Cookie for show popup convertCV the first time , after login
      setCookie("isShowFillModal", "true", 0.2);
      return;
    }
    setIsFormConvertCvModalOpen(true);
  };

  useEffect(() => {
    const isLoad = getCookie("isShowFillModal") ?? false;

    // TODO: session?.user?.roles.includes("resume") && isLoad cho nay convert gi ne
    if (isLoggedIn && isLoad) {
      setIsFormConvertCvModalOpen(true);

      //Clear cookie
      setCookie("isShowFillModal", "false", 0);
    }
  }, [isLoggedIn]);

  const handleCloseModal = () => {
    setIsFormConvertCvModalOpen(false);
    if (
      !!searchParams.has(IS_SHOW_CONVERT_CV_MODAL) &&
      !!searchParams.get(IS_SHOW_CONVERT_CV_MODAL)
    )
      router.push(pathname);
  };

  return (
    <>
      <div id="landing">
        <div className="bg-white">
          <div className="container relative flex flex-wrap items-center pt-8 md:pt-14">
            <div className="grid w-full gap-[8px] md:w-1/2">
              <div className="grid gap-[8px] text-center md:text-left">
                <p className="text-sm font-bold uppercase text-primary-400 md:text-xl md:text-primary">
                  CHỈ VỚI MỘT CLICK
                </p>
                <h1 className="font-body text-xl font-semibold text-black md:text-[48px] md:leading-[60px]">
                  Tạo CV online định dạng chuẩn ngay dành cho Developer
                </h1>
                <span className="text-sm text-gray-600 md:text-lg">
                  Xây dựng sự nghiệp ngành IT với công cụ tạo CV online và chuẩn
                  hóa CV của TopDev
                </span>
              </div>

              <div className="m-auto flex h-[280px] max-w-[272px] justify-center md:hidden">
                {isMobile && isClient ? (
                  <Image
                    src="/v4/assets/images/create-cv-online/landing-banner-right-mobile.webp"
                    width={272}
                    height={281}
                    alt="landing banner right"
                    className="h-[280px]"
                    sizes="250px"
                    priority={true}
                  />
                ) : (
                  <div className="h-[280px] bg-slate-100"></div>
                )}
              </div>

              <div className="flex flex-wrap gap-4 md:mt-8 md:flex-nowrap md:gap-6">
                <div className="w-full bg-gray-100 p-6 md:w-1/2">
                  <h2 className="text-gray-6000 font-bold md:text-xl">
                    Chuẩn hóa CV
                  </h2>
                  <p className="mb-4 text-sm text-gray-600 md:min-h-[66px] md:text-base">
                    Nhanh chóng chuyển đổi CV có sẵn của bạn thành định dạng CV
                    chuẩn Developer
                  </p>
                  {isClient && isMobile ? (
                    <div
                      id="btn-convert_cv_lp"
                      onClick={() => handleConvertCvMobile()}
                      className="flex w-full items-center justify-center rounded border border-primary-300 px-4 py-[6px] text-sm text-primary-400 transition-all hover:bg-primary-300 hover:text-white md:px-2 md:py-4 md:text-base md:font-bold md:text-primary"
                    >
                      Khám phá ngay
                      <HiMiniChevronRight className="ml-2 h-4 w-4 md:ml-3 md:h-6 md:w-6" />
                    </div>
                  ) : (
                    <Link
                      id="btn-convert_cv_lp"
                      href={`/users/profile?${IS_SHOW_CONVERT_CV_MODAL}=1`}
                      className="flex w-full items-center justify-center rounded border border-primary-300 px-4 py-[6px] text-sm text-primary-300 transition-all hover:bg-primary-300 hover:text-white md:px-2 md:py-4 md:text-base md:font-bold"
                    >
                      Khám phá ngay
                      <HiMiniChevronRight className="ml-2 h-4 w-4 md:ml-3 md:h-6 md:w-6" />
                    </Link>
                  )}
                </div>

                <div className="w-full bg-gray-100 p-6 md:w-1/2">
                  <h2 className="text-gray-6000 font-bold md:text-xl">
                    Tạo CV
                  </h2>
                  <p className="mb-4 text-sm text-gray-600 md:min-h-[66px] md:text-base">
                    Dễ dàng tạo CV chuẩn dành riêng cho Develop
                  </p>
                  <Link
                    href={"/users/my-cv?openPopupCreate=1"}
                    className="flex w-full items-center justify-center rounded border border-primary-300 px-4 py-[6px] text-sm text-primary-400 transition-all hover:bg-primary-300 hover:text-white md:px-2 md:py-4 md:text-base md:font-bold md:text-primary"
                  >
                    Khám phá ngay
                    <HiMiniChevronRight className="ml-3 h-6 w-6" />
                  </Link>
                </div>
              </div>
            </div>
            {!isMobile && isClient && (
              <div className="hidden md:block md:w-1/2">
                <Image
                  src="https://c.topdevvn.com/v4/assets/images/create-cv-online/landing-banner-right.png"
                  width={650}
                  height={671}
                  alt="landing banner right"
                  className="relative z-10"
                  sizes="(min-width: 62rem) 650px, 250px"
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <TheConvertCVModal
        openModal={isFormConvertCvModalOpen}
        onClose={() => handleCloseModal()}
      />
    </>
  );
};

export default BannerSection;
