import Image from "next/image";
import Link from "next/link";

const CreatingCVTipsSection = () => {
    return (
        <div className="container max-w-7xl px-5 py-10">
            <div className="lg:grid grid-cols-12">
                <div className="col-span-6">
                    <h3 className=" text-primary text-3xl font-bold">Tip làm CV dành cho Developer</h3>
                    <p className="mt-3">Cách để trở nên nổi bật cùng TopDev CV</p>
                </div>
                <div className="col-span-6 mt-5 lg:mt-0">
                    <div className="md:grid-cols-2 grid gap-x-5">
                        <div className="col-span-1 py-5 lg:p-0">
                            <div className="px-5 lg:p-0">
                                <Image src={`/v4/assets/images/create-cv-online/write-cv.jpeg`}
                                    alt="How to write a good CV" className="max-w-sm h-full w-full object-cover mx-auto"
                                    loading="lazy" width="300" height="168" />
                            </div>
                            <Link className="hover:text-primary block text-gray-700 mt-4 transition-all"
                                href="/blog/mau-cv-it-tieng-anh">
                                <h4 className="font-bold">
                                7 bước viết CV English IT dành cho dân lập trình
                                </h4>
                            </Link>
                            <p className="mt-4 text-gray-500 text-justify">
                            <span className="font-bold">CV English IT</span> của bạn liệu đã được hoàn thiện chưa? Đâu là cách thức viết <span className="font-bold">CV IT English</span> hiệu quả nhất. Cùng TopDev điểm qua 7 bước viết CV chuẩn giúp chinh phục nhà tuyển dụng IT.
                            </p>
                        </div>
                        <div className="col-span-1 py-5 lg:p-0">
                            <div className="px-5 lg:p-0">
                                <Image src={`/v4/assets/images/create-cv-online/skill-cv.png`}
                                    alt="How to make highlight my cv" className="max-w-sm mx-auto h-full w-full object-cover"
                                    loading="lazy" width="300" height="168" />
                            </div>
                            <Link className="hover:text-primary block text-gray-700 mt-4 transition-all"
                                href="/blog/cach-lam-noi-bat-muc-ky-nang-cv/">
                                <h4 className="font-bold">
                                Cách làm nổi bật mục Kỹ năng trong CV của bạn
                                </h4>
                            </Link>
                            <p className="mt-4 text-gray-500 text-justify">
                            Mục kỹ năng được xem là 1 trong 3 yếu tố quan trọng nhất mà nhà tuyển dụng khai thác ở ứng viên. Việc trình bày kỹ năng không chỉ đơn thuần dừng lại ở việc liệt kê. Nó còn đòi hỏi ứng viên cần nắm bắt tốt các khía cạnh khác để CV đạt hiệu quả.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default CreatingCVTipsSection;