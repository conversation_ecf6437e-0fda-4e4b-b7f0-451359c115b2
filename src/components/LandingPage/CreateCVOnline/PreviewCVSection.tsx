import isDevice from "@/utils/device";
import { FaTimes } from "react-icons/fa";
import { FaCircleArrowLeft, FaCircleArrowRight } from "react-icons/fa6";

const PreviewCVSection = () => {

    if (isDevice() == 'mobile') {
        return (
            <div id="preview-cv" className="hidden bg-opacity-80 bg-secondary z-[60] fixed top-0 left-0 w-full h-screen">
                <div className="relative h-full">
                    <button type="button" className="absolute top-4 right-4 text-white z-30 w-10 h-10 rounded-full bg-primary"
                        id="btn-close-preview">
                        <FaTimes />
                    </button>
                    <button id="prev-btn" type="button"
                        className="absolute top-1/2 -translate-y-1/2 left-2 text-3xl z-10 text-primary">
                        <FaCircleArrowLeft />
                    </button>
                    <div className="absolute top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 w-full">
                        <img src="" alt="Preview CV" className="max-w-full h-auto max-h-screen object-contain"
                            loading="lazy" id="img-preview" />
                    </div>
                    <button id="next-btn" type="button"
                        className="absolute top-1/2 -translate-y-1/2 right-2 text-3xl z-10 text-primary">
                        <FaCircleArrowRight />
                    </button>
                </div>
            </div>
        );
    }
};

export default PreviewCVSection;