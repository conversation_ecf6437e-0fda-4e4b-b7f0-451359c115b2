import Link from "next/link";
import isDevice from "@/utils/device";
import { MdArrowRightAlt } from "react-icons/md";
import { BsPencilSquare } from "react-icons/bs";
import { FaLocationArrow, FaRegCheckSquare } from "react-icons/fa";

const CreateStandardCVSection = () => {
  return (
    <div className="bg-red-50 pb-5 pt-10">
      <div className="container mx-auto max-w-xl px-5">
        <h3 className="mb-10 text-center text-3xl font-bold text-primary">
          Tạo CV chuẩn Developer cùng TopDev CV chỉ với 3 bước:
        </h3>
        <div>
          <div className="flex gap-3 py-5">
            <div className="text-2xl text-primary lg:text-4xl">
              <BsPencilSquare size={30} />
            </div>
            <div>
              <h4 className="text-lg font-bold text-gray-700">
                Tạo CV mới & cập nhật thông tin theo mẫu
              </h4>
              <p className="mt-3 text-gray-500">
                TopDev CV sẽ tự động điền thông tin từ hồ sơ của bạn trên
                TopDev. Nếu thông tin có thay đổi, bạn cũng có thể cập nhật ngay
                tại TopDev CV.
              </p>
            </div>
          </div>
          <div className="flex gap-3 py-5">
            <div className="text-2xl text-primary lg:text-4xl">
              <FaRegCheckSquare size={30} />
            </div>
            <div>
              <h4 className="text-lg font-bold text-gray-700">
                Chọn mẫu CV Developer ưng ý
              </h4>
              <p className="mt-3 text-gray-500">
                Bạn có thể chọn xem trước nhiều mẫu/ template CV khác nhau, chọn
                mẫu/ template ưng ý và Lưu. Cùng một thông tin, bạn có thể tạo
                nhiều mẫu CV khác nhau theo cá tính.
              </p>
            </div>
          </div>
          <div className="flex gap-3 py-5">
            <div className="text-2xl text-primary lg:text-4xl">
              <FaLocationArrow size={25} />
            </div>
            <div>
              <h4 className="text-lg font-bold text-gray-700">
                Tìm việc làm IT và ứng tuyển trực tiếp trên TopDev
              </h4>
              <p className="mt-3 text-gray-500">
                Không cần phải tải file PDF về máy, hệ thống sẽ tự động ghi nhận
                TopDev CV mới nhất của bạn để đề xuất khi bạn ứng tuyển.
              </p>
            </div>
          </div>
        </div>
        <div className="py-5 text-center">
          {isDevice() == "desktop" && (
            <Link
              href="/users/dash?openPopupCreate=1"
              className="lg:rounded-md group relative inline-block rounded-lg bg-primary px-6 py-3 text-base font-bold text-white transition-all duration-200 hover:bg-red-600 hover:pr-12 hover:shadow-md lg:text-2xl"
            >
              Đăng nhập và Tạo CV ngay
              <span className="absolute top-1/2 -translate-x-2 -translate-y-1/2 opacity-0 transition-all group-hover:opacity-100">
                <MdArrowRightAlt size={30} className="ml-2 h-[40px] w-[40px]" />
              </span>
            </Link>
          )}
          {isDevice() == "mobile" && (
            <Link
              href="https://preview.page.link/topdev.page.link/cvbuilder"
              className="lg:rounded-md group relative inline-block rounded-lg md:bg-primary bg-primary-400 px-6 py-3 text-base font-bold text-white transition-all duration-200 hover:bg-red-600 hover:pr-12 hover:shadow-md lg:text-2xl"
            >
              Tải app và Tạo CV ngay
              <span className="absolute right-2 top-1/2 -translate-x-2 -translate-y-1/2 opacity-0 transition-all group-hover:opacity-100">
                <MdArrowRightAlt />
              </span>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateStandardCVSection;
