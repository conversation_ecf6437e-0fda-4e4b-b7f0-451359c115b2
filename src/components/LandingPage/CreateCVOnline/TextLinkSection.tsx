import { useTranslations } from "next-intl";
import Link from "next/link";
import isDevice from "@/utils/device";
import { getCurrentLocale } from "@/utils/locale";
import { FaChevronUp } from "react-icons/fa";
import "@/assets/styles/components/_text_link.scss";

const hot_jobs = [
  {
    title: "text_link_fresher_java_job",
    url_vi: "https://topdev.vn/viec-lam-it/java-fresher-kt21",
    url_en: "https://topdev.vn/it-jobs/java-fresher-kt21",
  },
  {
    title: "text_link_fresher_javascript_job",
    url_vi: "https://topdev.vn/viec-lam-it/javascript-fresher-kt22",
    url_en: "https://topdev.vn/it-jobs/javascript-fresher-kt22",
  },
  {
    title: "text_link_fresher_php_job",
    url_vi: "https://topdev.vn/viec-lam-it/php-fresher-kt1",
    url_en: "https://topdev.vn/it-jobs/php-fresher-kt1",
  },
  {
    title: "text_link_fresher_python_job",
    url_vi: "https://topdev.vn/viec-lam-it/python-fresher-kt34",
    url_en: "https://topdev.vn/it-jobs/python-fresher-kt34",
  },
  {
    title: "text_link_fresher_reactjs_job",
    url_vi: "https://topdev.vn/viec-lam-it/reactjs-fresher-kt214",
    url_en: "https://topdev.vn/it-jobs/reactjs-fresher-kt214",
  },
  {
    title: "text_link_fresher_nodejs_job",
    url_vi: "https://topdev.vn/viec-lam-it/nodejs-fresher-kt36",
    url_en: "https://topdev.vn/it-jobs/nodejs-fresher-kt36",
  },
  {
    title: "text_link_fresher_c_cpp_job",
    url_vi: "https://topdev.vn/viec-lam-it/c-c-fresher-kt401",
    url_en: "https://topdev.vn/it-jobs/c-c-fresher-kt401",
  },
  {
    title: "text_link_fresher_tester_job",
    url_vi: "https://topdev.vn/viec-lam-it/tester-fresher-kt78",
    url_en: "https://topdev.vn/it-jobs/tester-fresher-kt78",
  },
  {
    title: "text_link_fresher_frontend_job",
    url_vi: "https://topdev.vn/viec-lam-it/front-end-fresher-kt209",
    url_en: "https://topdev.vn/it-jobs/front-end-fresher-kt209",
  },
  {
    title: "text_link_fresher_backend_job",
    url_vi: "https://topdev.vn/viec-lam-it/back-end-fresher-kt210",
    url_en: "https://topdev.vn/it-jobs/back-end-fresher-kt210",
  },
];

const industry_job = [
  {
    title: "text_link_job_in_outsourcing",
    url_vi: "https://topdev.vn/viec-lam-it/outsourcing-kt4276",
    url_en: "https://topdev.vn/it-jobs/outsourcing-kt4276",
  },
  {
    title: "text_link_job_in_product",
    url_vi: "https://topdev.vn/viec-lam-it/product-kt3919",
    url_en: "https://topdev.vn/it-jobs/product-kt3919",
  },
  {
    title: "text_link_job_in_bank",
    url_vi: "https://topdev.vn/viec-lam-it/ngan-hang-kt3935",
    url_en: "https://topdev.vn/it-jobs/ngan-hang-kt3935",
  },
  {
    title: "text_link_job_in_telecommunication",
    url_vi: "https://topdev.vn/viec-lam-it/vien-thong-kt3956",
    url_en: "https://topdev.vn/it-jobs/vien-thong-kt3956",
  },
  {
    title: "text_link_job_in_game",
    url_vi: "https://topdev.vn/viec-lam-it/giai-tri-game-kt3943",
    url_en: "https://topdev.vn/it-jobs/giai-tri-game-kt3943",
  },
  {
    title: "text_link_job_in_fintech",
    url_vi: "https://topdev.vn/viec-lam-it/fintech-kt3944",
    url_en: "https://topdev.vn/it-jobs/fintech-kt3944",
  },
  {
    title: "text_link_job_in_hardware",
    url_vi: "https://topdev.vn/viec-lam-it/phan-cung-kt3938",
    url_en: "https://topdev.vn/it-jobs/phan-cung-kt3938",
  },
  {
    title: "text_link_job_in_software",
    url_vi: "https://topdev.vn/viec-lam-it/phan-mem-kt3939",
    url_en: "https://topdev.vn/it-jobs/phan-mem-kt3939",
  },
  {
    title: "text_link_job_in_media_ads",
    url_vi: "https://topdev.vn/viec-lam-it/quang-cao-truyen-thong-kt3930",
    url_en: "https://topdev.vn/it-jobs/quang-cao-truyen-thong-kt3930",
  },
  {
    title: "text_link_job_in_it_service",
    url_vi: "https://topdev.vn/viec-lam-it/dich-vu-it-kt3948",
    url_en: "https://topdev.vn/it-jobs/dich-vu-it-kt3948",
  },
];

const hot_companies = [
  {
    title: "text_link_outsourcing_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/outsourcing-4276",
    url_en: "https://topdev.vn/companies/industries/outsourcing-4276",
  },
  {
    title: "text_link_software_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/phan-mem-3939",
    url_en: "https://topdev.vn/companies/industries/phan-mem-3939",
  },
  {
    title: "text_link_hardware_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/phan-cung-3938",
    url_en: "https://topdev.vn/companies/industries/phan-cung-3938",
  },
  {
    title: "text_link_product_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/product-3919",
    url_en: "https://topdev.vn/companies/industries/product-3919",
  },
  {
    title: "text_link_banking_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/ngan-hang-3935",
    url_en: "https://topdev.vn/companies/industries/ngan-hang-3935",
  },
  {
    title: "text_link_telecommunication_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/vien-thong-3956",
    url_en: "https://topdev.vn/companies/industries/vien-thong-3956",
  },
  {
    title: "text_link_game_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/giai-tri-game-3943",
    url_en: "https://topdev.vn/companies/industries/giai-tri-game-3943",
  },
  {
    title: "text_link_media_industry",
    url_vi:
      "https://topdev.vn/nha-tuyen-dung/industries/quang-cao-truyen-thong-3930",
    url_en:
      "https://topdev.vn/companies/industries/quang-cao-truyen-thong-3930",
  },
  {
    title: "text_link_service_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/dich-vu-it-3948",
    url_en: "https://topdev.vn/companies/industries/dich-vu-it-3948",
  },
  {
    title: "text_link_fintech_industry",
    url_vi: "https://topdev.vn/nha-tuyen-dung/industries/fintech-3944",
    url_en: "https://topdev.vn/companies/industries/fintech-3944",
  },
];

const TextLinkSection = () => {
  const t = useTranslations();
  const srcPageQueryParam = "topdev_createcv";
  const mediumPageQueryParam = "quicksearch";

  if (isDevice() == "mobile") {
    return (
      <div className="box-list-link mobile pb-3" id="box-list-link">
        <div className="container">
          <div className="card">
            <div className="box-item" id="headingOne">
              <button
                type="button"
                className="block w-full border-none p-[15px] text-left text-sm font-bold text-primary-400 outline-none bg-primary-100 md:text-base md:text-primary"
              >
                {t("common_top_job_for_fresher")}
              </button>
              <div className="icon">
                <FaChevronUp />
              </div>
            </div>
            <ul className="list-collapse">
              {hot_jobs.map((value, index) => (
                <li className="list-item" key={index}>
                  <Link
                    href={{
                      pathname: getCurrentLocale() == "en" ? value.url_en : value.url_vi,
                      query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
                    }}
                    className="text-link"
                  >
                    {t(`home_${value.title}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div className="card">
            <div className="box-item collapsed" id="headingTwo">
              <button
                type="button"
                className="block w-full border-none bg-primary-100 p-[15px] text-left text-sm text-primary-400 outline-none md:text-base font-bold md:text-primary"
              >
                {t("common_top_job_for_industry")}
              </button>
              <div className="icon">
                <FaChevronUp />
              </div>
            </div>
            <ul className="list-collapse">
              {industry_job.map((value, index) => (
                <li className="list-item" key={index}>
                  <Link
                    href={{
                      pathname: getCurrentLocale() == "en" ? value.url_en : value.url_vi,
                      query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
                    }}
                    className="text-link"
                  >
                    {t(`home_${value.title}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
          <div className="card">
            <div className="box-item collapsed" id="headingThree">
              <button
                type="button"
                className="block w-full border-none p-[15px] text-left text-sm font-bold text-primary-400 outline-none bg-primary-100 md:text-base md:text-primary"
              >
                {t("common_top_job_for_company")}
              </button>
              <div className="icon">
                <FaChevronUp />
              </div>
            </div>
            <ul className="list-collapse">
              {hot_companies.map((value, index) => (
                <li className="list-item" key={index}>
                  <Link
                    href={{
                      pathname: getCurrentLocale() == "en" ? value.url_en : value.url_vi,
                      query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
                    }}
                    className="text-link"
                  >
                    {t(`home_${value.title}`)}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="box-list-link pb-3" id="box-list-link">
      <div className="container">
        <div className="grid">
          <div className="grid-item">
            <div className="box-link">
              <p className="title">{t("common_top_job_for_fresher")}</p>
              <ul className="list-link">
                {hot_jobs.map((value, index) => (
                  <li className="list-item" key={index}>
                    <Link
                      href={{
                        pathname: getCurrentLocale() == "en" ? value.url_en : value.url_vi,
                        query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
                      }}
                      className="text-link"
                    >
                      {t(`home_${value.title}`)}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="grid-item">
            <div className="box-link">
              <p className="title">{t("common_top_job_for_industry")}</p>
              <ul className="list-link">
                {industry_job.map((value, index) => (
                  <li className="list-item" key={index}>
                    <Link
                      href={{
                        pathname: getCurrentLocale() == "en" ? value.url_en : value.url_vi,
                        query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
                      }}
                      className="text-link"
                    >
                      {t(`home_${value.title}`)}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          <div className="grid-item">
            <div className="box-link">
              <p className="title">{t("common_top_job_for_company")}</p>
              <ul className="list-link">
                {hot_companies.map((value, index) => (
                  <li className="list-item" key={index}>
                    <Link
                      href={{
                        pathname: getCurrentLocale() == "en" ? value.url_en : value.url_vi,
                        query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
                      }}
                      className="text-link"
                    >
                      {t(`home_${value.title}`)}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextLinkSection;
