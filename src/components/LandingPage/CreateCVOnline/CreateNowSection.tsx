import Link from "next/link";
import Image from "next/image";
import isDevice from "@/utils/device";
import { MdArrowRightAlt } from "react-icons/md";

const CreateNowSection = () => {
  return (
    <div className="container max-w-7xl px-5">
      <div className="flex flex-col-reverse lg:block">
        <div className="mt-5 block grid-cols-3 justify-center gap-x-10 lg:grid lg:pt-14">
          <div className="col-span-1 py-5 text-center lg:p-10">
            <h2 className="text-lg font-bold text-gray-700 lg:text-2xl">
              Cấu trúc CV chuẩn - Dành riêng cho Developer
            </h2>
            <p className="mt-2 text-center text-gray-600">
              Chọn lọc các thông tin tiêu chuẩn và cấu trúc thống nhất giúp <PERSON>hà
              tuyển dụng dễ dàng đánh gi<PERSON> kinh nghiệm và Tech stack của bạn
            </p>
          </div>
          <div className="col-span-1 py-5 text-center lg:p-10">
            <h2 className="text-lg font-bold text-gray-700 lg:text-2xl">
              Các mẫu CV IT được chọn lọc kỹ càng
            </h2>
            <p className="mt-2 text-center text-gray-600">
              Dựa trên hơn 1.000 mẫu CV Developer nổi bật trên thế giới, ứng
              tuyển thành công các vị trí tại các tập đoàn công nghệ lớn
              Microsoft, Google, Amazon…
            </p>
          </div>
          <div className="col-span-1 py-5 text-center lg:p-10">
            <h2 className="text-lg font-bold text-gray-700 lg:text-2xl">
              Quản lý CV online và ứng tuyển dễ dàng
            </h2>
            <p className="mt-2 text-center text-gray-600">
              Đồng bộ quản lý CV của bạn trên tất cả các thiết bị, ứng tuyển dễ
              dàng chỉ với một click
            </p>
          </div>
        </div>
        <div>
          {isDevice() == "desktop" && (
            <Image
              src={`https://c.topdevvn.com/v4/assets/images/create-cv-online/ghep-3.png`}
              alt="CV Preview"
              className="mx-auto h-auto max-w-full"
              width={1140}
              height={532}
            />
          )}

          {isDevice() == "mobile" && (
            <>
              <div className="main-carousel cv-carousel">
                <div className="carousel-cell">
                  <div className="p-3">
                    <Image
                      src={`https://c.topdevvn.com/v4/assets/images/create-cv-online/slide-1.png`}
                      alt="CV Template 1"
                      className="cv-image h-auto max-w-full shadow-md shadow-gray-400"
                      width="353"
                      height="499"
                      sizes="(min-width: 62rem) 1140px, 353px"
                    />
                  </div>
                </div>
                <div className="carousel-cell">
                  <div className="p-3">
                    <Image
                      src={`https://c.topdevvn.com/v4/assets/images/create-cv-online/slide-2.png`}
                      alt="CV Template 2"
                      className="cv-image h-auto max-w-full shadow-md shadow-gray-400"
                      width="353"
                      height="499"
                      sizes="(min-width: 62rem) 1140px, 353px"
                    />
                  </div>
                </div>
                <div className="carousel-cell">
                  <div className="p-3">
                    <Image
                      src={`https://c.topdevvn.com/v4/assets/images/create-cv-online/slide-3.png`}
                      alt="CV Template 3"
                      className="cv-image h-auto max-w-full shadow-md shadow-gray-400"
                      width="353"
                      height="499"
                      sizes="(min-width: 62rem) 1140px, 353px"
                    />
                  </div>
                </div>
              </div>
              <div className="mt-10 pt-5 text-center">
                <Link
                  href="/users/dash?openPopupCreate=1"
                  className="lg:rounded-md group relative block w-full rounded-lg md:bg-primary bg-primary-400 px-6 py-3 text-base font-bold text-white transition-all duration-200 hover:bg-red-600 hover:pr-12 hover:shadow-md lg:text-2xl"
                >
                  Tạo CV ngay
                  <span className="absolute right-3 top-1/2 -translate-x-2 -translate-y-1/2 opacity-0 transition-all group-hover:opacity-100">
                    <MdArrowRightAlt />
                  </span>
                </Link>
                <p className="mt-3 text-center text-[11px] italic lg:hidden">
                  * Tính năng Tạo CV hiện chỉ khả dụng trên PC và App TopDev
                </p>
              </div>
            </>
          )}
        </div>
      </div>
      {isDevice() != "mobile" && (
        <div className="py-5 text-center lg:py-10">
          <Link
            href="/users/dash?openPopupCreate=1"
            className="lg:rounded-md group relative inline-block rounded-lg bg-primary px-6 py-3 text-base font-bold text-white transition-all duration-200 hover:bg-red-600 hover:pr-12 hover:shadow-md lg:text-2xl"
          >
            Tạo CV ngay
            <span className="absolute top-1/2 -translate-x-2 -translate-y-1/2 opacity-0 transition-all group-hover:opacity-100">
              <MdArrowRightAlt size={30} className="ml-2 h-[40px] w-[40px]" />
            </span>
          </Link>
          <p className="mt-3 text-center text-[11px] italic lg:hidden">
            * Tính năng Tạo CV hiện chỉ khả dụng trên PC và App TopDev
          </p>
        </div>
      )}
      <div className="grid-cols-12 justify-between gap-10 py-8 lg:grid lg:py-16">
        <div className="col-span-6 py-10 lg:p-10">
          <h3 className="text-3xl font-bold text-primary">
            Thao tác đơn giản, tiện lợi
          </h3>
          <p className="pt-5 font-bold text-gray-700">
            Cấu trúc CV IT chuẩn, các trường thông tin được chọn lọc dành riêng
            cho việc ứng tuyển các vị trí trong ngành IT
          </p>
          <ul className="mt-4 list-disc pl-5 text-gray-600">
            <li>Tiết kiệm thời gian chọn & thiết kế cách trình bày CV</li>
            <li>Ưu tiên cho chất lượng nội dung chính trong CV</li>
            <li>
              Gây ấn tượng với Nhà tuyển dụng bằng các thông tin quan trọng như
              Kinh nghiệm làm việc & Kỹ năng lập trình
            </li>
            <li>
              Tạo điểm nhấn cho CV khi bổ sung các thông tin khác: Dự án, Sở
              thích, Hoạt động, Ngôn ngữ, Người tham vấn, Chứng chỉ, Giải
              thưởng…
            </li>
          </ul>
        </div>
        <div className="col-span-6">
          <Image
            src={`https://c.topdevvn.com/v4/assets/images/create-cv-online/make-a-cv.png`}
            alt="Create CV"
            className="ml-auto h-auto max-w-full"
            width="426"
            height="354"
          />
        </div>
      </div>
      <div className="flex grid-cols-12 flex-col-reverse justify-between gap-10 py-8 lg:grid lg:py-16">
        <div className="col-span-6">
          <Image
            src={`https://c.topdevvn.com/v4/assets/images/create-cv-online/device.png`}
            alt="Device"
            className="mr-auto h-auto max-w-full"
            width="524"
            height="316"
            loading="lazy"
          />
        </div>
        <div className="col-span-6">
          <h3 className="text-3xl font-bold text-primary">
            Thông tin đồng bộ, sử dụng trên mọi nền tảng
          </h3>
          <p className="pt-5 font-bold text-gray-700">
            Thông tin trên CV được cập nhật đầy đủ, chính xác, đồng bộ trên PC
            và Mobile
          </p>
          <ul className="mt-4 list-disc pl-5 text-gray-600">
            <li>
              Sau khi đăng nhập, bạn có thể tạo CV với những thông tin được điền
              tự động từ thông tin trước đó của bạn trên TopDev, cập nhật kinh
              nghiệm mới và sử dụng để tìm việc IT ngay
            </li>
            <li>Đồng bộ file trên cả PC & Mobile</li>
          </ul>
          <p className="mt-2 text-sm italic text-gray-400">
            (*) Tính năng Tạo và Chỉnh sửa CV được hỗ trợ trên PC và Mobile App.
            Các tính năng quản lý và đồng bộ được hỗ trợ trên cả PC & Mobile.
          </p>
        </div>
      </div>
      <div className="grid-cols-12 justify-between gap-10 py-8 lg:grid lg:py-16">
        <div className="col-span-6 py-10 lg:p-10">
          <h3 className="text-3xl font-bold text-primary">
            Ứng tuyển việc làm IT mọi nơi
          </h3>
          <p className="pt-5 font-bold text-gray-700">
            Sau khi hoàn thành CV Developer trên TopDev CV, bạn có thể ứng tuyển
            mọi nơi cùng với Cover Letter ấn tượng
          </p>
          <ul className="mt-4 list-disc pl-5 text-gray-600">
            <li>
              Quản lý lịch sử ứng tuyển sử dụng các mẫu CV đã tạo trên TopDev
            </li>
            <li>
              Theo dõi quá trình xử lý hồ sơ từng vị trí sau khi ứng tuyển
            </li>
            <li>Hỗ trợ lưu trữ File CV tải lên từ máy tính</li>
            <li>Tải về dưới dạng PDF</li>
          </ul>
        </div>
        <div className="col-span-6">
          <Image
            src={`https://c.topdevvn.com/v4/assets/images/create-cv-online/list-cv.png`}
            alt="List CV"
            className="ml-auto h-auto max-w-full"
            width="493"
            height="329"
            loading="lazy"
          />
        </div>
      </div>
    </div>
  );
};

export default CreateNowSection;
