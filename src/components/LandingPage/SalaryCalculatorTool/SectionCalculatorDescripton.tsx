import React from "react";

const SectionCalculatorDescripton = () => {
  return (
    <section className="section-calculate-card lg:max-h-auto lg:max-w-auto container mt-5 rounded border bg-white p-5">
      <div className="section-calculate-card--description">
        <h2 className="text-secondary mb-3 text-xl font-semibold">
          Lương Gross là gì? Lương Net là gì?
        </h2>
        <p className="mb-3 text-[14px]">
          <b>Lư<PERSON>ng Gross</b> đư<PERSON><PERSON> hiểu đơn giản là tổng thu nhập của người lao
          động, trong đó gồm tất cả các khoản đóng bảo hiểm và thuế. Lương thực
          lãnh của người lao động sẽ thấp hơn lương Gross vì họ sẽ phải trích ra
          một phần để đóng bảo hiểm và nộp thuế thu nhập cá nhân.
        </p>
        <p className="mb-3 text-[14px]">
          <b>Lương Net</b> đ<PERSON><PERSON><PERSON> hiểu đơn giản là số tiền lương chính xác mà
          người lao động sẽ nhận được sau khi đã trừ hết tất cả các khoản chi
          phí bảo hiểm và thuế thu nhập cá nhân.
        </p>
        <div className="example-salary-gross--card lg:max-h[105px] lg:max-w-auto rounded border border-solid border-gray-300 bg-white p-4 text-[14px]">
          <p>
            <strong>
              Ví dụ: Bạn nhận lương gross là 25 triệu VND/tháng, không có người
              phụ thuộc. Mỗi tháng, bạn sẽ bị trừ các khoản sau:
            </strong>
          </p>
          <p>- Đóng bảo hiểm bắt buộc = 10,5% x 25,000,000 = 2,625,000 VNĐ.</p>
          <p>- Thuế TNCN:</p>
          <ul className="my-1 ml-5 list-disc">
            <li>
              Giảm trừ gia cảnh đối với chính bản thân: 11,000,000 đồng/tháng.
            </li>
            <li>
              Thu nhập tính thuế = Thu nhập tính thuế (TNTT) = (Lương Gross –
              Phí bảo hiểm – Các khoản giảm trừ) <br/>= 25,000,000 - 2,625,000 -
              11,000,000 =11375000 VNĐ
            </li>
            <li>Thuế TNCN tính theo bậc 3 : thuế suất 15%</li>
            <li>
              Tổng thuế TNCN = 15% TNTT - 0,75 triệu đồng = 15%*11,365,000 -
              750000 = 956250
            </li>
            <li>
              Lương thực nhận = 25,000,000 - 2,625,000 - 956250 = 21,418,750VNĐ
            </li>
          </ul>

          <p>Theo quy định mới nhất (Từ 1/7/2023)</p>
        </div>
        <div className="example-salary-net--card md:max-h[190px] mt-3 rounded border border-solid border-gray-300 bg-white p-4 text-[14px] md:max-w-[773px]">
          <p>
            <strong>
              {" "}
              Ví dụ: Mức lương Net là 25,000,000 VND, bạn không có người phụ
              thuộc
            </strong>
          </p>
          <p>
            – Nếu bạn đóng bảo hiểm trên mức lương chính thức lương Gross sẽ là
            29,707, 525 VND
          </p>
          <p>– Trong đó:</p>
          <ul className="my-1 ml-5 list-disc">
            <li>Bảo hiểm xã hội chiếm 8% tương đương 2,376,602 VND</li>
            <li>Bảo hiểm y tế chiểm 1.5% tương đương 445,613 VND</li>
            <li>Bảo hiểm thất nghiệp chiểm 1% tương đương 297,075 VND</li>
            <li>Thuế thu nhập cá nhân: 1,588,235 VND</li>
          </ul>
          <p>Theo quy định mới nhất (Từ 1/7/2023)</p>
        </div>
        <div className="calculate--benefit text-[14px]">
          <h2 className="text-secondary mb-3 mt-5 text-xl font-semibold">
            Lợi ích khi bạn biết rõ về Gross/Net
          </h2>
          <p className="mb-3 text-[14px]">
            Khi bạn có sự hiểu biết về lương Gross và lương Net, bạn sẽ dễ dàng
            tránh được những hiểu lầm không đáng có với Công ty/ Doanh nghiệp
            tuyển dụng bạn. Ngoài ra bạn cũng sẽ dễ dàng bảo vệ và đòi hỏi được
            những quyền lợi đáng có của bản thân khi ký hợp đồng lao động.
          </p>
          <p className="mb-3 text-[14px]">
            Khi thỏa thuận dựa trên lương Net bạn sẽ nhận được đúng số tiền mà
            bạn đã thỏa thuận từ trước mà không cần phải tính toán hay chịu bất
            kỳ khoản chi phí nào vì công ty sẽ là người giải quyết những vấn đề
            đó.
          </p>
          <p className="mb-3 text-[14px]">
            Ngược lại, khi thỏa thuận dựa trên lương Gross thì bạn sẽ nhận được
            đầy đủ tất cả các quyền lợi về bảo hiểm, đồng thời bạn cũng có thể
            tự chủ động để tính toán mức lương bạn sẽ nhận được.
          </p>
          <p className="mb-3 text-[14px]">
            Nhìn chung, lương Gross và Net sẽ không làm bạn quá đau đầu khi bạn
            hiểu rõ về nó, nếu bạn hiểu rõ về nó bạn vẫn sẽ nhận được đúng số
            tiền mà bạn đã thỏa thuận dù là lương Gross hay Net
          </p>
          <p className="mb-3 text-[14px]">
            Cách đơn giản hơn hết là bạn tự đặt cho mình 1 mức lương bạn có thể
            chấp nhận được cho vị trí đó. Sau đó bạn chỉ cần lấy kết quả từ tool
            chuyển đổi lương Gross Net của TopDev để deal với công ty. Với cách
            này, bạn chắc chắn sẽ không bao giờ bị “hố” khi deal lương với bất
            kỳ công ty nào!.
          </p>
        </div>
        <div className="income-tax-detail">
          <h2 className="text-secondary text-secondary text-xl font-semibold mb-3">
            Chi tiết thuế thu nhập cá nhân (VNĐ)
          </h2>
          <p className="text-[14px]">
            Cách tính thuế thu nhập cá nhân (VND) Thuế suất thuế thu nhập cá
            nhân đối với thu nhập từ tiền lương được áp dụng theo Biểu thuế lũy
            tiến từng phần, cụ thể như bảng dưới:
          </p>
          <div className="mb-5 overflow-x-auto mt-3">
            <table className="h-full w-full justify-between rounded border border-solid text-left text-[14px] text-sm">
              <thead>
                <tr className="border border-solid bg-[#efefef] text-[14px] font-semibold">
                  <th className="col-span-10 px-6 py-2">Mức chịu thuế </th>
                  <th className="col-span-2  px-6 py-2">Thuế Xuất</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border border-solid text-[14px]">
                  <td className="px-6 py-2">Đến 5 triệu VNĐ</td>
                  <td className="px-6 py-2"> 5%</td>
                </tr>
                <tr className="border-solidtext-[14px] border">
                  <td className="px-6 py-2">
                    Trên 5 triệu VNĐ đến 10 triệu VNĐ
                  </td>
                  <td className="px-6 py-2">10%</td>
                </tr>
                <tr className="border border-solid text-[14px]">
                  <td className="px-6 py-2">
                    Trên 10 triệu VNĐ đến 18 triệu VNĐ
                  </td>
                  <td className="px-6 py-2"> 15%</td>
                </tr>
                <tr className="border border-solid text-[14px]">
                  <td className="px-6 py-2">
                    Trên 18 triệu VNĐ đến 32 triệu VNĐ
                  </td>
                  <td className="px-6 py-2">20%</td>
                </tr>
                <tr className="border border-solid text-[14px]">
                  <td className="px-6 py-2">
                    Trên 32 triệu VNĐ đến 52 triệu VNĐ
                  </td>
                  <td className="px-6 py-2">25%</td>
                </tr>
                <tr className="border border-solid text-[14px]">
                  <td className="px-6 py-2">
                    Trên 52 triệu VNĐ đến 80 triệu VNĐ
                  </td>
                  <td className="px-6 py-2">30%</td>
                </tr>
                <tr className="border border-solid text-[14px]">
                  <td className="px-6 py-2">Trên 80 triệu VNĐ</td>
                  <td className="px-6 py-2">35%</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div className="text-[14px]">
            Cách tính thuế TNCN là tổng số thuế tính theo từng bậc thu nhập. (Số
            thuế tính theo từng bậc thu nhập) = (thu nhập tính thuế của bậc thu
            nhập) x (thuế suất tương ứng của bậc thu nhập đó)
          </div>
        </div>
      </div>
    </section>
  );
};

export default SectionCalculatorDescripton;
