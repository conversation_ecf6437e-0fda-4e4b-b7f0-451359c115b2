"use client";

import React, { useState } from "react";
import SalaryConvertForm from "@/components/LandingPage/SalaryCalculatorTool/SalaryForm/SalaryConvertForm";
import AdvanceDialog from "@/components/LandingPage/SalaryCalculatorTool/Dialog/AdvanceDialog";

const SectionCalculateCard = () => {
  const [isOpenAdvanceDialog, setIsOpenAdvanceDialog] =
    useState<boolean>(false);
  return (
    <section className="section-calculate-card container rounded border bg-white p-5 lg:max-h-[472px]  lg:max-w-[813px]">
      <div className="section-calculate-card--content">
        <h1 className="text-secondary mb-3 text-xl font-semibold">
          Công cụ tính lương Gross - Net chuẩn dành cho Developer
        </h1>
        <p className="mb-3 text-[14px]">
          <PERSON><PERSON> dụng mức giảm trừ gia cảnh mới nhất 11 triệu đồng/ tháng (132 triệu
          đồng/năm) với người nộp thuế và 4.4 triệu đồng/ tháng với mỗi người
          phụ thuộc (theo Nghị định số 954/2020/UBTVQH14)
        </p>
        <p className="mb-3 text-[14px]">
          Áp dụng mức lương tối thiểu vùng mới nhất có hiệu lực từ ngày
          01/07/2024 (Nghị định 73/2024/NĐ-CP)
        </p>
      </div>
      <div className="regulations-apply--card lg:max-h[84px] flex flex-col rounded border border-solid border-gray-300 bg-white p-4 lg:max-w-[773px]">
        <div className="mb-2 flex flex-col text-[14px] font-semibold md:flex md:flex-row">
          <span className="mr-5">Áp dụng quy định: </span>
          <label className="items-center">
            <span>Từ 01/07/2024 </span>
            <small className="ml-1 self-end font-normal text-primary">
              (Mới nhất)
            </small>
          </label>
        </div>
        <div className="flex flex-col  text-[14px] md:flex md:flex-row">
          <p className="mr-3">
            Lương cơ sở:
            <span className="text-primary"> 2,340,000đ</span>
          </p>
          <p className="lg:mr-2">
            Giảm trừ gia cảnh bản thân:
            <span className="text-primary"> 11,000,000đ</span>
          </p>
          <p className="mr-3">
            Người phụ thuộc:
            <span className="text-primary"> 4,400,000đ</span>
          </p>
        </div>
      </div>
      <div className="salary-convert-tool mb-5 bg-white p-5">
        <h2 className="text-secondary mb-3 text-xl font-semibold">
          Tính lương GROSS - NET
        </h2>
        <div className="mb-3 text-[14px]">
          Bảng tính lương GROSS - NET cơ bản.
          <button
            type="button"
            className="font-semibold text-primary underline underline-offset-2 ml-1"
            onClick={() => setIsOpenAdvanceDialog(true)}
          >
            Tính nâng cao
          </button>
          <span className="ml-1">(trong trường hợp mức đóng bảo hiểm khác với mức lương chính thức)</span>
        </div>
        <SalaryConvertForm />
      </div>
      <AdvanceDialog
        open={isOpenAdvanceDialog}
        onClose={() => setIsOpenAdvanceDialog(false)}
      />
    </section>
  );
};

export default SectionCalculateCard;
