"use client";

import { getBlogInSalaryTool } from "@/services/blogAPI";
import { BlogType } from "@/types/blog";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import {
  HiIdentification,
  HiNewspaper,
  HiOutlineClock,
  HiTag,
} from "react-icons/hi2";

const BlogITSection = () => {
  const [posts, setPosts] = useState<BlogType[]>([]);
  const [isClient, setIsClient] = useState<boolean>(false);

  useEffect(() => {
    setIsClient(true);
    getBlogInSalaryTool(10).then((data) => setPosts(data));
  }, []);

  if (posts.length === 0) return <></>;

  return (
    <section className="max-w-[386px container max-h-[auto] rounded-b border bg-white p-5 lg:max-h-[auto]">
      <h2 className="text-secondary mb-3 border-b-2 border-solid pb-2 text-xl font-semibold ">
        <PERSON><PERSON><PERSON><PERSON> sống IT
      </h2>
      <ul>
        {posts.map((post, index) => (
          <li
            key={index}
            className="w-full border-b-2 border-dashed pb-2 text-left"
          >
            <div className="mt-2 flex w-full gap-1">
              <span className="mr-2 mt-2 text-[14px]">
                <HiNewspaper size={16} />
              </span>
              <div className="w-full pr-3">
                <Link
                  target="_blank"
                  href={post.permalink}
                  className="text-[14px]"
                >
                  {post.post_title}
                </Link>
                <div className="mt-1 flex flex-row justify-between text-xs">
                  <p className="flex gap-1 text-[#aaaaaa]">
                    <span className="text-[12px]">
                      <HiOutlineClock size={14} />
                    </span>
                    <span className="text-[12px]">{post.post_date}</span>
                  </p>
                  <p className="flex gap-1 text-[#aaaaaa]">
                    <span className="text-[12px]">
                      <HiIdentification size={14} />
                    </span>
                    <span className="text-[12px]">
                      {post.author.display_name}
                    </span>
                  </p>
                </div>
                <p className="mt-1 flex gap-1 text-[#aaaaaa]">
                  <span className="text-[12px]">
                    <HiTag size={14} />
                  </span>
                  <span className="text-[12px]">
                    {post.terms?.post_tag?.map((value) => value.name + ", ")}
                  </span>
                </p>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </section>
  );
};

export default BlogITSection;
