import { postSalaryConvert } from "@/services/salaryAPI";
import { PostAdvanceSalaryDataType, ResponseApiData } from "@/types/salary";
import { useFormik } from "formik";
import React, { useRef, useState } from "react";
import ExplainDialog from "@/components/LandingPage/SalaryCalculatorTool/Dialog/ExplainDialog";
import { HiArrowRight } from "react-icons/hi2";
import {
  formatSalary,
  replaceAllSalary,
  replaceZeroFirst,
} from "@/utils/SalaryFormat";
import { SchemaSalaryForm } from "@/schemas/SchemaSalaryForm";
import ToastNotification from "@/components/Swal/ToastNotification";
import dynamic from "next/dynamic";
const CaculatorResultDialog = dynamic(
  () =>
    import(
      "@/components/LandingPage/SalaryCalculatorTool/Dialog/CaculatorResultDialog"
    ),
  { ssr: false },
);
interface ICancelProps {
  onCancel: () => void;
}

const SalaryConvertAdvanceForm = ({ onCancel }: ICancelProps) => {
  const VND = "VND";
  // const USD = "USD";

  const formRef = useRef<HTMLFormElement>(null);

  const [isOpenExplainDialog, setIsOpenExplainDialog] =
    useState<boolean>(false);
  const [isOpenCalculatorResultDialog, setIsOpenCalculatorResultDialog] =
    useState<boolean>(false);
  const [isCheckRadio, setIsCheckRadio] = useState("full");
  const [salaryResult, setSalaryResult] = useState<ResponseApiData>();
  const [isCheckSalary, setIsCheckSalary] = useState<string>(VND);

  const initValuesData = {
    salary_area: "v1",
    salary_number: 0,
    salary_option: "1",
    salary_person: 0,
    salary_primary: "full",
    salary_primary_other: 0,
    salary_type: "",
  };

  const { setFieldValue, resetForm, handleSubmit, values } =
    useFormik<PostAdvanceSalaryDataType>({
      initialValues: initValuesData,
      validationSchema: SchemaSalaryForm(),
      onSubmit: async (values: PostAdvanceSalaryDataType) => {
        if (
          values.salary_primary_other === 0 &&
          values.salary_primary === "other"
        ) {
          setIsOpenCalculatorResultDialog(false);
          ToastNotification({
            icon: "error",
            description: "Vui lòng nhập mức đóng bảo hiểm!",
            timer: 2000,
          });
          return;
        } else {
          postSalaryConvert(values)
            .then((res) => {
              setSalaryResult(res.data);
              setIsOpenCalculatorResultDialog(true);
              setIsCheckRadio("full");
            })
            .catch(() => {
              setIsOpenCalculatorResultDialog(false);
              if (
                typeof values.salary_primary_other != "number" &&
                values.salary_primary === "other"
              ) {
                ToastNotification({
                  icon: "error",
                  description: "Vui lòng nhập mức đóng bảo hiểm là số!",
                  timer: 2000,
                });
                return;
              }
              if (typeof values.salary_number != "number") {
                ToastNotification({
                  icon: "error",
                  description: "Vui lòng nhập mức lương là số !",
                  timer: 2000,
                });
                return;
              }
              if (values.salary_number === 0) {
                ToastNotification({
                  icon: "error",
                  description: "Vui lòng nhập mức lương lớn hơn 0!",
                  timer: 2000,
                });
                return;
              } else {
                ToastNotification({
                  icon: "error",
                  description: "Vui lòng nhập đúng định dạng !",
                });
              }
            });
        }

        resetForm({
          values: initValuesData,
        });
      },
    });

  const handleViewResult = (type: "gross_to_net" | "net_to_gross") => {
    setFieldValue("salary_type", type);
    setIsOpenCalculatorResultDialog(true);
  };

  return (
    <div className="max-w-[400px] md:max-w-full max-h-[600px] md:max-h-full">
      <form
        id="form-convert-salary-avanden"
        method="post"
        ref={formRef}
        role="form"
        autoComplete="off"
        onSubmit={handleSubmit}
        className="shadow relative rounded-lg bg-white md:max-w-auto px-2"
      >
        <div className="flex items-center justify-between rounded-t border-b px-5 py-3">
          <h3 className="text-xl font-medium text-gray-900">
            Bảng tính lương mẫu chi tiết
          </h3>
          <button
            type="button"
            className="ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-xs text-gray-400 hover:bg-gray-200 hover:text-gray-900"
            onClick={onCancel}
          >
            <svg
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close modal</span>
          </button>
        </div>
        <div className="space-y-6 p-6 text-sm text-gray-500">
          <div className="flex">
            <div
              className="flex basis-1/5 items-center gap-2"
              onClick={() => setIsCheckSalary(VND)}
            >
              <input
                defaultChecked={isCheckSalary === VND}
                type="radio"
                value={VND}
                id={VND}
              />
              <label htmlFor={VND}>VND</label>
            </div>
            {/* <div
                    className="flex items-center gap-2"
                    onClick={() => setIsCheckSalary(USD)}
                  >
                    <input
                      checked={isCheckSalary === USD}
                      type="radio"
                      value={USD}
                      required
                      onChange={(event) =>
                        setFieldValue("salary_number", event.target.value)
                      }
                      id={USD}
                    />{" "}
                    <label htmlFor={USD}>USD</label>
                  </div> */}
          </div>
          <div className="flex gap-2">
            <label className="basis-1/5">Thu nhập</label>
            <div className="w-full">
              <input
                placeholder="(VNĐ)"
                type="text"
                required
                min={1}
                max={999999999999999}
                value={formatSalary(values.salary_number)}
                onChange={(event) => {
                  const valueSalary = replaceAllSalary(event.target.value);
                  const newVal = replaceZeroFirst(valueSalary);
                  if (newVal.length < 17)
                    setFieldValue("salary_number", newVal);
                }}
                className="mb-2 w-full border-gray-300 px-2 py-1.5 text-sm"
              />
              {/* {isCheckSalary === USD && (
                      <p className="text-sm">
                        {values.salary_number
                          ? (values.salary_number * 23000).toLocaleString(
                              "it-IT",
                              { style: "currency", currency: "VND" },
                            )
                          : 0 + "VND"}{" "}
                      </p>
                    )} */}
            </div>
          </div>
          <div className="lg:flex lg:flex-row">
            <label className="basis-1/5">Mức đóng bảo hiểm</label>
            <div className="w-full">
              <div className="mb-3 flex items-center gap-2">
                <input
                  type="radio"
                  name="insurance"
                  id="full"
                  checked={values.salary_primary === "full"}
                  value="full"
                  onFocus={() => setIsCheckRadio("full")}
                  onChange={() => setFieldValue("salary_primary", "full")}
                />
                <label htmlFor="full">Trên mức lương chính thức</label>
              </div>
              <div className="flex w-full items-center gap-2">
                <input
                  type="radio"
                  name="insurance"
                  id="other"
                  value="other"
                  checked={values.salary_primary === "other"}
                  onFocus={() => setIsCheckRadio("other")}
                  onChange={() => setFieldValue("salary_primary", "other")}
                />
                <label htmlFor="other">Khác</label>
                <input
                  required
                  placeholder="(VNĐ)"
                  type="text"
                  min={1}
                  max={999999999999999}
                  disabled={isCheckRadio !== "other"}
                  value={formatSalary(values.salary_primary_other)}
                  onChange={(event) => {
                    const salaryNumberOther = replaceAllSalary(
                      event.target.value,
                    );
                    const newSalaryNumberOther =
                      replaceZeroFirst(salaryNumberOther);
                    if (newSalaryNumberOther.length < 16) {
                      setFieldValue("salary_primary", "other");
                      setFieldValue(
                        "salary_primary_other",
                        newSalaryNumberOther,
                      );
                    }
                  }}
                  className="w-full grow border-gray-300 px-2 py-1.5 text-sm"
                />
              </div>
            </div>
          </div>
          <div className="gap-2 lg:flex lg:flex-row">
            <label className="basis-1/5">Bảo hiểm xã hội</label>
            <input
              type="text"
              value="8.0%"
              disabled
              className="w-full border-gray-300 px-2 py-1.5 text-right text-sm"
            />
          </div>
          <div className="gap-2 lg:flex lg:flex-row">
            <label className="basis-1/5">Bảo hiểm y tế</label>
            <input
              type="text"
              value="1.5%"
              disabled
              className="w-full border-gray-300 px-2 py-1.5 text-right text-sm"
            />
          </div>
          <div className="gap-2 lg:flex lg:flex-row">
            <label className="basis-1/5">Bảo hiểm thất nghiệp</label>
            <input
              type="text"
              value="1.0%"
              disabled
              className="w-full border-gray-300 px-2 py-1.5 text-right text-sm"
            />
          </div>
          <div className="flex gap-2">
            <label className="flex basis-1/5 items-center gap-1">
              <span>Vùng</span>
              <button
                type="button"
                data-modal-toggle="explain-region-modal"
                className="text-xs text-primary"
                onClick={() => setIsOpenExplainDialog(true)}
              >
                (Giải thích)
              </button>
            </label>
            <ExplainDialog
              open={isOpenExplainDialog}
              onClose={() => setIsOpenExplainDialog(false)}
            />
            <select
              onChange={(event) =>
                setFieldValue("salary_area", event.target.value)
              }
              className="w-full border-gray-300 px-2 py-1.5 text-sm"
            >
              <option value="v1">Vùng 1</option>
              <option value="v2">Vùng 2</option>
              <option value="v3">Vùng 3</option>
              <option value="v4">Vùng 4</option>
            </select>
          </div>
          <div className="flex gap-2">
            <label className="basis-1/5">Giảm trừ cá nhân</label>
            <input
              type="text"
              value="11,000,000 VND"
              disabled
              className="w-full border-gray-300 px-2 py-1.5 text-right text-sm"
            />
          </div>
          <div className="gap-2 lg:flex lg:flex-row">
            <label className="flex basis-1/5 items-center gap-1">
              Số người phụ thuộc
            </label>
            <select
              className="w-full border-gray-300 px-2 py-1.5 text-sm"
              onChange={(event) =>
                setFieldValue("salary_person", parseInt(event.target.value))
              }
            >
              <option value="0">0</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
              <option value="6">6</option>
              <option value="7">7</option>
              <option value="8">8</option>
              <option value="9">9</option>
              <option value="10">10</option>
            </select>
          </div>
          <div className="max-w-auto flex flex-col md:items-center md:justify-end gap-2 text-sm text-gray-500 sm:flex md:flex-row">
            <button
              type="submit"
              id="salary_type"
              value="net_to_gross"
              onClick={() => handleViewResult("gross_to_net")}
              className="flex border border-solid border-primary bg-primary px-4 py-2.5 text-white hover:opacity-80"
            >
              Gross
              <span className="font-[900]">
                <HiArrowRight size={20} />
              </span>
              Net
            </button>
            <button
              type="submit"
              id="salary_type"
              value="gross_to_net"
              onClick={() => handleViewResult("net_to_gross")}
              className="flex border border-solid border-primary px-4 py-2.5 text-primary hover:bg-primary hover:text-white hover:opacity-80"
            >
              Net
              <span className="font-[900]">
                <HiArrowRight size={20} />
              </span>
              Gross
            </button>

            <CaculatorResultDialog
              open={isOpenCalculatorResultDialog}
              onClose={() => setIsOpenCalculatorResultDialog(false)}
              grossVnd={salaryResult?.data?.calculate_values.grossVnd ?? 0}
              bhxhVnd={salaryResult?.data?.calculate_values.bhxhVnd ?? 0}
              bhytVnd={salaryResult?.data?.calculate_values.bhytVnd ?? 0}
              bhtnVnd={salaryResult?.data?.calculate_values.bhtnVnd ?? 0}
              thuNhapTruocThueVnd={
                salaryResult?.data?.calculate_values.thuNhapTruocThueVnd ?? 0
              }
              giamTruBanThanVnd={
                salaryResult?.data?.calculate_values.giamTruBanThanVnd ?? 0
              }
              giamTruPhuThuocVnd={
                salaryResult?.data?.calculate_values.giamTruPhuThuocVnd ?? 0
              }
              thuNhapChiuThueVnd={
                salaryResult?.data?.calculate_values.thuNhapChiuThueVnd ?? 0
              }
              thueTncnVnd={
                salaryResult?.data?.calculate_values.thueTncnVnd ?? 0
              }
              luongNet={salaryResult?.data?.calculate_values.netVnd ?? 0}
            />

            <button
              type="button"
              className="border border-solid border-b-gray-200 bg-gray-200 px-10 py-2 hover:opacity-80"
              onClick={onCancel}
            >
              Hủy
            </button>
          </div>
        </div>
        <div className="space-x-2 rounded-b border-t border-gray-200 px-6 py-3 text-sm">
          <p className="ml-2 text-primary">Ghi chú</p>
          <p>Kết quả có thể sai lệch, chỉ là ước tính tạm thời</p>
          <p>Đơn vị tiền tệ tính bằng VND (Việt Nam Đồng)</p>
        </div>
      </form>
    </div>
  );
};

export default SalaryConvertAdvanceForm;
