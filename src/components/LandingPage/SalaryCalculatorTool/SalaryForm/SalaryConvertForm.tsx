"use client";

import { useFormik } from "formik";
import React, { useRef, useState } from "react";
import {
  HiArrowRight,
  HiOutlineCurrencyDollar,
  HiOutlineUserGroup,
} from "react-icons/hi2";
import { PostSalaryDataType, ResponseApiData } from "@/types/salary";
import { postSalaryConvert } from "@/services/salaryAPI";
import { SchemaSalaryForm } from "@/schemas/SchemaSalaryForm";
import ToastNotification from "@/components/Swal/ToastNotification";
import {
  formatSalary,
  replaceAllSalary,
  replaceZeroFirst,
} from "@/utils/SalaryFormat";
import dynamic from "next/dynamic";
const CaculatorResultDialog = dynamic(
  () =>
    import(
      "@/components/LandingPage/SalaryCalculatorTool/Dialog/CaculatorResultDialog"
    ),
  { ssr: false },
);
const SalaryConvertForm = () => {
  const formRef = useRef<HTMLFormElement>(null);
  const [isOpenCalculatorResultDialog, setIsOpenCalculatorResultDialog] =
    useState<boolean>(false);
  const [salaryResult, setSalaryResult] = useState<ResponseApiData>();
  const initValuesDataNormal = {
    salary_area: "v1",
    salary_number: 0,
    salary_option: "1",
    salary_person: 0,
    salary_primary: "full",
    salary_primary_other: 0,
    salary_type: "",
  };
  const {
    setFieldValue,
    resetForm,
    handleSubmit,
    values: valuesForm,
    errors: errorForm,
    touched,
  } = useFormik<PostSalaryDataType>({
    initialValues: initValuesDataNormal,
    validationSchema: SchemaSalaryForm(),
    onSubmit: async (values: PostSalaryDataType) => {
      postSalaryConvert(values)
        .then((res) => {
          setSalaryResult(res.data);
          setIsOpenCalculatorResultDialog(true);
        })
        .catch(() => {
          setIsOpenCalculatorResultDialog(false);
          if (values.salary_number === 0) {
            ToastNotification({
              icon: "error",
              description: "Vui lòng nhập mức lương lớn hơn 0!",
              timer: 2000,
            });
            return;
          }
          if (typeof values.salary_number != "number") {
            ToastNotification({
              icon: "error",
              description: "Vui lòng nhập mức lương là số !",
              timer: 2000,
            });
            return;
          }
        });
      resetForm({
        values: initValuesDataNormal,
      });
    },
  });
  const handleViewResult = (type: "gross_to_net" | "net_to_gross") => {
    setFieldValue("salary_type", type);
    setIsOpenCalculatorResultDialog(true);
  };
  return (
    <section>
      <form
        className="salary-convert-tool--input flex flex-col items-start justify-between gap-3 text-[14px] lg:flex-row lg:items-end"
        ref={formRef}
        id="form-convert-salary"
        method="post"
        role="form"
        autoComplete="off"
        onSubmit={handleSubmit}
      >
        <div className="z-[1] w-full grow lg:w-1/3">
          <label className="font-semibold">Thu nhập</label>{" "}
          {errorForm.salary_number && touched.salary_number && (
            <span className="text-red-500">{errorForm.salary_number}</span>
          )}
          <div className="relative z-10 mt-1">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span>
                <HiOutlineCurrencyDollar />
              </span>
            </div>
            <input
              placeholder="(VNĐ)"
              type="text"
              aria-required
              min={1}
              max={999999999999999}
              id="salary_number"
              value={formatSalary(valuesForm.salary_number)}
              onChange={(event) => {
                const valueSalary = replaceAllSalary(event.target.value);
                const newVal = replaceZeroFirst(valueSalary);
                if (newVal.length < 17) setFieldValue("salary_number", newVal);
              }}
              className="w-full border border-gray-300 py-2 pl-10 pr-3 text-right focus:border-gray-300"
            />
          </div>
        </div>
        <div className="z-[1] w-full grow lg:w-1/3">
          <label className="font-semibold">Số người phụ thuộc</label>{" "}
          <div className="relative z-10 mt-1">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <span>
                <HiOutlineUserGroup />
              </span>
            </div>{" "}
            <input
              type="tel"
              id="salary_person"
              min={0}
              max={10}
              value={valuesForm.salary_person}
              onChange={(e) => {
                const inputSalaryPerson = Number(e.target.value);
                if (inputSalaryPerson < 11) {
                  setFieldValue("salary_person", inputSalaryPerson);
                }
              }}
              placeholder="(Người)"
              className="w-full border border-gray-300 py-2 pl-10 pr-3 text-right focus:border-gray-300"
            />
            {errorForm.salary_person && touched.salary_person && (
              <div className="text-red-500">{errorForm.salary_person}</div>
            )}
          </div>
        </div>
        <div className="flex lg:flex lg:w-1/3 ">
          <button
            type="submit"
            id="salary_type_gross_to_net"
            onClick={() => handleViewResult("gross_to_net")}
            className="mr-2 flex border border-solid border-primary bg-primary px-4 py-2.5 text-white hover:opacity-80"
          >
            Gross
            <span className="font-[900]">
              <HiArrowRight size={20} />{" "}
            </span>
            Net
          </button>
          <button
            type="submit"
            id="salary_type_net_to_gross"
            onClick={() => handleViewResult("net_to_gross")}
            className="flex border border-solid border-primary px-4 py-2.5 text-primary hover:bg-primary hover:text-white hover:opacity-80"
          >
            Net{" "}
            <span className="font-[900]">
              <HiArrowRight size={20} />{" "}
            </span>
            Gross
          </button>

          <CaculatorResultDialog
            open={isOpenCalculatorResultDialog}
            onClose={() => setIsOpenCalculatorResultDialog(false)}
            grossVnd={salaryResult?.data?.calculate_values.grossVnd ?? 0}
            bhxhVnd={salaryResult?.data?.calculate_values.bhxhVnd ?? 0}
            bhytVnd={salaryResult?.data?.calculate_values.bhytVnd ?? 0}
            bhtnVnd={salaryResult?.data?.calculate_values.bhtnVnd ?? 0}
            thuNhapTruocThueVnd={
              salaryResult?.data?.calculate_values.thuNhapTruocThueVnd ?? 0
            }
            giamTruBanThanVnd={
              salaryResult?.data?.calculate_values.giamTruBanThanVnd ?? 0
            }
            giamTruPhuThuocVnd={
              salaryResult?.data?.calculate_values.giamTruPhuThuocVnd ?? 0
            }
            thuNhapChiuThueVnd={
              salaryResult?.data?.calculate_values.thuNhapChiuThueVnd ?? 0
            }
            thueTncnVnd={salaryResult?.data?.calculate_values.thueTncnVnd ?? 0}
            luongNet={salaryResult?.data?.calculate_values.netVnd ?? 0}
          />
        </div>
      </form>
    </section>
  );
};

export default SalaryConvertForm;
