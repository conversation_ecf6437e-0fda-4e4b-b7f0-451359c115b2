import { CaculatorResultSalaryProps } from "@/types/salary";
import { formatPriceVND } from "@/utils";
import { Modal } from "flowbite-react";
import React, { FC } from "react";

interface CalculatorResultDialogPops extends CaculatorResultSalaryProps {
  open: boolean;
  onClose: () => void;
}

const CaculatorResultDialog: FC<CalculatorResultDialogPops> = ({
  open,
  onClose,
  grossVnd,
  bhxhVnd,
  bhytVnd,
  bhtnVnd,
  thuNhapTruocThueVnd,
  giamTruBanThanVnd,
  giamTruPhuThuocVnd,
  thuNhapChiuThueVnd,
  thueTncnVnd,
  luongNet,
}) => {
  return (
    <>
      <Modal
        show={open}
        onClose={() => (onClose ? onClose() : "")}
        className="fixed left-0 right-0 top-0 z-[200] flex h-modal w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0 md:h-full"
      >
        <Modal.Body className="shadow relative rounded-lg bg-white">
          <div className="flex items-center justify-between rounded-t border-b px-5 py-3">
            <h3 className="text-xl font-medium text-gray-900">
              Chi tiết tính lương Gross
            </h3>
            <button
              onClick={onClose}
              type="button"
              className="ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-xs text-gray-400 hover:bg-gray-200 hover:text-gray-900"
            >
              <svg
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
              >
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                ></path>
              </svg>
              <span className="sr-only">Close modal</span>
            </button>
          </div>
          <div className="p-6 text-sm text-gray-500">
            <table className="w-full rounded border text-left text-[14px]">
              <thead>
                <tr className="font-semibold">
                  <th scope="col" className="px-6 py-2">
                    Tên
                  </th>
                  <th scope="col" className="px-6 py-2 text-right">
                    Giá trị
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr className="bg-bgGray border-b text-primary">
                  <td className="px-6 py-2">Lương Gross</td>
                  <td className="px-6 py-2 text-right">
                    {formatPriceVND(grossVnd)}
                  </td>
                </tr>
                <tr className="border-b bg-white">
                  <td className="px-6 py-2">BHXH (8%)</td>
                  <td className="px-6 py-2 text-right">
                    - {formatPriceVND(bhxhVnd)}
                  </td>
                </tr>
                <tr className="border-b bg-white">
                  <td className="px-6 py-2">BHYT (1.5%)</td>
                  <td className="px-6 py-2 text-right">
                    - {formatPriceVND(bhytVnd)}
                  </td>
                </tr>
                <tr className="border-b bg-white">
                  <td className="px-6 py-2">BHTN (1%)</td>
                  <td className="px-6 py-2 text-right">
                    - {formatPriceVND(bhtnVnd)}
                  </td>
                </tr>
                <tr className="bg-bgGray border-b">
                  <td className="px-6 py-2">Thu nhập trước thuế</td>
                  <td className="px-6 py-2 text-right">
                    {formatPriceVND(thuNhapTruocThueVnd)}
                  </td>
                </tr>
                <tr className="border-b bg-white">
                  <td className="px-6 py-2">Giảm trừ gia cảnh cá nhân</td>
                  <td className="px-6 py-2 text-right">
                    - {formatPriceVND(giamTruBanThanVnd)}
                  </td>
                </tr>
                <tr className="border-b bg-white">
                  <td className="px-6 py-2">
                    Giảm trừ gia cảnh người phụ thuộc
                  </td>
                  <td className="px-6 py-2 text-right">
                    - {formatPriceVND(giamTruPhuThuocVnd)}
                  </td>
                </tr>
                <tr className="bg-bgGray border-b">
                  <td className="px-6 py-2">Thu nhập chịu thuế</td>
                  <td className="px-6 py-2 text-right">
                    {formatPriceVND(thuNhapChiuThueVnd)}
                  </td>
                </tr>
                <tr className="border-b bg-white">
                  <td className="px-6 py-2">Thuế thu nhập cá nhân</td>
                  <td className="px-6 py-2 text-right">
                    - {formatPriceVND(thueTncnVnd)}
                  </td>
                </tr>
                <tr className="bg-bgGray border-b text-primary">
                  <td className="px-6 py-2">
                    <p>Lương NET</p>
                    <small className="text-primary">
                      (Thu nhập trước thuế - Thuế thu nhập cá nhân)
                    </small>
                  </td>
                  <td className="px-6 py-2 text-right">
                    {formatPriceVND(luongNet)}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default CaculatorResultDialog;
