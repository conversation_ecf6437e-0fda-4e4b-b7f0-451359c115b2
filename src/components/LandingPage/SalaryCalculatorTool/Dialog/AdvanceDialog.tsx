import React, { <PERSON> } from "react";
import { Modal } from "flowbite-react";

import SalaryConvertAdvanceForm from "@/components/LandingPage/SalaryCalculatorTool/SalaryForm/SalaryConvertAdvanceForm";
interface PropsAdvanceDialogType {
  open: boolean;
  onClose: () => void;
}
const AdvanceDialog: FC<PropsAdvanceDialogType> = ({ open, onClose }) => {
  return (
    <>
      <Modal show={open} onClose={() => (onClose ? onClose() : "")}>
        <Modal.Body
          id="enhancement-modal"
          tabIndex={-1}
          className="fixed left-0 right-0 top-0 z-[200] flex w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0 lg:h-modal"
          aria-modal="true"
          role="dialog"
        >
          <div className="relative p-2 lg:w-full lg:max-w-xl">
            <SalaryConvertAdvanceForm onCancel={onClose} />
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default AdvanceDialog;
