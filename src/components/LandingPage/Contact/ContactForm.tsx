"use client";

import { PostContactData } from "@/app/[locale]/(landing-page)/contact/types";
import SchemaContactForm from "@/schemas/SchemaContactForm";
import { postContactForm } from "@/services/contactAPI";
import { useFormik } from "formik";
import { useTranslations } from "next-intl";
import React, { useRef } from "react";
import { useState } from "react";
import ReCAP<PERSON><PERSON> from "react-google-recaptcha";

const ContactForm = () => {
  const [message, setMessage] = useState<string>("");
  const t = useTranslations();
  const formRef = useRef<HTMLFormElement>(null);
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
  const enable = process.env.NEXT_PUBLIC_RECAPTCHA_ENABLE;
  const {
    setFieldValue,
    setSubmitting,
    setFieldError,
    resetForm,
    handleSubmit,
    isSubmitting,
    values: valuesForm,
    errors: errorForm,
    touched: touchedForm,
  } = useFormik<PostContactData>({
    initialValues: {
      fullname: "",
      company: "",
      email: "",
      phone: "",
      message: "",
      token: "",
    },
    validationSchema: SchemaContactForm(t),
    onSubmit: async (values: PostContactData) => {
      setSubmitting(true);
      const token = await recaptchaRef?.current?.executeAsync();
      values.token = token || "";
      postContactForm(values)
        .then((res) => {
          if (res.message) {
            setMessage(res.message);
          }
        })
        .catch((reason) => {
          if (reason.message) {
            setMessage(reason.message);
          }
          if (reason.error) {
            setFieldError("fullname", reason?.error?.fullname ?? "");
            setFieldError("company", reason?.error?.company ?? "");
            setFieldError("phone", reason?.error?.phone ?? "");
            setFieldError("email", reason?.error?.email ?? "");
            setFieldError("message", reason?.error?.message ?? "");
          }
        })
        .finally(() => {
          resetForm();
          setSubmitting(false);
        });
    },
  });

  return (
    <>
      <form
        ref={formRef}
        id="form-contact"
        onSubmit={handleSubmit}
        method="post"
        role="form"
        className="mt-2"
        autoComplete="off"
      >
        {siteKey && enable ? (
          <ReCAPTCHA ref={recaptchaRef} size="invisible" sitekey={siteKey} />
        ) : (
          <></>
        )}
        <div className="mt-5">
          <label htmlFor="fullname" className="mb-1 inline-block">
            Full name(*)
          </label>
          <input
            type="text"
            name="fullname"
            id="fullname"
            className="w-full border border-solid border-gray-600"
            placeholder="Your Full Name"
            required
            value={valuesForm.fullname}
            onChange={(e) => setFieldValue("fullname", e.target.value)}
          />
          {errorForm.fullname && touchedForm.fullname && (
            <p className="text-sm italic text-red-600" v-if="">
              (*) {errorForm.fullname ?? ""}
            </p>
          )}
        </div>
        <div className="mt-5">
          <label htmlFor="company" className="mb-1 inline-block">
            Company(*)
          </label>
          <input
            type="text"
            name="company"
            id="company"
            className="w-full border border-solid border-gray-600"
            placeholder="Your Company"
            required
            value={valuesForm.company}
            onChange={(e) => setFieldValue("company", e.target.value)}
          />
          {errorForm.company && touchedForm.company && (
            <p className="text-sm italic text-red-600" v-if="">
              (*) {errorForm.company ?? ""}
            </p>
          )}
        </div>
        <div className="mt-5">
          <label htmlFor="phone" className="mb-1 inline-block">
            Phone(*)
          </label>
          <input
            type="text"
            name="phone"
            id="phone"
            className="w-full border border-solid border-gray-600"
            placeholder="Your Phone Number"
            required
            value={valuesForm.phone}
            onChange={(e) => setFieldValue("phone", e.target.value)}
          />
          {errorForm.phone && touchedForm.phone && (
            <p className="text-sm italic text-red-600" v-if="">
              (*) {errorForm.phone ?? ""}
            </p>
          )}
        </div>
        <div className="mt-5">
          <label htmlFor="email" className="mb-1 inline-block">
            Email(*)
          </label>
          <input
            type="text"
            name="email"
            id="email"
            className="w-full border border-solid border-gray-600"
            placeholder="Your Email"
            required
            value={valuesForm.email}
            onChange={(e) => setFieldValue("email", e.target.value)}
          />
          {errorForm.email && touchedForm.email && (
            <p className="text-sm italic text-red-600" v-if="">
              (*) {errorForm.email ?? ""}
            </p>
          )}
        </div>
        <div className="mt-5">
          <label htmlFor="message" className="mb-1 inline-block">
            Message(*)
          </label>
          <textarea
            placeholder="Write Your Message Here"
            name="message"
            id="message"
            cols={30}
            rows={5}
            className="w-full"
            required
            value={valuesForm.message}
            onChange={(e) => setFieldValue("message", e.target.value)}
          ></textarea>
          {errorForm.message && touchedForm.message && (
            <p className="text-sm italic text-red-600" v-if="">
              (*) {errorForm.message ?? ""}
            </p>
          )}
        </div>
        <p className="pt-5 text-blue-600" v-if="message">
          {message}
        </p>
        <div className="mt-5">
          <button
            form="form-contact"
            type="submit"
            className={
              "w-full border border-solid border-primary bg-primary py-2 text-white transition-all hover:bg-transparent hover:text-primary hover:shadow-md" +
              (isSubmitting
                ? " bg-gray-600 hover:border-gray-600 hover:bg-gray-600 hover:text-white"
                : "")
            }
          >
            {isSubmitting ? "Please wait..." : "Submit"}
          </button>
        </div>
      </form>
    </>
  );
};

export default ContactForm;
