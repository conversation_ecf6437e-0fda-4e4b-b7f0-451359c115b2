const Benefit = () => (
  <svg
    width="9"
    height="8"
    viewBox="0 0 9 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_9215_6181)">
      <path
        d="M1.74948 5.50002C1.80255 5.49995 1.85428 5.51677 1.89715 5.54805C1.94003 5.57934 1.97184 5.62345 1.98798 5.67402L2.11598 6.07252C2.14071 6.14589 2.18197 6.2126 2.23659 6.26748C2.29121 6.32236 2.35772 6.36394 2.43098 6.38902L2.82548 6.51302C2.87625 6.52892 2.92063 6.5606 2.95216 6.60346C2.98369 6.64632 3.00072 6.69813 3.00078 6.75134C3.00083 6.80454 2.98391 6.85638 2.95247 6.89931C2.92103 6.94224 2.87672 6.97401 2.82598 6.99002L2.43098 7.11602C2.35754 7.14075 2.29085 7.18222 2.23618 7.23715C2.18151 7.29208 2.14036 7.35896 2.11598 7.43252L1.98998 7.82602C1.97396 7.87618 1.94252 7.92 1.90013 7.95124C1.85775 7.98248 1.80658 7.99953 1.75392 7.99998C1.70127 8.00042 1.64982 7.98423 1.60692 7.95371C1.56401 7.92319 1.53183 7.8799 1.51498 7.83002L1.38198 7.43502C1.35667 7.36114 1.31488 7.29398 1.25979 7.23863C1.2047 7.18327 1.13774 7.14118 1.06398 7.11552L0.673477 6.98952C0.623425 6.97318 0.579808 6.94146 0.548843 6.89888C0.517879 6.8563 0.501149 6.80503 0.501038 6.75238C0.500927 6.69973 0.517441 6.64839 0.548226 6.60568C0.579011 6.56297 0.622494 6.53106 0.672477 6.51452L1.06948 6.38452C1.14316 6.35945 1.21012 6.31784 1.26524 6.2629C1.32036 6.20795 1.36217 6.14112 1.38748 6.06752L1.51148 5.67452C1.52752 5.62386 1.5593 5.57963 1.60218 5.54826C1.64507 5.51688 1.69634 5.49998 1.74948 5.50002ZM0.999977 3.75652C0.999738 3.65082 1.033 3.54777 1.09498 3.46216C1.15697 3.37655 1.24449 3.31278 1.34498 3.28002L2.63648 2.85802C2.92209 2.76097 3.18168 2.59978 3.39532 2.38683C3.60897 2.17388 3.771 1.91481 3.86898 1.62952L4.27298 0.349518C4.30492 0.248306 4.36824 0.159884 4.45377 0.0970482C4.5393 0.0342126 4.64261 0.000223835 4.74875 1.10172e-06C4.85488 -0.000221632 4.95833 0.0333332 5.04412 0.0958093C5.12992 0.158285 5.19361 0.246441 5.22598 0.347518L5.64198 1.64652C5.73767 1.93146 5.89793 2.19043 6.11026 2.40319C6.32259 2.61595 6.58123 2.77675 6.86598 2.87302L8.15048 3.27852C8.25169 3.31046 8.34011 3.37378 8.40295 3.45931C8.46578 3.54485 8.49977 3.64815 8.49999 3.75429C8.50022 3.86042 8.46666 3.96387 8.40419 4.04967C8.34171 4.13546 8.25355 4.19915 8.15248 4.23152L6.86648 4.64302C6.58158 4.73927 6.32281 4.90012 6.11039 5.11297C5.89797 5.32583 5.73765 5.58493 5.64198 5.87002L5.23198 7.15252C5.19984 7.2528 5.13686 7.34038 5.05202 7.40276C4.96718 7.46514 4.86481 7.49915 4.75951 7.49992C4.65421 7.5007 4.55135 7.46821 4.4656 7.40708C4.37985 7.34596 4.31559 7.25932 4.28198 7.15952L3.84898 5.87602V5.87502C3.75128 5.58962 3.58981 5.33025 3.37686 5.1166C3.1639 4.90295 2.90505 4.74065 2.61998 4.64202L1.34648 4.23152C1.24595 4.19909 1.15828 4.13566 1.09603 4.05033C1.03378 3.965 1.00016 3.86214 0.999977 3.75652Z"
        fill="#F0F5FE"
      />
    </g>
    <defs>
      <clipPath id="clip0_9215_6181">
        <rect width="8" height="8" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </svg>
);
export default Benefit;
