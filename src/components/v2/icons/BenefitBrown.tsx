const BenefitBrown = () => (
  <svg
    width="11"
    height="10"
    viewBox="0 0 11 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.06189 6.87502C2.12824 6.87494 2.19289 6.89597 2.24649 6.93507C2.30009 6.97417 2.33985 7.02932 2.36002 7.09252L2.52002 7.59065C2.55093 7.68237 2.60251 7.76575 2.67079 7.83435C2.73906 7.90295 2.8222 7.95493 2.91377 7.98627L3.40689 8.14127C3.47036 8.16115 3.52584 8.20075 3.56525 8.25433C3.60466 8.30791 3.62595 8.37266 3.62602 8.43917C3.62609 8.50568 3.60493 8.57048 3.56564 8.62414C3.52634 8.67779 3.47095 8.71752 3.40752 8.73752L2.91377 8.89502C2.82197 8.92594 2.73861 8.97778 2.67027 9.04644C2.60194 9.1151 2.5505 9.1987 2.52002 9.29065L2.36252 9.78252C2.3425 9.84522 2.3032 9.9 2.25021 9.93905C2.19723 9.9781 2.13327 9.99942 2.06745 9.99997C2.00164 10.0005 1.93733 9.98028 1.88369 9.94213C1.83006 9.90399 1.78984 9.84988 1.76877 9.78752L1.60252 9.29377C1.57088 9.20142 1.51865 9.11747 1.44978 9.04828C1.38092 8.97909 1.29722 8.92647 1.20502 8.8944L0.716892 8.7369C0.654327 8.71648 0.599805 8.67683 0.5611 8.6236C0.522395 8.57038 0.501482 8.50629 0.501343 8.44048C0.501205 8.37466 0.521848 8.31049 0.560328 8.2571C0.598809 8.20371 0.653163 8.16383 0.715642 8.14315L1.21189 7.98065C1.30399 7.94931 1.3877 7.89731 1.4566 7.82862C1.5255 7.75994 1.57776 7.6764 1.60939 7.5844L1.76439 7.09315C1.78445 7.02983 1.82417 6.97454 1.87777 6.93532C1.93138 6.8961 1.99547 6.87498 2.06189 6.87502ZM1.12502 4.69565C1.12472 4.56353 1.16629 4.43472 1.24378 4.3277C1.32126 4.22069 1.43066 4.14098 1.55627 4.10002L3.17064 3.57252C3.52766 3.45121 3.85214 3.24973 4.1192 2.98354C4.38626 2.71735 4.58879 2.39352 4.71127 2.0369L5.21627 0.436898C5.25619 0.310383 5.33534 0.199855 5.44226 0.12131C5.54918 0.0427658 5.67831 0.000279794 5.81098 1.37716e-06C5.94364 -0.00027704 6.07296 0.0416665 6.1802 0.119762C6.28745 0.197857 6.36706 0.308052 6.40752 0.434398L6.92752 2.05815C7.04713 2.41433 7.24746 2.73804 7.51287 3.00399C7.77828 3.26994 8.10158 3.47093 8.45752 3.59127L10.0631 4.09815C10.1897 4.13808 10.3002 4.21723 10.3787 4.32414C10.4573 4.43106 10.4998 4.56019 10.5 4.69286C10.5003 4.82552 10.4584 4.95484 10.3803 5.06208C10.3022 5.16933 10.192 5.24894 10.0656 5.2894L8.45814 5.80377C8.10202 5.92409 7.77856 6.12515 7.51303 6.39122C7.2475 6.65728 7.04711 6.98116 6.92752 7.33752L6.41502 8.94065C6.37485 9.066 6.29613 9.17547 6.19008 9.25345C6.08403 9.33143 5.95606 9.37393 5.82443 9.3749C5.69281 9.37587 5.56423 9.33526 5.45704 9.25885C5.34986 9.18245 5.26953 9.07415 5.22752 8.9494L4.68627 7.34502V7.34377C4.56414 6.98703 4.36231 6.66281 4.09612 6.39575C3.82992 6.12869 3.50636 5.92581 3.15002 5.80252L1.55814 5.2894C1.43249 5.24887 1.3229 5.16958 1.24509 5.06291C1.16727 4.95625 1.12524 4.82768 1.12502 4.69565Z"
      fill="#9E370E"
    />
  </svg>
);
export default BenefitBrown;
