const Robot = () => (
  <svg
    width="100%"
    height="100%"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.17915 0.0541639C6.83373 0.140601 6.57515 0.288204 6.31469 0.547679C5.60228 1.25743 5.58168 2.34472 6.26641 3.09603C6.38247 3.22333 6.56916 3.36564 6.72776 3.44764L6.99425 3.58544L6.99438 4.2931L6.99454 5.00076H6.81194C6.32032 5.00076 5.85146 5.19718 5.49422 5.55277C5.0926 5.95259 4.94336 6.36316 4.94336 7.0684V7.45004L4.5844 7.47342C3.63765 7.53512 2.87974 8.10798 2.55963 9.00385C2.49461 9.18583 2.46753 9.38213 2.45166 9.78691L2.43045 10.3271L1.74745 10.3407C1.07162 10.3542 1.06161 10.3557 0.793029 10.4881C0.477147 10.6437 0.26009 10.8663 0.108097 11.1904L0 11.421V13.4106V15.4003L0.108097 15.6308C0.26009 15.9549 0.477147 16.1775 0.793029 16.3331C1.06161 16.4655 1.07162 16.467 1.74745 16.4805L2.43045 16.4941L2.4508 17.0343C2.46741 17.4745 2.49002 17.6252 2.57297 17.8485C2.82042 18.5147 3.29371 18.9839 3.98258 19.246L4.22544 19.3383L8.0948 19.3499L11.9642 19.3615L13.2433 20.0929C14.7838 20.9739 14.8342 21 14.9942 21C15.1977 21 15.4341 20.8549 15.5269 20.6729C15.598 20.5336 15.6095 20.4313 15.6095 19.9403V19.3696L16.1736 19.3485C16.6045 19.3324 16.795 19.307 16.9805 19.2405C17.6537 18.9995 18.1415 18.5177 18.3901 17.8485C18.4731 17.6252 18.4957 17.4745 18.5123 17.0343L18.5327 16.4941L19.2157 16.4805C19.8915 16.467 19.9015 16.4655 20.1701 16.3331C20.486 16.1775 20.703 15.9549 20.855 15.6308L20.9631 15.4003V13.4098V11.4192L20.8294 11.1476C20.6738 10.8315 20.4513 10.6145 20.127 10.4624C19.912 10.3617 19.8505 10.3534 19.2146 10.3407L18.5327 10.3271L18.5114 9.78691C18.4956 9.38213 18.4685 9.18583 18.4035 9.00385C18.0834 8.10798 17.3255 7.53512 16.3787 7.47342L16.0198 7.45004V7.0684C16.0198 6.36898 15.869 5.95468 15.4677 5.55163C15.1128 5.19513 14.6483 5.00076 14.1512 5.00076H13.9686V4.29253V3.58429L14.1839 3.48358C14.4499 3.3592 14.8588 2.95581 14.9887 2.68965C15.356 1.93682 15.2274 1.12948 14.6473 0.546735C13.9226 -0.18123 12.79 -0.180738 12.058 0.547884C11.3456 1.25706 11.325 2.34472 12.0097 3.09603C12.1258 3.22333 12.3125 3.36564 12.4711 3.44764L12.7376 3.58544L12.7377 4.2931L12.7379 5.00076H10.4816H8.22525V4.29253V3.58429L8.44063 3.48358C8.70662 3.3592 9.11551 2.95581 9.24539 2.68965C9.61267 1.93682 9.48406 1.12948 8.90395 0.546735C8.44132 0.0819779 7.8 -0.101152 7.17915 0.0541639ZM7.89813 1.30916C8.08113 1.40253 8.22525 1.63862 8.22525 1.84497C8.22525 2.14818 7.91306 2.45729 7.60686 2.45729C7.41606 2.45729 7.16261 2.29775 7.07236 2.12081C6.92086 1.82384 7.01595 1.49963 7.30603 1.32401C7.50245 1.20512 7.68517 1.20053 7.89813 1.30916ZM13.6414 1.30916C13.8245 1.40253 13.9686 1.63862 13.9686 1.84497C13.9686 2.14818 13.6564 2.45729 13.3502 2.45729C13.1594 2.45729 12.9059 2.29775 12.8157 2.12081C12.6642 1.82384 12.7593 1.49963 13.0493 1.32401C13.2458 1.20512 13.4285 1.20053 13.6414 1.30916ZM14.4619 6.31405C14.7068 6.43901 14.789 6.63436 14.789 7.09133V7.46218H10.4816H6.17407L6.17579 7.08271C6.1778 6.63391 6.24274 6.47589 6.48556 6.3289L6.64584 6.23188L10.4729 6.23168C14.2278 6.23147 14.3031 6.23303 14.4619 6.31405ZM16.6148 8.76083C16.7327 8.80103 16.8839 8.89854 16.9849 8.9995C17.3078 9.32244 17.2915 9.08725 17.2915 13.4106C17.2915 17.734 17.3078 17.4988 16.9849 17.8217C16.7166 18.09 16.5488 18.1283 15.6416 18.1283C14.9475 18.1283 14.8512 18.1368 14.7062 18.2108C14.4751 18.3287 14.4032 18.4881 14.3788 18.9363L14.3583 19.3134L13.4968 18.8195C13.023 18.5478 12.5482 18.2812 12.4418 18.227L12.2483 18.1283H8.398C4.06496 18.1283 4.30121 18.1447 3.97823 17.8217C3.65529 17.4988 3.67162 17.734 3.67162 13.4106C3.67162 9.0861 3.65517 9.32257 3.97897 8.99876C4.30096 8.67673 3.80757 8.69974 10.464 8.69613C15.9012 8.69318 16.4326 8.69876 16.6148 8.76083ZM7.17915 9.9819C6.83373 10.0683 6.57515 10.2159 6.31469 10.4754C5.96956 10.8193 5.76383 11.2963 5.76383 11.7528C5.76383 12.0813 6.05945 12.385 6.37918 12.385C6.44868 12.385 6.5784 12.3479 6.66742 12.3024C6.84944 12.2096 6.99454 11.9732 6.99454 11.7697C6.99454 11.4664 7.30509 11.1543 7.60686 11.1543C7.92196 11.1543 8.22525 11.4561 8.22525 11.7697C8.22525 12.0818 8.52846 12.385 8.84061 12.385C9.04417 12.385 9.2805 12.2399 9.37338 12.0579C9.57895 11.6549 9.36686 10.9395 8.90395 10.4745C8.44132 10.0097 7.8 9.82658 7.17915 9.9819ZM12.9225 9.9819C12.577 10.0683 12.3185 10.2159 12.058 10.4754C11.7129 10.8193 11.5071 11.2963 11.5071 11.7528C11.5071 12.0813 11.8028 12.385 12.1225 12.385C12.4347 12.385 12.7379 12.0818 12.7379 11.7697C12.7379 11.4664 13.0484 11.1543 13.3502 11.1543C13.6653 11.1543 13.9686 11.4561 13.9686 11.7697C13.9686 12.0818 14.2718 12.385 14.5839 12.385C14.7875 12.385 15.0238 12.2399 15.1167 12.0579C15.3223 11.6549 15.1102 10.9395 14.6473 10.4745C14.1846 10.0097 13.5433 9.82658 12.9225 9.9819ZM2.44091 13.4106V15.2567H1.89C1.44391 15.2567 1.32687 15.2444 1.27465 15.1922C1.21812 15.1357 1.2102 14.917 1.2102 13.4106C1.2102 11.9043 1.21812 11.6856 1.27465 11.629C1.32687 11.5768 1.44391 11.5646 1.89 11.5646H2.44091V13.4106ZM19.6885 11.629C19.745 11.6856 19.7529 11.9043 19.7529 13.4106C19.7529 14.917 19.745 15.1357 19.6885 15.1922C19.6362 15.2444 19.5192 15.2567 19.0731 15.2567H18.5222V13.4106V11.5646H19.0731C19.5192 11.5646 19.6362 11.5768 19.6885 11.629ZM8.24228 14.9047C7.98879 15.0151 7.85801 15.3005 7.91647 15.6156C7.95129 15.8034 8.2232 16.0799 8.63467 16.3461C9.78144 17.0879 11.2368 17.0707 12.3927 16.3016C12.9485 15.9318 13.1162 15.6806 13.0485 15.3194C12.9912 15.0142 12.6702 14.8038 12.3526 14.8634C12.2665 14.8796 12.0992 14.9768 11.9487 15.0982C11.0247 15.8433 9.9329 15.8515 9.04453 15.1201C8.70646 14.8418 8.50881 14.7887 8.24228 14.9047Z"
      fill="currentColor"
    />
  </svg>
);
export default Robot;
