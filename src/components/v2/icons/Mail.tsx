const Mail = () => (
  <svg
    width="21"
    height="20"
    viewBox="0 0 21 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_8162_1772)">
      <path
        d="M18.4611 3.333C18.3854 3.3252 18.3091 3.3252 18.2333 3.333H2.67778C2.57808 3.33454 2.47904 3.34949 2.38333 3.37745L10.4111 11.3719L18.4611 3.333Z"
        fill="currentColor"
      />
      <path
        d="M19.2833 4.10547L11.1944 12.161C10.9863 12.368 10.7046 12.4841 10.4111 12.4841C10.1176 12.4841 9.83596 12.368 9.62778 12.161L1.61111 4.16658C1.58646 4.25716 1.5734 4.35049 1.57222 4.44436V15.5555C1.57222 15.8502 1.68928 16.1328 1.89766 16.3411C2.10603 16.5495 2.38865 16.6666 2.68333 16.6666H18.2389C18.5336 16.6666 18.8162 16.5495 19.0246 16.3411C19.2329 16.1328 19.35 15.8502 19.35 15.5555V4.44436C19.3456 4.32861 19.3231 4.21427 19.2833 4.10547ZM3.44444 15.5555H2.67222V14.761L6.71111 10.7555L7.49444 11.5388L3.44444 15.5555ZM18.2278 15.5555H17.45L13.4 11.5388L14.1833 10.7555L18.2222 14.761L18.2278 15.5555Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_8162_1772">
        <rect
          width="20"
          height="20"
          fill="currentColor"
          transform="translate(0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export default Mail;
