import { getHomeBlogPosts } from "@/services/blogAPI";
import {
  getHomepageFeaturedCompanies,
  getHomepageSpotlightCompanies,
} from "@/services/companyAPI";
import {
  fetchCategories,
  getHomepageHighlightCompanyJobs,
  getHomepageJobsBasic,
  getHomepagePopularCompanyJobs,
  getHomepageSuperHotJobsToday,
  getRecommendJobs,
} from "@/services/jobAPI";
import { isMobile } from "@/utils/device";
import { getCurrentLocaleForParams } from "@/utils/locale";
import { CarouselWithDots } from "../shared/banner-home/page";
import Ads from "./home/<USER>";
import Blog from "./home/<USER>";
import FeatureCompanies from "./home/<USER>";
import HighLightCompanies from "./home/<USER>";
import HotCategories from "./home/<USER>";
import HotJobs from "./home/<USER>";
import PopularJobs from "./home/<USER>";
import Recommended from "./home/<USER>";
import SmartTool from "./home/<USER>";
import Subscribe from "./home/<USER>";
import TopCompaniesHiring from "./home/<USER>";
import { JobTailor } from "./home/<USER>";
import JobBasics from "./home/<USER>";
import { getBannerTop } from "@/services/bannerAPI";

export default async function HomePage() {
  const topCompanies = await getHomepageSpotlightCompanies(
    getCurrentLocaleForParams(),
  );
  const homePageFeatured = await getHomepageFeaturedCompanies();
  const hotJobs = await getHomepageSuperHotJobsToday(
    getCurrentLocaleForParams(),
  );
  const popularCompanyJobs = await getHomepagePopularCompanyJobs(
    40,
    getCurrentLocaleForParams(),
  );
  const highLightCompanyJobs = await getHomepageHighlightCompanyJobs(
    40,
    getCurrentLocaleForParams(),
  );
  const jobsBasic = await getHomepageJobsBasic(40, getCurrentLocaleForParams());
  const blogs = await getHomeBlogPosts(4);
  const recommendJobs = await getRecommendJobs(1);
  const banners = await getBannerTop();
  const categories = await fetchCategories();

  return (
    <main>
      <CarouselWithDots banners={banners} />
      <div className="bg-[linear-gradient(180deg,_#FFFFFF_29.88%,_#98BDFC_236.02%)] pb-8 pt-6">
        <FeatureCompanies companies={homePageFeatured} isMobile={isMobile()} />
      </div>
      <div className="mx-4 md:container md:mx-auto">
        <HotJobs hotJobs={hotJobs?.data} meta={hotJobs?.meta} />
        <TopCompaniesHiring companies={topCompanies} />
      </div>
      <div className="bg-brand-50">
        <PopularJobs jobs={popularCompanyJobs?.jobs} />
      </div>
      <div className="mx-4 py-6 md:container md:mx-auto md:pb-10 md:pt-0">
        <HotCategories categories={categories} />
        <Ads />
        <Recommended recommendJobs={recommendJobs?.data} />
      </div>

      <div className="mx-4 pb-12 pt-0 md:container md:mx-auto">
        {/* <Ads /> */}
        <HighLightCompanies jobs={highLightCompanyJobs} isMobile={isMobile()} />
        <JobBasics jobs={jobsBasic} isMobile={isMobile()} />
        {isMobile() ? <></> : <SmartTool />}
        <Blog blogs={blogs} isMobile={isMobile()} />
        {isMobile() ? <></> : <Subscribe />}
      </div>
      {isMobile() ? <></> : <JobTailor jobs={recommendJobs?.data} />}
    </main>
  );
}
