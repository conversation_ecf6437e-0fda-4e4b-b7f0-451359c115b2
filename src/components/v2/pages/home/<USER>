"use client";
import { TypographySection } from "../../shared/typography-section/page";

import { Card, CardContent } from "@/components/v2/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/v2/ui/carousel";
import { LIST_TOOLS } from "@/contansts/menu";
import { useCarouselControls } from "@/hooks/useCarouselControls";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import CarouselButton from "../../shared/carousel-button/page";

const SmartTool = () => {
  const { api, setApi, canScrollPrev, canScrollNext } = useCarouselControls();

  return (
    <div className="container">
      <div className="flex items-center justify-between">
        <TypographySection title="Smart tools" />
        <CarouselButton
          onPrev={() => api?.scrollPrev()}
          onNext={() => api?.scrollNext()}
          disabledPrev={!canScrollPrev}
          disabledNext={!canScrollNext}
        />
      </div>
      <Carousel
        opts={{
          align: "start",
          slidesToScroll: 4,
        }}
        setApi={setApi}
        className="w-full"
      >
        <CarouselContent className="pt-6">
          {LIST_TOOLS.map((item) => (
            <CarouselItem key={item?.id} className="lg:basis-1/4">
              <Card
                style={{
                  backgroundImage: `url(${item?.backgroundImage})`,
                  backgroundSize: "cover",
                }}
                className="h-[270px] w-full rounded-[16px] border border-brand-500"
              >
                <CardContent className="rounded-xl px-6 py-7">
                  <span className="inline-flex h-6 w-6 items-center rounded-[4px] bg-brand-50 p-[6px] text-brand-500">
                    {item?.icon}
                  </span>
                  <span className="mt-1 block font-semibold">{item?.name}</span>
                  <span className="mt-1 block text-xs text-[#6D6D6D]">
                    Make an impressive CV in minutes with ease
                  </span>
                  <Link
                    href={item.link}
                    className="mt-1 flex items-center gap-2 text-xs text-brand-500"
                  >
                    Create now{" "}
                    <span className="rounded-full bg-[#EDF1FD] p-1 font-medium">
                      <ArrowRight className="h-3 w-3 text-brand-500" />
                    </span>
                  </Link>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
};

export default SmartTool;
