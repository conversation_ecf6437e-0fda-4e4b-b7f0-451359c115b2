"use client";
import { useCarouselControls } from "@/hooks/useCarouselControls";
import { JobType } from "@/types/job";
import { isMobile } from "react-device-detect";
import CarouselButton from "../../shared/carousel-button/page";
import HotJobCard from "../../shared/hotjob-card/page";
import { TypographySection } from "../../shared/typography-section/page";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/v2/ui/carousel";
const HotJobs = ({ hotJobs, meta }: { hotJobs: JobType[]; meta: any }) => {
  const { api, setApi, canScrollPrev, canScrollNext } = useCarouselControls();
  if (hotJobs?.length === 0) {
    return <></>;
  }
  return (
    <div className="mt-6 md:mt-10">
      <div className="flex items-center justify-between">
        <TypographySection title="SUPER HOT JOBS" />
        <CarouselButton
          onPrev={() => api?.scrollPrev()}
          onNext={() => api?.scrollNext()}
          disabledPrev={!canScrollPrev}
          disabledNext={!canScrollNext}
        />
      </div>
      <span className="mt-1 block text-xs text-text-700 md:text-sm">
        Get noticed fast – these positions are getting the most views and
        applications.
      </span>
      <div className="w-full">
        {/* {isMobile ? (
          <>
            <CarouselCardsMobile jobs={hotJobs} />
          </>
        ) : ( */}
        <>
          <Carousel
            opts={{
              align: "start",
              slidesToScroll: isMobile ? 1 : 2,
            }}
            setApi={setApi}
            className="w-full"
          >
            <CarouselContent className="py-6">
              {hotJobs?.length > 0 &&
                hotJobs?.map((item, index) => (
                  <CarouselItem
                    key={index}
                    className="basis-[80%] md:basis-1/4"
                  >
                    <HotJobCard
                      key={item?.id}
                      job={item}
                      className="border-none md:w-[304px]"
                    />
                  </CarouselItem>
                ))}
            </CarouselContent>
          </Carousel>
        </>
        {/* )} */}
      </div>
    </div>
  );
};

export default HotJobs;
