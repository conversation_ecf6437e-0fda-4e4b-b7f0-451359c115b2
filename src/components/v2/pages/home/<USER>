import React from "react";
import { Input } from "../../ui/input";
import { Button } from "../../ui/button";

const Subscribe = () => {
  return (
    <div className="mt-12 flex items-center rounded-[16px] bg-brand-50 px-[86px] py-12">
      <h2 className="text-[40px]/[52px] font-bold text-text-700">
        Never want to miss any{" "}
        <span className=" text-brand-500">Job News?</span>
      </h2>
      <div className="relative h-fit w-[90%]">
        <Input
          type="email"
          className="shadow-none h-[73px] rounded-[64px] border-none bg-white pl-5 placeholder-text-200 ring-0 placeholder:font-medium"
          placeholder="Enter your email.."
        />
        <Button className="absolute right-3 top-3 h-12 rounded-[64px] bg-brand-500 px-10 text-lg font-medium text-white">
          Subscribe
        </Button>
      </div>
    </div>
  );
};

export default Subscribe;
