"use client";
import { useCarouselControls } from "@/hooks/useCarouselControls";
import { JobType } from "@/types/job";
import _ from "lodash";
import { isMobile } from "react-device-detect";
import CardJob from "../../shared/card-jobs/page";
import CarouselButton from "../../shared/carousel-button/page";
import { TypographySection } from "../../shared/typography-section/page";
import { Carousel, CarouselContent, CarouselItem } from "../../ui/carousel";

const PopularJobs = ({ jobs }: { jobs: JobType[] }) => {
  const { api, setApi, canScrollPrev, canScrollNext } = useCarouselControls();

  const chunkedJobs = _.chunk(jobs, 20);
  const chunkedJobsMobile = _.chunk(jobs, 2);

  return (
    <div className="mx-4 py-6 md:container md:mx-auto md:py-10">
      <div className="mb-4 flex items-center justify-between md:mb-8">
        <TypographySection title="Popular jobs" />
        <CarouselButton
          onPrev={() => api?.scrollPrev()}
          onNext={() => api?.scrollNext()}
          disabledPrev={!canScrollPrev}
          disabledNext={!canScrollNext}
        />
      </div>
      {isMobile ? (
        <Carousel
          opts={{
            align: "start",
          }}
          className="w-full"
          setApi={setApi}
        >
          <CarouselContent className="-ml-2">
            {chunkedJobsMobile.map((columnJobs, index) => (
              <CarouselItem key={index} className="shrink-0 basis-[75%] pl-2">
                <div className="grid grid-rows-2 gap-2">
                  {columnJobs.map((job) => (
                    <CardJob
                      key={job.id}
                      job={job}
                      className={"border-[0.5px] border-brand-200 bg-white"}
                    />
                  ))}
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      ) : (
        <>
          <Carousel
            opts={{
              align: "start",
            }}
            setApi={setApi}
            className="w-full"
          >
            <CarouselContent>
              {chunkedJobs.map((group, index) => (
                <CarouselItem key={index}>
                  <div className="gap-4 md:grid md:grid-cols-4">
                    {group.map((job) => (
                      <CardJob
                        className="border border-text-200 bg-white"
                        job={job}
                        key={job?.id}
                        isTopJob={job?.is_content_image}
                      />
                    ))}
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </>
      )}
    </div>
  );
};

export default PopularJobs;
