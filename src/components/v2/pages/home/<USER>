"use client";
import { cn } from "@/lib/utils";
import { JobType } from "@/types/job";
import _ from "lodash";
import { ChevronLeftIcon, ChevronRightIcon, X } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";
import { TopDev } from "../../icons";
import CardJobMini from "../../shared/card-jobs-mini/page";
import { Button } from "../../ui/button";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "../../ui/carousel";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";

export function JobTailor({ jobs }: { jobs: JobType[] }) {
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    if (!api) return;

    const updateCarouselState = () => {
      setCurrentIndex(api.selectedScrollSnap());
      setTotalItems(api.scrollSnapList().length);
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    updateCarouselState();
    api.on("select", updateCarouselState);

    return () => {
      api.off("select", updateCarouselState); // Clean up on unmount
    };
  }, [api]);

  const scrollToIndex = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };
  const chunkedJobs = _.chunk(jobs, 2);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger className="fixed bottom-52 right-0" asChild>
        <Button className="flex flex-col gap-4" variant="ghost">
          <span className="inline-block h-24 w-[147px] whitespace-normal break-words text-2xl font-medium uppercase text-[#404040]">
            Jobs <span className="font-semibold text-brand-600">tailored</span>{" "}
            for you
          </span>
          <Image
            src="https://c.topdevvn.com/uploads/2025/07/21/Mask-group.png"
            alt="job-image"
            style={{
              width: "115px",
              height: "115px",
            }}
            width={115}
            height={115}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="min-[1500px]:translate-y-[15%] w-[561px] translate-y-[25%] rounded-[16px] border-none bg-brand-50  p-7">
        <div className="flex justify-between">
          <Image
            src="https://c.topdevvn.com/uploads/2025/07/15/logo_v2.png"
            alt="TopDev"
            className="h-[40px] w-[145px]"
            loading="lazy"
            width="145"
            height="50"
          />
          <Button
            variant="ghost"
            onClick={() => setOpen(false)}
            className="h-8 w-8 rounded-full bg-text-500"
          >
            <X className="text-brand-50" />
          </Button>
        </div>
        <div className="mt-4 rounded-[16px] bg-white px-10 py-4 shadow-[0px_2px_4px_0px_#1B368040]">
          <div className="mb-6 flex gap-2">
            <span className="flex h-fit items-center justify-center rounded-full bg-brand-50 p-[10px]">
              <TopDev />
            </span>
            <div className="flex flex-col gap-4 pt-3">
              <h2 className="text-[32px]/[24px] font-bold text-brand-500">
                Jobs suitable for you
              </h2>
              <span className="text-sm text-text-700">
                Hello! I’ve found some job opportunities just for you. Based on
                your skills and interests, here are a few roles that might be a
                great match:
              </span>
            </div>
          </div>
          <Carousel
            opts={{
              align: "start",
            }}
            setApi={setApi}
            className="w-full"
          >
            <CarouselContent>
              {chunkedJobs.map((group, index) => (
                <CarouselItem key={index}>
                  <div className="grid gap-1">
                    {group.map((job) => (
                      <CardJobMini job={job} key={job?.id} />
                    ))}
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
          <div className="mt-4 flex items-center justify-center space-x-2">
            <Button
              className="h-fit p-0"
              variant="ghost"
              onClick={() => api?.scrollPrev()}
              disabled={!canScrollPrev}
            >
              <ChevronLeftIcon className="h-4 w-4 text-brand-500" />
            </Button>

            {Array.from({ length: totalItems }).map((_, index) => (
              <button
                key={index}
                onClick={() => scrollToIndex(index)}
                className={cn(
                  "relative h-2 w-2 rounded-full transition-all md:h-3 md:w-3",
                  currentIndex === index ? "bg-white" : "bg-brand-200",
                )}
                aria-label={`Go to slide ${index + 1}`}
              >
                {currentIndex === index && (
                  <span className="absolute inset-0 m-auto h-[5px] w-[5px] rounded-full bg-brand-600 md:h-[7px] md:w-[7px]" />
                )}
                {currentIndex === index && (
                  <span className="absolute inset-0 m-auto h-2 w-2 rounded-full border border-brand-600 md:h-3 md:w-3" />
                )}
              </button>
            ))}
            <Button
              className="h-fit p-0"
              variant="ghost"
              onClick={() => api?.scrollNext()}
              disabled={!canScrollNext}
            >
              <ChevronRightIcon className="h-4 w-4 text-brand-500" />
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
