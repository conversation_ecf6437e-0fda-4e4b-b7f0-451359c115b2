"use client";
import { useEffect, useMemo, useState } from "react";
import CarouselButton from "../../shared/carousel-button/page";
import { TypographySection } from "../../shared/typography-section/page";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "../../ui/carousel";

// components/MarketplaceCard.tsx
import { BlogType } from "@/types/blog";
import Image from "next/image";
import Link from "next/link";
import ViewAll from "../../shared/view-all/page";
import { Card, CardContent } from "../../ui/card";

const srcPage = "home";
const mediumPage = "blogIT";

export const MarketplaceCard = ({ blog }: { blog: BlogType }) => {
  const queryObject = useMemo(() => {
    let queryObj: { [key: string]: string } = {};
    if (srcPage) {
      queryObj["src"] = `topdev_${srcPage}`;
    }
    if (mediumPage) {
      queryObj["medium"] = mediumPage;
    }
    return queryObj;
  }, []);
  return (
    <Card className="border-none md:pl-2">
      <div className="rounded-[16px] border border-text-100 bg-white md:w-[99.5%] md:flex-row md:shadow-[0px_0px_8px_0px_#6464644D]">
        <CardContent className="flex justify-center gap-2 py-2 md:gap-9 md:py-7">
          <div className="relative w-[30%] flex-shrink-0 md:h-[242px] md:w-[256px]">
            <Image
              src={blog?.image}
              alt="Marketplace Illustration"
              layout="fill"
              className="h-full w-full object-contain"
            />
          </div>
          <div className="flex flex-col justify-center">
            <Link
              href={{
                pathname: blog.permalink,
                query: queryObject,
              }}
              target="_blank"
              className="mb-1 line-clamp-2 font-semibold text-[#4876EF] md:mb-5 md:text-[20px]/[30px] md:text-[24px]"
            >
              {blog?.post_title}
            </Link>
            <p className="mb-4 line-clamp-2 text-sm leading-relaxed text-text-700 md:line-clamp-5">
              {blog.post_excerpt}
            </p>
            <Link
              href={{
                pathname: blog.permalink,
                query: queryObject,
              }}
              target="_blank"
              className="hidden items-center gap-1 text-sm font-medium text-[#4876EF] hover:underline md:inline-flex"
            >
              Read more <span className="text-base">→</span>
            </Link>
          </div>
        </CardContent>
      </div>
    </Card>
  );
};

const Blog = ({
  blogs,
  isMobile = false,
}: {
  blogs: BlogType[];
  isMobile?: boolean;
}) => {
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  useEffect(() => {
    if (!api) return;

    const updateButtons = () => {
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    updateButtons();
    api.on("select", updateButtons);

    return () => {
      api.off("select", updateButtons);
    };
  }, [api]);
  return (
    <div className="mt-0 md:mt-12">
      <div className="flex items-center justify-between">
        <TypographySection title="blog" />
        {isMobile ? (
          <></>
        ) : (
          <CarouselButton
            onPrev={() => api?.scrollPrev()}
            onNext={() => api?.scrollNext()}
            disabledPrev={!canScrollPrev}
            disabledNext={!canScrollNext}
          />
        )}
      </div>
      <span className="mt-1 hidden text-text-700 md:block">
        Get noticed fast – these positions are getting the most views and
        applications.
      </span>
      {isMobile ? (
        <div className="mt-4 flex flex-col gap-[10px]">
          {blogs.slice(0, 2).map((blog) => (
            <MarketplaceCard key={blog?.id} blog={blog} />
          ))}
        </div>
      ) : (
        <Carousel
          opts={{
            align: "start",
            slidesToScroll: 2,
          }}
          setApi={setApi}
          className="w-full"
        >
          <CarouselContent className="px-1 pb-1 pt-6">
            {blogs?.length > 0 &&
              blogs?.map((blog) => (
                <CarouselItem key={blog?.id} className="lg:basis-1/2">
                  <MarketplaceCard blog={blog} />
                </CarouselItem>
              ))}
          </CarouselContent>
        </Carousel>
      )}
      <div className="mt-[6px] md:mt-0">
        <ViewAll />
      </div>
    </div>
  );
};

export default Blog;
