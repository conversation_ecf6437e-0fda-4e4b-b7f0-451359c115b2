import React from "react";
import { TypographySection } from "../../shared/typography-section/page";
import CardCategories from "../../shared/card-categories/page";
import { LIST_MENU_TOPIC } from "@/contansts/menu";
import BlinkIcon from "../../icons/BlinkIcon";
import { Category } from "@/types/job";

const HotCategories = ({ categories }: { categories: Category[] }) => {
  return (
    <div className="mb-6 md:mb-10 md:mt-12">
      <div className="rounded-t-[18px] bg-brand-100 p-4 md:px-8 md:py-6">
        <TypographySection
          title={
            <span className="flex items-center gap-2">
              Hot categories{" "}
              <span className="h-4 w-4">
                <BlinkIcon />
              </span>
            </span>
          }
        />
        <span className="mt-1 block text-xs text-text-700 md:text-sm">
          From tech and design to education and finance – browse all career
          fields and find the role that suits your passion and skills.
        </span>
      </div>
      <div className="grid grid-cols-2 gap-4 rounded-b-[16px] border-[2px] border-brand-100 p-[14px] md:grid-cols-6 md:px-2 md:py-7">
        {categories.map((item) => (
          <CardCategories item={item} key={item?.id} />
        ))}
      </div>
    </div>
  );
};

export default HotCategories;
