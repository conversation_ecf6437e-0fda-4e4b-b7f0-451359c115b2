"use client";
import Image from "next/image";
import { TypographySection } from "../../shared/typography-section/page";

import { Card, CardContent } from "@/components/v2/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/v2/ui/carousel";
import { useCarouselControls } from "@/hooks/useCarouselControls";
import { CompanyType } from "@/types/company";
import Link from "next/link";
import CarouselButton from "../../shared/carousel-button/page";

const FeatureCompanies = ({
  companies,
  isMobile = false,
}: {
  companies: CompanyType[];
  isMobile: boolean;
}) => {
  const { api, setApi, canScrollPrev, canScrollNext } = useCarouselControls();

  return (
    <div className="mx-4 md:container md:mx-auto">
      <div className="flex items-center justify-center md:justify-between">
        <TypographySection title="Featured Companies" />
        {isMobile ? (
          <></>
        ) : (
          <CarouselButton
            onPrev={() => api?.scrollPrev()}
            onNext={() => api?.scrollNext()}
            disabledPrev={!canScrollPrev}
            disabledNext={!canScrollNext}
          />
        )}
      </div>
      <Carousel
        opts={{
          align: "start",
          slidesToScroll: 1,
        }}
        setApi={setApi}
        className="w-full"
      >
        <CarouselContent className="pt-6">
          {companies?.length > 0 &&
            companies?.map((item) => (
              <CarouselItem
                key={item?.id}
                className="basis-1/3 lg:basis-[12.5%]"
              >
                <Card className="h-[64px] rounded-none border-none md:h-20">
                  <CardContent className="rounded-xl bg-white px-4 py-3">
                    <Link
                      href={{
                        pathname: `/companies/${item?.slug}-${item?.id}`,
                        query: {
                          src: "topdev_home",
                          medium: "featuredcompanies",
                        },
                      }}
                      className="relative block h-5 w-full rounded-xl md:h-11"
                    >
                      <Image
                        src={
                          item?.image_logo ?? "https://picsum.photos/200/300"
                        }
                        alt="carousel"
                        className="h-5 md:h-11"
                        style={{
                          objectFit: "contain",
                        }}
                        sizes="(max-width: 768px) 50vw, 111px"
                        fill
                      />
                    </Link>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
};

export default FeatureCompanies;
