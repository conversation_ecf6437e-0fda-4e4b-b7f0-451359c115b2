"use client";
import { useCarouselControls } from "@/hooks/useCarouselControls";
import { JobType } from "@/types/job";
import _ from "lodash";
import CardBasic from "../../shared/card-basic/page";
import CarouselButton from "../../shared/carousel-button/page";
import { TypographySection } from "../../shared/typography-section/page";
import ViewAll from "../../shared/view-all/page";
import { Carousel, CarouselContent, CarouselItem } from "../../ui/carousel";

const JobBasics = ({
  jobs,
  isMobile = false,
}: {
  jobs: JobType[];
  isMobile: boolean;
}) => {
  const { api, setApi, canScrollPrev, canScrollNext } = useCarouselControls();
  const chunkedJobs = _.chunk(jobs, 20);
  const chunkedJobsMobile = _.chunk(jobs, 2);

  return (
    <div className="container pb-6 md:pb-10">
      <div className="mb-2 flex items-center justify-between md:mb-8">
        <TypographySection title="JOB BASICS" />
        <CarouselButton
          onPrev={() => api?.scrollPrev()}
          onNext={() => api?.scrollNext()}
          disabledPrev={!canScrollPrev}
          disabledNext={!canScrollNext}
        />
      </div>
      {isMobile ? (
        <Carousel
          opts={{
            align: "start",
          }}
          className="w-full"
          setApi={setApi}
        >
          <CarouselContent className="-ml-[6px]">
            {chunkedJobsMobile.map((columnJobs, index) => (
              <CarouselItem
                key={index}
                className="shrink-0 basis-[70%] pl-[6px]"
              >
                <div className="grid grid-rows-2 gap-[6px]">
                  {columnJobs.map((job) => (
                    <CardBasic job={job} key={job?.id} />
                  ))}
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      ) : (
        <>
          <Carousel
            opts={{
              align: "start",
            }}
            setApi={setApi}
            className="w-full"
          >
            <CarouselContent>
              {chunkedJobs.map((group, index) => (
                <CarouselItem key={index}>
                  <div className="gap-4 md:grid md:grid-cols-5">
                    {group.map((job) => (
                      <CardBasic job={job} key={job?.id} />
                    ))}
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>
        </>
      )}
      <div className="mt-[6px] md:mt-0">
        <ViewAll />
      </div>
    </div>
  );
};

export default JobBasics;
