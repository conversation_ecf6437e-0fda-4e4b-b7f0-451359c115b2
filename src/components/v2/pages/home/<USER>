"use client";
import Image from "next/image";
import { TypographySection } from "../../shared/typography-section/page";

import { Card, CardContent } from "@/components/v2/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/v2/ui/carousel";
import { useCarouselControls } from "@/hooks/useCarouselControls";
import { CompanyType } from "@/types/company";
import { Heart } from "lucide-react";
import Link from "next/link";
import { isMobile } from "react-device-detect";
import CarouselButton from "../../shared/carousel-button/page";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

const JobsIcon = () => (
  <svg
    width="16"
    height="14"
    viewBox="0 0 16 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.46875 9.79994C8.72759 9.79994 8.9375 9.59101 8.9375 9.33329V7.46666C8.9375 7.20897 8.72769 7 8.46875 7H7.53125C7.27241 7 7.0625 7.20888 7.0625 7.46666V9.33329C7.0625 9.59098 7.27237 9.79994 7.53125 9.79994H8.46875Z"
      fill="#4F4F4F"
    />
    <path
      d="M14.5938 1.86663H11.2812C11.2812 1.77264 11.2812 1.30599 11.2812 1.39997C11.2812 0.627996 10.6505 0 9.875 0C9.77325 0 5.98578 0 6.125 0C5.34956 0 4.71875 0.627965 4.71875 1.39997C4.71875 1.49396 4.71875 1.96061 4.71875 1.86663H1.40625C0.630813 1.86663 0 2.49459 0 3.2666C0 4.18053 0.1125 4.98091 0.335875 5.66301C0.55925 6.34511 0.893531 6.90896 1.33712 7.34985C2.08359 8.09199 3.03441 8.39983 4.10203 8.39983H6.125C6.125 8.30585 6.125 7.37253 6.125 7.46652C6.125 6.69454 6.75578 6.06655 7.53125 6.06655C7.62566 6.06655 8.56316 6.06655 8.46875 6.06655C9.24419 6.06655 9.875 6.69451 9.875 7.46652C9.875 7.5605 9.875 8.49382 9.875 8.39983H10.9165C11.8497 8.36122 13.376 8.64371 14.6624 7.36899C15.1062 6.92921 15.4406 6.36437 15.6641 5.67906C15.8875 4.99376 16 4.18806 16 3.2666C16 2.49463 15.3692 1.86663 14.5938 1.86663ZM5.65625 1.39997C5.65625 1.1425 5.86634 0.933315 6.125 0.933315H9.875C10.1336 0.933315 10.3438 1.14247 10.3438 1.39997C10.3438 1.49396 10.3438 1.96061 10.3438 1.86663H5.65625C5.65625 1.77264 5.65625 1.30599 5.65625 1.39997Z"
      fill="#4F4F4F"
    />
    <path
      d="M10.9293 9.33357H9.875C9.875 10.1055 9.24422 10.7335 8.46875 10.7335C8.37434 10.7335 7.43684 10.7335 7.53125 10.7335C6.75581 10.7335 6.125 10.1056 6.125 9.33357C6.01791 9.33357 3.99037 9.33357 4.10612 9.33357C2.76912 9.33357 1.59478 8.92515 0.674719 8.01063C0.414812 7.75232 0.192875 7.46146 0 7.15039V13.5335C0 13.7914 0.209656 14.0001 0.46875 14.0001H15.5312C15.7903 14.0001 16 13.7914 16 13.5335V7.16958C15.8045 7.48539 15.5817 7.77556 15.3239 8.03113C13.8448 9.4959 12.2696 9.27054 10.9293 9.33357Z"
      fill="#4F4F4F"
    />
  </svg>
);
const CardMobile = ({ item }: { item: CompanyType }) => (
  <div className="flex h-full flex-col p-5 md:flex-row md:gap-5">
    <div className="grid w-full grid-cols-2 gap-1 px-6 md:max-w-[226px] md:p-0">
      <div className="relative col-span-2 h-[94px] overflow-hidden rounded-xl md:aspect-[226/94]">
        <Image
          src={item.image_cover ?? "https://picsum.photos/200/300"}
          alt="cover"
          fill
          className="rounded-xl object-cover"
          sizes="(max-width: 768px) 100vw, 226px"
        />
      </div>

      <div className="relative h-[98px] overflow-hidden rounded-xl md:aspect-[111/98]">
        <Image
          src={item.image_galleries[0]?.url ?? "https://picsum.photos/200/300"}
          alt="gallery 1"
          fill
          className="rounded-xl object-cover"
          sizes="(max-width: 768px) 50vw, 111px"
        />
      </div>

      <div className="relative h-[98px] overflow-hidden rounded-xl md:aspect-[111/98]">
        <Image
          src={item.image_galleries[1]?.url ?? "https://picsum.photos/200/300"}
          alt="gallery 2"
          fill
          className="rounded-xl object-cover"
          sizes="(max-width: 768px) 50vw, 111px"
        />
      </div>
    </div>

    <div className="mt-5 flex flex-col items-center justify-between md:mt-0 md:items-start">
      <div className="flex flex-col items-center">
        <span className="rounded-[64px] border-[0.5px] border-text-700 bg-transparent px-6 py-[2px] text-[10px]/[10px] font-medium text-text-700 md:mb-3 md:text-sm/[14px]">
          {item?.industries_arr?.[0] ?? "General"}
        </span>
        <Link
          href={item?.detail_url}
          className="mb-3 mt-2 line-clamp-1 block h-12 text-center text-lg font-bold text-text-700 md:mb-4 md:mt-3 md:line-clamp-none md:text-left"
        >
          {item?.display_name ?? "Data empty"}
        </Link>
        <span className="line-clamp-1 h-4 text-xs font-medium text-text-700 md:line-clamp-none md:text-base">
          {item?.tagline ?? "Data empty"}
        </span>
      </div>
      <div className="mt-5 flex w-full justify-between md:mt-0">
        <span className="flex items-center gap-1 text-xs text-text-700 md:text-base">
          <JobsIcon />
          {item?.num_job_openings} jobs opening
        </span>
        <Heart className="h-4 w-4 text-text-700  md:h-6 md:w-6 " />
      </div>
    </div>
  </div>
);

const CardDesktop = ({ item }: { item: CompanyType }) => (
  <div className="flex flex-col gap-5 p-5 md:flex-row">
    <div className="grid w-full grid-cols-2 gap-1 p-6 md:max-w-[226px] md:p-0">
      <div className="relative col-span-2 overflow-hidden rounded-xl md:aspect-[226/94] md:h-[94px]">
        <Image
          src={item.image_cover ?? "https://picsum.photos/200/300"}
          alt="cover"
          fill
          className="rounded-xl object-cover"
          sizes="(max-width: 768px) 100vw, 226px"
        />
      </div>

      <div className="relative overflow-hidden rounded-xl md:aspect-[111/98]">
        <Image
          src={item.image_galleries[0]?.url ?? "https://picsum.photos/200/300"}
          alt="gallery 1"
          fill
          className="rounded-xl object-cover"
          sizes="(max-width: 768px) 50vw, 111px"
        />
      </div>

      <div className="relative overflow-hidden rounded-xl md:aspect-[111/98]">
        <Image
          src={item.image_galleries[1]?.url ?? "https://picsum.photos/200/300"}
          alt="gallery 2"
          fill
          className="rounded-xl object-cover"
          sizes="(max-width: 768px) 50vw, 111px"
        />
      </div>
    </div>

    <div className="flex flex-col justify-between">
      <div>
        <span className="mb-3 rounded-[64px] border-[0.5px] border-[#4F4F4F] bg-transparent px-6 py-[2px] text-sm/[14px] font-medium text-[#4F4F4F]">
          {item?.industries_arr?.[0] ?? "General"}
        </span>
        <Link
          href={item?.detail_url}
          className="mb-4 mt-3 block text-lg font-bold text-text-700"
        >
          {item?.display_name}
        </Link>
        <span className="font-medium text-text-700">{item?.tagline}</span>
      </div>
      <span className="flex items-center gap-1 text-text-700">
        <JobsIcon />
        {item?.num_job_openings} jobs opening
      </span>
    </div>
  </div>
);

const TopCompaniesHiring = ({ companies }: { companies: CompanyType[] }) => {
  const { api, setApi, canScrollPrev, canScrollNext } = useCarouselControls();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  useEffect(() => {
    if (!api) return;

    const updateCarouselState = () => {
      setCurrentIndex(api.selectedScrollSnap());
      setTotalItems(api.scrollSnapList().length);
    };

    updateCarouselState();
    api.on("select", updateCarouselState);

    return () => {
      api.off("select", updateCarouselState); // Clean up on unmount
    };
  }, [api]);
  const scrollToIndex = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };
  return (
    <div className="mt-6 pb-6 md:pb-0">
      <div className="mt-6 flex items-center justify-between md:mt-0">
        <TypographySection title="TOP COMPANIES HIRING NOW" />
        {isMobile ? (
          <></>
        ) : (
          <CarouselButton
            onPrev={() => api?.scrollPrev()}
            onNext={() => api?.scrollNext()}
            disabledPrev={!canScrollPrev}
            disabledNext={!canScrollNext}
          />
        )}
      </div>
      <Carousel
        opts={{
          align: "start",
          slidesToScroll: isMobile ? 1 : 2,
        }}
        setApi={setApi}
        className="w-full"
      >
        <CarouselContent className=" pb-2 pt-4 md:pb-6 ">
          {companies?.length > 0 &&
            companies?.map((item, index) => (
              <CarouselItem
                key={index}
                className="shrink-0 basis-[80%] md:basis-1/2"
              >
                <Card className="h-fit rounded-none border-none md:h-[236px]">
                  <CardContent className="rounded-xl bg-brand-100 p-0">
                    {isMobile ? (
                      <CardMobile item={item} />
                    ) : (
                      <CardDesktop item={item} />
                    )}
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
        </CarouselContent>
      </Carousel>
      <div className="mt-2 flex justify-center space-x-2 md:mt-0 md:hidden">
        {Array.from({ length: totalItems }).map((_, index) => (
          <button
            key={index}
            onClick={() => scrollToIndex(index)}
            className={cn(
              "relative h-2 w-2 rounded-full transition-all md:h-3 md:w-3",
              currentIndex === index ? "bg-white" : "bg-brand-200",
            )}
            aria-label={`Go to slide ${index + 1}`}
          >
            {currentIndex === index && (
              <span className="absolute inset-0 m-auto h-[5px] w-[5px] rounded-full bg-brand-600 md:h-[7px] md:w-[7px]" />
            )}
            {currentIndex === index && (
              <span className="absolute inset-0 m-auto h-2 w-2 rounded-full border border-brand-600 md:h-3 md:w-3" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default TopCompaniesHiring;
