"use client";
import { JobType } from "@/types/job";
import _ from "lodash";
import BlinkIcon from "../../icons/BlinkIcon";
import Card<PERSON><PERSON> from "../../shared/card-jobs/page";
import { TypographySection } from "../../shared/typography-section/page";
import ViewAll from "../../shared/view-all/page";
import { Carousel, CarouselContent, CarouselItem } from "../../ui/carousel";
import { useAuthStore } from "@/stores/useAuthStore";
import { isMobile } from "react-device-detect";

const Recommended = ({ recommendJobs }: { recommendJobs: JobType[] }) => {
  const chunkedJobsMobile = _.chunk(recommendJobs, 2);
  const profile = useAuthStore((s) => s.profile);
  if (!profile) {
    return <></>;
  }
  return (
    <div className="mt-12">
      <div className="rounded-t-[18px] bg-brand-100 p-3 md:px-8 md:py-6">
        <TypographySection
          title={
            <span className="flex items-center gap-2">
              Recommended jobs for you{" "}
              <span className="h-4 w-4">
                <BlinkIcon />
              </span>
            </span>
          }
        />
        <span className="mt-1 block text-xs text-text-700 md:text-sm">
          Our system automatically recommends the most suitable positions for
          your profile, helping you save time and easily discover your dream
          job.
        </span>
      </div>
      <div className="rounded-b-[16px] border-[2px] border-brand-100 pb-[6px] md:pb-0">
        <div className="gap-4 px-3 py-2 md:grid md:grid-cols-3 md:py-3">
          {isMobile ? (
            <Carousel
              opts={{
                align: "start",
              }}
              className="w-full"
            >
              <CarouselContent className="-ml-2">
                {chunkedJobsMobile.map((columnJobs, index) => (
                  <CarouselItem
                    key={index}
                    className="shrink-0 basis-[75%] pl-2"
                  >
                    <div className="grid grid-rows-2 gap-2">
                      {columnJobs.map((job) => (
                        <CardJob
                          key={job.id}
                          job={job}
                          className={"border-[0.5px] border-brand-200 bg-white"}
                        />
                      ))}
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>
          ) : (
            <>
              {recommendJobs?.length > 0 &&
                recommendJobs
                  ?.slice(0, 6)
                  ?.map((job) => (
                    <CardJob
                      key={job?.id}
                      job={job}
                      className="border-brand-200 bg-white"
                    />
                  ))}
            </>
          )}
        </div>
        <ViewAll />
      </div>
    </div>
  );
};

export default Recommended;
