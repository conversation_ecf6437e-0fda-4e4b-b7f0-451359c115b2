import { getCompanies } from "@/services/jobAPI";
import Image from "next/image";
import CompanyList from "./CompanyList";
import SearchInput from "./SearchInput";
import { getTaxonomiesDynamic } from "@/services/taxonomyAPI";
import Industries from "./Industries";
import { isMobile } from "@/utils/device";

const Company = async ({
  params,
  searchParams,
}: {
  params: { locale: string };
  searchParams: { [key: string]: string | string[] | undefined };
}) => {
  const listCompanies = await getCompanies(searchParams);
  const fetchIndustries = await getTaxonomiesDynamic(["industries"]);
  const { industries } = fetchIndustries;
  const { meta, data } = listCompanies;

  return (
    <div className="container mx-auto pb-28 pt-4 md:pt-10">
      <div className="flex items-center justify-between px-4 md:px-6">
        <div className="mb-4 w-full md:mb-10">
          <h2 className="text-[22px]/[28px] font-semibold text-brand-500 md:text-4xl">
            Khám phá công ty
          </h2>
          <span className="text-sm text-text-700">
            Tra cứu thông tin công ty và tìm kiếm nơi làm việc tốt nhất dành cho
            bạn
          </span>
          <div className="mt-4 w-full md:mt-6 md:w-[60%]">
            <SearchInput />
          </div>
        </div>
        <div className="hidden h-[148px] w-[163px] md:block">
          <Image
            src="https://c.topdevvn.com/uploads/2025/08/26/meme-topdev.png"
            alt="meme-topdev"
            width="163"
            height="148"
          />
        </div>
      </div>
      <div className="rounded-xl border-text-200 px-4 md:border md:p-7">
        <div className="mb-6 hidden items-center justify-between md:flex">
          <span className=" text-[22px]/[28px] font-semibold text-brand-500">
            Công ty nổi bật{" "}
            <span className="text-text-700">({meta?.total})</span>
          </span>
          {isMobile() ? (
            <></>
          ) : (
            <Industries data={industries} locale={params?.locale} />
          )}
        </div>
        <CompanyList data={data} total={meta?.total} />
      </div>
    </div>
  );
};

export default Company;
