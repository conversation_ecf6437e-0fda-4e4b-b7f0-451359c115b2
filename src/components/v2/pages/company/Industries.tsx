"use client";
import { MdOutlineArrowDropDown } from "react-icons/md";

import { RadioGroup, RadioGroupItem } from "@/components/v2/ui/radio-group";
import { TaxonomyType } from "@/types/taxonomy";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";

import { Button } from "@/components/v2/ui/button";
import { Label } from "@/components/v2/ui/label";
import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
const Industries = ({
  data,
  locale,
}: {
  data: TaxonomyType[];
  locale: string;
}) => {
  const [open, setOpen] = useState(false);
  const [value, setValue] = useState<string>("");
  const searchParams = useSearchParams();
  const router = useRouter();

  const onChange = (val: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("company_industry_ids", String(value));
    router.replace(`?${params.toString()}`, { scroll: false });
    setValue(val);
  };
  const getLabel = (id: string) => {
    const selected = data.find((l) => String(l.id) === id);
    return selected
      ? locale === "vi"
        ? selected.text_vi
        : selected.text_en
      : null;
  };
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="shadow-none flex h-12 w-fit items-center gap-1 rounded border-text-200 bg-white px-3 py-1 text-xs text-text-700  md:px-4 md:py-2 md:text-sm"
        >
          {value ? getLabel(value) : "Tất cả lĩnh vực"}
          <ChevronDown className="h-4 w-4 text-text-700" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-fit rounded-[4px] border-none bg-white p-4">
        <RadioGroup
          value={value}
          onValueChange={(val) => {
            onChange(val);
            setOpen(false);
          }}
          className="flex flex-col gap-2"
        >
          {data.map((level: TaxonomyType) => {
            const id = String(level.id);
            const label = locale === "vi" ? level.text_vi : level.text_en;
            return (
              <div key={id} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={id}
                  id={`level-${id}`}
                  className="h-[14px] w-[14px] border-text-700 fill-current text-text-700  data-[state=checked]:border-brand-500 data-[state=checked]:bg-white data-[state=checked]:text-brand-500"
                />
                <Label
                  htmlFor={`level-${id}`}
                  className="cursor-pointer text-sm text-text-700 checked:text-brand-500"
                >
                  {label}
                </Label>
              </div>
            );
          })}
        </RadioGroup>
      </PopoverContent>
    </Popover>
  );
};

export default Industries;
