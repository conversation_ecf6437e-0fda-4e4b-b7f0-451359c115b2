"use client";
import { CompanyType } from "@/types/company";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import CardPageCompany from "../../shared/card-page-company/page";
import PaginationCustom from "../../shared/pagination/page";
const CompanyList = ({
  data,
  total = 0,
}: {
  data: CompanyType[];
  total: number;
}) => {
  const searchParams = useSearchParams();
  const initialPage = Number(searchParams.get("page") || 1);
  const [page, setPage] = useState(initialPage);
  const router = useRouter();
  const handlePageChange = (newPage: number) => {
    if (newPage === page) return;
    setPage(newPage);
    fetchJobs(newPage, false); // replace
  };
  const fetchJobs = async (page: number, append = false) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", String(page));
    router.replace(`?${params.toString()}`, { scroll: false });
  };

  return (
    <>
      <div className="grid gap-6 md:grid-cols-3">
        {data?.map((company: CompanyType) => {
          return <CardPageCompany key={company?.id} company={company} />;
        })}
      </div>
      {data?.length === 0 ? (
        <span className="block w-full text-center text-2xl text-text-700">
          No results found for your search.
        </span>
      ) : (
        <></>
      )}
      <div className="mt-8">
        <PaginationCustom
          totalItems={total}
          currentPage={page}
          onPageChange={handlePageChange}
        />
      </div>
    </>
  );
};

export default CompanyList;
