"use client";
import React, { useState } from "react";
import { Input } from "../../ui/input";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "../../ui/button";
import { Search } from "lucide-react";

const SearchInput = () => {
  const searchParams = useSearchParams();
  const [value, setValue] = useState<string>();
  const router = useRouter();

  const handleClickSearch = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("keyword", String(value));
    params.set("page", "1");
    router.replace(`?${params.toString()}`, { scroll: false });
  };
  return (
    <div className="relative flex w-full gap-2 bg-brand-100 p-2 md:p-[10px]">
      <Input
        type="input"
        placeholder="Nhập tên công ty"
        defaultValue={searchParams.get("keyword") ?? ""}
        className="shadow-none h-12 w-full rounded-[4px] border border-text-200 bg-white pl-[57px] text-sm placeholder-text-300 ring-0 md:h-14 md:pl-[77px] md:text-base"
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={(event) => {
          if (event.key === "Enter") {
            handleClickSearch();
          }
        }}
      />
      <span className="absolute left-8 top-0 translate-y-[80%] md:left-10 md:translate-y-[100%]">
        <Search className="text-brand-500" />
      </span>
      <Button
        onClick={handleClickSearch}
        variant="ghost"
        className="h-12 w-44 rounded-[4px] bg-brand-500 text-white md:h-14"
      >
        Search
      </Button>
    </div>
  );
};

export default SearchInput;
