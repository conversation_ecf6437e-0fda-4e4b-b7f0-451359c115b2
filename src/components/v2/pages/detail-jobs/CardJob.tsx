import { cn } from "@/lib/utils";
import { JobType } from "@/types/job";
import { isMobile } from "@/utils/device";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { HiClock } from "react-icons/hi2";
import { Calendar, Clock, LevelUp, Location, MoneyIcon } from "../../icons";
import { ApplyModal } from "../../shared/apply-modal/page";
import SkillsArr from "../../shared/skills-arr/page";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";

const CardJob = async ({
  className = "",
  job,
}: {
  className?: string;
  job: JobType;
}) => {
  const t = useTranslations();
  // const handleBtnFollowClick = () => {
  //   if (!isLoggedIn) {
  //     openLoginPopup([
  //       {
  //         name: "referring_name",
  //         value: "save_job",
  //       },
  //     ]);
  //     return;
  //   }

  //   if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
  //     Swal.fire({
  //       title: t("common_sorry"),
  //       text: t("detail_job_page_not_right_access"),
  //       icon: "warning",
  //       confirmButtonColor: "#DD3F24",
  //     });

  //     return;
  //   }

  //   followJobApi(job?.id).then((response) => {
  //     const { data } = response.data;
  //     if (data.is_followed) {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_followed_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     } else {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_unfollow_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     }
  //   });
  // };
  return (
    <>
      <Card
        className={cn(
          "mb-6 w-full rounded-xl border border-text-200 bg-white  transition-colors md:h-fit",
          className,
        )}
      >
        <CardContent className="relative flex gap-2 p-3">
          <div
            // href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
            className="w-full"
          >
            <div className="flex flex-col gap-[6px] md:flex-row md:gap-3">
              <Image
                src={job?.company?.image_logo}
                alt="job-image"
                width={112}
                height={112}
                className="h-[72px] w-[72px] rounded-[4px] object-contain md:h-[112px] md:w-[112px]"
              />
              <div className="flex w-full flex-col justify-between">
                <span className="line-clamp-1 text-sm/[18px] font-semibold text-brand-500 md:line-clamp-1 md:text-base/[24px]">
                  {job?.title}
                </span>
                <span className="line-clamp-1 text-xs/[16px] font-medium text-text-500">
                  {job?.company?.display_name}
                </span>
                <span className="line-clamp-1 flex items-center gap-[6px] text-sm/[20px] font-semibold text-brand-500">
                  <MoneyIcon /> {job?.salary?.value}
                </span>
                <div className="my-2 grid grid-cols-2 gap-[6px] md:mb-2 md:mt-1 md:gap-0">
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <div className="block h-[14px] w-[14px]">
                      <Location />
                    </div>
                    <span className="line-clamp-1">
                      {job?.addresses?.sort_addresses}
                    </span>
                  </span>
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <LevelUp /> {job?.job_levels_str}
                  </span>
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <Clock /> Fulltime
                  </span>
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <Calendar /> 6+ years exp
                  </span>
                </div>
              </div>
            </div>
            <div className="mt-3 flex flex-col gap-3">
              {isMobile() ? <SkillsArr job={job} /> : <></>}

              <div className="flex items-center justify-between">
                <span className="flex w-fit items-center gap-1 bg-text-50 p-1 text-xs text-text-500">
                  <HiClock />
                  Hạn nộp hồ sơ: {job?.published?.since}
                </span>
              </div>
              <div className="hidden items-center gap-2 md:flex">
                <ApplyModal job={job} />
                <Button
                  className="h-12 w-[30%] rounded-[4px] border border-brand-500 text-sm font-semibold text-brand-500"
                  variant="ghost"
                >
                  Save this job
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default CardJob;
