import React from "react";
import Image from "next/image";
import { JobType } from "@/types/job";
import { MoneyIcon } from "../../icons";
import FavoriteButton from "../../shared/favorite-button/page";
import Link from "next/link";
import { MoveRight } from "lucide-react";

const SameJobs = ({ jobs }: { jobs: JobType[] }) => {
  return (
    <div className="border-text-200 mt-6 rounded-xl border px-5 py-6">
      <span className="text-text-900 mb-4 block text-xl font-semibold">
        Việc làm cùng công ty
      </span>
      {jobs?.map((job) => (
        <div
          key={job?.id}
          className="border-brand-200 rounded-xl border-[0.5px] bg-[#F8FBFF] px-5 py-3"
        >
          <div className="mb-2 flex gap-3">
            <Image
              src={job?.company?.image_logo}
              alt="job-image"
              width={40}
              height={40}
              className="border-brand-500 h-[40px] w-[40px] rounded-[4px] border object-contain md:h-[40px] md:w-[40px]"
            />
            <span className="text-text-700 font-semibold">{job?.title}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-brand-500 line-clamp-1 flex items-center gap-[6px] text-sm/[20px] font-semibold">
              <MoneyIcon /> {job?.salary?.value}
            </span>
            <div className="block">
              <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
            </div>
          </div>
        </div>
      ))}
      <Link
        href={"/jobs"}
        className="text-brand-500 mt-5 flex w-full items-center justify-center gap-1 text-sm"
      >
        Xem thêm công việc <MoveRight className="h-3 w-3" />
      </Link>
    </div>
  );
};

export default SameJobs;
