import { ChevronRight } from "lucide-react";
import Link from "next/link";
import React from "react";

const CandidatesSupporters = () => {
  return (
    <div className="mt-6 bg-brand-100 p-4 shadow-[0px_0px_4px_0px_#20356C4D]">
      <span className="font-semibold text-brand-500">
        ⚙️ Candidates supporters
      </span>
      <div className="mt-3 flex flex-col gap-2 rounded-xl bg-white p-4">
        <span className="text-sm font-semibold text-text-900">
          🧑🏾‍💻 Prepare for interviews
        </span>
        <span>
          Checking TopDev QnA tool to practice your answers to common interview
          questions.
        </span>
        <Link
          href={"/jobs"}
          className="flex items-center gap-1 text-sm font-semibold text-brand-500"
        >
          Read QnA for interviews <ChevronRight className="h-3 w-3" />
        </Link>
      </div>
    </div>
  );
};

export default CandidatesSupporters;
