import { JobType } from "@/types/job";
import React from "react";
import ContentJob from "./ContentJob";
import <PERSON><PERSON>ob from "./CardJob";
import Robot from "../../icons/Robot";
import CardCompanyInfo from "./CardCompanyInfo";
import SameJobs from "./SameJobs";
import CandidatesSupporters from "./CandidatesSupporters";
import { getRecommendJobsByJobId } from "@/services/jobAPI";
import { getCurrentLocaleForParams } from "@/utils/locale";
import CardRecommend from "../../shared/card-recommend/page";
import { isMobile } from "@/utils/device";
import { Button } from "../../ui/button";
import FavoriteButton from "../../shared/favorite-button/page";
import { ApplyModal } from "../../shared/apply-modal/page";

const DetailPage = async ({ job }: { job: JobType }) => {
  const jobRecommendData = await getRecommendJobsByJobId(
    job?.id,
    getCurrentLocaleForParams(),
  );
  const validMobile = isMobile();

  const RecommendJobs = () => (
    <>
      {jobRecommendData?.data && jobRecommendData?.data?.length > 0 ? (
        <div className="mt-6 rounded-xl border border-text-200 px-4 py-4 md:py-6">
          <span className="mb-6 hidden gap-1 rounded-xl bg-brand-50 p-4 text-sm text-brand-500 md:flex">
            <span className="block h-4 w-12 md:w-4">
              <Robot />
            </span>
            <span>
              Ứng tuyển nhanh sẽ gửi CV bạn vừa dùng để ứng tuyển trên TopDev.vn
              là Cutemoe_UI/UX Designer.pdf (8:30, 20/06/2024)
            </span>
          </span>
          <span className="text-base font-semibold md:text-[22px]/[28px]">
            More jobs for you
          </span>
          <div className="mt-4 flex flex-col gap-2">
            {jobRecommendData?.data.map((job) => (
              <CardRecommend key={job?.id} job={job} />
            ))}
          </div>
        </div>
      ) : (
        <></>
      )}
    </>
  );

  return (
    <div className="container mx-auto flex flex-col gap-5 px-4 py-7 md:flex-row md:px-0">
      <div className="w-full md:w-[65%]">
        <CardJob job={job} />
        <ContentJob job={job} />
        {!validMobile ? <RecommendJobs /> : <></>}
      </div>
      <div className="w-full md:w-[35%]">
        <CardCompanyInfo job={job} />
        <SameJobs jobs={[job]} />
        {validMobile ? <></> : <CandidatesSupporters />}
        {validMobile ? <RecommendJobs /> : <></>}
      </div>
      {validMobile ? (
        <div className="fixed bottom-0 flex w-full items-center gap-2 bg-white py-3">
          <ApplyModal job={job} />

          <FavoriteButton
            id={job?.id}
            isFollow={job?.is_followed}
            className="h-9 w-9"
          />
        </div>
      ) : (
        <></>
      )}
    </div>
  );
};

export default DetailPage;
