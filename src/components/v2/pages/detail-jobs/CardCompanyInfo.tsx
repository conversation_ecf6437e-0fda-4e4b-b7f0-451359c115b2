import { cn } from "@/lib/utils";
import { JobType } from "@/types/job";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { FaArrowRightLong } from "react-icons/fa6";
import { GoLinkExternal } from "react-icons/go";
import { Calendar, Location } from "../../icons";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import { IoIosPeople } from "react-icons/io";
import { MdMailOutline } from "react-icons/md";

const CardCompanyInfo = ({
  className = "",
  job,
  srcPage,
  mediumPage,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  return (
    <Card
      className={cn(
        "border-brand-500 h-fit w-full rounded-xl border bg-white transition-colors",
        className,
      )}
    >
      <CardContent className="relative flex flex-col items-center p-3 md:p-4">
        <Image
          src={job?.company?.image_logo}
          alt="job-image"
          width={120}
          height={120}
          className="border-brand-500 rounded-[4px] border bg-white object-contain p-3 md:h-[120px] md:w-[120px]"
        />
        <Link
          href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
          target="_blank"
        >
          <div className="mt-[30px] flex flex-col justify-center gap-[6px] md:flex-row md:gap-3">
            <div className="flex flex-col justify-between">
              <span className="text-brand-500 text-center text-sm/[18px] font-semibold md:line-clamp-2 md:text-2xl">
                {job?.title}
              </span>
              <span className="text-text-300 text-center text-sm/[16px] font-medium">
                {job?.company?.tagline}
              </span>
            </div>
          </div>
          <div className="my-3 flex w-full flex-col items-center justify-between gap-1 md:gap-1">
            <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium">
              <IoIosPeople />
              <span className="line-clamp-1">
                {" "}
                {job?.company?.company_size} Employees
              </span>
            </span>
            <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium md:text-sm">
              <div className="block h-[14px] w-[14px]">
                <Location />
              </div>
              <span className="line-clamp-1">
                {job?.addresses?.sort_addresses}
              </span>
            </span>
            <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium">
              <MdMailOutline />
              20+ jobs
            </span>
          </div>

          <div className="mt-2">
            <div className="border-t-text-400 flex items-center justify-center border-t pt-[6px] md:justify-between md:pt-[7px]"></div>
          </div>
        </Link>
        <Link
          className="text-brand-500 flex w-full items-center justify-center gap-2 text-center text-sm"
          href={job?.company?.detail_url}
        >
          View company <GoLinkExternal />
        </Link>
      </CardContent>
    </Card>
  );
};

export default CardCompanyInfo;
