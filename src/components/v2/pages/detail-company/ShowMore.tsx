"use client";
import { FC, useState, useRef, useEffect, ReactNode } from "react";
import { Button } from "../../ui/button";

interface ShowMoreTextProps {
  text: string | ReactNode;
  maxLines?: number;
  className?: string;
}

const ShowMore: FC<ShowMoreTextProps> = ({ text, maxLines = 5, className }) => {
  const [isTruncated, setIsTruncated] = useState(true);
  const [showMoreVisible, setShowMoreVisible] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const measureRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkLineCount = () => {
      if (measureRef.current) {
        const lineHeight =
          parseFloat(getComputedStyle(measureRef.current).lineHeight) || 20;
        const height = measureRef.current.offsetHeight;
        const lineCount = Math.round(height / lineHeight);

        setShowMoreVisible(lineCount > maxLines);
      }
    };

    checkLineCount();
    window.addEventListener("resize", checkLineCount);
    return () => window.removeEventListener("resize", checkLineCount);
  }, [text, maxLines]);

  return (
    <div className={className}>
      {/* The visible text */}
      <div
        ref={textRef}
        className="text-gray-700"
        style={{
          display: "-webkit-box",
          WebkitLineClamp: isTruncated ? maxLines : "unset",
          WebkitBoxOrient: "vertical",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {text}
      </div>

      {/* Hidden measurement element */}
      <div
        ref={measureRef}
        className="invisible absolute left-0 top-0 h-auto w-full whitespace-normal break-words"
        style={{
          position: "absolute",
          visibility: "hidden",
          pointerEvents: "none",
        }}
      >
        {text}
      </div>

      {showMoreVisible && (
        <Button
          variant="ghost"
          className="inline-block h-fit p-0 text-sm text-blue-500"
          onClick={() => setIsTruncated(!isTruncated)}
        >
          {isTruncated ? "see more" : "see less"}
        </Button>
      )}
    </div>
  );
};

export default ShowMore;
