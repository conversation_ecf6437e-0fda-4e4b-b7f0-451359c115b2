import { FC } from "react";
import { FaFacebookSquare, FaLinkedin, FaYoutube } from "react-icons/fa";
import WebSite from "../../icons/WebSite";
import Link from "next/link";

interface SocialNetwork {
  social: string;
  url: string;
}

const CompanyLinks: FC<{ socialNetwork: SocialNetwork[]; website: string }> = ({
  socialNetwork,
  website,
}) => {
  const socialIcons: Record<string, React.ComponentType> = {
    facebook: FaFacebookSquare,
    linkedin: FaLinkedin,
    youtube: FaYoutube,
  };

  const socialNames: Record<string, string> = {
    facebook: "Company Fanpage",
    linkedin: "Company Linkedin",
    youtube: "Company Youtube",
  };

  const socialLinks =
    socialNetwork?.reduce(
      (acc, item) => {
        acc[item.social] = item.url;
        return acc;
      },
      {} as Record<string, string>,
    ) || {};

  return (
    <>
      <Link href={website ?? "/"} className="flex items-center gap-1 text-sm">
        <span className="h-[14px] w-[14px]">
          <WebSite />
        </span>
        <span className="hidden md:block">Company Website</span>
      </Link>

      {/* Dynamically render social media links */}
      {Object.keys(socialIcons).map((platform) => {
        if (socialLinks[platform]) {
          const Icon = socialIcons[platform];
          return (
            <Link
              key={platform}
              href={socialLinks[platform]}
              className="flex items-center gap-1 text-sm"
            >
              <span className="h-[14px] w-[14px]">
                <Icon />
              </span>
              <span className="hidden md:block"> {socialNames[platform]}</span>
            </Link>
          );
        }
        return null;
      })}
    </>
  );
};

export default CompanyLinks;
