"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/v2/ui/input";
import { Button } from "@/components/v2/ui/button";
import { FaCopy } from "react-icons/fa";
import { toast } from "sonner";
import { FaCheck } from "react-icons/fa";

const ShareLink = () => {
  const [url, setUrl] = useState("");

  useEffect(() => {
    if (typeof window !== "undefined") {
      setUrl(window.location.href);
    }
  }, []);

  const handleCopy = async () => {
    if (url) {
      toast("Copy!", {
        icon: <FaCheck className="h-5 w-5 text-brand-500" />,
        description: "Sao chép đường dẫn thành công",
      });
      await navigator.clipboard.writeText(url);
    }
  };

  return (
    <div className="relative flex items-center gap-2">
      <Input
        value={url}
        readOnly
        className="h-12 w-full cursor-text rounded border border-text-100 pr-12 text-sm shadow-transparent md:w-[400px] md:text-base"
      />
      <Button
        type="button"
        size="icon"
        variant="ghost"
        className="absolute right-2 rounded-sm bg-brand-50 p-2"
        onClick={handleCopy}
      >
        <FaCopy className="h-4 w-4 text-brand-500" />
      </Button>
    </div>
  );
};

export default ShareLink;
