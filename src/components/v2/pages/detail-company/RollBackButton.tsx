"use client";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

const RollBackButton = ({ name }: { name: string }) => {
  const router = useRouter();

  return (
    <div
      onClick={() => router.back()}
      className="flex items-center gap-6 px-5 py-4"
    >
      <ArrowLeft />
      <span className="text-lg font-bold">{name}</span>
    </div>
  );
};

export default RollBackButton;
