"use client";

import { useState } from "react";
import Image from "next/image";
import { Dialog, DialogContent } from "@/components/v2/ui/dialog";
import { ImageGalleryType } from "@/types/company";

const ImageGallery = ({ images }: { images: ImageGalleryType[] }) => {
  const [selectedImage, setSelectedImage] = useState<ImageGalleryType | null>(
    null,
  );
  const maxVisible = 3;
  const extraCount = images.length - maxVisible;
  return (
    <>
      {images?.slice(0, 3).map((image, index) => (
        <div
          key={image.id}
          className="relative cursor-pointer"
          onClick={() => setSelectedImage(image)}
        >
          <Image
            src={image.url}
            alt={image.name}
            width={396}
            height={396}
            className="h-[396px] rounded-xl object-cover"
          />
          {extraCount > 0 && index === 2 && (
            <div className="absolute inset-0 right-0 flex h-[396px] w-[396px] items-center justify-center rounded-xl bg-[#141414]/75 text-lg font-semibold text-white">
              +{extraCount}
            </div>
          )}
        </div>
      ))}
      <Dialog
        open={!!selectedImage}
        onOpenChange={() => setSelectedImage(null)}
      >
        <DialogContent className="shadow-none max-w-4xl border-0 bg-transparent p-0">
          {selectedImage && (
            <div className="relative flex items-center justify-center">
              <Image
                src={selectedImage.url}
                alt={selectedImage.name}
                width={1000}
                height={800}
                className="rounded-2xl h-[70vh] w-auto object-contain"
              />
            </div>
          )}
          <div className="flex items-center justify-center gap-3 overflow-x-auto pb-2">
            {images.map((image) => (
              <button
                key={image.id}
                onClick={() => setSelectedImage(image)}
                className={`relative h-30 w-36 flex-shrink-0 overflow-hidden rounded-xl border-2 transition-all ${
                  selectedImage?.id === image.id
                    ? "border-brand-500"
                    : "border-transparent hover:border-gray-300"
                }`}
              >
                <Image
                  src={image.url}
                  alt={image.name}
                  fill
                  className="object-cover"
                />
              </button>
            ))}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ImageGallery;
