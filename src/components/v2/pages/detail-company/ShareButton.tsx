"use client";
import { FaCheck, FaFacebookSquare } from "react-icons/fa";
import { Button } from "../../ui/button";

import { shareFacebookApply } from "@/services/promotion";
import { toast } from "sonner";
import { usePathname } from "next/navigation";

const ShareButton = ({ id }: { id: number }) => {
  const pathName = usePathname();
  const share = () => {
    const currentUrl = process.env.NEXT_PUBLIC_BASE_URL + pathName;

    const popupShareWindow = window.open(
      "http://facebook.com/sharer/sharer.php?u=" +
        encodeURIComponent(currentUrl),
      "",
      "left=0,top=0,width=650,height=420,personalbar=0,toolbar=0,scrollbars=0,resizable=0",
    );

    if (!popupShareWindow) {
      alert("Vui lòng cho phép cửa sổ pop-up để thực hiện chia sẻ.");
      return;
    }

    if (id) {
      const handleFocus = () => {
        shareFacebookApply(id).then(({ data }) => {
          if (data?.success) {
            toast("Copy!", {
              icon: <FaCheck className="h-5 w-5 text-brand-500" />,
              description: "Chia sẻ thành công!",
            });
          }
        });

        window.removeEventListener("focus", handleFocus);
      };

      window.addEventListener("focus", handleFocus);
    }
  };
  return (
    <Button variant="ghost" type="button" onClick={share} className="p-0">
      <FaFacebookSquare className="h-6 w-6 text-brand-500" />
    </Button>
  );
};

export default ShareButton;
