import { ProductType } from "@/types/company";
import React from "react";
import Image from "next/image";
const Products = ({ products }: { products: ProductType[] }) => {
  return (
    <div>
      {products?.map((product) => (
        <div
          key={product.link}
          className="flex w-full gap-3 rounded-xl border border-text-200 p-1 md:w-1/2 md:rounded md:py-2 md:pl-11 md:pr-2"
        >
          <Image
            src={products[0]?.image}
            alt={products[0].name}
            width={154}
            height={80}
            className="h-[80px] rounded-xl object-cover"
          />
          <span className="max-h-[80px] overflow-auto text-sm text-text-700">
            {products[0]?.description}
          </span>
        </div>
      ))}
    </div>
  );
};

export default Products;
