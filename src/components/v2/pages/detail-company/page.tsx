import { getListJobOpenings } from "@/services/companyAPI";
import { COMPANY_DEFAULT_PARAMS, CompanyType } from "@/types/company";
import { JOB_DEFAULT_PARAMS } from "@/types/job";
import Image from "next/image";
import {
  BsBoxFill,
  BsCone,
  BsFillLaptopFill,
  BsFillLayersFill,
  BsFillLightbulbFill,
  BsFillLightningChargeFill,
  BsFillPenFill,
  BsFillRocketTakeoffFill,
  BsFillSendFill,
  BsGiftFill,
  BsHurricane,
  BsWrenchAdjustableCircleFill,
} from "react-icons/bs";

import { ArrowLeft, Plus } from "lucide-react";
import { Location } from "../../icons";
import CardSearch from "../../shared/card-search/page";
import { Button } from "../../ui/button";
import CompanyLinks from "./ComanyLink";
import ImageGallery from "./ImageGallery";
import SliderMobile from "./SliderMobile";
import Products from "./Products";
import ShareButton from "./ShareButton";
import ShareLink from "./ShareLink";
import ShowMore from "./ShowMore";
import { isMobile } from "@/utils/device";
import Link from "next/link";
import RollBackButton from "./RollBackButton";

const icons = [
  BsFillLayersFill,
  BsFillLaptopFill,
  BsFillLightbulbFill,
  BsFillLightningChargeFill,
  BsFillPenFill,
  BsFillSendFill,
  BsFillRocketTakeoffFill,
  BsGiftFill,
  BsHurricane,
  BsWrenchAdjustableCircleFill,
  BsBoxFill,
  BsCone,
];

const Title = ({ text }: { text: string }) => {
  return (
    <span className="text-base font-semibold text-brand-500 md:text-[22px]/[28px]">
      {text}
    </span>
  );
};

const DetailCompany = async ({ data }: { data: CompanyType }) => {
  const page = 1;
  const params = {
    "fields[job]": JOB_DEFAULT_PARAMS.join(","),
    "fields[company]": COMPANY_DEFAULT_PARAMS.join(","),
    ordering: "newest_job",
    page,
  };
  const listJobOpenings = await getListJobOpenings(data?.id, params);
  const shuffleArray = <T,>(arr: T[]): T[] => {
    const copy = [...arr];
    for (let i = copy.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [copy[i], copy[j]] = [copy[j], copy[i]];
    }
    return copy;
  };
  const shuffledIcons = shuffleArray(icons);

  return (
    <>
      {isMobile() ? (
        <>
          <RollBackButton name={data?.display_name} />
          <div className="top relative h-[167px] w-screen">
            <Image
              src={data?.image_cover}
              alt={data?.slug}
              fill
              className="object-cover"
            />
          </div>
        </>
      ) : (
        <></>
      )}
      <div className="container mx-auto -translate-y-20 px-4 md:-translate-y-0 md:px-0">
        <div className="overflow-hidden rounded-xl md:border md:border-text-200 md:px-0">
          {isMobile() ? (
            <></>
          ) : (
            <div className="relative h-[167px] w-full md:h-64">
              <Image
                src={data?.image_cover}
                alt={data?.slug}
                fill
                className="object-cover"
              />
              <div className="absolute bottom-0 left-44 flex translate-y-1/2 items-end gap-2">
                <Image
                  src={
                    data?.image_logo ??
                    "https://c.topdevvn.com/uploads/2025/08/26/default-topdev.png"
                  }
                  alt="company-image-123123"
                  loading="lazy"
                  className="h-32 w-32 rounded-sm border-[2px] border-brand-500 bg-white object-contain"
                  width={128}
                  height={128}
                />
              </div>
            </div>
          )}
          <div className="rounded-xl border border-text-200 bg-white p-4 md:mt-[88px] md:rounded-none md:border-none md:px-28">
            <Image
              src={
                data?.image_logo ??
                "https://c.topdevvn.com/uploads/2025/08/26/default-topdev.png"
              }
              alt="company-image-123123"
              loading="lazy"
              className="w-h-14 block h-14 rounded-sm border border-brand-500 bg-white object-contain md:hidden"
              width={56}
              height={56}
            />
            <div className="flex items-center justify-between">
              <span className="block text-xl font-semibold text-brand-500 md:text-3xl">
                {data?.display_name}
              </span>
              {isMobile() ? (
                <></>
              ) : (
                <Button
                  variant="ghost"
                  className="h-[30px] rounded-full border border-brand-500 bg-brand-50 text-sm text-brand-500"
                >
                  <Plus /> Follow
                </Button>
              )}
            </div>
            <span className="block text-sm font-medium text-text-700 md:my-1 md:text-base">
              {data?.tagline}
            </span>
            <div className="mb-[10px] flex flex-col gap-1 md:mb-5">
              {data?.addresses?.full_addresses?.map((address) => (
                <span className="flex text-sm text-text-400" key={address}>
                  <span className="block h-4 w-4 pt-1">
                    <Location />
                  </span>
                  {address}
                </span>
              ))}
            </div>
            <div className="flex flex-col gap-1 md:mt-0 md:flex-row md:items-center md:gap-10">
              <div className="flex gap-1 md:flex-col">
                <span className="text-sm text-text-400">Country:</span>
                {data?.nationalities_arr.map((national) => (
                  <span
                    key={national?.flag}
                    className="flex items-center gap-2 text-sm font-semibold text-text-700"
                  >
                    <span>{national.national}</span>
                    {national.flag && (
                      <Image
                        src={national.flag}
                        alt={national.national}
                        width={26}
                        height={18}
                        className="h-[18px] w-[26px] max-w-full object-contain"
                      />
                    )}
                  </span>
                ))}
              </div>
              <div className="flex gap-1 md:flex-col">
                <span className="text-sm text-text-400">Company industry:</span>
                {data?.industries_arr.map((industry) => (
                  <span
                    key={industry}
                    className="flex gap-2 text-sm font-semibold text-text-700"
                  >
                    <span>{industry}</span>
                  </span>
                ))}
              </div>
              <div className="flex gap-1 md:flex-col">
                <span className="text-sm text-text-400">Company size:</span>
                <span className="text-sm font-semibold text-text-700">
                  {data?.company_size}
                </span>
              </div>
            </div>
            <div className="mt-[10px] flex items-center gap-10 text-brand-600 md:mt-5">
              <CompanyLinks
                website={data?.website}
                socialNetwork={data?.social_network}
              />
            </div>
            {isMobile() ? (
              <Button
                variant="ghost"
                className="mt-4 h-[30px] rounded-full border border-brand-500 bg-brand-50 text-sm text-brand-500"
              >
                <Plus className="h-4 w-4" /> Follow
              </Button>
            ) : (
              <></>
            )}
          </div>
        </div>
        <div className="my-6 rounded-xl border border-text-200 p-4 md:my-10 md:p-6">
          <Title text="COMPANY OVERVIEW" />
          <span className="my-3 block h-[1px] w-full bg-text-200"></span>
          <ShowMore
            text={
              <div
                dangerouslySetInnerHTML={{
                  __html: data.description,
                }}
                className="text-sm md:text-base"
              ></div>
            }
          />
        </div>
        {data?.benefits?.length > 0 ? (
          <div className="my-6 rounded-xl border border-text-200 p-4 md:my-10 md:p-6">
            <Title text="BENEFITS" />
            <span className="my-3 block h-[1px] w-full bg-text-200"></span>
            <div className="flex flex-col gap-2">
              {data?.benefits?.map((benefit, index) => {
                const Icon = shuffledIcons[index % shuffledIcons.length];
                return (
                  <span
                    key={benefit?.value || index}
                    className="flex items-center gap-2 text-sm md:text-base"
                  >
                    <span className="text-5 h-5 w-5 text-brand-500 md:h-6 md:w-6 md:text-[26px]">
                      <Icon />
                    </span>
                    {benefit?.value}
                  </span>
                );
              })}
            </div>
          </div>
        ) : (
          <></>
        )}
        {data?.image_galleries?.length > 0 ? (
          <div className="my-6 rounded-xl border border-text-200 p-4 md:my-10 md:p-6">
            <Title text="OUR PEOPLE" />
            <span className="my-2 block h-[1px] w-full bg-text-200 md:my-3"></span>
            <div className="flex w-full gap-[14px]">
              {isMobile() ? (
                <SliderMobile images={data?.image_galleries} />
              ) : (
                <ImageGallery images={data?.image_galleries} />
              )}
            </div>
          </div>
        ) : (
          <></>
        )}
        {listJobOpenings?.data?.length > 0 ? (
          <div className="my-6 rounded-xl border border-text-200 p-4 md:my-10 md:p-6">
            <Title text="JOB OPENINGS" />
            <div className="mt-3 grid gap-3 md:grid-cols-3 md:gap-4">
              {listJobOpenings?.data
                ?.slice(0, 9)
                .map((job) => (
                  <CardSearch
                    key={job?.id}
                    job={job}
                    className="border border-text-200 md:w-[392px]"
                  />
                ))}
            </div>
          </div>
        ) : (
          <></>
        )}
        {data?.products?.length > 0 ? (
          <div className="my-6 rounded-xl border border-text-200 p-4 md:my-10 md:p-6">
            <Title text="PRODUCT" />
            <Products products={data?.products} />
          </div>
        ) : (
          <></>
        )}
        <div className="my-6 rounded-xl border border-text-200 p-4 md:my-10 md:p-6">
          <span className="text-xs font-semibold text-brand-500 md:text-[22px]/[28px]">
            DON&apos;T FORGET TO SHARE WITH YOUR FRIENDS
          </span>
          <span className="my-3 block h-[1px] w-full bg-text-200"></span>
          <div className="flex flex-col justify-between md:flex-row md:items-center">
            <div className="flex flex-col gap-3 md:flex-row md:items-center">
              <span className="text-sm font-semibold text-text-700">
                Chia sẻ qua đường dẫn
              </span>
              <ShareLink />
            </div>
            <div className="mt-4 flex items-center gap-2 md:mt-0">
              <span className="mr-2 block text-sm font-semibold text-text-700">
                Chia sẻ qua mạng xã hội
              </span>
              <ShareButton id={data?.id} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DetailCompany;
