"use client";
import Image from "next/image";

import { Card, CardContent } from "@/components/v2/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/v2/ui/carousel";
import { useCarouselControls } from "@/hooks/useCarouselControls";
import { cn } from "@/lib/utils";
import { ImageGalleryType } from "@/types/company";
import { useEffect, useState } from "react";
import { isMobile } from "react-device-detect";
const SliderMobile = ({ images }: { images: ImageGalleryType[] }) => {
  const { api, setApi } = useCarouselControls();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  useEffect(() => {
    if (!api) return;

    const updateCarouselState = () => {
      setCurrentIndex(api.selectedScrollSnap());
      setTotalItems(api.scrollSnapList().length);
    };

    updateCarouselState();
    api.on("select", updateCarouselState);

    return () => {
      api.off("select", updateCarouselState); // Clean up on unmount
    };
  }, [api]);
  const scrollToIndex = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };
  return (
    <div className="w-full">
      <Carousel
        opts={{
          align: "start",
          slidesToScroll: isMobile ? 1 : 2,
        }}
        setApi={setApi}
      >
        <CarouselContent>
          {images?.length > 0 &&
            images?.map((item, index) => (
              <CarouselItem
                key={index}
                className="shrink-0 basis-[80%] md:basis-1/2"
              >
                <Card className="h-[224px] overflow-hidden rounded-xl border-none">
                  <CardContent className="rounded-xl bg-brand-100 p-0">
                    <div className="relative flex h-[224px] w-full items-center justify-center">
                      <Image
                        src={item.url}
                        alt={item.name}
                        fill
                        className="rounded-2xl w-auto object-cover"
                      />
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
        </CarouselContent>
      </Carousel>
      <div className="mt-2 flex justify-center space-x-2 md:mt-0 md:hidden">
        {Array.from({ length: totalItems }).map((_, index) => (
          <button
            key={index}
            onClick={() => scrollToIndex(index)}
            className={cn(
              "relative h-2 w-2 rounded-full transition-all md:h-3 md:w-3",
              currentIndex === index ? "bg-white" : "bg-brand-200",
            )}
            aria-label={`Go to slide ${index + 1}`}
          >
            {currentIndex === index && (
              <span className="absolute inset-0 m-auto h-[5px] w-[5px] rounded-full bg-brand-600 md:h-[7px] md:w-[7px]" />
            )}
            {currentIndex === index && (
              <span className="absolute inset-0 m-auto h-2 w-2 rounded-full border border-brand-600 md:h-3 md:w-3" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
};
export default SliderMobile;
