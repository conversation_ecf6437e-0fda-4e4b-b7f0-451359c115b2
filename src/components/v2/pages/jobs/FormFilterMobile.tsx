"use client";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { BsBarChartLineFill } from "react-icons/bs";
import { MdOutlineArrowDropDown } from "react-icons/md";

import { getTaxonomiesDynamic } from "@/services/taxonomyAPI";
import { TaxonomyType } from "@/types/taxonomy";
import { useLocale } from "next-intl";
import { Checkbox } from "../../ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "../../ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";
import { FilterAdvanced } from "./FilterAdvanced";

import { Label } from "@/components/v2/ui/label";
import { getAllProvincesCache } from "@/services/areaAPI";
import { FormValueSearch } from "@/types/search";
import { useRouter, useSearchParams } from "next/navigation";
import { HiLocationMarker } from "react-icons/hi";
import { useDebounce } from "usehooks-ts";
import { SingleSelectCheckboxDropdown } from "./(component)/SingleSelectCheckboxDropdown";

const FormFilterMobile = () => {
  const locale = useLocale();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [levels, setLevels] = useState<TaxonomyType[]>([]);
  const [benefits, setBenefits] = useState<TaxonomyType[]>([]);
  const [companySize, setCompanySize] = useState<TaxonomyType[]>([]);
  const [workTypes, setWorkTypes] = useState<TaxonomyType[]>([]);
  const [contractTypes, setContractTypes] = useState<TaxonomyType[]>([]);
  const [industries, setIndustries] = useState<TaxonomyType[]>([]);
  const [open, setOpen] = useState(false);
  const [valueForm, setValueForm] = useState<FormValueSearch>({
    job_levels_ids: "",
    company_size: "",
    benefit_ids: [],
    work_type: "",
    salary_range: [],
    experience: "",
    contract_type: "",
    region_ids: [],
    company_industry_ids: [],
  });
  const [locations, setLocations] = useState<
    { label: string; value: string }[]
  >([]);
  const form = useForm<FormValueSearch>({
    defaultValues: {
      region_ids: [],
      job_levels_ids: "",
    },
  });

  const region = form.watch("region_ids");
  const levelCandidate = form.watch("job_levels_ids");
  const debouncedRegion = useDebounce(region, 300);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const fetchTaxonomies = await getTaxonomiesDynamic([
          "benefits",
          "job_levels",
          "num_employees",
          "job_types",
          "contract_types",
          "industries",
        ]);
        const {
          job_levels,
          benefits,
          contract_types,
          industries,
          job_types,
          num_employees,
        } = fetchTaxonomies;
        if (job_levels) {
          setLevels(job_levels);
        }
        if (benefits) {
          setBenefits(benefits);
        }
        if (num_employees) {
          setCompanySize(num_employees);
        }
        if (job_types) {
          setWorkTypes(job_types);
        }
        if (contract_types) {
          setContractTypes(contract_types);
        }
        if (industries) {
          setIndustries(industries);
        }
      } catch (error) {
        console.error("Failed to fetch initial data:", error);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const data = await getAllProvincesCache();
        setLocations(
          data.map((province: { id: string; text: string }) => {
            return {
              label: province.text,
              value: province.id,
            };
          }),
        );
      } catch (error) {
        console.error("Failed to fetch locations:", error);
      }
    };
    fetchLocations();
  }, []);

  const onSubmit = (data: any) => {
    console.log(data); // Outputs { email: '1', benefits: ['1', '2', ...] }
  };
  const handleClick = () => {
    const allData = form.getValues();
    setValueForm({ ...allData });
  };
  useEffect(() => {
    const benefitIds =
      searchParams
        .get("benefit_ids")
        ?.split(",")
        .map((id) => +id) ?? [];
    const companies =
      searchParams
        .get("company_industry_ids")
        ?.split(",")
        .map((id) => +id) ?? [];
    const regions =
      searchParams
        .get("region_ids")
        ?.split(",")
        .map((value) => value) ?? [];
    form.reset({
      salary_range: [0, 50000000],
      job_levels_ids: searchParams.get("job_levels_ids") ?? "",
      company_size: searchParams.get("company_size") ?? "",
      work_type: searchParams.get("work_type") ?? "",
      region_ids: regions ?? "",
      company_industry_ids: companies ?? [],
      benefit_ids: benefitIds ?? [],
      salary_min: searchParams.get("salary_min")
        ? Number(searchParams.get("salary_min"))
        : 0,
      salary_max: searchParams.get("salary_max")
        ? Number(searchParams.get("salary_max"))
        : 50000000,
    });
  }, [searchParams, form]);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (!debouncedRegion || debouncedRegion.length === 0) {
      params.delete("region_ids");
    } else {
      params.set("region_ids", debouncedRegion.join(","));
    }

    router.replace(`?${params.toString()}`, { scroll: false });
  }, [debouncedRegion, router]);
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (!levelCandidate) {
      params.delete("job_levels_ids");
    } else {
      params.set("job_levels_ids", levelCandidate);
    }

    router.replace(`?${params.toString()}`, { scroll: false });
  }, [levelCandidate, router]);

  const onChangeLocation = (data: any) => {
    form.setValue("region_ids", data);
  };
  return (
    <div className="flex items-center gap-2 py-4">
      <FilterAdvanced
        benefits={benefits}
        level={levels}
        companySize={companySize}
        workTypes={workTypes}
        contractTypes={contractTypes}
        industries={industries}
        onOpenFilter={handleClick}
        defaultValue={valueForm}
      />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex items-center gap-2"
        >
          <FormField
            control={form.control}
            name="region_ids"
            render={() => (
              <FormItem>
                <FormControl>
                  <Controller
                    control={form.control}
                    name="region_ids"
                    render={({ field: { onChange, value } }) => (
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <div className="flex h-6 w-fit cursor-pointer items-center gap-1 rounded-[64px] border border-brand-500 bg-white px-3 py-1 text-xs text-brand-500 md:text-sm">
                            <HiLocationMarker className="h-3 w-3 text-brand-500" />
                            {value && value?.length > 0 ? (
                              <span className="flex items-center ">
                                {(() => {
                                  const labels = value
                                    .map((value) => {
                                      const location = locations.find(
                                        (b) => b.value === value,
                                      );
                                      return location
                                        ? locale === "vi"
                                          ? location.label
                                          : location.label
                                        : "";
                                    })
                                    .filter((label) => label); // remove empty

                                  const firstLabel = labels[0];
                                  const moreCount = labels.length - 1;

                                  return moreCount > 0
                                    ? `${firstLabel} +${moreCount}`
                                    : firstLabel;
                                })()}
                                <MdOutlineArrowDropDown className="h-4 w-4 text-brand-500" />
                              </span>
                            ) : (
                              <span className="flex items-center">
                                Location{" "}
                                <MdOutlineArrowDropDown className="h-4 w-4 text-brand-500" />
                              </span>
                            )}
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="h-[235px] w-[220px] overflow-auto rounded-[8px] border-none bg-white p-2">
                          {value &&
                            locations.map((location) => (
                              <div
                                key={location.value}
                                className="flex items-center gap-2 p-2"
                              >
                                <Checkbox
                                  checked={(value as string[]).includes(
                                    location.value,
                                  )}
                                  id={`location-${location.value}`}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      const newValue = [
                                        ...(value ?? []),
                                        location.value,
                                      ];
                                      onChangeLocation(newValue);
                                      onChange(newValue);
                                    } else {
                                      const newValue = value?.filter(
                                        (id) => id !== location.value,
                                      );
                                      onChangeLocation(newValue);
                                      onChange(newValue);
                                    }
                                  }}
                                  className="rounded-[4px] border-text-700 fill-current text-text-700 data-[state=checked]:border-none data-[state=checked]:bg-brand-500  data-[state=checked]:text-white"
                                />
                                <Label
                                  htmlFor={`location-${location.value}`}
                                  className="cursor-pointer text-sm text-text-700 checked:text-brand-500"
                                >
                                  {locale === "vi"
                                    ? location.label
                                    : location.label}
                                </Label>
                              </div>
                            ))}
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="job_levels_ids"
            render={() => (
              <SingleSelectCheckboxDropdown
                label="Experience level"
                name="job_levels_ids"
                data={levels}
                locale={locale}
                icon={
                  <BsBarChartLineFill className="h-3 w-3 text-brand-500 md:h-4 md:w-4" />
                }
              />
            )}
          />
        </form>
      </Form>
    </div>
  );
};

export default FormFilterMobile;
