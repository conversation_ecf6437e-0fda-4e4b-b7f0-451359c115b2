"use client";
import { getAllProvincesCache } from "@/services/areaAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import { setLocations as setLocationRedux } from "@/store/slices/searchSlice";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { FaSearch } from "react-icons/fa";
import { HiLocationMarker } from "react-icons/hi";
import { HiOutlineChevronDown } from "react-icons/hi2";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input";

const LocationFilter = () => {
  const t = useTranslations();
  const dispatch = useAppDispatch();
  const locationValues = useAppSelector(
    (state) => state.search.formSearch.locations,
  );
  const [isOpen, setIsOpen] = useState(false);
  const [locations, setLocations] = useState<
    { label: string; value: string }[]
  >([]);
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const data = await getAllProvincesCache();
        setLocations(
          data.map((province: { id: string; text: string }) => {
            return {
              label: province.text,
              value: province.id,
            };
          }),
        );
      } catch (error) {
        console.error("Failed to fetch locations:", error);
      }
    };
    fetchLocations();
  }, []);

  return (
    <div className="relative">
      <Button
        onClick={() => setIsOpen(!isOpen)}
        variant="ghost"
        className="max-w-[150px] items-center px-0"
      >
        <div className="h-4 w-4">
          <HiLocationMarker width={24} height={24} className="text-brand-500" />
        </div>
        {locationValues.length === 0 ? (
          <span className="flex-grow overflow-hidden text-ellipsis whitespace-nowrap text-left text-brand-500">
            {t("search_page_location")}
          </span>
        ) : (
          <span className="flex-grow overflow-hidden text-ellipsis whitespace-nowrap text-left text-brand-500">
            {locations
              .filter((item) => locationValues.includes(item.value))
              .map((item) => item.label)
              .join(", ")}
          </span>
        )}
        {locationValues?.length > 1 && (
          <span className="text-brand-500">+{locationValues?.length}</span>
        )}
        <HiOutlineChevronDown />
      </Button>

      <div
        className={classNames(
          "absolute top-10 z-50 max-h-40 w-max overflow-y-auto rounded bg-white shadow-sm",
          !isOpen ? "hidden" : "",
        )}
      >
        <ul className="w-full">
          {locations.map((location) => (
            <li
              key={location.value}
              className="hover:cursor-pointer hover:bg-gray-100"
            >
              <label className="flex items-center gap-2  px-4 py-2 hover:cursor-pointer">
                <Input
                  type="checkbox"
                  name="location"
                  value={location.value}
                  className="h-4 w-4 rounded outline-none ring-0 checked:bg-brand-500 checked:text-brand-500 focus:shadow-transparent focus:outline-none focus:ring-0"
                  checked={locationValues.includes(location.value)}
                  onChange={() => {
                    dispatch(
                      setLocationRedux(
                        locationValues.includes(location.value)
                          ? locationValues.filter(
                              (item) => item !== location.value,
                            )
                          : [...locationValues, location.value],
                      ),
                    );
                  }}
                />
                <span>{location.label}</span>
              </label>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default function InputSearch() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [searchValue, setSearchValue] = useState<string>("");
  const t = useTranslations();
  const handleClickJobs = useCallback(
    (value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set("keyword", value);

      const basePath = "/jobs/search";

      router.push(`${basePath}?${params.toString()}`);
    },
    [router, searchParams],
  );
  return (
    <div className="relative flex h-[72px] w-full items-center justify-center rounded bg-white px-14">
      <div className="flex w-full">
        <div className="flex w-full items-center">
          <FaSearch width={24} height={24} className="text-brand-500" />
          <Input
            type="text"
            className="focus:shadow-none shadow-none h-6 w-full border-0 border-r-2 border-brand-300 focus:border-0 focus:border-r-2 focus:border-brand-300"
            placeholder={t("main_search_search_placeholder")}
            defaultValue={searchParams.get("keyword") ?? ""}
            onChange={(e) => setSearchValue(e.target.value)}
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                handleClickJobs(searchValue);
              }
            }}
          />
        </div>

        <div className="col-span-2 flex items-center pl-2">
          <LocationFilter />
        </div>
      </div>
    </div>
  );
}
