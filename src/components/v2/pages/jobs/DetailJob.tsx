"use client";
import { usePageSearch } from "@/stores/usePageSearch";
import BigCardSearchCompany from "../../shared/big-card-search-company/page";
import BigCardSearch from "../../shared/big-card-search/page";
import { Badge } from "../../ui/badge";
const DetailJob = () => {
  const job = usePageSearch((state) => state.job);
  if (!job) return null;
  return (
    <div className="w-full rounded-xl border border-text-200 px-4 py-6">
      <div className="sticky top-28 z-[49] w-full">
        <BigCardSearch job={job} />
        <div className="xl:h-[66vh] h-[54vh] overflow-auto">
          {job?.content && (
            <div
              className="py-4 text-sm text-text-600"
              dangerouslySetInnerHTML={{
                __html: job?.content,
              }}
            />
          )}
          <span className="flex items-center gap-1 font-semibold text-[#3659B3]">
            <Badge className="rounded-[4px] border-none bg-brand-100 text-sm text-brand-500 hover:bg-brand-100">
              1
            </Badge>
            Your role & responsibilities
          </span>
          <div className="mt-2">
            {job?.responsibilities_original && (
              <div
                className="prose-ul bg-[#F5F5F5] px-2 py-4 text-sm text-text-900"
                dangerouslySetInnerHTML={{
                  __html: job?.responsibilities_original,
                }}
              />
            )}
          </div>
          <span className="mt-4 flex items-center gap-1 font-semibold text-[#3659B3]">
            <Badge className="rounded-[4px] border-none bg-brand-100 text-sm text-brand-500 hover:bg-brand-100">
              2
            </Badge>
            Your skills & qualifications
          </span>
          <div className="mt-2">
            {job?.requirements_original && (
              <div
                className="prose-ul bg-[#F5F5F5] px-2 py-4 text-sm text-text-900"
                dangerouslySetInnerHTML={{
                  __html: job?.requirements_original,
                }}
              />
            )}
          </div>
          <span className="mt-4 flex items-center gap-1 font-semibold text-[#3659B3]">
            <Badge className="rounded-[4px] border-none bg-brand-100 text-sm text-brand-500 hover:bg-brand-100">
              3
            </Badge>
            Benefits
          </span>
          <div className="mb-5 mt-2">
            {job?.benefits_original && (
              <div
                className="prose-ul bg-[#F5F5F5] px-2 py-4 text-sm text-text-900"
                dangerouslySetInnerHTML={{
                  __html: job?.benefits_original?.[0]?.value,
                }}
              />
            )}
          </div>
          <BigCardSearchCompany job={job} />
        </div>
      </div>
    </div>
  );
};

export default DetailJob;
