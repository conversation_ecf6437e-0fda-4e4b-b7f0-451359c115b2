"use client";

import { onSearchClick } from "@/hooks/useSearch";
import { fetchJobCategories } from "@/services/jobAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  setCategories as setCategoriesReducer,
  setCountRoles,
} from "@/store/slices/searchSlice";
import { Category, Role } from "@/types/job";
import { classNames } from "@/utils";
import { getSelectedCategories } from "@/utils/categoryUtils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { HiChevronRight } from "react-icons/hi2";
import { Button } from "../../ui/button";

// Constants

const CLOSED = "closed";
const OPENED = "opened";

const CATEGORY_MENU_CONFIG = {
  MENU_WIDTH: 255,
  ROLES_WIDTH: 600,
  MAX_COUNT_DISPLAY: 99,
} as const;

const IMAGES = {
  HAMBURGER: "https://c.topdevvn.com/uploads/2025/05/14/hamberger.svg",
  EXPERIENCE_LEVEL:
    "https://c.topdevvn.com/uploads/2025/05/14/experience-level.svg",
} as const;

const ButtonCategory = ({ onClick }: { onClick: () => void }) => {
  const t = useTranslations();
  const countRoles = useAppSelector((state) => state.search.countRoles);
  const displayCount =
    countRoles > CATEGORY_MENU_CONFIG.MAX_COUNT_DISPLAY
      ? `${CATEGORY_MENU_CONFIG.MAX_COUNT_DISPLAY}+`
      : countRoles;

  return (
    <Button
      onClick={onClick}
      variant="ghost"
      className="flex h-[72px] w-44 items-center justify-center rounded bg-brand-500"
    >
      <div className="flex gap-2 text-white">
        <Image
          src={IMAGES.HAMBURGER}
          alt="Menu"
          width={16}
          height={16}
          loading="lazy"
        />
        {t("search_page_categories")} ({displayCount})
      </div>
    </Button>
  );
};

const CategoryItem = ({
  category,
  isSelected,
  children,
}: {
  category: Category;
  isSelected: boolean;
  children: React.ReactNode;
}) => {
  return (
    <div
      className={classNames(
        "group flex h-8 items-center justify-between rounded pl-2 hover:cursor-pointer hover:bg-brand-100",
        isSelected ? "bg-brand-100" : "",
      )}
    >
      <div className="flex items-center whitespace-nowrap">
        <Image
          src={IMAGES.EXPERIENCE_LEVEL}
          alt="Category icon"
          width={10}
          height={10}
          loading="lazy"
        />
        <span
          className={classNames(
            "ml-[4px] inline-block flex-grow text-[14px]/[18px] group-hover:text-brand-500",
            isSelected ? "text-brand-500" : "",
          )}
        >
          {category.name}
        </span>
        <span className="ml-[2px] inline-block text-[14px]/[18px] font-medium text-brand-500">
          {children}
        </span>
      </div>
      <HiChevronRight className="-translate-x-2" />
    </div>
  );
};

const CategoryRole = ({
  role,
  onClick,
  isSelected = false,
}: {
  role: Role;
  onClick: () => void;
  isSelected: boolean;
}) => {
  return (
    <Button
      onClick={onClick}
      className={classNames(
        "mb-3 mr-3 inline-block items-center rounded-full px-8 py-2 text-xs",
        isSelected
          ? "bg-brand-100 font-medium text-brand-500"
          : " bg-[#F6F6F6]",
      )}
    >
      {role.name}
    </Button>
  );
};

interface CategoryMenuProps {
  isVisible: boolean;
  onClose: () => void;
  triggerEvent: string | null;
}

const CategoryMenu = ({
  isVisible,
  onClose,
  triggerEvent,
}: CategoryMenuProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<
    Category | undefined
  >();
  const [activeRoles, setActiveRoles] = useState<number[]>([]);
  const [activeCategories, setActiveCategories] = useState<string[]>([]);

  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const searchState = useAppSelector((state) => state.search.formSearch);
  const t = useTranslations();

  const ids = useMemo(() => {
    const categoryIds = searchParams.get("job_categories_ids");
    return categoryIds ? categoryIds.split(",").map(Number) : [];
  }, [searchParams]);

  // Fetch categories on mount
  useEffect(() => {
    fetchJobCategories().then(setCategories);
  }, []);

  // Map category name to role IDs
  const categoryMap = useMemo(() => {
    return categories.reduce(
      (acc, category) => {
        acc[category.name] = category.roles?.map((role) => role.id) || [];
        return acc;
      },
      {} as Record<string, number[]>,
    );
  }, [categories]);

  // Calculate roles from URL
  const rolesFilterDefault = useMemo(() => {
    if (!ids.length || !categories.length) return [];
    return categories.flatMap(
      (category) =>
        category.roles
          ?.filter((role) => ids.includes(role.id))
          .map((role) => role.id) || [],
    );
  }, [categories, ids]);

  // Get active category names based on activeRoles
  const selectedCategoryNames = useMemo(() => {
    return getSelectedCategories(categoryMap, activeRoles);
  }, [categoryMap, activeRoles]);

  // Sync Redux when roles change
  useEffect(() => {
    dispatch(setCountRoles(activeRoles.length));
  }, [activeRoles, dispatch]);

  // Initial load from URL params
  useEffect(() => {
    setActiveRoles(rolesFilterDefault);
  }, [rolesFilterDefault]);

  useEffect(() => {
    setActiveCategories(selectedCategoryNames);
  }, [selectedCategoryNames]);

  // Reset roles/categories from external event
  //   useEffect(() => {
  //     if (triggerEvent === CLOSED) {
  //       setActiveRoles(rolesFilterDefault);
  //       setActiveCategories(selectedCategoryNames);
  //       setSelectedCategory(undefined);
  //     }
  //   }, [rolesFilterDefault]);

  // Helpers
  const handleClickCategories = useCallback(
    (category: Category) => {
      const isActive = activeCategories.includes(category.name);
      const roleIds = category.roles?.map((r) => r.id) || [];

      setActiveCategories((prev) =>
        isActive
          ? prev.filter((name) => name !== category.name)
          : [...prev, category.name],
      );

      setActiveRoles((prev) =>
        isActive
          ? prev.filter((id) => !roleIds.includes(id))
          : [...prev, ...roleIds],
      );
    },
    [activeCategories],
  );

  const handleButtonClick = useCallback((role: Role) => {
    setActiveRoles((prev) =>
      prev.includes(role.id)
        ? prev.filter((id) => id !== role.id)
        : [...prev, role.id],
    );
  }, []);

  const handleRemoveCheck = useCallback(() => {
    setActiveRoles([]);
    setActiveCategories([]);
    setSelectedCategory(undefined);
  }, []);

  const handleClickJobs = useCallback(() => {
    onSearchClick({ ...searchState, categories: activeRoles });
  }, [searchState, activeRoles]);

  const handleCancel = () => {
    setActiveRoles(rolesFilterDefault);
    setActiveCategories(selectedCategoryNames);
    setSelectedCategory(undefined);
    onClose();
  };

  // Optional: display count
  const calculateItem = (name: string) => {
    const count = categoryMap[name]?.filter((id) => activeRoles.includes(id))
      .length;
    return count ? `(${count})` : null;
  };

  return (
    <div
      className={classNames(
        "absolute top-[80px] z-50 rounded bg-white p-3 shadow-sm",
        !isVisible ? "hidden" : "",
      )}
    >
      <div className="flex flex-col gap-4">
        <div className="flex gap-4">
          <div
            className="border-r border-[#FEE6E2] pr-1"
            style={{ width: CATEGORY_MENU_CONFIG.MENU_WIDTH }}
          >
            <ul className="flex flex-col gap-1">
              {categories.map((category) => (
                <li
                  key={category.id}
                  onMouseOver={() => setSelectedCategory(category)}
                  onClick={() => {
                    handleClickCategories(category);
                  }}
                >
                  <CategoryItem
                    key={category.id}
                    category={category}
                    isSelected={categoryMap[category.name]?.some((roleId) =>
                      activeRoles.includes(roleId),
                    )}
                  >
                    {calculateItem(category.name)}
                  </CategoryItem>
                </li>
              ))}
            </ul>
          </div>
          <div style={{ width: CATEGORY_MENU_CONFIG.ROLES_WIDTH }}>
            {selectedCategory?.roles.map((role) => (
              <CategoryRole
                key={role.id}
                role={role}
                isSelected={activeRoles.includes(role.id)}
                onClick={() => handleButtonClick(role)}
              />
            ))}
          </div>
        </div>
        <hr />
        <div className="flex items-center justify-end gap-3">
          <Button
            className="bg-none text-xs text-brand-500"
            variant="ghost"
            onClick={() => {
              handleRemoveCheck();
              dispatch(setCategoriesReducer([]));
            }}
          >
            {t("search_page_clear_all")} ({activeRoles?.length})
          </Button>
          <Button
            className="rounded-full bg-[#E7E7E7] px-8 py-2 text-xs"
            onClick={() => handleCancel()}
          >
            {t("user_profile_open_to_work_notice_btn_cancel")}
          </Button>
          <Button
            className="rounded-full bg-brand-500 px-8 py-2 text-xs text-white"
            onClick={() => handleClickJobs()}
          >
            {t("search_page_apply")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default function SearchForm() {
  const [isShowMenu, setIsShowMenu] = useState(false);
  const [triggerEvent, setTriggerEvent] = useState<string | null>(null);

  const handleButtonClick = () => {
    setIsShowMenu(!isShowMenu);
    setTriggerEvent(isShowMenu ? CLOSED : OPENED);
  };

  return (
    <div className="relative">
      <ButtonCategory onClick={handleButtonClick} />

      <CategoryMenu
        isVisible={isShowMenu}
        onClose={() => setIsShowMenu(false)}
        triggerEvent={triggerEvent}
      />
    </div>
  );
}
