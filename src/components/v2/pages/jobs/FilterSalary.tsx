"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/v2/ui/form";
import { useFormContext } from "react-hook-form";

export function FilterSalary() {
  const { control, setValue, watch } = useFormContext();
  const minValue = 0;
  const maxValue = 200000000;
  const step = 10;

  const min = watch("salary_min") ?? minValue;
  const max = watch("salary_max") ?? maxValue;

  const formatVND = (value: number) =>
    new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
      maximumFractionDigits: 0,
    }).format(value);

  const percentMin = ((min - minValue) / (maxValue - minValue)) * 100;
  const percentMax = ((max - minValue) / (maxValue - minValue)) * 100;

  const handleMinChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = Math.min(Number(e.target.value), max - step);
    setValue("salary_min", val);
  };

  const handleMaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = Math.max(Number(e.target.value), min + step);
    setValue("salary_max", val);
  };

  return (
    <div className="space-y-4">
      {/* Min field */}
      <FormField
        control={control}
        name="salary_min"
        render={() => (
          <FormItem>
            <FormControl>
              <input type="hidden" value={min} readOnly />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Max field */}
      <FormField
        control={control}
        name="salary_max"
        render={() => (
          <FormItem>
            <FormControl>
              <input type="hidden" value={max} readOnly />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Slider */}
      <div className="relative h-2 rounded bg-text-200">
        {/* Track highlight */}
        <div
          className="absolute h-2 rounded bg-brand-500"
          style={{
            left: `${percentMin}%`,
            width: `${percentMax - percentMin}%`,
          }}
        />

        {/* Floating labels */}
        <div
          className="absolute -top-8 whitespace-nowrap rounded-[4px] bg-brand-500 px-2 py-1 text-xs font-medium text-white"
          style={{ left: `calc(${percentMin}% - 20px)` }}
        >
          {formatVND(min)}
        </div>
        <div
          className="absolute -top-8 whitespace-nowrap rounded-[4px] bg-brand-500 px-2 py-1 text-xs font-medium text-white"
          style={{ left: `calc(${percentMax}% - 20px)` }}
        >
          {formatVND(max)}
        </div>

        {/* Range inputs */}
        <input
          type="range"
          min={minValue}
          max={maxValue}
          step={step}
          value={min}
          onChange={handleMinChange}
          className="pointer-events-none absolute h-2 w-full appearance-none bg-transparent"
          style={{ zIndex: 20 }}
        />
        <input
          type="range"
          min={minValue}
          max={maxValue}
          step={step}
          value={max}
          onChange={handleMaxChange}
          className="pointer-events-none absolute h-2 w-full appearance-none bg-transparent"
          style={{ zIndex: 10 }}
        />

        {/* Thumb styling */}
        <style>{`
          input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            height: 16px;
            width: 16px;
            border-radius: 9999px;
            background: #4876EF;
            box-shadow: 0 0 2px rgba(0,0,0,0.4);
            pointer-events: auto;
          }

          input[type="range"]::-moz-range-thumb {
            height: 16px;
            width: 16px;
            border-radius: 9999px;
            background: #4876EF;
            pointer-events: auto;
          }
        `}</style>
      </div>
    </div>
  );
}
