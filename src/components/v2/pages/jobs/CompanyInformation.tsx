"use client";
import { JobType } from "@/types/job";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import CardCompanySearch from "../../shared/card-company-search/page";
import CarouselButton from "../../shared/carousel-button/page";
import { TypographySection } from "../../shared/typography-section/page";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "../../ui/carousel";
import ViewAll from "../../shared/view-all/page";
const CompanyInformation = ({
  jobs,
  isMobile,
}: {
  jobs: JobType[];
  isMobile: boolean;
}) => {
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  useEffect(() => {
    if (!api) return;

    const updateButtons = () => {
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    updateButtons();
    api.on("select", updateButtons);

    return () => {};
  }, [api]);
  return (
    <div>
      <div className="mb-4 flex items-center justify-between md:mb-8">
        <TypographySection title="COMPANY INFORMATION" />
        {isMobile ? (
          <></>
        ) : (
          <CarouselButton
            onPrev={() => api?.scrollPrev()}
            onNext={() => api?.scrollNext()}
            disabledPrev={!canScrollPrev}
            disabledNext={!canScrollNext}
          />
        )}
      </div>
      <Carousel
        opts={{
          align: "start",
        }}
        setApi={setApi}
        className="w-full"
      >
        <CarouselContent className="-ml-1 md:-ml-6">
          {jobs?.map((job) => (
            <CarouselItem
              key={job?.id}
              // mobile: ~90% => 1.1 item visible
              // tablet: ~45% => 2 item + 1/10 dư (hiển thị 1/3 cái tiếp theo)
              // desktop: ~30% => 3 item + dư
              className="basis-[46%] pl-1 md:basis-1/3 md:pl-6"
            >
              <CardCompanySearch job={job} />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
      <ViewAll className="mt-3 h-fit" />
    </div>
  );
};

export default CompanyInformation;
