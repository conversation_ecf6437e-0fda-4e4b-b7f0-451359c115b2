"use client";

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/v2/ui/form";
import { Label } from "@/components/v2/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/v2/ui/radio-group";
import { TaxonomyType } from "@/types/taxonomy";
import { useLocale } from "next-intl";
import { useFormContext } from "react-hook-form";

export function RadioField({
  name,
  data,
}: {
  name: string;
  data: TaxonomyType[];
}) {
  const locale = useLocale();
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <RadioGroup
              value={field.value}
              onValueChange={field.onChange}
              className="grid grid-cols-2 gap-2"
            >
              {data?.map((item) => {
                const id = String(item.id);
                const label = locale === "vi" ? item.text_vi : item.text_en;
                return (
                  <div key={id} className="flex items-center space-x-2">
                    <RadioGroupItem
                      value={id}
                      id={`${name}-${id}`}
                      className="h-[14px] w-[14px] border-text-700 fill-current text-text-700  data-[state=checked]:border-brand-500 data-[state=checked]:bg-white data-[state=checked]:text-brand-500"
                    />
                    <Label
                      htmlFor={`${name}-${id}`}
                      className="cursor-pointer text-sm text-text-700 checked:text-brand-500"
                    >
                      {label}
                    </Label>
                  </div>
                );
              })}
            </RadioGroup>
          </FormControl>

          <FormMessage />
        </FormItem>
      )}
    />
  );
}
