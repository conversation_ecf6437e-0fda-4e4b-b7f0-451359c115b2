"use client";

import { Badge } from "@/components/v2/ui/badge";
import {
  Command,
  CommandEmpty,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/v2/ui/command";
import { FormField, FormItem, FormMessage } from "@/components/v2/ui/form";
import { cn } from "@/lib/utils";
import { TaxonomyType } from "@/types/taxonomy";
import { useLocale } from "next-intl";
import { useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";
import { IoMdCloseCircle } from "react-icons/io";

export function AutocompleteField({
  data,
  name,
  bottom,
}: {
  data: TaxonomyType[];
  name: string;
  bottom?: boolean;
}) {
  const { control } = useFormContext();
  const [search, setSearch] = useState("");
  const [activeIndex, setActiveIndex] = useState(0);
  const [isFocused, setIsFocused] = useState(false);

  const locale = useLocale();

  useEffect(() => {
    setActiveIndex(0);
  }, [search]);
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const selected = field.value || [];
        const selectedWithValue = data.filter(
          (option) =>
            selected?.some((item: string | number) => item === option?.id),
        );
        const filteredOptions = data.filter((opt) => {
          const text = locale === "vi" ? opt.text_vi : opt.text_en;
          return (
            text.toLowerCase().includes(search.toLowerCase()) &&
            !selectedWithValue.some((sel: any) => sel.id === opt.id)
          );
        });
        return (
          <FormItem className="relative space-y-2">
            <div className="mt-2 flex flex-wrap gap-2">
              {selectedWithValue.map((item: any) => (
                <Badge
                  key={item.id}
                  variant="secondary"
                  className="h-10 gap-1 rounded-[4px] bg-brand-500 text-sm font-semibold text-white"
                >
                  {locale === "vi" ? item.text_vi : item.text_en}

                  <IoMdCloseCircle
                    className="h-4 w-4 cursor-pointer"
                    onClick={() =>
                      field.onChange(selected.filter((v: any) => v !== item.id))
                    }
                  />
                </Badge>
              ))}
            </div>
            <Command className="rounded-md border-none">
              <CommandInput
                placeholder="Please select/enter your expected job benefits"
                value={search}
                className="h-10 w-full justify-start rounded-[4px] border-brand-500 text-left font-normal text-text-200 placeholder:text-text-200"
                onValueChange={setSearch}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setTimeout(() => setIsFocused(false), 100)}
                onKeyDown={(e) => {
                  if (e.key === "ArrowDown") {
                    e.preventDefault();
                    setActiveIndex((prev) =>
                      prev + 1 < filteredOptions.length ? prev + 1 : prev,
                    );
                  } else if (e.key === "ArrowUp") {
                    e.preventDefault();
                    setActiveIndex((prev) => (prev > 0 ? prev - 1 : 0));
                  } else if (e.key === "Enter") {
                    e.preventDefault();
                    if (filteredOptions[activeIndex]?.id) {
                      field.onChange([
                        ...selected,
                        filteredOptions[activeIndex]?.id,
                      ]);
                      setSearch("");
                    }
                  }
                }}
              />
              {isFocused && (
                <CommandList
                  className={cn(
                    "absolute border border-[#E3E3E3] bg-white",
                    bottom ? "-translate-y-full" : "top-full",
                  )}
                >
                  <CommandEmpty>No results found.</CommandEmpty>
                  {filteredOptions.map((item, index) => (
                    <CommandItem
                      key={item.id}
                      value={String(item.id)}
                      onSelect={() => {
                        field.onChange([...selected, item?.id]);
                        setSearch("");
                      }}
                      className={cn(
                        "cursor-pointer",
                        index === activeIndex && "bg-brand-100 text-brand-700",
                      )}
                    >
                      {locale === "vi" ? item.text_vi : item.text_en}
                    </CommandItem>
                  ))}
                </CommandList>
              )}
            </Command>

            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}
