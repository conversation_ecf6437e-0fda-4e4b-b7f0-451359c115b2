"use client";
import { ReactNode, useState } from "react";
import { MdOutlineArrowDropDown } from "react-icons/md";

import { RadioGroup, RadioGroupItem } from "@/components/v2/ui/radio-group";
import { TaxonomyType } from "@/types/taxonomy";
import { FormItem, FormMessage } from "../../../ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "../../../ui/popover";

import { Button } from "@/components/v2/ui/button";
import { Label } from "@/components/v2/ui/label";
import { useController, useFormContext } from "react-hook-form";
export function SingleSelectCheckboxDropdown({
  name,
  data,
  locale,
  label = "",
  icon,
}: {
  name: string;
  data: TaxonomyType[];
  locale: string;
  label: string;
  icon?: ReactNode;
}) {
  const { control } = useFormContext();
  const {
    field: { value, onChange },
  } = useController({ name, control });

  const [open, setOpen] = useState(false);

  const getLabel = (id: string) => {
    const selected = data.find((l) => String(l.id) === id);
    return selected
      ? locale === "vi"
        ? selected.text_vi
        : selected.text_en
      : null;
  };

  return (
    <FormItem>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="shadow-none flex h-6 w-fit items-center gap-1 rounded-[64px] border-brand-500 bg-white px-3 py-1 text-xs text-brand-500 md:h-[30px] md:px-4 md:py-2 md:text-sm"
          >
            {icon}
            {value ? getLabel(value) : label}
            <MdOutlineArrowDropDown className="h-4 w-4 text-brand-500 md:h-6 md:w-6" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-fit rounded-[4px] border-none bg-white p-4">
          <RadioGroup
            value={value}
            onValueChange={(val) => {
              onChange(val);
              setOpen(false);
            }}
            className="flex flex-col gap-2"
          >
            {data.map((level: TaxonomyType) => {
              const id = String(level.id);
              const label = locale === "vi" ? level.text_vi : level.text_en;
              return (
                <div key={id} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={id}
                    id={`level-${id}`}
                    className="h-[14px] w-[14px] border-text-700 fill-current text-text-700  data-[state=checked]:border-brand-500 data-[state=checked]:bg-white data-[state=checked]:text-brand-500"
                  />
                  <Label
                    htmlFor={`level-${id}`}
                    className="cursor-pointer text-sm text-text-700 checked:text-brand-500"
                  >
                    {label}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
        </PopoverContent>
      </Popover>
      <FormMessage />
    </FormItem>
  );
}
