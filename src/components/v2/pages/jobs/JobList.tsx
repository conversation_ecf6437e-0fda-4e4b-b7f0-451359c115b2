"use client";

import { DEFAULT_SEARCH_PARAMS } from "@/contansts/search";
import { searchJobV2 } from "@/services/searchAPI";
import { usePageSearch } from "@/stores/usePageSearch";
import { JobType } from "@/types/job";
import _ from "lodash";
import { MoveRight } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import CardSearch from "../../shared/card-search/page";
import PaginationCustom from "../../shared/pagination/page";
import { Button } from "../../ui/button";
import { isMobile } from "react-device-detect";

export default function JobList({
  jobs: initialJobs,
  meta,
}: {
  jobs: JobType[];
  meta: any;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();

  const initialPage = Number(searchParams.get("page") || 1);
  const [page, setPage] = useState(initialPage);
  const [jobs, setJobs] = useState<JobType[]>([]);

  const setJob = usePageSearch((state) => state.setJob);

  useEffect(() => {
    if (initialJobs?.length > 0) {
      setJobs(initialJobs);
    }
  }, [initialJobs]);
  useEffect(() => {
    if (jobs?.length > 0) {
      setJob(jobs[0]);
    }
  }, [jobs, setJob]);
  useEffect(() => {
    if (initialPage) setPage(initialPage);
  }, [initialPage]);

  // 👉 hàm fetch job
  const handleScroll = useCallback(() => {
    setTimeout(() => {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }, 0);
  }, []);
  const fetchJobs = async (page: number, append = false) => {
    const data = await searchJobV2(
      _.pickBy(
        {
          ...Object.fromEntries(searchParams), // giữ nguyên query hiện tại
          page,
          "fields[job]": DEFAULT_SEARCH_PARAMS["fields[job]"],
          "fields[company]": DEFAULT_SEARCH_PARAMS["fields[company]"],
        },
        _.identity,
      ),
    );
    setJobs((prev) => (append ? [...prev, ...data.data] : data.data));

    const params = new URLSearchParams(searchParams.toString());
    params.set("page", String(page));
    router.replace(`?${params.toString()}`, { scroll: false });
    !isMobile && handleScroll();
  };

  const handleLoadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchJobs(nextPage, true); // append
  };

  const handlePageChange = (newPage: number) => {
    if (newPage === page) return;
    setPage(newPage);
    fetchJobs(newPage, false); // replace
  };

  const total = meta?.total;
  if (jobs?.length === 0) {
    return <></>;
  }
  return (
    <>
      {jobs?.map((job: JobType) => (
        <CardSearch
          key={job?.id}
          job={job}
          className="hover:border-brand-600"
        />
      ))}

      {/* Mobile Load More */}
      {jobs?.length >= total ? (
        <></>
      ) : (
        <Button
          onClick={handleLoadMore}
          variant="ghost"
          className="flex items-center text-sm text-brand-500 md:hidden"
        >
          Load more <MoveRight className="h-3 w-3" />
        </Button>
      )}

      {/* Desktop Pagination */}
      <div className="mb-6 mt-3 hidden md:block">
        <PaginationCustom
          totalItems={total}
          currentPage={page}
          onPageChange={handlePageChange}
        />
      </div>
    </>
  );
}
