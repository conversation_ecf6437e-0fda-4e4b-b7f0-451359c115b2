import { DEFAULT_SEARCH_PARAMS } from "@/contansts/search";
import { searchBlog, searchJobV2 } from "@/services/searchAPI";
import { SearchParams } from "@/types/job";
import { isMobile } from "@/utils/device";
import _ from "lodash";
import dynamic from "next/dynamic";
import HighLightCompany from "../../shared/highlight-company/page";
import CompanyInformation from "./CompanyInformation";
import FormFilterMobile from "./FormFilterMobile";
import InputSearch from "./InputSearch";
import Main from "./Main";
import RelatedBlog from "./RelatedBlog";
import SearchForm from "./SearchForm";
import SortBy from "./SortBy";
import { getHighlightCompanies } from "@/services/companyAPI";
import { VisibleCondition } from "../../shared/visible-condition/page";

const FormFilter = dynamic(() => import("./FormFilter"));
const Jobs = async ({ searchParams }: { searchParams: SearchParams }) => {
  const jobs = await searchJobV2(
    _.pickBy(
      {
        ...searchParams,
        "fields[job]": DEFAULT_SEARCH_PARAMS["fields[job]"],
        "fields[company]": DEFAULT_SEARCH_PARAMS["fields[company]"],
      },
      _.identity,
    ),
  );
  const blogs = await searchBlog(
    _.pickBy({ keyword: searchParams.keyword as string }, _.identity),
  );
  const highlightCompanies = await getHighlightCompanies();
  return (
    <>
      <div className="md:bg-brand-50 md:pb-8 md:pt-10">
        <div className="container mx-auto hidden items-center gap-[10px] md:flex">
          <SearchForm />
          <div className="w-full rounded border border-brand-500">
            <InputSearch />
          </div>
        </div>
        {isMobile() ? (
          <div className="px-4">
            <FormFilterMobile />
          </div>
        ) : (
          <div className="container mx-auto">
            <FormFilter />
          </div>
        )}
      </div>
      <div className="container relative mx-auto px-4 md:px-0">
        <SortBy />
        <Main jobs={jobs?.data} meta={jobs?.meta} />
        {isMobile() ? (
          <></>
        ) : (
          <VisibleCondition condition={highlightCompanies?.data?.length > 0}>
            <HighLightCompany company={highlightCompanies?.data?.[0]} />
          </VisibleCondition>
        )}
      </div>
      <VisibleCondition condition={jobs?.data?.length > 0}>
        <div className="bg-brand-50 pb-4 pt-8">
          <div className="container mx-auto px-4 md:px-0">
            <CompanyInformation jobs={jobs?.data} isMobile={isMobile()} />
          </div>
        </div>
      </VisibleCondition>

      <VisibleCondition condition={jobs?.data?.length > 0}>
        <div className="container mx-auto px-4 py-5 md:px-0 md:py-0">
          <RelatedBlog blogs={blogs?.data} isMobile={isMobile()} />
        </div>
      </VisibleCondition>
    </>
  );
};

export default Jobs;
