import { JobType } from "@/types/job";
import { ToggleSwitch } from "../../shared/noti-toggle/page";
import DetailJob from "./DetailJob";
import JobList from "./JobList";
import { isMobile } from "@/utils/device";
import { getRecommendJobs } from "@/services/jobAPI";
import Image from "next/image";
import { VisibleCondition } from "../../shared/visible-condition/page";

const Main = async ({ jobs = [], meta }: { jobs: JobType[]; meta: any }) => {
  const recommendJobs =
    jobs?.length > 0
      ? { data: [], links: {}, meta: {} }
      : await getRecommendJobs(1);
  const jobsList = jobs?.length > 0 ? jobs : recommendJobs.data;
  const isRecommendJobs = jobs?.length === 0;
  return (
    <>
      <VisibleCondition condition={isRecommendJobs}>
        <div className="my-6 flex w-full flex-col items-center justify-center gap-4">
          <Image
            src="https://c.topdevvn.com/uploads/2025/09/04/Isolation_Mode.png"
            alt="empty"
            className="mx-auto h-auto max-w-full"
            width="164"
            height="180"
          />
          <span className="text-[28px]/[36px] font-semibold text-brand-500">
            NO MATCHING JOBS FOUND
          </span>
        </div>
      </VisibleCondition>
      <div className="flex gap-2 pb-6">
        <div className="flex w-full max-w-[425px] flex-col gap-2">
          <ToggleSwitch total={meta?.total} isRecommendJobs={isRecommendJobs} />
          <JobList jobs={jobsList} meta={meta} />
        </div>
        {isMobile() ? <></> : <DetailJob />}
      </div>
    </>
  );
};

export default Main;
