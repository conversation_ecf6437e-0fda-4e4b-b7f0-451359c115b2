"use client";
import React from "react";
import { Button } from "../../ui/button";
import { useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";

const HIGH_LOW_SALARY = "high_low_salary";
const LOW_HIGH_SALARY = "low_high_salary";
const NEWEST_REFRESH = "newest_refresh";
const OLDEST_REFRESH = "oldest_refresh";

const SortBy = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const ordering = searchParams.get("ordering"); // string | null
  const handleSort = (type: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (type === ordering) {
      params.delete("ordering");
    } else {
      params.set("ordering", String(type));
    }
    router.push(`?${params.toString()}`, { scroll: false });
  };
  return (
    <div className="overflow-hidden">
      <span className="block text-xs font-medium text-text-700 md:hidden">
        Sort by
      </span>
      <div className="mb-5 mt-1 flex items-center gap-2 overflow-y-auto rounded-[4px] border border-brand-200 px-6 py-4 md:my-6">
        <span className="hidden text-sm font-semibold text-text-700 md:block">
          Sort by
        </span>
        <Button
          variant="outline"
          onClick={() => handleSort(HIGH_LOW_SALARY)}
          className={cn(
            "shadow-none h-[20px] rounded-[64px] border-brand-500 text-xs text-brand-500",
            ordering === HIGH_LOW_SALARY && "bg-brand-500 text-white",
          )}
        >
          Salary (High-Low){" "}
        </Button>
        <Button
          variant="outline"
          onClick={() => handleSort(LOW_HIGH_SALARY)}
          className={cn(
            "shadow-none h-[20px] rounded-[64px] border-brand-500 text-xs text-brand-500",
            ordering === LOW_HIGH_SALARY && "bg-brand-500 text-white",
          )}
        >
          Salary (Low-High)
        </Button>
        <Button
          variant="outline"
          onClick={() => handleSort(NEWEST_REFRESH)}
          className={cn(
            "shadow-none h-[20px] rounded-[64px] border-brand-500 text-xs text-brand-500",
            ordering === NEWEST_REFRESH && "bg-brand-500 text-white",
          )}
        >
          Date posted (latest)
        </Button>
        <Button
          variant="outline"
          onClick={() => handleSort(OLDEST_REFRESH)}
          className={cn(
            "shadow-none h-[20px] rounded-[64px] border-brand-500 text-xs text-brand-500",
            ordering === OLDEST_REFRESH && "bg-brand-500 text-white",
          )}
        >
          Date posted (oldest)
        </Button>
      </div>
    </div>
  );
};

export default SortBy;
