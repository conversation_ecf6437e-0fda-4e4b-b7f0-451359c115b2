"use client";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { BsBarChartLineFill } from "react-icons/bs";
import { FaChalkboardUser } from "react-icons/fa6";
import { IoGift } from "react-icons/io5";
import { MdOutlineArrowDropDown } from "react-icons/md";
import { RiBuildingFill } from "react-icons/ri";

import { getTaxonomiesDynamic } from "@/services/taxonomyAPI";
import { TaxonomyType } from "@/types/taxonomy";
import { useLocale } from "next-intl";
import { Checkbox } from "../../ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "../../ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";
import { FilterAdvanced } from "./FilterAdvanced";

import { Label } from "@/components/v2/ui/label";
import { SingleSelectCheckboxDropdown } from "./(component)/SingleSelectCheckboxDropdown";
import { FormValueSearch } from "@/types/search";
import { useSearchParams } from "next/navigation";

const FormFilter = () => {
  const locale = useLocale();
  const [levels, setLevels] = useState<TaxonomyType[]>([]);
  const [benefits, setBenefits] = useState<TaxonomyType[]>([]);
  const [companySize, setCompanySize] = useState<TaxonomyType[]>([]);
  const [workTypes, setWorkTypes] = useState<TaxonomyType[]>([]);
  const [contractTypes, setContractTypes] = useState<TaxonomyType[]>([]);
  const [industries, setIndustries] = useState<TaxonomyType[]>([]);
  const [valueForm, setValueForm] = useState<FormValueSearch>({
    job_levels_ids: "",
    company_size: "",
    benefit_ids: [],
    work_type: "",
    salary_range: [],
    experience: "",
    contract_type: "",
    company_industry_ids: [],
  });
  const [open, setOpen] = useState(false);
  const searchParams = useSearchParams();

  const form = useForm<FormValueSearch>({
    defaultValues: {
      benefit_ids: [],
      job_levels_ids: "",
      company_size: "",
      work_type: "",
    },
    resolver: async (data) => {
      const errors: Record<string, any> = {};

      if (!data.job_levels_ids) {
        errors.job_levels_ids = {
          type: "required",
          message: "Select an experience level",
        };
      }

      if (!Array.isArray(data.benefit_ids) || data.benefit_ids.length === 0) {
        errors.benefit_ids = {
          type: "required",
          message: "Select at least one benefit",
        };
      }

      return {
        values: data,
        errors,
      };
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const fetchTaxonomies = await getTaxonomiesDynamic([
          "benefits",
          "job_levels",
          "num_employees",
          "job_types",
          "contract_types",
          "industries",
        ]);
        const {
          job_levels,
          benefits,
          contract_types,
          industries,
          job_types,
          num_employees,
        } = fetchTaxonomies;
        if (job_levels) {
          setLevels(job_levels);
        }
        if (benefits) {
          setBenefits(benefits);
        }
        if (num_employees) {
          setCompanySize(num_employees);
        }
        if (job_types) {
          setWorkTypes(job_types);
        }
        if (contract_types) {
          setContractTypes(contract_types);
        }
        if (industries) {
          setIndustries(industries);
        }
      } catch (error) {
        console.error("Failed to fetch initial data:", error);
      }
    };

    fetchData();
  }, []);

  const handleClick = () => {
    const allData = form.getValues();
    setValueForm({ ...allData });
  };

  const onSubmit = (data: any) => {
    console.log(data); // Outputs { email: '1', benefits: ['1', '2', ...] }
  };
  useEffect(() => {
    const benefitIds =
      searchParams
        .get("benefit_ids")
        ?.split(",")
        .map((id) => +id) ?? [];
    form.reset({
      salary_range: [0, 50000000],
      job_levels_ids: searchParams.get("job_levels_ids") ?? "",
      company_size: searchParams.get("company_size") ?? "",
      work_type: searchParams.get("work_type") ?? "",
      benefit_ids: benefitIds ?? [],
      salary_min: searchParams.get("salary_min")
        ? Number(searchParams.get("salary_min"))
        : 0,
      salary_max: searchParams.get("salary_max")
        ? Number(searchParams.get("salary_max"))
        : 50000000,
    });
  }, [searchParams, form]);
  return (
    <div className="flex items-center gap-2 py-4">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="flex items-center gap-2"
        >
          <FormField
            control={form.control}
            name="job_levels_ids"
            render={() => (
              <SingleSelectCheckboxDropdown
                label="Experience level"
                name="job_levels_ids"
                data={levels}
                locale={locale}
                icon={<BsBarChartLineFill className="h-4 w-4 text-brand-500" />}
              />
            )}
          />
          <FormField
            control={form.control}
            name="benefit_ids"
            render={() => (
              <FormItem>
                <FormControl>
                  <Controller
                    control={form.control}
                    name="benefit_ids"
                    render={({ field: { onChange, value } }) => (
                      <Popover open={open} onOpenChange={setOpen}>
                        <PopoverTrigger asChild>
                          <div className="flex h-[30px] w-fit cursor-pointer items-center gap-1 rounded-[64px] border border-brand-500 bg-white px-3 py-2 text-sm text-brand-500">
                            <IoGift className="h-4 w-4 text-brand-500" />
                            {value && value?.length > 0 ? (
                              <span className="flex items-center ">
                                {(() => {
                                  const labels = value
                                    .map((id) => {
                                      const benefit = benefits.find(
                                        (b) => b.id === id,
                                      );
                                      return benefit
                                        ? locale === "vi"
                                          ? benefit.text_vi
                                          : benefit.text_en
                                        : "";
                                    })
                                    .filter((label) => label); // remove empty

                                  const firstLabel = labels[0];
                                  const moreCount = labels.length - 1;

                                  return moreCount > 0
                                    ? `${firstLabel} +${moreCount}`
                                    : firstLabel;
                                })()}
                                <MdOutlineArrowDropDown className="h-4 w-4 text-brand-500" />
                              </span>
                            ) : (
                              <span className="flex items-center">
                                Select Benefits{" "}
                                <MdOutlineArrowDropDown className="h-4 w-4 text-brand-500" />
                              </span>
                            )}
                          </div>
                        </PopoverTrigger>
                        <PopoverContent className="h-[300px] w-[200px] overflow-auto rounded-[8px] border-none bg-white p-2">
                          {benefits.map((benefit) => (
                            <div
                              key={benefit.id}
                              className="flex items-center gap-2 p-2"
                            >
                              <Checkbox
                                checked={(value as number[]).includes(
                                  benefit.id,
                                )}
                                id={`benefit-${benefit.id}`}
                                onCheckedChange={(checked) => {
                                  if (checked) {
                                    onChange([...(value ?? []), benefit.id]);
                                  } else {
                                    onChange(
                                      value?.filter((id) => id !== benefit.id),
                                    );
                                  }
                                }}
                                className="rounded-[4px] border-text-700 fill-current text-text-700 data-[state=checked]:border-none data-[state=checked]:bg-brand-500  data-[state=checked]:text-white"
                              />
                              <Label
                                htmlFor={`benefit-${benefit.id}`}
                                className="cursor-pointer text-sm text-text-700 checked:text-brand-500"
                              >
                                {locale === "vi"
                                  ? benefit.text_vi
                                  : benefit.text_en}
                              </Label>
                            </div>
                          ))}
                        </PopoverContent>
                      </Popover>
                    )}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="company_size"
            render={() => (
              <SingleSelectCheckboxDropdown
                label="Company size"
                name="company_size"
                data={companySize}
                locale={locale}
                icon={<RiBuildingFill className="h-4 w-4 text-brand-500" />}
              />
            )}
          />
          <FormField
            control={form.control}
            name="work_type"
            render={() => (
              <SingleSelectCheckboxDropdown
                label="Work types"
                name="work_type"
                data={workTypes}
                locale={locale}
                icon={<FaChalkboardUser className="h-4 w-4 text-brand-500" />}
              />
            )}
          />
        </form>
      </Form>
      <span className="block h-6 w-[1px] bg-brand-300"></span>
      <FilterAdvanced
        benefits={benefits}
        level={levels}
        companySize={companySize}
        workTypes={workTypes}
        contractTypes={contractTypes}
        industries={industries}
        onOpenFilter={handleClick}
        defaultValue={valueForm}
      />
    </div>
  );
};

export default FormFilter;
