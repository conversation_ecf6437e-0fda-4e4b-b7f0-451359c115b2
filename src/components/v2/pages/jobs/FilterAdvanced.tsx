"use client";
import { <PERSON><PERSON> } from "@/components/v2/ui/button";
import { Form } from "@/components/v2/ui/form";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/v2/ui/sheet";
import { BiSolidEraser } from "react-icons/bi";
import { FaIndustry } from "react-icons/fa";

import { TaxonomyType } from "@/types/taxonomy";
import { useForm } from "react-hook-form";
import { BsBarChartLineFill, BsFillGiftFill } from "react-icons/bs";
import { FaChalkboardUser, FaMoneyBills } from "react-icons/fa6";
import { HiMiniAdjustmentsHorizontal } from "react-icons/hi2";
import { RiBuildingFill } from "react-icons/ri";
import { AutocompleteField } from "./(component)/AutocompleteField";
import { RadioField } from "./(component)/RadioField";
import { FilterSalary } from "./FilterSalary";
import { FormValueSearch } from "@/types/search";
import { useEffect, useState, useTransition } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export function FilterAdvanced({
  benefits,
  level,
  companySize,
  workTypes,
  contractTypes,
  industries,
  onOpenFilter,
  defaultValue,
}: {
  benefits: TaxonomyType[];
  level: TaxonomyType[];
  companySize: TaxonomyType[];
  workTypes: TaxonomyType[];
  contractTypes: TaxonomyType[];
  industries: TaxonomyType[];
  onOpenFilter: () => void;
  defaultValue: FormValueSearch;
}) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [open, setOpen] = useState(false);

  const form = useForm<FormValueSearch>({
    defaultValues: {
      salary_range: [0, 50000000],
      salary_min: 0,
      salary_max: 50000000,
      benefit_ids: [],
      job_levels_ids: "",
      company_size: "",
      work_type: "",
      contract_type: "",
      company_industry_ids: [],
    },
  });

  const onSubmit = (data: any) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(data).forEach(([key, value]) => {
      if (
        value === undefined ||
        value === null ||
        value === "" ||
        (Array.isArray(value) && value.length === 0)
      ) {
        // remove if empty
        params.delete(key);
        return;
      }

      if (Array.isArray(value)) {
        // ✅ if array of objects → map to .id
        const ids = value.map((v) =>
          typeof v === "object" && v !== null && "id" in v ? v.id : v,
        );
        params.set(key, ids.join(","));
      } else {
        params.set(key, String(value));
      }
    });
    params.delete("salary_range");
    startTransition(() => {
      router.replace(`?${params.toString()}`, { scroll: false });
    });
  };

  useEffect(() => {
    if (defaultValue) {
      form.reset({
        salary_range: [0, 50000000],
        benefit_ids: defaultValue?.benefit_ids,
        job_levels_ids: defaultValue?.job_levels_ids,
        company_size: defaultValue?.company_size,
        work_type: defaultValue?.work_type,
        salary_min: defaultValue?.salary_min,
        salary_max: defaultValue?.salary_max,
        contract_type: "",
        company_industry_ids: defaultValue.company_industry_ids,
      });
    }
  }, [defaultValue, form]);

  useEffect(() => {
    if (!isPending) {
      setOpen(false); // close the sheet when transition is done
    }
  }, [isPending]);

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger>
        <Button
          onClick={() => {
            onOpenFilter();
            setOpen(true); // open manually
          }}
          variant="ghost"
          className="flex h-6 w-6 items-center gap-1 rounded-full bg-brand-500 p-0 text-white md:h-[30px] md:w-fit md:rounded-[64px] md:px-4 md:py-1"
        >
          <HiMiniAdjustmentsHorizontal className="h-3 w-3 text-white" />
          <span className="hidden md:block">All Filters</span>
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full overflow-auto border-none bg-white sm:max-w-[517px]">
        <SheetHeader>
          <SheetTitle className="text-2xl text-brand-500">Filters</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="bg-[#F8FBFF] p-4 text-text-700">
              <div className="flex-start mb-16 flex justify-between">
                <span className="flex items-center gap-2 text-sm md:text-base">
                  <span className="flex items-center gap-2 text-base font-semibold text-brand-500">
                    <FaMoneyBills />
                    Salary{" "}
                  </span>
                  (Drag to set salary range)
                </span>
                <Button
                  variant="ghost"
                  type="button"
                  onClick={(e) => {
                    form.reset({
                      ...form.getValues(),
                      salary_range: [0, 50000000],
                    });
                  }}
                  className="flex items-center gap-2 text-text-500"
                >
                  <BiSolidEraser />
                  Clear all
                </Button>
              </div>
              <FilterSalary />
            </div>
            <div className="bg-[#F8FBFF] px-6  py-4">
              <div className="flex-start mb-4 flex justify-between">
                <span className="flex w-full items-center justify-between gap-2">
                  <span className="flex items-center gap-2 text-base font-semibold text-brand-500">
                    <BsFillGiftFill />
                    Job Benefits {" "}
                  </span>
                  {/* (Select from drop-down for best results){" "} */}
                  <Button
                    variant="ghost"
                    type="button"
                    onClick={() =>
                      form.reset({
                        ...form.getValues(),
                        benefit_ids: [],
                      })
                    }
                    className="flex items-center gap-2 text-text-500"
                  >
                    <BiSolidEraser />
                    Clear all
                  </Button>
                </span>
              </div>
              <AutocompleteField data={benefits} name="benefit_ids" />
            </div>
            <div>
              <div className="mb-4 px-6">
                <div className="flex-start mb-4 flex justify-between">
                  <span className="flex items-center gap-2">
                    <span className="flex items-center gap-2 text-base font-semibold text-brand-500">
                      <BsBarChartLineFill className="h-4 w-4 text-brand-500" />
                      Experience Level
                    </span>
                  </span>
                </div>
                <RadioField data={level} name="job_levels_ids" />
              </div>
              <div className="mb-4 px-6">
                <div className="flex-start mb-4 flex justify-between">
                  <span className="flex items-center gap-2">
                    <span className="flex items-center gap-2 text-base font-semibold text-brand-500">
                      <FaChalkboardUser className="h-4 w-4 text-brand-500" />
                      Work Type
                    </span>
                  </span>
                </div>
                <RadioField data={workTypes} name="work_type" />
              </div>
              <div className="mb-4 px-6">
                <div className="flex-start mb-4 flex justify-between">
                  <span className="flex items-center gap-2">
                    <span className="flex items-center gap-2 text-base font-semibold text-brand-500">
                      <FaChalkboardUser className="h-4 w-4 text-brand-500" />
                      Contract Type
                    </span>
                  </span>
                </div>
                <RadioField data={contractTypes} name="contract_type" />
              </div>

              <div className="mb-4 px-6">
                <div className="flex-start mb-4 flex justify-between">
                  <span className="flex items-center gap-2">
                    <span className="flex items-center gap-2 text-base font-semibold text-brand-500">
                      <RiBuildingFill className="h-4 w-4 text-brand-500" />
                      Company Size
                    </span>
                  </span>
                </div>
                <RadioField data={companySize} name="company_size" />
              </div>
              <div className="bg-[#F8FBFF] px-6  py-4">
                <div className="flex-start mb-4 flex justify-between">
                  <span className="flex w-full items-center justify-between gap-2">
                    <span className="flex items-center gap-2 text-base font-semibold text-brand-500">
                      <FaIndustry className="h-4 w-4 text-brand-500" />
                      Company Industry
                    </span>
                    <Button
                      variant="ghost"
                      type="button"
                      onClick={() =>
                        form.reset({
                          ...form.getValues(),
                          company_industry_ids: [],
                        })
                      }
                      className="flex items-center gap-2 text-text-500"
                    >
                      <BiSolidEraser />
                      Clear all
                    </Button>
                  </span>
                </div>
                <AutocompleteField
                  data={industries}
                  name="company_industry_ids"
                  bottom
                />
              </div>
            </div>

            <SheetFooter className="mt-5 justify-end gap-1">
              <Button
                variant="ghost"
                onClick={() =>
                  form?.reset({
                    salary_range: [0, 50000000],
                    benefit_ids: [],
                    job_levels_ids: "",
                    company_size: "",
                    work_type: "",
                    contract_type: "",
                    company_industry_ids: [],
                  })
                }
                disabled={isPending}
                className="h-8 rounded-[4px] bg-text-200 px-6 font-semibold text-text-500"
                type="button"
              >
                Clear filter
              </Button>
              <Button
                className="h-8 rounded-[4px] bg-brand-500 px-6 font-semibold text-white"
                variant="ghost"
                disabled={isPending}
              >
                {isPending ? "Loading..." : "Search"}
              </Button>
            </SheetFooter>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
}
