"use client";
import { useState } from "react";
import { Switch } from "@/components/v2/ui/switch";
import { Label } from "@/components/v2/ui/label";
import { cn } from "@/lib/utils";

export function ToggleSwitch({
  total = 0,
  isRecommendJobs = false,
}: {
  total: number;
  isRecommendJobs: boolean;
}) {
  const [isChecked, setIsChecked] = useState(false);

  const handleToggle = () => {
    setIsChecked(!isChecked);
  };

  return (
    <div
      className={cn(
        "flex max-w-[425px] items-center justify-between rounded-[4px] p-4",
        isChecked ? "bg-brand-50" : "bg-brand-500",
      )}
    >
      <span
        className={cn(
          "font-semibold",
          isChecked ? "text-brand-500" : "text-brand-50",
        )}
      >
        {isRecommendJobs ? (
          <>Jobs you may be interested in</>
        ) : (
          <>{total} results</>
        )}
      </span>
      {isRecommendJobs ? (
        <></>
      ) : (
        <div className="flex items-center space-x-2">
          <Label
            htmlFor="airplane-mode"
            className={cn(isChecked ? "text-brand-500" : "text-brand-50")}
          >
            Set alert
          </Label>
          <Switch
            id="airplane-mode"
            className="bg-white after:left-0.5 after:top-0.5 after:hidden after:h-5 after:w-5 after:rounded-full after:bg-gray-400 after:transition-transform after:content-[''] data-[state=checked]:bg-brand-500 data-[state=checked]:text-text-700 data-[state=checked]:after:translate-x-5 data-[state=checked]:after:bg-white md:after:absolute"
            checked={isChecked}
            onCheckedChange={handleToggle}
          />
        </div>
      )}
    </div>
  );
}
