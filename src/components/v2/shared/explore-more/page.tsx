import React from "react";
import { But<PERSON> } from "../../ui/button";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

const ExploreMore = ({ meta }: { meta: any }) => {
  return (
    <Link
      href="/"
      className="flex h-full w-full flex-col items-center justify-center rounded-[16px] border-[1.5px] border-dashed border-brand-500"
    >
      <Button className="h-[67px] w-[67px] rounded-full bg-brand-100">
        <ChevronRight className="h-14 w-14 text-brand-500" />
      </Button>
      <span className="mt-4 block text-lg/[24px] font-bold text-brand-500">
        Explore more
      </span>
      <span className="text-sm/[24px] font-medium text-brand-500">
        View all {meta?.total} jobs
      </span>
    </Link>
  );
};

export default ExploreMore;
