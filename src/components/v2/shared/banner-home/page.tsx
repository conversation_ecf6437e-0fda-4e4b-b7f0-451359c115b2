"use client";
import * as React from "react";

import Image from "next/image";
import { cn } from "@/lib/utils";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/v2/ui/carousel";
import { Card, CardContent } from "@/components/v2/ui/card";
import { Button } from "../../ui/button";
import { Promotion } from "@/types/banner";
import Link from "next/link";

export function CarouselWithDots({ banners }: { banners: Promotion[] }) {
  const [api, setApi] = React.useState<CarouselApi | null>(null);
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [totalItems, setTotalItems] = React.useState(0);

  React.useEffect(() => {
    if (!api) return;

    const updateCarouselState = () => {
      setCurrentIndex(api.selectedScrollSnap());
      setTotalItems(api.scrollSnapList().length);
    };

    updateCarouselState();
    api.on("select", updateCarouselState);

    return () => {
      api.off("select", updateCarouselState); // Clean up on unmount
    };
  }, [api]);

  // Function to scroll to a specific slide when a dot is clicked
  const scrollToIndex = (index: number) => {
    if (api) {
      api.scrollTo(index);
    }
  };
  return (
    <div className="relative mx-auto max-w-[90rem]">
      <Carousel setApi={setApi} className="w-full">
        <CarouselContent>
          {banners?.map((item, index) => (
            <CarouselItem key={item?.id ?? index} className="pt-2 md:py-6">
              <div className="">
                <Card className="h-[121px] rounded-none border-none md:h-[371px]">
                  <CardContent className="flex h-[121px] items-center justify-center p-0 md:h-[371px]">
                    <div
                      className="relative h-full w-full"
                      style={{
                        aspectRatio: "1536 / 371",
                      }}
                    >
                      <Image
                        src={item?.top_banner_url?.[0]}
                        alt="carousel"
                        fill
                        // style={{ objectFit: "contain" }}
                        sizes="100vw"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
              <div className="flex flex-col items-center justify-between rounded-b-xl bg-brand-100 px-3 py-3 md:flex-row md:px-10 md:py-6">
                <div className="flex flex-row items-center gap-3">
                  <div className="border-brand-500 relative h-9 w-9 rounded border bg-white md:h-[72px] md:w-[72px]">
                    <Image
                      src={item?.company?.logo_url}
                      alt="carousel"
                      fill
                      objectFit="contain"
                    />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-brand-500 text-sm font-semibold md:text-2xl">
                      {item?.description}
                    </span>
                    <span className="text-text-700 text-sm font-medium">
                      {item?.company?.display_name}
                    </span>
                  </div>
                </div>
                <Link
                  href={`/companies/${item?.company?.slug}`}
                  target="_blank"
                  className="border-brand-500 text-brand-500 mt-2 rounded border bg-white px-6 py-2 text-xs md:mt-0 md:py-[10px] md:text-sm"
                >
                  {item?.cta_text}
                </Link>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
      {banners?.length > 1 ? (
        <div className="mt-2 flex justify-center space-x-2 md:mt-0">
          {banners?.map((_, index) => (
            <button
              key={index}
              onClick={() => scrollToIndex(index)}
              className={cn(
                "relative h-2 w-2 rounded-full transition-all md:h-3 md:w-3",
                currentIndex === index ? "bg-white" : "bg-brand-200",
              )}
              aria-label={`Go to slide ${index + 1}`}
            >
              {currentIndex === index && (
                <span className="absolute inset-0 m-auto h-[5px] w-[5px] rounded-full bg-brand-600 md:h-[7px] md:w-[7px]" />
              )}
              {currentIndex === index && (
                <span className="absolute inset-0 m-auto h-2 w-2 rounded-full border border-brand-600 md:h-3 md:w-3" />
              )}
            </button>
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  );
}
