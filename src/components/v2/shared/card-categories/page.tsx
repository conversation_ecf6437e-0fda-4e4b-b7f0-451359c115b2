import Link from "next/link";
import { Card, CardContent } from "../../ui/card";
import { ArrowRight } from "lucide-react";

const CardCategories = ({ item }: { item: any }) => {
  return (
    <Card className="h-[133px] w-full rounded-[16px] border-[0.5px] border-brand-500 md:h-[175px]">
      <CardContent className="flex h-full flex-col items-center justify-center px-3 py-0 md:p-5">
        <span className="h-[48px] text-brand-500">{item?.icon}</span>
        <span className="mb-[6px] mt-3 block text-center text-xs font-bold text-brand-500 md:text-xl">
          {item?.name}
        </span>
        <Link
          href="/"
          className="flex items-center gap-1 text-[10px]/[10px] font-medium text-text-500 md:text-xs"
        >
          {item?.job_count} jobs available <ArrowRight className="h-4 w-4" />
        </Link>
      </CardContent>
    </Card>
  );
};

export default CardCategories;
