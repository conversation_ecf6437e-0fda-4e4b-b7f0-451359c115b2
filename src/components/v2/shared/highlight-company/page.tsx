import { cn } from "@/lib/utils";
import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { FaArrowRightLong } from "react-icons/fa6";
import { GoLinkExternal } from "react-icons/go";
import { Calendar, Location } from "../../icons";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import { VisibleCondition } from "../visible-condition/page";

const HighLightCompany = ({
  className = "",
  company,
  srcPage,
  mediumPage,
}: {
  className?: string;
  company: CompanyType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  if (!company) {
    return <></>;
  }
  return (
    <div className="fixed right-3 top-[66%] z-50 w-[200px] -translate-y-1/2 md:h-[333px] xl:top-1/2 xl:w-[282px]">
      <span className="mb-3 block w-full bg-brand-500 py-1 text-center font-semibold text-white">
        HIGHLIGHT COMPANY
      </span>
      <Card
        className={cn(
          "h-fit w-full rounded-xl border border-brand-500 bg-white shadow-[0px_1px_2px_0px_#0000004D] transition-colors",
          className,
        )}
      >
        <CardContent className="relative p-3 md:p-4">
          <div className="relative">
            <VisibleCondition condition={!!company?.image_cover}>
              <Image
                src={company?.image_cover}
                alt="job-image"
                width={250}
                height={56}
                className="rounded-[4px] object-cover md:h-[56px] md:w-[250px]"
              />
            </VisibleCondition>
            <VisibleCondition condition={!!company?.image_logo}>
              <Image
                src={company?.image_logo}
                alt="job-image"
                width={48}
                height={48}
                className="absolute bottom-0 right-1/2 translate-x-1/2 translate-y-1/2 rounded-[4px] border border-brand-500 bg-white object-cover p-1 md:h-[48px] md:w-[48px]"
              />
            </VisibleCondition>
          </div>
          <Link
            href={`/detail-jobs/${company?.slug}-${company?.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
            target="_blank"
          >
            <div className="mt-[30px] flex flex-col justify-center gap-[6px] md:flex-row md:gap-3">
              <div className="flex flex-col justify-between">
                <span className="text-center text-sm/[18px] font-semibold text-brand-500 md:line-clamp-2 md:text-base/[24px]">
                  {company?.display_name}
                </span>
                <span className="text-center text-sm/[16px] font-medium text-text-300">
                  {company?.tagline}
                </span>
              </div>
            </div>
            <div className="my-3 flex w-full flex-col items-center justify-between gap-1 md:gap-1">
              <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500">
                <Calendar />
                <span className="line-clamp-1"> {company?.company_size}</span>
              </span>
              <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500">
                <span className="h-3 w-3">
                  <Location />
                </span>
                <span className="line-clamp-1">
                  {company?.addresses?.address_region_list}
                </span>
              </span>
              <Button
                variant="ghost"
                className="flex h-fit items-center gap-1 p-0 text-sm text-brand-500"
              >
                20+ jobs <FaArrowRightLong />
              </Button>
            </div>

            <div className="mt-2">
              <div className="flex items-center justify-center border-t border-t-text-100 pt-[6px] md:justify-between md:pt-[7px]"></div>
            </div>
          </Link>
          <Link
            className="flex w-full items-center justify-center gap-2 text-center text-sm text-brand-500"
            href={company?.detail_url}
          >
            View company <GoLinkExternal />
          </Link>
        </CardContent>
      </Card>
    </div>
  );
};

export default HighLightCompany;
