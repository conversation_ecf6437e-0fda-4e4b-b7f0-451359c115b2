import ToastNotification from "@/components/Swal/ToastNotification";
import { cn } from "@/lib/utils";
import { follow<PERSON>ob<PERSON><PERSON> } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { MdGroups } from "react-icons/md";
import { Location } from "../../icons";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";
import { MdOutlineWork } from "react-icons/md";
import { FaArrowRightLong } from "react-icons/fa6";

const BigCardSearchCompany = ({
  className = "",
  job,
  srcPage,
  mediumPage,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const { company } = job;
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  // const handleBtnFollowClick = () => {
  //   if (!isLoggedIn) {
  //     openLoginPopup([
  //       {
  //         name: "referring_name",
  //         value: "save_job",
  //       },
  //     ]);
  //     return;
  //   }

  //   if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
  //     Swal.fire({
  //       title: t("common_sorry"),
  //       text: t("detail_job_page_not_right_access"),
  //       icon: "warning",
  //       confirmButtonColor: "#DD3F24",
  //     });

  //     return;
  //   }

  //   followJobApi(job?.id).then((response) => {
  //     const { data } = response.data;
  //     if (data.is_followed) {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_followed_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     } else {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_unfollow_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     }
  //   });
  // };
  return (
    <Card
      className={cn(
        "w-full rounded-xl border border-brand-500 bg-white transition-colors md:h-fit",
        className,
      )}
    >
      <CardContent className="relative flex h-36 gap-2 p-3 md:p-4">
        <div className="absolute right-4 top-4 block md:hidden">
          <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
        </div>
        <Link
          href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
          target="_blank"
          className="w-full"
        >
          <div className="flex gap-[6px] md:flex-row md:gap-3">
            <Image
              src={job?.company?.image_logo}
              alt="job-image"
              width={68}
              height={68}
              className="h-[40px] w-[40px] rounded-[4px] object-contain md:h-[68px] md:w-[68px]"
            />
            <div className="flex w-full flex-col justify-between">
              <span className="line-clamp-1 text-sm/[18px] font-semibold text-brand-500 md:line-clamp-1 md:text-base/[24px]">
                {company?.display_name}
              </span>
              <span className="line-clamp-1 text-xs/[16px] font-medium text-text-300">
                {company?.tagline}
              </span>
              <div className="mb-2 mt-1 flex gap-4">
                <span className="flex items-center gap-1 text-sm font-medium text-text-500 md:text-sm">
                  <div className="block h-[14px] w-[14px]">
                    <MdOutlineWork />
                  </div>
                  <span className="line-clamp-1">
                    {company?.company_size} Employees
                  </span>
                </span>
                <span className="flex items-center gap-1 text-sm font-medium text-text-500 md:text-sm">
                  <div className="block h-[14px] w-[14px]">
                    <Location />
                  </div>
                  <span className="line-clamp-1">
                    {job?.addresses?.address_region_list}
                  </span>
                </span>
                <span className="flex items-center gap-1 text-sm font-medium text-text-500 md:text-sm">
                  <MdGroups />{" "}
                  <span className="line-clamp-1">{company?.company_size}</span>{" "}
                  Employees
                </span>
              </div>
              <div className="flex w-full items-center justify-between">
                <Button
                  className="absolute right-4 top-4 h-7 w-[148px] rounded-[4px] bg-brand-500 text-sm font-semibold uppercase text-[#FEF4F2]"
                  variant="ghost"
                >
                  + Follow
                </Button>
                <div className="flex items-end gap-1">
                  <div
                    className="line-clamp-2 text-sm text-text-500"
                    dangerouslySetInnerHTML={{
                      __html: job?.content,
                    }}
                  ></div>
                  <Button
                    className="flex h-fit items-center gap-2 p-0 text-brand-500"
                    variant="ghost"
                  >
                    View company <FaArrowRightLong />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

export default BigCardSearchCompany;
