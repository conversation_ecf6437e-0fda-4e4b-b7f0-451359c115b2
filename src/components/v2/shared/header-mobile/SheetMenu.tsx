"use client";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/v2/ui/collapsible";
import {
  Sheet,
  Sheet<PERSON>lose,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/v2/ui/sheet";
import { LIST_MENU_MOBILE } from "@/contansts/menu";
import { cn } from "@/lib/utils";
import { fetchJobCategories } from "@/services/jobAPI";
import { Category, MenuItem, Role } from "@/types/job";
import { ChevronDown, Menu } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Button } from "../../ui/button";
import SwitchLanguage from "./SwitchLanguage";
export function SheetMenu() {
  const [activeMenu, setActiveMenu] = useState<MenuItem | null>(
    (LIST_MENU_MOBILE[0] as MenuItem) || null,
  );
  const t = useTranslations();
  const searchParams = useSearchParams();
  const [categories, setCategories] = useState<Category[]>([]);
  const router = useRouter();

  const [activeButtons, setActiveButtons] = useState<number[]>([]);
  const [activeRoles, setActiveRoles] = useState<number[]>([]);
  const categoryMap = useMemo(() => {
    if (!Array.isArray(categories)) return {};

    return categories.reduce((acc: { [key: string]: number[] }, category) => {
      acc[category.name] = category.roles?.map((role) => role.id) || [];
      return acc;
    }, {});
  }, [categories]);
  useEffect(() => {
    fetchJobCategories().then((response) => {
      setCategories(response);
    });
  }, []);

  useEffect(() => {
    const categoryIdsParam = searchParams.get("job_categories_ids");

    if (categoryIdsParam) {
      const ids = categoryIdsParam.split(",").map(Number);
      setActiveButtons(ids);

      const selectedRoles: number[] = [];

      categories.forEach((category) => {
        category.roles?.forEach((role) => {
          if (ids.includes(role.id)) {
            selectedRoles.push(role.id);
          }
        });
      });

      setActiveRoles(selectedRoles);
    } else {
      setActiveButtons([]);
      setActiveRoles([]);
    }
  }, [searchParams, categories]);
  const handleButtonClick = (role: Role) => {
    setActiveButtons((prev) => {
      // Use Set to automatically remove duplicates
      const uniqueButtons = new Set([...prev, role.parent_id]);
      return Array.from(uniqueButtons);
    });
    setActiveRoles((prev) => {
      const uniqueRoles = new Set(prev);
      if (uniqueRoles.has(role.id)) {
        uniqueRoles.delete(role.id);
      } else {
        uniqueRoles.add(role.id);
      }
      return Array.from(uniqueRoles);
    });
  };
  const calculateItem = (name: string) => {
    const countItems = categoryMap[name]?.filter((roleId) =>
      activeRoles.includes(roleId),
    );
    const length = countItems?.length || null;

    return length ? `(${length})` : null;
  };

  const handleRemoveCheck = () => {
    setActiveRoles([]);
    setActiveButtons([]);
  };
  const handleClickJobs = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString());

    // Set or update the param
    if (activeRoles.length > 0) {
      params.set("job_categories_ids", activeRoles.map(String).join(","));
    } else {
      params.delete("job_categories_ids");
    }

    // Ensure path is `/jobs/search`
    const basePath = "/jobs/search";

    router.push(`${basePath}?${params.toString()}`);
  }, [router, activeRoles, searchParams]);
  useEffect(() => {
    const categoryIdsParam = searchParams.get("job_categories_ids");

    if (categoryIdsParam) {
      const ids = categoryIdsParam
        .split(",")
        .map((id) => parseInt(id, 10))
        .filter((id) => !isNaN(id));

      const selectedRoles: number[] = [];

      categories.forEach((category) => {
        category.roles?.forEach((role) => {
          if (ids.includes(role.id)) {
            selectedRoles.push(role.id);
          }
        });
      });

      setActiveRoles(selectedRoles);
    }
  }, [categories, searchParams]);
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost">
          <Menu className="text-brand-500" />
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full translate-x-0 border-none bg-white px-4 py-5 transition-all duration-500 ease-in-out data-[state=closed]:translate-x-full">
        <SheetHeader>
          <SheetTitle className="border-b border-b-brand-500 pb-4">
            <Link href="/">
              <Image
                src="https://c.topdevvn.com/uploads/2025/07/15/logo_v2.png"
                alt="TopDev"
                className="h-[32px] w-[116px]"
                loading="lazy"
                width="116"
                height="32"
              />
            </Link>
          </SheetTitle>
        </SheetHeader>
        <div className="flex gap-4">
          <nav className="mt-4 space-y-1">
            {LIST_MENU_MOBILE.map((menu) => {
              const isActive = activeMenu?.id === menu.id;
              const hasColor = menu?.color;
              const styles = hasColor
                ? { color: menu?.color, fontWeight: 600 }
                : {};
              const baseClass = cn(
                "w-[92px] h-[48px] flex flex-col text-xs space-y-1 items-center text-text-700 justify-center font-medium rounded-lg transition-colors",
                isActive && "bg-brand-50 font-semibold text-brand-500",
              );

              if (menu?.link) {
                return (
                  <Link
                    style={styles}
                    key={menu.id}
                    href="/"
                    className={baseClass}
                  >
                    <>
                      {menu?.icon}
                      {menu?.bilingualKey ? t(menu.bilingualKey) : menu?.name}
                    </>
                  </Link>
                );
              }

              return (
                <Button
                  style={styles}
                  key={menu.id}
                  variant="ghost"
                  className={baseClass}
                  onClick={() => setActiveMenu(menu)}
                >
                  <>
                    {menu?.icon}
                    <span className="flex items-center gap-1">
                      {menu?.bilingualKey ? t(menu.bilingualKey) : menu?.name}
                      {menu?.name === "Jobs" && activeRoles.length > 0 && (
                        <span>({activeRoles.length})</span>
                      )}
                    </span>
                  </>
                </Button>
              );
            })}
          </nav>
          {activeMenu?.subMenu && activeMenu?.name !== "Jobs" && (
            <div className="mt-6 flex w-full flex-col gap-3">
              {activeMenu?.subMenu.map((subMenu) => {
                if (subMenu?.subMenu && subMenu?.subMenu?.length > 0) {
                  return (
                    <Collapsible key={subMenu?.id}>
                      <CollapsibleTrigger className="flex items-center gap-1 text-sm text-text-700">
                        <>
                          <span className="text-brand-400">
                            {subMenu?.icon}
                          </span>

                          {subMenu?.bilingualKey
                            ? t(subMenu.bilingualKey)
                            : subMenu?.name}
                          <ChevronDown className="h-3 w-3 text-brand-400" />
                        </>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <ul className="space-y-2 pl-5 pt-3">
                          {subMenu?.subMenu?.map((menu) => (
                            <li key={menu.id}>
                              <Link
                                className="flex items-center gap-2 text-sm text-text-700"
                                href={menu?.link ?? "/"}
                              >
                                {menu?.icon}
                                {menu?.bilingualKey
                                  ? t(menu.bilingualKey)
                                  : menu?.name}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </CollapsibleContent>
                    </Collapsible>
                  );
                }
                if (subMenu?.link) {
                  return (
                    <Link
                      key={subMenu.id}
                      className="flex items-center gap-2 text-sm text-text-700"
                      href={subMenu?.link ?? "/"}
                    >
                      <>
                        <span className="text-brand-400">{subMenu?.icon}</span>
                        {subMenu?.bilingualKey
                          ? t(subMenu.bilingualKey)
                          : subMenu?.name}
                      </>
                    </Link>
                  );
                }
                return (
                  <Button
                    key={subMenu.id}
                    variant="ghost"
                    onClick={() => setActiveMenu(subMenu)}
                  >
                    <>
                      <span className="text-brand-400">{subMenu?.icon}</span>
                      {subMenu?.bilingualKey
                        ? t(subMenu.bilingualKey)
                        : subMenu?.name}
                    </>
                  </Button>
                );
              })}
            </div>
          )}
          {categories?.length > 0 && activeMenu?.name === "Jobs" && (
            <div className="mt-6 flex h-[40vh] w-full flex-col gap-3 overflow-auto">
              {categories?.map((category) => {
                const isCategoryActive = categoryMap[category?.name]?.some(
                  (roleId) => activeRoles.includes(roleId),
                );
                return (
                  <Collapsible key={category?.id}>
                    <CollapsibleTrigger
                      className={cn(
                        "flex w-full items-center justify-between gap-1 rounded-lg p-1 text-sm text-text-700",
                        isCategoryActive
                          ? "bg-brand-50 text-brand-500"
                          : "bg-transparent",
                      )}
                    >
                      <>
                        <span>
                          {category?.name} {calculateItem(category.name)}
                        </span>
                        <ChevronDown className="h-3 w-3 text-brand-400" />
                      </>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="pt-2">
                      <ul className="flex flex-wrap gap-2 border-l border-l-brand-100 pl-2">
                        {category?.roles?.map((role) => {
                          const isActive = activeRoles.includes(role?.id);

                          return (
                            <li key={role.id}>
                              <Button
                                variant="ghost"
                                onClick={() => handleButtonClick(role)}
                                className={cn(
                                  "flex h-6 items-center gap-2 rounded-full text-xs transition-colors",
                                  isActive
                                    ? "bg-brand-50 text-brand-500"
                                    : "bg-text-100 text-text-700",
                                )}
                              >
                                {role?.name}
                              </Button>
                            </li>
                          );
                        })}
                      </ul>
                    </CollapsibleContent>
                  </Collapsible>
                );
              })}
            </div>
          )}
        </div>
        {activeButtons.length > 0 && (
          <div className="fixed bottom-14 flex w-full justify-between">
            <div className="flex items-center gap-3">
              <Button
                onClick={handleRemoveCheck}
                variant="ghost"
                className="text-xs text-brand-500"
              >
                {t("search_page_clear_all" as never)} ({activeRoles.length})
              </Button>
              <div className="bg-border h-4 w-px" />
            </div>

            <div className="flex items-center gap-2 pr-10">
              <SheetClose
                asChild
                className="flex h-6 items-center gap-2 rounded-full border-none bg-text-100 text-xs transition-colors"
              >
                <Button variant="ghost">
                  {t("user_profile_open_to_work_notice_btn_cancel" as never)}
                </Button>
              </SheetClose>

              <Button
                onClick={handleClickJobs}
                className="h-6 rounded-full bg-brand-500 text-xs text-white"
              >
                {t("search_page_apply" as never)}
              </Button>
            </div>
          </div>
        )}
        <SheetFooter className="fixed bottom-3 w-full border-t border-brand-500 pt-3">
          <SwitchLanguage />
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
