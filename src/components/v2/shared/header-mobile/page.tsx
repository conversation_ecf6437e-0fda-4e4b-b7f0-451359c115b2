import Image from "next/image";
import Link from "next/link";
import InputSearch from "./InputSearch";
import { SheetMenu } from "./SheetMenu";

const HeaderMobileV2 = () => {
  return (
    <>
      <header className="sticky top-0 z-50 border-b border-b-brand-200 bg-white px-4 py-5 shadow-[0px_1px_2px_0px_#5A5A5A40]">
        <div className="mb-3 flex items-center justify-between">
          <Link href="/">
            <Image
              src="https://c.topdevvn.com/uploads/2025/07/15/logo_v2.png"
              alt="TopDev"
              className="h-[32px] w-[116px]"
              loading="lazy"
              width="116"
              height="32"
            />
          </Link>
          <SheetMenu />
        </div>
        <InputSearch />
      </header>
    </>
  );
};

export default HeaderMobileV2;
