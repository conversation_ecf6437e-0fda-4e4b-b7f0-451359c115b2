"use client";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/v2/ui/avatar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/v2/ui/collapsible";
import {
  Menubar,
  MenubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarTrigger,
} from "@/components/v2/ui/menubar";
import { MENU_LIST } from "@/contansts/menu";
import { getUserData } from "@/services/userAPI";
import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname, useSelectedLayoutSegment } from "next/navigation";
import { useEffect, useState } from "react";
const InfoAccount = () => (
  <div className="flex items-center justify-between border-b border-b-text-300">
    <div className="mb-3 flex flex-col gap-2">
      <span>Hello,</span>
      <span className="text-xl font-medium text-brand-500">Name</span>
      <span className="text-text-300"><EMAIL></span>
    </div>
    <Avatar className="mr-3 h-14 w-14 border-[2px] border-brand-500">
      <AvatarImage src="https://assets.topdev.vn/uploads/2021/01/13/default.png" />
      <AvatarFallback>CN</AvatarFallback>
    </Avatar>
  </div>
);

export function StickyBar() {
  const pathname = usePathname();

  const [menuOpen, setMenuOpen] = useState<boolean>(false);
  const [user, setUser] = useState<any>(null);
  const [error, setError] = useState("");
  const t = useTranslations();
  const isDetailPage = pathname.includes("detail-jobs");
  useEffect(() => {
    getUserData()
      .then((data) => setUser(data))
      .catch((err) => setError(err.message));
  }, []);
  if (isDetailPage) {
    return <></>;
  }
  return (
    <div>
      {menuOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/30 backdrop-blur-sm"
          onClick={() => setMenuOpen(false)} // optional
        />
      )}
      <Menubar
        onValueChange={(value) => {
          setMenuOpen(!!value);
        }}
        className="fixed bottom-0 z-50 h-[60px] w-full justify-between border-none bg-white px-4 py-3"
      >
        {MENU_LIST?.map((item) => {
          if (item?.subMenu)
            return (
              <MenubarMenu key={item?.id}>
                {item?.id === "account" && !user ? (
                  <Link
                    className="flex flex-col items-center gap-1 text-sm text-text-700"
                    href={process.env.NEXT_PUBLIC_OAUTH2_URL_ACCOUNT ?? "/"}
                  >
                    <span className="block h-3 w-3">{item?.icon}</span>
                    {item?.name}
                  </Link>
                ) : (
                  <MenubarTrigger className="text-text-700 data-[state=open]:text-brand-400">
                    <span className="flex flex-col items-center gap-1 text-sm">
                      <span className="block h-3 w-3">{item?.icon}</span>
                      {item?.name}
                    </span>
                  </MenubarTrigger>
                )}

                <MenubarContent className="shadow-none w-[100vw] translate-y-1 rounded-t-[16px] border-0 border-b border-b-brand-100 bg-white p-4 pb-2 text-sm text-text-700">
                  {item?.id === "account" && <InfoAccount />}
                  {user?.roles &&
                    user.roles[0] === "employer" &&
                    item?.subMenuEmployer &&
                    item?.subMenuEmployer?.map((subItem) => (
                      <MenubarItem key={subItem?.id}>
                        <Link
                          href={subItem?.link ?? "/"}
                          className="mb-2 flex items-center gap-2"
                        >
                          <span className="block h-3 w-3">{subItem?.icon}</span>
                          {subItem?.name}
                        </Link>
                      </MenubarItem>
                    ))}

                  {item?.subMenu?.map((subItem) => {
                    if (subItem?.subMenu && subItem?.subMenu?.length > 0) {
                      return (
                        <Collapsible key={subItem?.id}>
                          <CollapsibleTrigger className="mb-2 flex items-center gap-2 px-2 text-sm text-text-700">
                            <>
                              <span>{subItem?.icon}</span>

                              {subItem?.bilingualKey
                                ? t(subItem.bilingualKey)
                                : subItem?.name}
                              <ChevronDown className="h-3 w-3 text-brand-400" />
                            </>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                            <ul className="list-disc space-y-2 pl-6 pt-1">
                              {subItem?.subMenu?.map((menu) => (
                                <li key={menu.id}>
                                  <Link
                                    className="flex items-center gap-2 text-sm text-text-700"
                                    href={menu?.link ?? "/"}
                                  >
                                    {menu?.icon}
                                    {menu?.bilingualKey
                                      ? t(menu.bilingualKey)
                                      : menu?.name}
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          </CollapsibleContent>
                        </Collapsible>
                      );
                    }
                    return (
                      <MenubarItem key={subItem?.id}>
                        <Link
                          href={subItem?.link ?? "/"}
                          className="mb-2 flex items-center gap-2"
                        >
                          <span className="block h-3 w-3">{subItem?.icon}</span>
                          {subItem?.name}
                        </Link>
                      </MenubarItem>
                    );
                  })}
                </MenubarContent>
              </MenubarMenu>
            );
          return (
            <Link
              key={item?.id}
              className="flex flex-col items-center justify-between gap-1 text-sm text-text-700"
              href={item?.link ?? "/"}
            >
              {item?.icon}
              {item?.name}
            </Link>
          );
        })}
      </Menubar>
    </div>
  );
}
