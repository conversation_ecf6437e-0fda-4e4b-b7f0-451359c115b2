"use client";

import { Languages } from "lucide-react";
import { usePathname, useSearchParams, useRouter } from "next/navigation";
import React from "react";
import { cn } from "@/lib/utils";
import { Lang } from "@/types/page";
import { Button } from "../../ui/button";

interface SwitchLanguageProps {
  language?: Lang;
}

const SwitchLanguage = ({ language = "vi" }: SwitchLanguageProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handleChangeLocale = (lang: Lang) => {
    if (lang === language) return;

    const redirectUri = encodeURIComponent(
      pathname + (searchParams.toString() ? `?${searchParams}` : ""),
    );

    // Navigate without full page reload
    router.push(`/locale/${lang}?redirect_uri=${redirectUri}`);
  };

  return (
    <div className="text-muted-foreground flex items-center gap-2 text-sm">
      <Languages className="h-4 w-4 text-brand-500" />
      <div className="flex items-center gap-1 py-1">
        <Button
          onClick={() => handleChangeLocale("en")}
          variant="ghost"
          className={cn(
            "h-3 rounded-full px-2 py-0.5 text-xs uppercase transition-colors",
            language === "en" ? " text-brand-500" : "text-text-700",
          )}
        >
          En
        </Button>
        <span className="text-muted-foreground">|</span>
        <Button
          onClick={() => handleChangeLocale("vi")}
          variant="ghost"
          className={cn(
            "h-3 rounded-full px-2 py-0.5 text-xs uppercase transition-colors",
            language === "vi"
              ? " font-semibold text-brand-500"
              : "text-text-700",
          )}
        >
          Vi
        </Button>
      </div>
    </div>
  );
};

export default SwitchLanguage;
