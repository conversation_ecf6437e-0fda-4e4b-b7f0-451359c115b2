"use client";
import { cn } from "@/lib/utils";
import React, { useCallback, useState } from "react";
import { Input } from "../../ui/input";
import { Button } from "../../ui/button";
import { Search } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

const InputSearch = () => {
  const [value, setValue] = useState<string>("");
  const searchParams = useSearchParams();
  const router = useRouter();

  const handleClickJobs = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("keyword", value);

    const basePath = "/jobs/search";

    router.push(`${basePath}?${params.toString()}`);
  }, [router, searchParams, value]);

  return (
    <div className="relative">
      <Input
        type="input"
        placeholder="Vị trí tuyển dụng, công ty..."
        className={cn(
          "shadow-none h-12 w-full rounded-full border border-brand-500 pl-8 placeholder-text-300 ring-0",
        )}
        onChange={(e) => setValue(e.target.value)}
      />
      <Button
        onClick={handleClickJobs}
        variant="ghost"
        className="absolute right-2 top-0 translate-y-1"
      >
        <Search className="text-brand-500" />
      </Button>
    </div>
  );
};

export default InputSearch;
