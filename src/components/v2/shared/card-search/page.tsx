"use client";
import ToastNotification from "@/components/Swal/ToastNotification";
import { cn } from "@/lib/utils";
import { followJob<PERSON><PERSON> } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Calendar, Clock, LevelUp, Location, MoneyIcon } from "../../icons";
import { Badge } from "../../ui/badge";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";
import { usePageSearch } from "@/stores/usePageSearch";
import Link from "next/link";
import SkillsArr from "../skills-arr/page";
import { VisibleCondition } from "../visible-condition/page";
import SalaryText from "../salary-text/page";

const CardSearch = ({
  className = "",
  job,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const setJob = usePageSearch((state) => state.setJob);
  const jobActive = usePageSearch((state) => state.job);
  const t = useTranslations();
  const { is_free, is_basic, is_basic_plus, is_distinction } = job;
  // const handleBtnFollowClick = () => {
  //   if (!isLoggedIn) {
  //     openLoginPopup([
  //       {
  //         name: "referring_name",
  //         value: "save_job",
  //       },
  //     ]);
  //     return;
  //   }

  //   if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
  //     Swal.fire({
  //       title: t("common_sorry"),
  //       text: t("detail_job_page_not_right_access"),
  //       icon: "warning",
  //       confirmButtonColor: "#DD3F24",
  //     });

  //     return;
  //   }

  //   followJobApi(job?.id).then((response) => {
  //     const { data } = response.data;
  //     if (data.is_followed) {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_followed_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     } else {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_unfollow_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     }
  //   });
  // };
  return (
    <Card
      onClick={() => {
        setJob(job);
      }}
      className={cn(
        "h-fit w-full cursor-pointer rounded-[16px] border border-brand-200 bg-white transition-colors hover:border hover:border-brand-200 md:h-fit md:w-[425px]",
        className,
        jobActive?.id === job?.id && "border-brand-600",
      )}
    >
      <CardContent className="relative flex flex-col gap-2 p-3 md:flex-row md:p-4">
        {job?.company?.image_logo && (
          <Image
            src={job?.company?.image_logo}
            alt="job-image"
            width={64}
            height={64}
            className="h-[56px] w-[56px] rounded-[4px] object-contain md:h-[64px] md:w-[64px]"
          />
        )}
        <span className="w-full">
          <div className="flex flex-col gap-[6px] md:flex-row md:gap-3">
            <div className="flex flex-col justify-between">
              {/* <div className="flex gap-1">
                <Badge className="h-4 rounded-[4px] bg-[#FFF0F3] px-[10px] py-1 text-[10px] text-[#E60A32] hover:bg-[#FFF0F3] ">
                  Hot jobs
                </Badge>
              </div> */}
              <span
                className={cn(
                  "line-clamp-1 text-sm/[18px] font-semibold text-brand-500 md:line-clamp-1 md:text-base/[24px]",
                  is_basic && "text-text-700",
                )}
              >
                {job?.title}
              </span>
              <span className="line-clamp-1 text-xs/[16px] font-medium text-text-500">
                {job?.company?.display_name}
              </span>
            </div>
          </div>
          <VisibleCondition condition={!is_free}>
            <div className="mt-2">
              <SalaryText salary={job?.salary} />
              <VisibleCondition condition={is_basic_plus || is_distinction}>
                <div className="mt-2 grid grid-cols-2 gap-[6px] md:mb-3 md:grid-cols-2 md:gap-1">
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <div className="block h-[14px] w-[14px]">
                      <Location />
                    </div>
                    <span className="line-clamp-1">
                      {job?.addresses?.sort_addresses}
                    </span>
                  </span>
                  <VisibleCondition condition={is_distinction}>
                    <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                      <LevelUp />{" "}
                      <span className="line-clamp-1">
                        {job?.job_levels_str}
                      </span>
                    </span>
                  </VisibleCondition>

                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <Clock /> Fulltime
                  </span>
                  <VisibleCondition condition={is_distinction}>
                    <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                      <Calendar /> 6+ years exp
                    </span>
                  </VisibleCondition>
                </div>
              </VisibleCondition>
              <div className="mb-[10px] mt-[10px]">
                <SkillsArr job={job} />
              </div>
              <VisibleCondition condition={is_distinction}>
                <div className="mb-[10px] mt-[10px]">
                  {job?.benefits?.slice(0, 3).map((benefit) => (
                    <span
                      key={benefit?.name}
                      className="line-clamp-1 text-xs text-text-500"
                    >
                      {benefit?.description}
                    </span>
                  ))}
                </div>
              </VisibleCondition>
              <div className="flex items-center justify-between border-t border-t-text-100 pt-[6px]  md:pt-[7px]">
                <span className="flex items-center gap-1 text-xs text-text-500">
                  {job?.published?.since}
                </span>
                <div className="flex items-center gap-[6px] ">
                  <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
                </div>
              </div>
            </div>
          </VisibleCondition>
        </span>
      </CardContent>
    </Card>
  );
};

export default CardSearch;
