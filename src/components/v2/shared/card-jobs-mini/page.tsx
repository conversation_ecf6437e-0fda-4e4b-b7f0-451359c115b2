import ToastNotification from "@/components/Swal/ToastNotification";
import { cn } from "@/lib/utils";
import { follow<PERSON>ob<PERSON><PERSON> } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { LevelUp, Location, MoneyIcon } from "../../icons";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";

const CardJobMini = ({
  className = "",
  job,
  srcPage,
  mediumPage,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  // const handleBtnFollowClick = () => {
  //   if (!isLoggedIn) {
  //     openLoginPopup([
  //       {
  //         name: "referring_name",
  //         value: "save_job",
  //       },
  //     ]);
  //     return;
  //   }

  //   if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
  //     Swal.fire({
  //       title: t("common_sorry"),
  //       text: t("detail_job_page_not_right_access"),
  //       icon: "warning",
  //       confirmButtonColor: "#DD3F24",
  //     });

  //     return;
  //   }

  //   followJobApi(job?.id).then((response) => {
  //     const { data } = response.data;
  //     if (data.is_followed) {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_followed_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     } else {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_unfollow_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     }
  //   });
  // };
  return (
    <Card
      className={cn(
        "h-[146px] w-full rounded-xl border-none bg-brand-50 md:h-[146px]",
        className,
      )}
    >
      <CardContent className="relative p-4 md:p-4">
        <div className="absolute right-4 top-4 block md:hidden">
          <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
        </div>
        <Link
          href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
          target="_blank"
        >
          <div className="flex flex-col gap-[6px] md:flex-row md:gap-3">
            <Image
              src={job?.company?.image_logo}
              alt="job-image"
              className="rounded-[4px]"
              style={{
                width: "40px",
                height: "40px",
                objectFit: "contain", // Ensures image fills the container
              }}
              width={40}
              height={40}
            />
            <div>
              <span className="line-clamp-2 text-base/[18px] font-semibold text-brand-500 md:line-clamp-1 md:text-xl/[24px]">
                {job?.title}
              </span>
              <span className="line-clamp-1 text-xs/[16px] font-medium text-text-500">
                {job?.company?.display_name}
              </span>
            </div>
          </div>
          <div className="mt-4">
            <div className="mb-3 mt-2 flex items-center justify-between">
              <span className="max-w-1/2 line-clamp-1  gap-[6px]  text-sm/[16px] font-semibold text-brand-500">
                <span className="inline-block">
                  <MoneyIcon />
                </span>{" "}
                {job?.salary?.value}
              </span>
              <span className="inline-flex items-center gap-1 text-xs/[12px] font-medium text-text-500">
                <div className="block h-[14px] w-[14px]">
                  <Location />
                </div>
                <span className="line-clamp-1">
                  {job?.addresses?.sort_addresses}
                </span>
              </span>
            </div>
            <div className="flex items-center justify-between border-t border-t-text-100 pt-[7px]">
              <span className="flex items-center gap-1 text-xs text-text-500">
                {job?.published?.since}
              </span>
              <div className="hidden items-center gap-[6px] md:flex">
                <FavoriteButton id={job?.id} isFollow={job?.is_followed} />

                <Button className="h-6 rounded-[64px] bg-brand-500 px-3 py-2 text-[10px]/[24px] font-bold uppercase text-brand-50 hover:bg-brand-600">
                  apply now
                </Button>
              </div>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

export default CardJobMini;
