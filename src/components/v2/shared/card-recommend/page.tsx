import { cn } from "@/lib/utils";
import { JobType } from "@/types/job";
import { isMobile } from "@/utils/device";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { MoneyIcon } from "../../icons";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";
import SkillsArr from "../skills-arr/page";

const CardRecommend = ({
  className = "",
  job,
  srcPage,
  mediumPage,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  // const handleBtnFollowClick = () => {
  //   if (!isLoggedIn) {
  //     openLoginPopup([
  //       {
  //         name: "referring_name",
  //         value: "save_job",
  //       },
  //     ]);
  //     return;
  //   }

  //   if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
  //     Swal.fire({
  //       title: t("common_sorry"),
  //       text: t("detail_job_page_not_right_access"),
  //       icon: "warning",
  //       confirmButtonColor: "#DD3F24",
  //     });

  //     return;
  //   }

  //   followJobApi(job?.id).then((response) => {
  //     const { data } = response.data;
  //     if (data.is_followed) {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_followed_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     } else {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_unfollow_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     }
  //   });
  // };
  return (
    <>
      <Card
        className={cn(
          "border-brand-500 w-full rounded-xl border bg-white  transition-colors md:h-fit",
          className,
        )}
      >
        <CardContent className="relative flex gap-2 p-3">
          <div className="right-4 top-4  hidden md:absolute">
            <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
          </div>
          <div
            // href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}

            className="w-full"
          >
            <div className="gap-[6px] md:flex md:flex-row md:gap-3">
              <div className="flex gap-[6px] md:block">
                <Image
                  src={job?.company?.image_logo}
                  alt="job-image"
                  width={112}
                  height={112}
                  className="h-[40px] w-[40px] rounded-[4px] object-contain md:h-[112px] md:w-[112px]"
                />
                {isMobile() ? (
                  <div className="flex w-full flex-col justify-between">
                    <span className="text-brand-500 line-clamp-1 text-sm/[18px] font-semibold md:line-clamp-1 md:text-base/[24px]">
                      {job?.title}
                    </span>
                    <span className="text-text-500 line-clamp-1 text-xs/[16px] font-medium">
                      {job?.company?.display_name}
                    </span>
                  </div>
                ) : (
                  <></>
                )}
              </div>
              <div className="flex w-full flex-col justify-between">
                <span className="text-brand-500 line-clamp-1 hidden text-sm/[18px] font-semibold md:line-clamp-1 md:block md:text-base/[24px]">
                  {job?.title}
                </span>
                <span className="text-text-500 line-clamp-1 hidden text-xs/[16px] font-medium md:block">
                  {job?.company?.display_name}
                </span>
                <span className="text-brand-500 mb-3 mt-2 line-clamp-1 flex items-center gap-[6px] text-sm/[20px] font-semibold">
                  <MoneyIcon /> {job?.salary?.value}
                </span>
                <div className="flex items-center justify-between">
                  <SkillsArr job={job} />

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="right-3 top-3  block md:absolute">
                        <FavoriteButton
                          id={job?.id}
                          isFollow={job?.is_followed}
                        />
                      </div>
                      <Link
                        href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
                        className="bg-brand-500 hidden h-8 w-[200px] items-center justify-center rounded-[4px] text-sm font-semibold uppercase text-[#FEF4F2] md:flex"
                        target="_blank"
                      >
                        EASY APPLY
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default CardRecommend;
