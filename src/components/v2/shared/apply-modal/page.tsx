"use client";
import { <PERSON><PERSON> } from "@/components/v2/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/v2/ui/dialog";
import { Input } from "@/components/v2/ui/input";
import { Label } from "@/components/v2/ui/label";
import { cn } from "@/lib/utils";
import { tracking } from "@/services/activity";
import { applyJobsApi } from "@/services/jobAPI";
import { fetchResumesApi } from "@/services/resumeAPI";
import { useAuthStore } from "@/stores/useAuthStore";
import { JobType } from "@/types/job";
import { UserResumes } from "@/types/resume";
import { getFormData, gtag } from "@/utils";
import { getCookie, setCookie } from "@/utils/cookies";
import { TOKEN_DEVICE } from "@/utils/enums";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { isMobile } from "react-device-detect";
import { Control, useForm } from "react-hook-form";
import { FaFileUpload } from "react-icons/fa";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../ui/form";
import { RadioGroup, RadioGroupItem } from "../../ui/radio-group";
import TextEditor from "../tinymce-editor/page";
import { downloadUserProfileCv, getUserProfile } from "@/services/userAPI";
import Profile from "@/types/profile";
import Link from "next/link";
import { HiChevronRight, HiEye, HiPencilSquare } from "react-icons/hi2";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

interface FormFieldInputProps {
  control: Control<any>; // you can make this generic <T> for stricter typing
  name: string;
  label: string;
  id?: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
}

interface FormDataProps {
  display_name: string;
  email: string;
  phone: number | string;
  cover_letter: string;
  resume: UserResumes;
  tnc: boolean;
}

type UploadButtonProps = {
  onFileChange?: (file: File | null) => void; // callback to parent
};

export function UploadButton({ onFileChange }: UploadButtonProps) {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [fileName, setFileName] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleClick = () => {
    inputRef.current?.click(); // programmatically open file picker
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validTypes = [
      "application/msword", // .doc
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
      "application/pdf", // .pdf
    ];

    // Validate type
    if (!validTypes.includes(file.type)) {
      setError("Only .doc, .docx, or .pdf files are allowed.");
      setFileName(null);
      onFileChange?.(null);
      return;
    }

    // Validate size (< 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      setError("File must be smaller than 5MB.");
      setFileName(null);
      onFileChange?.(null);
      return;
    }

    // If valid
    setFileName(file.name);
    setError(null);
    onFileChange?.(file); // ✅ pass file to parent
    console.log("Selected file:", file);
  };

  return (
    <div className="mt-4 flex flex-col gap-2">
      {/* Hidden file input */}
      <Input
        ref={inputRef}
        id="file"
        type="file"
        className="hidden"
        accept=".doc,.docx,.pdf"
        onChange={handleFileChange}
      />

      {/* Styled button */}
      <div className="flex flex-col gap-3 md:flex-row md:items-end">
        <Button
          type="button"
          onClick={handleClick}
          className="flex h-7 w-fit items-center gap-2 rounded bg-brand-500 px-14 text-xs text-white hover:bg-brand-600"
        >
          <FaFileUpload />
          Upload New CV
        </Button>
        <span className="text-xs text-text-500">
          Support *.doc, *.docx, *.pdf and &lt;5MB
        </span>
      </div>

      {/* Feedback */}
      {fileName && (
        <span className="text-sm text-green-600">{fileName} selected</span>
      )}
      {error && <span className="text-sm text-red-500">{error}</span>}
    </div>
  );
}

export function FormFieldInput({
  control,
  name,
  label,
  id,
  type = "text",
  placeholder,
  required = false,
}: FormFieldInputProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field, fieldState }) => (
        <FormItem className="w-full">
          <FormControl>
            <div className="grid w-full gap-1">
              <Label
                htmlFor={id || name}
                className="text-xs font-semibold text-text-700 md:text-sm"
              >
                {label} {required && <span className="text-red-500">*</span>}
              </Label>

              <Input
                id={id || name}
                ref={field.ref}
                type={type}
                placeholder={placeholder}
                required={required}
                className="shadow-none w-full rounded border-[0.5px] border-text-200 text-xs placeholder-text-300 md:text-base"
                value={field.value ?? ""} // 👈 ensure controlled
                onChange={field.onChange}
                onBlur={field.onBlur}
              />

              {fieldState.error && (
                <span className="text-sm text-red-500">
                  {fieldState.error.message}
                </span>
              )}
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
}
export function ApplyModal({ job }: { job: JobType }) {
  const [resumes, setResumes] = useState<Array<UserResumes>>([]);
  const [file, setFile] = useState<any>("");
  const [dataProfile, setDataProfile] = useState<Profile>();
  const [isDownloading, setIsDownloading] = useState(false);

  const pathName = usePathname();
  const router = useRouter();
  const { profile, appliedJobs } = useAuthStore((s) => s);
  const t = useTranslations();
  const form = useForm({
    defaultValues: {
      full_name: "",
      phone: "",
      email: "",
      cover_letter: "",
      resume: "",
    },
  });
  useEffect(() => {
    if (profile) {
      const fetchResumes = async () => {
        form.reset({
          full_name: profile.full_name,
          phone:
            profile.phone !== undefined ? String(profile.phone) : undefined,
          email: profile.email,
          cover_letter: profile?.cover_letter,
        });
        try {
          const dataProfile = await getUserProfile();
          const data = await fetchResumesApi({
            ready_to_apply: true,
            page_size: 10,
          });
          if (data?.data) {
            setResumes(data?.data?.data);
          }
          if (dataProfile) {
            setDataProfile(dataProfile?.data);
          }
        } catch (err) {
          console.log(err);
        }
      };

      profile && fetchResumes();
    }
  }, [profile, form]);
  const handleApplyJob = async (values: FormDataProps) => {
    let filesCv = null;
    let mediaId = null;
    let cvBuilderId = null;
    let userProfileId = profile?.id;
    let resumeId = null;
    let dataType = null;
    let dataName = null;
    const checkedElement =
      (document.querySelector(
        '[class*="div-resumes"] input[type="radio"]:checked',
      ) as HTMLInputElement) ?? null;
    if (checkedElement) {
      resumeId = checkedElement.value;
      dataType = checkedElement.dataset.type;
      dataName = checkedElement.dataset.name;
    }
    if (resumeId != userProfileId) {
      filesCv = dataType == "upload" ? "fileCV" : null;
      mediaId = dataType == "media" ? resumeId : null;
      cvBuilderId = dataType == "cvbuilder" ? resumeId : null;
      userProfileId = null;
    }
    if (!checkedElement) {
      // setChooseCvValid(true);
      // setFileCvVaidate({ uploadError: false });
      // setSubmitting(false);
      return;
    }
    if (!profile && !values?.tnc) {
      // setFieldError("tnc", t("detail_job_page_apply_required_tnc_apply"));
      // setSubmitting(false);
      return;
    }

    const url = new URL(window.location.href);
    const params = url.searchParams;
    const SID =
      params.has("sid") &&
      (typeof params.get("sid") === "string"
        ? params.get("sid")
        : params.get("sid")?.[0]);

    const data: any = {
      display_name: values.display_name,
      email: values.email,
      tnc: values.tnc ?? null,
      cover_letter: values.cover_letter || "",
      phone: values.phone,
      job_id: job.id ? job.id : null,
      files_cv: filesCv,
      case: profile ? "ApplyLogined" : "ApplyNonLogin",
      media_id: mediaId,
      cvbuilder_id: cvBuilderId,
      user_profile_id: userProfileId,
      file_name: dataName,
      token_device: localStorage.getItem(TOKEN_DEVICE) ?? null,
      sid: SID ?? null,
      utm_source: params.has("utm_source") ? params.get("utm_source") : null,
      utm_medium: params.has("utm_medium") ? params.get("utm_medium") : null,
      utm_campaign: params.has("utm_campaign")
        ? params.get("utm_campaign")
        : null,
      upload_from: "UploadFromTopdev",
      source: params.has("source") ? params.get("source") : "ApplyNow",
      device_apply: isMobile ? "MobileWeb" : "PC",
      query_src: params.has("src") ? params.get("src") : null,
      query_medium: params.has("medium") ? params.get("medium") : null,
      tracking_variant: "forced-login",
      tracking_login: profile ? 1 : 0,
      tracking_signup: getCookie("just_signup") ? 1 : 0,
      tracking_event_location: getCookie("referring_name"),
    };

    const formData = getFormData(data);
    try {
      const { data } = await applyJobsApi(formData);

      if (data.message === "applied" && data.success === false) {
        // handleClearEvent();
        return;
      }

      if (!data.success) {
        // ToastNotification({
        //   icon: "error",
        //   title: t("common_error"),
        //   description: data.message,
        // });

        // setSubmitting(false);
        // handleClearEvent();
        return;
      }

      const comebackUrl = data?.data?.comeback_url;

      // GTM event eec.sendCV
      gtag({
        event: "eec.sendCV",
        ecommerce: {
          checkout: {
            actionField: { list: "ApplyCV" },
            products: [
              {
                name: process.env.NEXT_PUBLIC_BASE_URL + "/" + pathName,
                id: job.id,
                brand: job.company.display_name,
                category: "Job",
                variant: job.job_types_str,
              },
            ],
          },
        },
      });

      gtag(["js", new Date()]);
      gtag(["config", "AW-*********"]);

      // GTM event page_view
      gtag([
        "event",
        "page_view",
        {
          send_to: "AW-*********",
          dynx_itemid: job.id,
          dynx_pagetype: "conversion",
          job_id: job.id,
          job_locid: "HCM",
          job_pagetype: "conversion",
        },
      ]);

      setCookie("recently_apply", String(job.id), 1);

      tracking({
        job_ids: job.id,
        collection: "click-ok-when-sent-cv",
      });

      if (!!data.data) {
        //Show notification for dialog
        // dispatch(showDialogNotification(true));
      }
      // update newest_candidate

      //Show data notification for dialog
      // dispatch(
      //   setDataDialogNotification({
      //     email: values.email,
      //     status: data.success,
      //     comebackUrl: comebackUrl,
      //     theme: "theme_noti_applay",
      //     event: !!data.data ? data.data.events : [],
      //     promotion_reward: data?.data?.promotion_reward ?? null,
      //   }),
      // );

      //Show data job for job applied
      // dispatch(
      //   setActiveAppliedJob({
      //     jobId: job.id,
      //     status: true,
      //   }),
      // );

      /**
       * Push current job to applied jobs of current user
       */
      // dispatch(pushAppliedJob(jobState.id));

      // handleClearEvent();

      //Show event notification
      // if (profile && !!data.data) {
      //   setTimeout(() => {
      //   }, 3000);
      // }
    } catch (error) {
      // setSubmitting(false);
      // ToastNotification({
      //   icon: "error",
      //   title: t("common_sorry"),
      //   description: t("detail_job_page_apply_notification_apply_failed"),
      // });
      // handleClearEvent();
      console.error(error);
    }
  };
  const onSubmit = (values: any) => {
    handleApplyJob(values).then(() => {
      if (appliedJobs?.length > 0) {
        gtag({
          event: "already_applied",
        });
      }
      if (isMobile) {
        gtag({
          event: "apply_recommenedjob_mw",
        });
      }
    });
  };

  const setContent = (value: string) => {
    const htmlNode = document.createElement("div");
    htmlNode.innerHTML = value;
    htmlNode?.querySelectorAll("*").forEach(function (node) {
      for (let i = 0; i < node.attributes.length; i++) {
        node.removeAttribute(node.attributes[i].name);
      }
    });
    if (htmlNode.innerHTML) {
      form.setValue("cover_letter", htmlNode.innerHTML);
    } else {
      form.setValue("cover_letter", "");
    }
  };

  const handleFile = (file: File | null) => {
    if (file) {
      setFile(file);
    } else {
      console.log("No valid file selected");
    }
  };

  const navigateToProfile = (action: string): void => {
    let string = `&isShowBasicInfoModal=1`;
    if (action === "edit") {
      string = "";
    }
    router.push(
      `/users/profile?topdev_detailjob&medium=popupapply&job_title=${encodeURIComponent(
        job?.title as string,
      )}&job_url=${job?.job_url}&job_company=${job?.company
        ?.display_name}&job_id=${job?.id}${string}`,
    );
  };

  const handleDownloadClick = async () => {
    if (isDownloading) return;

    setIsDownloading(true);
    getUserProfile()
      .then((response) => {
        downloadUserProfileCv(response.data, true).finally(() =>
          setIsDownloading(false),
        );
      })
      .catch(() => setIsDownloading(false));
  };
  function getPercentColor(percent: number): string {
    if (percent >= 10 && percent <= 49)
      return "text-red-500 bg-red-50 rounded-[64px] text-xs px-2 py-[2px]";
    if (percent >= 50 && percent <= 99)
      return "text-brand-500 bg-brand-50 rounded-[64px] text-xs px-2 py-[2px]";
    if (percent === 100)
      return "text-green-500 bg-green-50 rounded-[64px] text-xs px-2 py-[2px]";
    return "text-gray-500"; // fallback nếu < 10% hoặc chưa có dữ liệu
  }

  return (
    <Dialog>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-[83%] md:w-full"
        >
          <DialogTrigger className="flex h-9 w-full items-center justify-center rounded-[4px] bg-brand-500  text-sm  font-semibold uppercase text-[#FEF4F2] md:h-12 md:w-full md:rounded-[4px]">
            APPLY JOB
          </DialogTrigger>
          <DialogContent className="h-full gap-2 overflow-y-auto border-none bg-white md:h-fit md:w-full md:max-w-[730px] md:gap-4">
            <DialogHeader className="items-start border-b border-b-text-200 pb-2 md:pb-4">
              <DialogTitle className="text-xs font-semibold text-text-500 md:text-sm">
                You are applying for the position
              </DialogTitle>
              <DialogDescription className="text-left text-base font-semibold md:text-[22px]/[28px]">
                <span className="text-brand-500">{job?.title}</span> at{" "}
                {job?.company?.display_name}
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-1 md:gap-4">
              <span className="text-sm font-semibold text-brand-500 md:text-base">
                Basic Information
              </span>
              <FormFieldInput
                control={form.control}
                id="full_name"
                label="Full Name"
                name="full_name"
                placeholder="Enter a shortened & recognizable name (Eg. TopDev)"
                required
              />
              <div className="flex w-full gap-3">
                <FormFieldInput
                  control={form.control}
                  id="phone"
                  label="Phone Number"
                  name="phone"
                  placeholder="Enter a shortened & recognizable name (Eg. TopDev)"
                  required
                />
                <FormFieldInput
                  control={form.control}
                  id="email"
                  type="email"
                  label="Email"
                  name="email"
                  placeholder="Enter a shortened & recognizable name (Eg. TopDev)"
                  required
                />
              </div>
            </div>
            <div>
              <span className="mb-[6px] block text-sm font-semibold text-brand-500 md:text-base">
                Your CV{" "}
              </span>
              <FormField
                control={form.control}
                name="resume"
                render={({ field }) => (
                  <FormItem className="max-h-[254px] space-y-3 overflow-y-auto md:max-h-[160px]">
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col"
                      >
                        {resumes &&
                          resumes?.map((resume) => (
                            <FormItem
                              key={resume?.id}
                              className="flex flex-col justify-between space-y-1 rounded border-[0.5px] border-text-200 px-3 py-2 md:flex-row md:items-center md:space-y-0"
                            >
                              <div className="flex items-center gap-1">
                                <FormControl>
                                  <RadioGroupItem
                                    value="all"
                                    className={cn(
                                      "h-3 w-3 border-text-950",
                                      field.value && "border-brand-500",
                                    )}
                                  />
                                </FormControl>
                                <FormLabel className="pt-0 text-xs font-normal md:text-base">
                                  {resume?.name}
                                </FormLabel>
                              </div>
                              <div className="flex items-center gap-1">
                                <span className="text-xs font-semibold text-text-300">
                                  {resume?.source}
                                </span>
                                {resume?.updated_at && (
                                  <span className="text-xs text-text-300">
                                    {dayjs
                                      .unix(+resume?.updated_at)
                                      .format("HH:mm DD/MM/YYYY")}
                                  </span>
                                )}
                                <Link
                                  href={
                                    resume?.assets?.[0]?.download_url ?? "#"
                                  }
                                  className="group/tooltip"
                                  target="_blank"
                                >
                                  <HiEye
                                    title={t("detail_job_cv_type_action_view")}
                                    className="h-[18px] w-[18px] cursor-pointer text-neutral-400 md:ml-2"
                                    width={18}
                                    height={18}
                                  />
                                </Link>
                              </div>
                            </FormItem>
                          ))}
                        <div
                          className={cn(
                            "rounded border-[0.5px] border-text-200 px-3 py-2",
                            dataProfile?.complete_percent &&
                              dataProfile?.complete_percent < 100 &&
                              "point bg-text-100",
                          )}
                        >
                          <FormItem className="flex flex-col justify-between space-y-1 md:flex-row md:items-center md:space-y-0 ">
                            <div className="flex items-center gap-1">
                              <FormControl>
                                <RadioGroupItem
                                  disabled={
                                    !!dataProfile?.complete_percent &&
                                    dataProfile?.complete_percent < 100
                                  }
                                  value="default"
                                  className={cn(
                                    "h-3 w-3 border-text-950",
                                    field.value && "border-brand-500",
                                  )}
                                />
                              </FormControl>
                              <FormLabel className="pt-0 font-normal">
                                {dataProfile?.display_name ?? "My TopDev CV"}{" "}
                                <span
                                  className={getPercentColor(
                                    dataProfile?.complete_percent ?? 0,
                                  )}
                                >
                                  {dataProfile?.complete_percent}%
                                </span>
                              </FormLabel>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="text-xs font-semibold text-text-300">
                                Last Updated at
                              </span>
                              {dataProfile?.updated_at && (
                                <span className="text-xs text-text-300">
                                  {dataProfile?.updated_at}
                                </span>
                              )}
                              <Link
                                href={
                                  "/users/profile?src=topdev_detailjob&medium=popupapply"
                                }
                                onClick={(e) => e.preventDefault()}
                              >
                                <HiPencilSquare
                                  onClick={() => navigateToProfile("edit")}
                                  className="ml-2 h-[18px] w-[18px] cursor-pointer text-neutral-400"
                                  width={18}
                                  height={18}
                                />
                              </Link>
                              {isDownloading ? (
                                <AiOutlineLoading3Quarters className="text-brand h-[18px] w-[18px] animate-spin text-brand-600" />
                              ) : (
                                <HiEye
                                  onClick={handleDownloadClick}
                                  title={t("detail_job_cv_type_action_view")}
                                  className="h-[18px] w-[18px] cursor-pointer text-neutral-400"
                                />
                              )}
                            </div>
                          </FormItem>
                          <Link
                            href="/users/profile?src=topdev_detailjob&medium=popupapply"
                            onClick={(e) => e.preventDefault()}
                            className="text-sm font-normal md:pl-4"
                          >
                            <span
                              onClick={() => navigateToProfile("add")}
                              className="text-xs text-text-500"
                            >
                              {t.rich("detail_job_inprove_inteview_chances", {
                                strong: (chunk) => (
                                  <strong className="inline-flex items-center text-xs text-brand-500">
                                    {chunk}
                                    <span>
                                      <HiChevronRight
                                        width={20}
                                        height={20}
                                        className="h-4 w-4 text-xs"
                                      />
                                    </span>
                                  </strong>
                                ),
                              })}
                            </span>
                          </Link>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <UploadButton onFileChange={handleFile} />
            </div>
            <span className="block text-sm font-semibold text-brand-500 md:mb-[6px] md:text-base">
              Cover Letter
            </span>
            <TextEditor
              height={isMobile ? 220 : 150}
              onChange={setContent}
              placeholder={t("detail_job_apply_cover_letter_placeholder")}
              showLoading={true}
              value={profile?.cover_letter ?? ""}
            />
            <DialogFooter className="flex flex-row items-end justify-end">
              <DialogClose asChild>
                <Button
                  variant="ghost"
                  className="text-sm font-semibold text-text-700"
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                variant="ghost"
                className="h-8 rounded bg-brand-500 px-6 text-sm font-semibold text-white md:h-10"
                type="submit"
              >
                Send your CV
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Form>
    </Dialog>
  );
}
