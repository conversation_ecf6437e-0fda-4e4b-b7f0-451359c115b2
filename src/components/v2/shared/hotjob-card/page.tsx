import { cn } from "@/lib/utils";
import { JobType } from "@/types/job";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { Location, MoneyIcon } from "../../icons";
import Benefit from "../../icons/Benefit";
import BenefitBrown from "../../icons/BenefitBrown";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";

const HotJobCard = ({
  className = "",
  job,
  srcPage,
  mediumPage,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  return (
    <Card
      className={cn(
        `relative h-fit w-full rounded-[8px] border border-transparent transition-colors hover:border hover:border-brand-600 md:h-[416px]`,
        className,
      )}
      style={{
        backgroundImage: `url(${job?.company?.image_cover})`,
        backgroundSize: "cover",
      }}
    >
      <div className="absolute inset-0 rounded-[8px] bg-brand-950/70"></div>

      <CardContent className="relative px-7 py-8 md:p-4">
        <div className="absolute right-4 top-4 block md:hidden">
          <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
        </div>

        <Link
          href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
          target="_blank"
        >
          <div className="flex flex-col items-center gap-[6px]">
            <Image
              src={job?.company?.image_logo}
              alt="job-image"
              width={80}
              height={80}
              className="h-[40px] w-[40px] rounded-full bg-white object-contain md:h-[80px] md:w-[80px]"
            />
            <span className="line-clamp-1 text-sm/[16px] font-semibold text-brand-50">
              {job?.company?.display_name}
            </span>
            <div className="flex h-12 flex-col justify-between md:h-fit">
              <span className="line-clamp-2 text-xs/[16px] text-brand-50">
                {job?.company?.description_str}
              </span>
            </div>
            <span className="line-clamp-2 h-[44px] font-semibold text-brand-50">
              {job?.title}
            </span>
          </div>
          <div className="mt-2 flex flex-col justify-between gap-1">
            <span className="ms:flex line-clamp-1 items-center gap-[6px] text-sm/[18px] font-semibold text-brand-50">
              <span className="inline-block">
                <MoneyIcon />
              </span>{" "}
              {job?.salary?.value}
            </span>
            <span className="flex items-center gap-1 text-xs/[12px] font-medium text-brand-50 md:text-sm">
              <div className="block h-[14px] w-[14px]">
                <Location />
              </div>
              <span className="line-clamp-1">
                {job?.addresses?.sort_addresses}
              </span>
            </span>
          </div>
          <div className="mt-1 flex flex-col gap-1 md:gap-2">
            {job?.benefits_v2?.slice(0.3).map((item) => (
              <div
                key={item?.name}
                className="flex items-center gap-2 text-xs text-brand-50"
              >
                {item?.name && <Benefit />}
                {item?.name}
              </div>
            ))}
          </div>
        </Link>
        <span className="mt-12 flex w-fit items-center gap-1 bg-[#F8FBFF] px-4 text-[10px]/[16px] text-[#9E370E]">
          <BenefitBrown />
          Top 100 Largest Global Companies
        </span>
      </CardContent>
    </Card>
  );
};

export default HotJobCard;
