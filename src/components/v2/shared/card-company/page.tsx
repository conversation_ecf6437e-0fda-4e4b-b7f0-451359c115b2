import { JobType } from "@/types/job";
import Image from "next/image";
import { Card, CardContent } from "../../ui/card";
import Link from "next/link";

const CardCompany = ({ job }: { job: JobType }) => {
  const { company } = job;
  return (
    <Card className="h-[182px] w-full rounded-xl border border-brand-200">
      <CardContent className="flex h-full flex-col items-center gap-2 px-6 py-4">
        <Image
          src={company?.image_logo}
          alt="job-image"
          className="rounded-lg p-1"
          style={{
            width: "152px",
            height: "56px",
            objectFit: "contain", // Ensures image fills the container
          }}
          width={152}
          height={56}
        />
        <span className="text-center text-xs font-medium text-text-500">
          {company?.display_name}
        </span>
        <Link
          href={job?.detail_url}
          className="line-clamp-2 text-center font-semibold text-brand-500"
        >
          {job?.title}
        </Link>
      </CardContent>
    </Card>
  );
};

export default CardCompany;
