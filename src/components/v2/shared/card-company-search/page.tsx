import { JobType } from "@/types/job";
import Image from "next/image";
import { FaArrowRightLong } from "react-icons/fa6";
import { Calendar, Location } from "../../icons";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";

const CardCompanySearch = ({ job }: { job?: JobType }) => {
  return (
    <Card className="h-fit w-full rounded-[16px] border border-brand-500 bg-white md:min-w-[230px]">
      <CardContent className="flex h-full flex-col items-center justify-between p-3 md:p-6">
        {job?.company?.image_logo && (
          <Image
            src={job?.company?.image_logo}
            alt="job-image"
            className="mb-3 h-12 w-12 rounded-lg border-[0.5px] border-brand-200 p-2 md:h-[112px] md:w-[112px] md:p-8"
            style={{
              objectFit: "contain", // Ensures image fills the container
            }}
            width={112}
            height={112}
          />
        )}
        <span className="line-clamp-3 h-[60px] text-center text-sm font-semibold text-brand-500 md:line-clamp-2 md:h-11 md:text-base">
          {job?.company?.display_name}
        </span>
        <span className="line-clamp-1 block h-5 text-center text-sm text-text-500">
          {job?.company?.tagline}
        </span>
        <div className="my-3 flex w-full flex-col items-center justify-between gap-1 md:flex-row md:gap-0">
          <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500">
            <span className="h-3 w-3">
              <Location />
            </span>
            <span className="line-clamp-1">
              {job?.company?.addresses?.address_region_list}
            </span>
          </span>
          <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500">
            <Calendar />
            <span className="line-clamp-1"> {job?.company?.company_size}</span>
          </span>
        </div>
        <div className="flex w-full items-center justify-between border-t border-t-text-100 pt-[7px]">
          <Button
            variant="ghost"
            className="flex h-fit items-center gap-1 p-0 text-sm text-brand-500"
          >
            20+ jobs <FaArrowRightLong />
          </Button>
          <div className="flex items-center gap-[6px]">
            {job?.id && (
              <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
            )}
            <Button className="hidden h-6 rounded-[64px] bg-brand-500 px-3 py-2 text-[10px]/[24px] font-bold uppercase text-brand-50 hover:bg-brand-600 md:flex">
              follow
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CardCompanySearch;
