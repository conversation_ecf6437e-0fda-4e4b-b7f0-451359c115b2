"use client";
import React from "react";
import { But<PERSON> } from "../../ui/button";
import { cn } from "@/lib/utils";
import { followJob<PERSON><PERSON> } from "@/services/jobAPI";
import { useAuthStore } from "@/stores/useAuthStore";
import { openLoginPopup } from "@/utils";

const Heart = ({ isFollow }: { isFollow: boolean }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill={isFollow ? "currentColor" : "none"}
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_8044_31689)">
      <path
        d="M7.0001 3.45575C4.19582 0.0153934 1.23553 2.46032 1.19653 5.09561C1.19653 9.01789 5.89046 12.2373 7.0001 12.2373C8.10975 12.2373 12.8037 9.01696 12.8037 5.09468C12.7647 2.45939 9.80439 0.0153934 7.0001 3.45575Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_8044_31689">
        <rect
          width="13"
          height="13"
          fill="currentColor"
          transform="translate(0.5 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

const FavoriteButton = ({
  id,
  isFollow,
  className,
}: {
  id: number;
  isFollow: boolean;
  className?: string;
}) => {
  const isLoggedIn = useAuthStore((s) => s.isLoggedIn);

  const handleFollow = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (!isLoggedIn) {
      openLoginPopup([
        {
          name: "referring_name",
          value: "save_job",
        },
      ]);
      return;
    }

    followJobApi(id).then((response) => {
      const { data } = response.data;
      console.log(data);
    });
  };
  return (
    <Button
      className={cn(
        "bg-text-100 hover:bg-text-200 h-6 w-6 rounded-full p-1",
        isFollow && "bg-brand-50 hover:bg-brand-100",
        className,
      )}
      onClick={(e) => handleFollow(e)}
    >
      <span className={cn("text-text-700", isFollow && "text-brand-500")}>
        <Heart isFollow={isFollow} />
      </span>
    </Button>
  );
};

export default FavoriteButton;
