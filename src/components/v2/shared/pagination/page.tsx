"use client";
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/v2/ui/pagination";
import { cn } from "@/lib/utils";
import { useRouter, useSearchParams } from "next/navigation";

const PaginationCustom = ({
  totalItems,
  currentPage,
  onPageChange,
  itemsPerPage = 15,
}: {
  totalItems: number;
  currentPage: number;
  onPageChange: (number: number) => void;
  itemsPerPage?: number;
}) => {
  const total = Math.ceil(totalItems / itemsPerPage);
  const current = Math.min(Math.max(1, currentPage || 1), total);
  const searchParams = useSearchParams();
  const router = useRouter();

  const getPageRange = () => {
    const maxPagesToShow = 3;
    const sidePages = 1;
    const pages = [];

    if (total <= maxPagesToShow) {
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      let start = Math.max(1, current - sidePages);
      let end = Math.min(total, current + sidePages);

      if (end - start + 1 < maxPagesToShow) {
        if (current <= sidePages + 1) {
          end = maxPagesToShow;
        } else {
          start = total - maxPagesToShow + 1;
        }
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (start > 1) {
        pages.unshift("start-ellipsis");
        if (start > 2) {
          pages.unshift(1);
        }
      }
      if (end < total) {
        pages.push("end-ellipsis");
        if (end < total - 1) {
          pages.push(total);
        }
      }
    }

    return pages;
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page !== current && onPageChange) {
      onPageChange(page);
      const params = new URLSearchParams(searchParams.toString());
      params.set("page", String(page));
      router.push(`?${params.toString()}`);
    }
  };
  if (totalItems === 0) {
    return <></>;
  }
  return (
    <Pagination>
      <PaginationContent>
        {/* Previous Button */}
        <PaginationItem>
          <PaginationPrevious
            onClick={(e) => {
              e.preventDefault();
              if (current > 1) handlePageChange(current - 1);
            }}
            className={cn(
              current === 1 && "pointer-events-none px-0 opacity-0",
            )}
          />
        </PaginationItem>

        {/* Page Numbers and Ellipses */}
        {getPageRange().map((page, index) => (
          <PaginationItem
            className=""
            key={
              page === "start-ellipsis" || page === "end-ellipsis"
                ? `ellipsis-${index}`
                : page
            }
          >
            {page === "start-ellipsis" || page === "end-ellipsis" ? (
              <PaginationEllipsis />
            ) : (
              <PaginationLink
                isActive={page === current}
                onClick={(e) => {
                  e.preventDefault();
                  handlePageChange(+page);
                }}
                className={cn(
                  "h-8 w-8 cursor-pointer",
                  page === current &&
                    "rounded-full border-none bg-brand-500 text-white",
                )}
              >
                {page}
              </PaginationLink>
            )}
          </PaginationItem>
        ))}

        {/* Next Button */}
        <PaginationItem>
          <PaginationNext
            onClick={(e) => {
              e.preventDefault();
              if (current < total) handlePageChange(current + 1);
            }}
            className={cn(current === total && "pointer-events-none opacity-0")}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
};

export default PaginationCustom;
