"use client";
import { JobType } from "@/types/job";
import Link from "next/link";
import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "../../ui/tooltip";
import { isMobile } from "react-device-detect";
import { useSearchParams } from "next/navigation";

const SkillsArr = ({ job }: { job: JobType }) => {
  const NUM_SLICE_SKILL = isMobile ? 2 : 3;
  const searchParams = useSearchParams();
  const [open, setOpen] = useState(false);

  const createLink = (item: string) => {
    const params = new URLSearchParams(searchParams.toString());

    // remove `page` param
    params.delete("page");

    // set keyword
    params.set("keyword", item);

    return `/jobs/search?${params.toString()}`;
  };

  const handleToggle = () => {
    if (isMobile) {
      setOpen((prev) => !prev); // toggle only on mobile
    }
  };

  return (
    <div className="flex items-center gap-1">
      {job?.skills_arr?.slice(0, NUM_SLICE_SKILL).map((item) => (
        <Link
          href={createLink(item)}
          key={item}
          className="line-clamp-1 h-6 max-w-[90px] rounded-[64px] border border-brand-500 px-3 py-1 text-xs text-brand-500"
        >
          {item}
        </Link>
      ))}

      {job?.skills_arr?.length > NUM_SLICE_SKILL && (
        <TooltipProvider>
          <Tooltip
            open={isMobile ? open : undefined} // controlled only on mobile
            onOpenChange={isMobile ? setOpen : undefined}
          >
            <TooltipTrigger asChild>
              <span
                onClick={handleToggle}
                className="flex h-6 w-6 cursor-pointer items-center justify-center rounded-[64px] border border-text-700 bg-text-100 text-xs text-text-700"
              >
                {job.skills_arr.length - NUM_SLICE_SKILL}+
              </span>
            </TooltipTrigger>
            <TooltipContent className="flex max-w-xs flex-wrap gap-1 rounded-xl border border-brand-500 bg-white">
              {job.skills_arr.slice(NUM_SLICE_SKILL).map((item) => (
                <Link
                  href={createLink(item)}
                  key={item}
                  className="flex h-6 items-center rounded-[64px] border border-brand-500 px-3 text-xs text-brand-500"
                >
                  {item}
                </Link>
              ))}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
};

export default SkillsArr;
