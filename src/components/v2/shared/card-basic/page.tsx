import { JobType } from "@/types/job";
import Image from "next/image";
import { Card, CardContent } from "../../ui/card";
import Link from "next/link";

const CardBasic = ({ job }: { job: JobType }) => {
  const { company } = job;
  return (
    <Card className="h-[140px] w-full rounded-xl border border-text-200">
      <CardContent className="flex h-full flex-col items-center gap-2 px-6 py-4">
        {company?.image_logo && (
          <Image
            src={company?.image_logo}
            alt="job-image"
            className="rounded-[2px] p-1"
            style={{
              width: "37px",
              height: "37px",
              objectFit: "contain", // Ensures image fills the container
            }}
            width={37}
            height={37}
          />
        )}
        <span className="line-clamp-1 text-center text-xs font-medium text-text-500">
          {company?.display_name}
        </span>
        <Link
          href={job?.detail_url}
          className="line-clamp-2 text-center font-semibold text-text-700"
        >
          {job?.title}
        </Link>
      </CardContent>
    </Card>
  );
};

export default CardBasic;
