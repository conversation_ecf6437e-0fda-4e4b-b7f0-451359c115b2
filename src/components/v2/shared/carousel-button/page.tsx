import { But<PERSON> } from "@/components/v2/ui/button";
import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react";

interface CarouselButtonProps {
  onPrev?: () => void;
  onNext?: () => void;
  disabledPrev?: boolean;
  disabledNext?: boolean;
}

const CarouselButton = ({
  onPrev,
  onNext,
  disabledPrev = false,
  disabledNext = false,
}: CarouselButtonProps) => {
  return (
    <div className="flex h-6 w-[54px] items-center justify-center gap-[10px] rounded border border-text-200 p-0 md:h-fit md:w-fit md:justify-start md:gap-4 md:rounded-xl md:p-[7px_5px]">
      <Button
        className="h-fit p-0"
        variant="ghost"
        onClick={onPrev}
        disabled={disabledPrev}
      >
        <ChevronLeftIcon className="h-4 w-4 " />
      </Button>
      <Button
        className="h-fit p-0"
        variant="ghost"
        onClick={onNext}
        disabled={disabledNext}
      >
        <ChevronRightIcon className="h-4 w-4 " />
      </Button>
    </div>
  );
};

export default CarouselButton;
