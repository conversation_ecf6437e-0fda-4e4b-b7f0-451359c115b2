"use client";
import { cn } from "@/lib/utils";
import { Category, MenuItem, Role } from "@/types/job";
import { getSelectedCategories } from "@/utils/categoryUtils";
import { ChevronDown, ChevronRight } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Button } from "../../ui/button";

export function DropdownMenuJobs({
  menu,
  categories,
}: {
  menu: MenuItem;
  categories: Category[];
}) {
  const router = useRouter();

  const [selectedCategory, setSelectedCategory] = useState<Category>();
  const [activeRoles, setActiveRoles] = useState<number[]>([]);
  const [activeCategories, setActiveCategories] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const categoryIds = searchParams.get("job_categories_ids");
  const t = useTranslations();
  const categoryMap = useMemo(() => {
    if (!Array.isArray(categories)) return {};

    return categories.reduce((acc: { [key: string]: number[] }, category) => {
      acc[category.name] = category.roles?.map((role) => role.id) || [];
      return acc;
    }, {});
  }, [categories]);
  const ids = useMemo(
    () => (categoryIds ? categoryIds.split(",").map(Number) : []),
    [categoryIds],
  );
  useEffect(() => {
    const categoryIdsParam = searchParams.get("job_categories_ids");

    if (categoryIdsParam) {
      const ids = categoryIdsParam
        .split(",")
        .map((id) => parseInt(id, 10))
        .filter((id) => !isNaN(id));

      const selectedRoles: number[] = [];

      categories.forEach((category) => {
        category.roles?.forEach((role) => {
          if (ids.includes(role.id)) {
            selectedRoles.push(role.id);
          }
        });
      });

      setActiveRoles(selectedRoles);
    }
  }, [categories, searchParams]);

  const selectedCategoryNames = useMemo(() => {
    return getSelectedCategories(categoryMap, activeRoles);
  }, [categoryMap, activeRoles]);

  const rolesFilterDefault = useMemo(() => {
    if (categories.length > 0) {
      if (ids?.length > 0) {
        const selectedRoles: number[] = [];
        categories.forEach((category) => {
          category.roles?.forEach((role) => {
            if (ids.includes(role.id)) {
              selectedRoles.push(role.id);
            }
          });
        });
        return selectedRoles;
      }
    }
    return [];
  }, [categories, ids]);

  const handleButtonClick = (role: Role) => {
    setActiveRoles((prev) => {
      const uniqueRoles = new Set(prev);
      if (uniqueRoles.has(role.id)) {
        uniqueRoles.delete(role.id);
      } else {
        uniqueRoles.add(role.id);
      }
      return Array.from(uniqueRoles);
    });
  };
  const handleClickCategories = (item: Category) => {
    const isAlreadyActive = activeCategories.includes(item.name);
    setActiveCategories((prev) => {
      if (prev.includes(item.name)) {
        return prev.filter((name) => name !== item.name);
      }
      return [...prev, item.name];
    });
    setActiveRoles((prev) => {
      const getAllRoleIds = item?.roles?.map((role) => role.id);
      if (isAlreadyActive) {
        return prev.filter((id) => !getAllRoleIds?.includes(id));
      }
      return [...prev, ...getAllRoleIds];
    });
  };
  const getCategoryButtonClasses = (categoryName: string) => {
    const isCategoryActive = categoryMap[categoryName]?.some((roleId) =>
      activeRoles.includes(roleId),
    );
    return isCategoryActive;
  };

  const calculateItem = (name: string) => {
    const countItems = categoryMap[name]?.filter((roleId) =>
      activeRoles.includes(roleId),
    );
    const length = countItems?.length || null;

    return length ? `(${length})` : null;
  };
  const handleRemoveCheck = useCallback(() => {
    setActiveRoles([]);
    setActiveCategories([]);
    setSelectedCategory(undefined);
  }, []);
  const handleCancel = () => {
    setActiveRoles(() => (ids?.length > 0 ? rolesFilterDefault : []));
    setActiveCategories(() => (ids?.length > 0 ? selectedCategoryNames : []));
    setSelectedCategory(undefined);
  };
  const handleClickJobs = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString());

    // Set or update the param
    if (activeRoles.length > 0) {
      params.set("job_categories_ids", activeRoles.map(String).join(","));
    } else {
      params.delete("job_categories_ids");
    }

    // Ensure path is `/jobs/search`
    const basePath = "/jobs/search";

    router.push(`${basePath}?${params.toString()}`);
  }, [router, activeRoles, searchParams]);

  return (
    <div className="group relative inline-block">
      <>
        <Button
          variant="ghost"
          className="group/button flex h-fit items-center gap-2 p-0 font-medium text-text-700"
        >
          <span className="flex items-center gap-2 border-b-[2px] border-b-transparent py-2  group-hover:border-b-brand-500">
            {menu.icon && <span className="text-brand-500 ">{menu.icon}</span>}
            {menu.name}
            <ChevronDown className="h-4 w-4" />
          </span>
        </Button>
        {/* Submenu container */}
        <div className="absolute left-0 z-10 hidden min-w-[14rem] rounded-xl bg-white p-4 shadow-md group-hover:block">
          <div className="flex items-center">
            <div className="flex flex-col gap-1 pr-4">
              {categories?.length > 0 &&
                categories?.map((item) => (
                  <Button
                    variant="ghost"
                    key={item?.id}
                    onMouseEnter={() => setSelectedCategory(item)}
                    onClick={() => handleClickCategories(item)}
                    className={cn(
                      "group/submenu relative justify-start px-0",
                      getCategoryButtonClasses(item?.name) &&
                        "rounded bg-brand-100 text-brand-500",
                    )}
                  >
                    <div className="flex w-full cursor-pointer items-center justify-between rounded-xl px-4 py-2 text-sm hover:text-brand-500">
                      <div>
                        <span>{item.name}</span>
                        <span> {calculateItem(item.name)}</span>
                      </div>
                      <ChevronRight className="ml-2 h-4 w-4" />
                    </div>
                  </Button>
                ))}
            </div>
            <div className="h-[228px] w-[600px] overflow-y-auto border-l border-l-brand-100 pl-5">
              {selectedCategory?.roles?.map((item) => (
                <Button
                  variant="ghost"
                  className={cn(
                    "mb-3 mr-3 inline-block items-center rounded-full bg-[#F6F6F6] px-8 py-2  text-xs",
                    activeRoles?.includes(item?.id) && "bg-brand-100",
                  )}
                  key={item?.id}
                  onClick={() => handleButtonClick(item)}
                >
                  {item?.name}
                </Button>
              ))}
            </div>
          </div>
          <div className="mt-2 border-t border-t-brand-100 pt-2">
            <div className="flex items-center justify-end gap-3">
              <Button
                variant="ghost"
                className="bg-none text-xs text-brand-500"
                onClick={() => {
                  handleRemoveCheck();
                }}
              >
                {t("search_page_clear_all")} ({activeRoles?.length})
              </Button>
              <Button
                variant="ghost"
                className="rounded-full bg-[#E7E7E7] px-8 py-2 text-xs"
                onClick={() => handleCancel()}
              >
                {t("user_profile_open_to_work_notice_btn_cancel")}
              </Button>
              <Button
                variant="ghost"
                className="rounded-full bg-brand-500 px-8 py-2 text-xs text-white"
                onClick={() => handleClickJobs()}
              >
                {t("search_page_apply")}
              </Button>
            </div>
          </div>
        </div>
      </>
    </div>
  );
}
