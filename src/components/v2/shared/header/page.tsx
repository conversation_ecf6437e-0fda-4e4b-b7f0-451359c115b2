"use client";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/v2/ui/dropdown-menu";
import { URL_JOB_SEEKER } from "@/contansts/auth";
import { LIST_MENU } from "@/contansts/menu";
import { cn } from "@/lib/utils";
import { fetchJobCategories } from "@/services/jobAPI";
import { useAuthStore } from "@/stores/useAuthStore";
import { Category } from "@/types/job";
import { Lang } from "@/types/page";
import { listMenu } from "@/utils";
import { Search } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Employer, Phone, User } from "../../icons";
import { Avatar, AvatarImage } from "../../ui/avatar";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import { DropdownMenuJobs } from "./DropdownMenuJobs";
import { DropdownMenuList } from "./DropdownMenuList";
const HeaderV2 = () => {
  const router = useRouter();
  const pathname = usePathname();
  const profile = useAuthStore((s) => s.profile);
  const t = useTranslations();
  const [scrolled, setScrolled] = useState(false);
  const [value, setValue] = useState<string>("");
  const [categories, setCategories] = useState<Category[]>([]);
  const searchParams = useSearchParams();

  const refDiv = useRef(null);
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 150) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  useEffect(() => {
    fetchJobCategories().then((response) => {
      setCategories(response);
    });
  }, []);

  const handleClickJobs = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("keyword", value);

    const basePath = "/jobs/search";

    router.push(`${basePath}?${params.toString()}`);
  }, [router, searchParams, value]);
  const handleChangeLocale = (lang: Lang) => {
    const redirectUri = encodeURIComponent(
      pathname + (searchParams.toString() ? `?${searchParams}` : ""),
    );

    // Navigate without full page reload
    router.push(`/locale/${lang}?redirect_uri=${redirectUri}`);
  };
  const menuItems =
    listMenu.account[
      (profile?.roles?.[0] as keyof typeof listMenu.account) ?? "resume"
    ];
  const handleLogout = () => {
    const callbackUrl =
      window.location.origin +
      "/auth/logout-complete?callback=" +
      window.location.pathname;
    const logoutUrl = `${process.env.NEXT_PUBLIC_OAUTH2_URL_LOGOUT}?callback=${callbackUrl}`;
    if (logoutUrl) {
      window.location.href = logoutUrl;
    } else {
      console.error("Logout URL is not defined.");
    }
  };

  return (
    <>
      <div
        className={cn(
          "sticky top-0 z-50 bg-white pb-5 pt-8",
          scrolled && "shadow-[0px_1px_3px_1px_#********]",
        )}
      >
        <div
          ref={refDiv}
          className="container flex items-center justify-between"
        >
          <Link href="/" className="inline-block max-w-[145px]">
            <Image
              src="https://c.topdevvn.com/uploads/2025/07/15/logo_v2.png"
              alt="TopDev"
              className="h-[40px] w-[145px]"
              loading="lazy"
              width="145"
              height="40"
            />
          </Link>
          {scrolled && (
            <div className="container flex w-[45%] items-center justify-center gap-3">
              {LIST_MENU.map((item) => {
                if (item?.name === "Jobs") {
                  return (
                    <DropdownMenuJobs
                      key={item.id}
                      menu={item}
                      categories={categories}
                    />
                  );
                }
                return <DropdownMenuList key={item.id} menu={item} />;
              })}
            </div>
          )}
          <div className="relative w-fit">
            <Input
              type="input"
              placeholder="Vị trí tuyển dụng, công ty..."
              defaultValue={searchParams.get("keyword") ?? ""}
              className={cn(
                "shadow-none h-12 w-[475px] rounded-full border border-brand-500 pl-8 placeholder-text-300 ring-0",
                scrolled && "w-[350px]",
              )}
              onChange={(e) => setValue(e.target.value)}
              onKeyDown={(event) => {
                if (event.key === "Enter") {
                  handleClickJobs();
                }
              }}
            />
            <Button
              onClick={handleClickJobs}
              variant="ghost"
              className="absolute right-2 top-0 translate-y-1"
            >
              <Search className="text-brand-500" />
            </Button>
          </div>
          <div className="ml-8 flex items-center gap-[10px]">
            <Link
              href="tel:0888155500"
              className="flex items-center gap-1 text-sm text-brand-500"
            >
              <span className="flex h-[24px] w-[24px] items-center justify-center rounded-full bg-brand-50">
                <Phone />
              </span>
              {!scrolled && <>0888 1555 00</>}
            </Link>
            {profile ? (
              <></>
            ) : (
              <Link
                href="/employer"
                className="flex items-center gap-1 text-sm text-brand-500"
              >
                <span className="flex h-[24px] w-[24px] items-center justify-center rounded-full bg-brand-50">
                  <Employer />
                </span>
                {!scrolled && <> Employer</>}
              </Link>
            )}
            {!scrolled && (
              <span className="block h-4 w-[1px] bg-brand-500"></span>
            )}
            {profile ? (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger className="border-none focus-visible:border-none focus-visible:outline-0 focus-visible:ring-0">
                    <div className="flex items-center gap-1">
                      <Avatar className="h-6 w-6 focus-visible:border-0 focus-visible:ring-0">
                        <AvatarImage src={profile?.avatar} />
                      </Avatar>
                      <span className="text-sm text-brand-500">
                        {!scrolled && profile?.display_name}
                      </span>
                    </div>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="flex flex-col gap-3 rounded-lg border-none bg-white p-4 text-text-700 shadow-lg">
                    {menuItems.map((item, index) => {
                      if (item?.type === "logout") {
                        return (
                          <DropdownMenu key={index}>
                            <span
                              className="flex items-center gap-2 hover:text-brand-500"
                              style={{ cursor: "pointer" }}
                              onClick={handleLogout}
                            >
                              {React.createElement(item.icon)}
                              {t(item.title)}
                            </span>
                          </DropdownMenu>
                        );
                      }
                      return (
                        <DropdownMenu key={index}>
                          <Link
                            href={item.href ?? "/"}
                            className="flex items-center gap-2 hover:text-brand-500"
                          >
                            {React.createElement(item.icon)}
                            {t(item.title)}
                          </Link>
                        </DropdownMenu>
                      );
                    })}
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <div className="flex items-center gap-1 text-brand-500">
                <Link
                  href={
                    typeof window !== "undefined"
                      ? {
                          pathname: URL_JOB_SEEKER,
                          query: {
                            redirect_uri: window.location.href,
                          },
                        }
                      : {
                          pathname: URL_JOB_SEEKER,
                        }
                  }
                  className="flex items-center gap-1 text-sm font-medium "
                  prefetch={false}
                >
                  <span className="flex h-[24px] w-[24px] items-center justify-center rounded-full bg-brand-50">
                    <User />
                  </span>
                  {!scrolled && <> Log in</>}
                </Link>
                {/* {!scrolled && <>/</>}

                {!scrolled && (
                  <Link
                    href="/employer"
                    className="flex items-center gap-1 text-sm font-medium"
                  >
                    Sign up
                  </Link>
                )} */}
              </div>
            )}
            {!scrolled && (
              <span className="block h-4 w-[1px] bg-brand-500"></span>
            )}
            {!scrolled && (
              <Select defaultValue={"vi"} onValueChange={handleChangeLocale}>
                <SelectTrigger className="shadow-none flex w-[40px] items-center gap-1 border-none p-0 text-sm text-brand-500 ring-0 focus:border-none focus:ring-0">
                  <SelectValue className="shadow-none" />
                </SelectTrigger>
                <SelectContent className="w-16 border-none bg-white text-sm shadow-lg">
                  <SelectGroup>
                    <SelectItem value="en">EN</SelectItem>
                    <SelectItem value="vi">VI</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          </div>
        </div>
      </div>
      <div className="shadow-[0px_1px_3px_1px_#********]">
        <div className="container flex w-full items-center justify-center gap-7">
          {LIST_MENU.map((item) => {
            if (item?.name === "Jobs") {
              return (
                <DropdownMenuJobs
                  key={item.id}
                  menu={item}
                  categories={categories}
                />
              );
            }
            return <DropdownMenuList key={item.id} menu={item} />;
          })}
        </div>
      </div>
    </>
  );
};

export default HeaderV2;
