"use client";
import { ChevronDown } from "lucide-react";
import Link from "next/link";
import { Button } from "../../ui/button";
import { MenuItem } from "@/types/job";

export function DropdownMenuList({ menu }: { menu: MenuItem }) {
  const hasSubMenu = (menu?.subMenu?.length ?? 0) > 0;

  return (
    <div className="group relative inline-block">
      {hasSubMenu ? (
        <>
          <Button
            variant="ghost"
            className="group/button flex h-fit items-center gap-2 p-0 font-medium text-text-700"
          >
            <span className="flex items-center gap-2 border-b-[2px] border-b-transparent py-2  group-hover:border-b-brand-500">
              {menu.icon && (
                <span className="text-brand-500 ">{menu.icon}</span>
              )}
              {menu.name}
              <ChevronDown className="h-4 w-4" />
            </span>
          </Button>
          {/* Submenu container */}
          <div className="absolute left-0 z-10 hidden min-w-[14rem] rounded-xl bg-white p-2 shadow-md group-hover:block">
            {menu.subMenu?.map((item) => (
              <SubMenuItem key={item.id} menu={item} />
            ))}
          </div>
        </>
      ) : (
        <Link
          href={menu.link ?? "/"}
          className="inline-flex items-center gap-1 border-b-[2px] border-b-transparent py-2 text-sm group-hover:border-b-brand-500 hover:text-brand-500"
          style={{ color: menu?.color ?? "#4F4F4F" }}
        >
          {menu.icon && <span className="text-brand-500">{menu.icon}</span>}
          {menu.name}
        </Link>
      )}
    </div>
  );
}

function SubMenuItem({ menu }: { menu: MenuItem }) {
  const hasSubMenu = (menu?.subMenu?.length ?? 0) > 0;
  if (hasSubMenu) {
    return (
      <div className="group/submenu relative">
        <div className="flex cursor-pointer items-center justify-between rounded-xl px-4 py-2 text-sm hover:text-brand-500">
          <Link href={menu.link ?? "/"}>{menu.name}</Link>
          <ChevronDown className="ml-2 h-4 w-4" />
        </div>
        {/* Submenu chỉ hiện khi hover đúng group/submenu */}
        <div className="absolute left-full top-0 z-10 hidden min-w-[14rem] -translate-y-4 rounded-xl bg-white p-2 shadow-md group-hover/submenu:block">
          {menu.subMenu?.map((item) => (
            <SubMenuItem key={item.id} menu={item} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <Link
      href={menu.link ?? "/"}
      className="block rounded-xl px-4 py-2 text-sm hover:text-brand-500"
    >
      {menu.name}
    </Link>
  );
}
