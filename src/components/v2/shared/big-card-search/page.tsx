import ToastNotification from "@/components/Swal/ToastNotification";
import { cn } from "@/lib/utils";
import { followJob<PERSON><PERSON> } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { Calendar, Clock, LevelUp, Location, MoneyIcon } from "../../icons";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";
import { HiClock } from "react-icons/hi2";
import { Button } from "../../ui/button";
import { MdArrowForwardIos } from "react-icons/md";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../ui/tooltip";

const BigCardSearch = ({
  className = "",
  job,
  srcPage,
  mediumPage,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  // const handleBtnFollowClick = () => {
  //   if (!isLoggedIn) {
  //     openLoginPopup([
  //       {
  //         name: "referring_name",
  //         value: "save_job",
  //       },
  //     ]);
  //     return;
  //   }

  //   if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
  //     Swal.fire({
  //       title: t("common_sorry"),
  //       text: t("detail_job_page_not_right_access"),
  //       icon: "warning",
  //       confirmButtonColor: "#DD3F24",
  //     });

  //     return;
  //   }

  //   followJobApi(job?.id).then((response) => {
  //     const { data } = response.data;
  //     if (data.is_followed) {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_followed_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     } else {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_unfollow_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     }
  //   });
  // };
  return (
    <>
      <Card
        className={cn(
          "w-full rounded-xl border border-brand-500 bg-white  transition-colors md:h-fit",
          className,
        )}
      >
        <CardContent className="relative flex gap-2 p-3">
          <div className="absolute right-4 top-4 block md:hidden">
            <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
          </div>
          <div
            // href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}

            className="w-full"
          >
            <div className="flex gap-[6px] md:flex-row md:gap-3">
              <Image
                src={job?.company?.image_logo}
                alt="job-image"
                width={56}
                height={56}
                className="h-[40px] w-[40px] rounded-[4px] object-contain md:h-[56px] md:w-[56px]"
              />
              <div className="flex w-full flex-col justify-between">
                <span className="line-clamp-1 text-sm/[18px] font-semibold text-brand-500 md:line-clamp-1 md:text-base/[24px]">
                  {job?.title}
                </span>
                <span className="line-clamp-1 text-xs/[16px] font-medium text-text-500">
                  {job?.company?.display_name}
                </span>
                <span className="line-clamp-1 flex items-center gap-[6px] text-sm/[20px] font-semibold text-brand-500">
                  <MoneyIcon /> {job?.salary?.value}
                </span>
                <div className="my-2 grid grid-cols-1 gap-[6px] md:mb-2 md:mt-1 md:grid-cols-2 md:gap-0">
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <div className="block h-[14px] w-[14px]">
                      <Location />
                    </div>
                    <span className="line-clamp-1">
                      {job?.addresses?.sort_addresses}
                    </span>
                  </span>
                  {/* <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <LevelUp /> {job?.job_levels_str}
                  </span>
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <Clock /> Fulltime
                  </span>
                  <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                    <Calendar /> 6+ years exp
                  </span> */}
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {job?.skills_arr?.slice(0, 3).map((item) => (
                      <span
                        key={item}
                        className="flex h-6 items-center rounded-[64px] border border-brand-500 px-3 text-xs text-brand-500"
                      >
                        {item}
                      </span>
                    ))}

                    {job?.skills_arr?.length > 3 && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="flex h-6 w-6 cursor-pointer items-center justify-center rounded-[64px] border border-text-700 bg-text-100 text-xs text-text-700">
                              {job.skills_arr.length - 3}+
                            </span>
                          </TooltipTrigger>
                          <TooltipContent className="flex max-w-xs flex-wrap gap-1 rounded-xl border border-brand-500 bg-white">
                            {job.skills_arr.slice(3).map((item) => (
                              <span
                                key={item}
                                className="flex h-6 items-center rounded-[64px] border border-brand-500 px-3 text-xs text-brand-500"
                              >
                                {item}
                              </span>
                            ))}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="hidden md:block">
                        <FavoriteButton
                          id={job?.id}
                          isFollow={job?.is_followed}
                        />
                      </div>
                      <Link
                        href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
                        className="flex h-8 w-[200px] items-center justify-center rounded-[4px] bg-brand-500 text-sm font-semibold uppercase text-[#FEF4F2]"
                        target="_blank"
                      >
                        APPLY JOB
                      </Link>
                      {/* <Button
                        className="h-8 w-[170px] rounded-[4px] border border-brand-500 text-sm font-semibold text-brand-500"
                        variant="ghost"
                      >
                        Save this job
                      </Button> */}
                    </div>
                    <span className="absolute right-3 top-3 flex w-fit items-center gap-1 bg-text-50 p-1 text-xs text-text-500">
                      <HiClock />
                      Hạn nộp hồ sơ: {job?.published?.since}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <span className="my-5 block h-[1px] w-full bg-brand-500"></span>
    </>
  );
};

export default BigCardSearch;
