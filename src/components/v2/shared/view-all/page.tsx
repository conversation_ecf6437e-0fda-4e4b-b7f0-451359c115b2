import { cn } from "@/lib/utils";
import { MoveRight } from "lucide-react";
import Link from "next/link";

const ViewAll = ({ className }: { className?: string }) => {
  return (
    <div className="flex w-full items-center justify-center">
      <Link
        className={cn(
          "flex h-10 items-center gap-1 text-sm font-medium text-brand-500 md:mt-4 md:h-14",
          className && className,
        )}
        href="/"
      >
        View all <MoveRight className="h-3 w-3" />
      </Link>
    </div>
  );
};

export default ViewAll;
