import { cn } from "@/lib/utils";
import { JobType } from "@/types/job";
import Image from "next/image";
import { Calendar, Clock, LevelUp, Location, MoneyIcon } from "../../icons";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";
import Link from "next/link";
import { followJob<PERSON><PERSON> } from "@/services/jobAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { useTranslations } from "next-intl";
import { openLoginPopup } from "@/utils";
import { Badge } from "../../ui/badge";

const CompanyCard = ({
  className = "",
  job,
  srcPage,
  mediumPage,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const t = useTranslations();
  // const handleBtnFollowClick = () => {
  //   if (!isLoggedIn) {
  //     openLoginPopup([
  //       {
  //         name: "referring_name",
  //         value: "save_job",
  //       },
  //     ]);
  //     return;
  //   }

  //   if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
  //     Swal.fire({
  //       title: t("common_sorry"),
  //       text: t("detail_job_page_not_right_access"),
  //       icon: "warning",
  //       confirmButtonColor: "#DD3F24",
  //     });

  //     return;
  //   }

  //   followJobApi(job?.id).then((response) => {
  //     const { data } = response.data;
  //     if (data.is_followed) {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_followed_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     } else {
  //       ToastNotification({
  //         icon: "success",
  //         title: t("detail_job_page_your_are_unfollow_job", {
  //           jobTitle: jobTitle,
  //         }),
  //         timer: 3000,
  //         timerProgressBar: true,
  //       });
  //     }
  //   });
  // };
  return (
    <Card
      className={cn(
        "h-[247px] w-full rounded-[16px] border border-transparent bg-[#F8FBFF] transition-colors hover:border hover:border-brand-600 md:h-[208px] md:w-[407px]",
        className,
      )}
    >
      <CardContent className="relative p-3 md:p-4">
        <div className="absolute right-4 top-4 block md:hidden">
          <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
        </div>

        <Link
          href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
          target="_blank"
        >
          <div className="flex flex-col gap-[6px] md:flex-row md:gap-3">
            <Image
              src={job?.company?.image_logo}
              alt="job-image"
              width={64}
              height={64}
              className="h-[40px] w-[40px] rounded-[4px] object-contain md:h-[64px] md:w-[64px]"
            />
            <div className="flex flex-col justify-between">
              <div className="flex gap-1">
                <Badge className="h-4 rounded-[4px] bg-[#FFF0F3] px-[10px] py-1 text-[10px] text-[#E60A32] hover:bg-[#FFF0F3] ">
                  Hot jobs
                </Badge>
              </div>
              <span className="line-clamp-1 text-sm/[18px] font-semibold text-brand-500 md:line-clamp-1 md:text-base/[24px]">
                {job?.title}
              </span>
              <span className="line-clamp-1 text-xs/[16px] font-medium text-text-500">
                {job?.company?.display_name}
              </span>
            </div>
          </div>
          <div className="mt-2">
            <span className="line-clamp-1 flex items-center gap-[6px] text-sm/[14px] font-semibold text-brand-500">
              <MoneyIcon /> {job?.salary?.value}
            </span>
            <div className="mb-2 mt-2 grid grid-cols-1 gap-[6px] md:mb-3 md:grid-cols-2 md:gap-1">
              <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                <div className="block h-[14px] w-[14px]">
                  <Location />
                </div>
                <span className="line-clamp-1">
                  {job?.addresses?.sort_addresses}
                </span>
              </span>
              <span className="flex items-center gap-1 text-xs/[12px] font-medium text-text-500 md:text-sm">
                <LevelUp />{" "}
                <span className="line-clamp-1 ">{job?.job_levels_str}</span>
              </span>
            </div>
            <div className="flex items-center justify-center border-t border-t-text-100 pt-[6px] md:justify-between md:pt-[7px]">
              <span className="flex items-center gap-1 text-xs text-text-500">
                {job?.published?.since}
              </span>
              <div className="hidden items-center gap-[6px] md:flex">
                <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
                <Button className="h-6 rounded-[64px] bg-brand-500 px-3 py-2 text-[10px]/[24px] font-bold uppercase text-brand-50 hover:bg-brand-600">
                  apply now
                </Button>
              </div>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );
};

export default CompanyCard;
