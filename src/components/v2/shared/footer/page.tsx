import useCheckPageTechcombank from "@/hooks/useCheckPageTechcombank";
import { ChevronRight } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import {
  Facebook,
  LinkedIn,
  Location,
  Mail,
  Phone,
  Youtube,
} from "../../icons";

const FooterV2 = () => {
  const t = useTranslations();
  const checkPageTechcombank = useCheckPageTechcombank();

  //Check page in group Ex: "/group/techcombank"
  if (checkPageTechcombank) {
    return <></>;
  }

  return (
    <footer>
      <div className="bg-text-50 flex flex-col pb-6 pt-9">
        <div className="border-b-text-300 container mb-8 flex items-center justify-around border-b pb-5">
          <span className="text-text-700 text-[40px]/[24px] uppercase">
            let’s <span className="text-brand-500">connect</span> there
          </span>
          <div className="flex items-center gap-2">
            <Link
              href="tel:0888 1555 00"
              className="bg-brand-500 flex h-10 w-10 items-center justify-center rounded-full text-white"
            >
              <Phone />
            </Link>
            <Link
              href="tel:0888 1555 00"
              className="bg-brand-500 flex h-10 w-10 items-center justify-center rounded-full text-white"
            >
              <Mail />
            </Link>
          </div>
        </div>
        <div className="container">
          <Link href="/" className="mb-4 inline-block max-w-[146px]">
            <Image
              src="https://c.topdevvn.com/uploads/2025/07/15/logo_v2.png"
              alt="TopDev"
              className="h-[40px] w-[146px]"
              loading="lazy"
              width="146"
              height="40"
            />
          </Link>
          <div className="flex justify-between gap-20">
            <div>
              <span className="text-brand-500 flex items-center gap-1 font-semibold uppercase">
                We are here <Location />
              </span>
              <span className="text-text-700 mt-3 flex items-center gap-3 text-sm font-medium">
                Tầng 13, tòa nhà AP Tower, 518B Điện Biên
                <br /> Phủ, Phường Thạnh Mỹ Tây, TP.HCM
              </span>
              <span className="text-brand-500 mb-2 mt-4 flex items-center gap-1 font-semibold uppercase">
                Subscrible us on
              </span>
              <div className="flex items-center gap-1">
                <Link href="https://www.facebook.com/topdevvietnam">
                  <Facebook />
                </Link>
                <Link href="https://www.linkedin.com/company/topdev-vn/posts/?feedView=all">
                  <LinkedIn />
                </Link>
                <Link href="https://www.youtube.com/@CreatoryVWS">
                  <Youtube />
                </Link>
                <Link href="https://www.facebook.com/topdevvietnam">
                  <Facebook />
                </Link>
              </div>
              <span className="text-brand-500 mb-3 mt-4 flex items-center gap-1 font-semibold uppercase">
                Download app here
              </span>
              <ul className="flex justify-start gap-1">
                <li>
                  <Link href="https://topdev.vn/s/MrEHZlyk">
                    <Image
                      src="https://cdn.topdev.vn/v4/assets/images/promote_app/app_store_img.png"
                      className="h-auto w-[114px]"
                      alt="TopDev in app store"
                      loading="lazy"
                      width="114"
                      height="33"
                    />
                  </Link>
                </li>
                <li>
                  <Link href="https://play.google.com/store/apps/details?id=it.jobs.topdev&referrer=utm_source%3Dtopdev%26utm_medium%3Dfooter_default">
                    <Image
                      src="https://cdn.topdev.vn/v4/assets/images/promote_app/google_play_img.png"
                      className="h-[33px] w-[114px]"
                      alt="TopDev in app store"
                      loading="lazy"
                      width="114"
                      height="33"
                    />
                  </Link>
                </li>
              </ul>
            </div>
            <div className="flex flex-col gap-4">
              <span className="text-brand-500 flex items-center gap-1 font-semibold uppercase">
                About TopDev
              </span>
              <Link
                href="/about-us"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                About us
              </Link>
              <Link
                href="/term-of-service"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Terms of service
              </Link>
              <Link
                href="/page/topdev-buddies"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Career{" "}
              </Link>
              <Link
                href="/privacy-policy"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Privacy policy
              </Link>
              <Link
                href="/operation-regulation"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Operation regulation of TopDev e-commerce trading floor
              </Link>
              <Link
                href="/resolve-complaints"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Resolve complaints
              </Link>
            </div>
            <div className="flex flex-col gap-4">
              <span className="text-brand-500 flex items-center gap-1 font-semibold uppercase">
                For Jobseekers
              </span>
              <Link
                href="/tool/tinh-luong-gross-net"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Salary calculation Gross-Net
              </Link>
              <Link
                href="/tao-cv-online"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Create CV
              </Link>
              <Link
                href="/jobs/search"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Browse all IT jobs
              </Link>
              <Link
                href="/page/trac-nghiem-tinh-cach"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Personality test
              </Link>
            </div>
            <div className="flex flex-col gap-4">
              <span className="text-brand-500 flex items-center gap-1 font-semibold uppercase">
                For Employers
              </span>
              <Link
                href="/"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Post a job
              </Link>
              <Link
                href="/"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Talent solutions
              </Link>
              <Link
                href="/"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                IT market report
              </Link>
              <Link
                href="/"
                className="text-text-700 flex max-w-[191px] gap-3 text-sm font-medium"
              >
                <span className="block h-3 w-3">
                  <ChevronRight className="text-brand-500" />
                </span>{" "}
                Create account
              </Link>
            </div>
          </div>
        </div>
        <div className="text-text-700 mx-auto mt-7 flex w-full items-center justify-center gap-2 text-sm">
          <span className="block">
            Copyright © CÔNG TY CỔ PHẦN APPLANCER /{" "}
          </span>
          <span>
            Business Registration: 031 303 2338 - Issues on: 27/11/2014
          </span>
        </div>
      </div>
    </footer>
  );
};

export default FooterV2;
