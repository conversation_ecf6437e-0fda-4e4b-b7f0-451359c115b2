import React, { useState } from "react";
import { Editor } from "@tinymce/tinymce-react";
import { VscLoading } from "react-icons/vsc";

export default function TextEditor({
  value,
  showLoading,
  placeholder,
  height = 150,
  onChange,
}: {
  value: string;
  showLoading?: boolean;
  placeholder?: string;
  height?: number;
  onChange: (newValue: string) => void;
}) {
  const [loading, setLoading] = useState<boolean>(true);

  return (
    <div
      className={`relative ${
        showLoading ? "min-h-[25vh] md:min-h-[150px]" : ""
      } rounded border border-brand-300`}
    >
      {showLoading && loading && (
        <div className="absolute z-10 flex h-full w-full items-center justify-center bg-[rgba(0,0,0,0.4)]">
          <VscLoading className="h-6 w-6 animate-spin" />
        </div>
      )}

      <Editor
        tinymceScriptSrc="https://assets.topdev.vn/tinymce/tinymce.min.js"
        value={value}
        onInit={() => setLoading(false)}
        onEditorChange={(newValue) => onChange(newValue)}
        init={{
          height: "25vh",
          menubar: false,
          plugins: ["lists", "wordcount"],
          branding: false,
          resize: false,
          elementpath: false,
          toolbar: "bold italic underline | bullist numlist",
          paste_as_text: true,
          content_css: false,
          skin: "oxide", // lighter default skin
          placeholder: placeholder ?? "",
          content_style: `
            body {
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Inter, sans-serif;
              font-size: 15px;
              line-height: 1.65;
              color: #374151; /* gray-700 */
            }
            p { margin: 0 0 0.75rem; }
          `,
        }}
      />
    </div>
  );
}
