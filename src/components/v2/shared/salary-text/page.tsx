"use client";
import { useAuthStore } from "@/stores/useAuthStore";
import React from "react";
import { MoneyIcon } from "../../icons";
import { Salary } from "@/types/job";
import { forceLogin, isRunningInApp } from "@/services/connector";
import { openLoginPopup } from "@/utils";
import { isMobile } from "react-device-detect";
import { useTranslations } from "next-intl";

const SalaryText = ({ salary }: { salary: Salary }) => {
  const isLoggedIn = useAuthStore((s) => s.isLoggedIn);
  const t = useTranslations();
  return (
    <span className="line-clamp-1 flex items-center gap-[6px] text-sm/[16px] font-semibold text-brand-500">
      {isLoggedIn ? (
        <>
          <MoneyIcon /> {salary?.value}
        </>
      ) : (
        <span
          onClick={(e) => {
            e.preventDefault();
            isMobile && isRunningInApp() ? forceLogin() : openLoginPopup();
          }}
        >
          {t("search_page_login_to_view_salary")}
        </span>
      )}
    </span>
  );
};

export default SalaryText;
