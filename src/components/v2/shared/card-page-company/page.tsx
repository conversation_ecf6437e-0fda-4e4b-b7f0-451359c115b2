import { CompanyType } from "@/types/company";
import Image from "next/image";
import { Card, CardContent } from "../../ui/card";
import { Button } from "../../ui/button";
import { MoveRight } from "lucide-react";
import Link from "next/link";

const CardPageCompany = ({ company }: { company: CompanyType }) => {
  return (
    <Card className="h-fit w-full rounded-xl border border-text-200">
      <CardContent className="flex h-full flex-col items-center gap-2 p-4">
        <div className="relative h-24 w-full">
          <Image
            src={
              company?.image_cover ??
              "https://c.topdevvn.com/uploads/2025/08/26/default-empty.png"
            }
            alt="company-image-123123"
            loading="lazy"
            objectFit="cover"
            className="rounded"
            layout="fill"
          />
          <div className="absolute bottom-0 left-10 flex translate-y-[70%] items-end gap-2">
            <Image
              src={
                company?.image_logo ??
                "https://c.topdevvn.com/uploads/2025/08/26/default-topdev.png"
              }
              alt="company-image-123123"
              loading="lazy"
              className="h-14 w-14 rounded border-[0.5px] border-brand-500 bg-white object-contain p-2"
              width={56}
              height={56}
            />
            <div className="flex flex-col justify-end">
              <span className="text-sm font-semibold text-brand-500">
                {company?.display_name}
              </span>
              <span className="text-xs text-text-400">987 followers</span>
            </div>
          </div>
        </div>
        <div
          className="mt-11 line-clamp-3 max-h-[60px] max-w-full !text-xs leading-snug text-text-500"
          dangerouslySetInnerHTML={{
            __html: company.description,
          }}
        ></div>
        <div className="mt-3 flex w-full justify-between">
          <div className="flex items-center gap-1">
            <Link
              href={company?.detail_url ?? "/"}
              className="flex h-[30px] w-[138px] items-center justify-center rounded border-[0.75px] border-brand-500 bg-brand-50 text-xs text-brand-500"
            >
              Xem công ty
            </Link>
            <Button
              variant="ghost"
              className="h-[30px] rounded border-[0.75px] border-brand-500 bg-brand-50 text-xs text-brand-500"
            >
              + Follow
            </Button>
          </div>
          <Link
            href={company?.detail_url ?? "/"}
            className="flex items-center gap-1 text-xs text-brand-500"
          >
            {company.num_job_openings} jobs <MoveRight className="h-3 w-3" />
          </Link>
        </div>
      </CardContent>
    </Card>
  );
};

export default CardPageCompany;
