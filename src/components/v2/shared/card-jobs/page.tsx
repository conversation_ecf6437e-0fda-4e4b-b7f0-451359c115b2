"use client";
import { cn } from "@/lib/utils";
import { JobType } from "@/types/job";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { createPortal } from "react-dom";
import { CiImageOn } from "react-icons/ci";
import { Calendar, Clock, LevelUp, Location, MoneyIcon } from "../../icons";
import BenefitBrown from "../../icons/BenefitBrown";
import { Button } from "../../ui/button";
import { Card, CardContent } from "../../ui/card";
import FavoriteButton from "../favorite-button/page";

const CardJob = ({
  className = "",
  job,
  srcPage,
  mediumPage,
  isTopJob,
}: {
  className?: string;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
  isTopJob?: boolean;
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";
  const [pos, setPos] = useState<{
    top: number;
    left: number;
    width: number;
  } | null>(null);

  const t = useTranslations();
  return (
    <Card
      className={cn(
        "group h-[247px] w-full rounded-xl border border-transparent bg-[#F8FBFF] transition-all hover:border hover:border-brand-600 md:h-[208px]",
        className,
        isTopJob && "hover:border-transparent",
      )}
      onMouseEnter={(e) => {
        const rect = e.currentTarget.getBoundingClientRect();
        setPos({
          top: rect.top + window.scrollY,
          left: rect.left + window.scrollX,
          width: rect.width,
        });
      }}
      onMouseLeave={() => setPos(null)}
    >
      <CardContent className="relative p-3 md:p-4">
        <div className="absolute right-4 top-4 block md:hidden">
          <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
        </div>
        <div>
          <div className="flex flex-col gap-[6px] md:flex-row md:gap-3">
            <Image
              src={job?.company?.image_logo}
              alt="job-image"
              width={64}
              height={64}
              className="h-[40px] w-[40px] rounded-[4px] object-contain md:h-[64px] md:w-[64px]"
            />
            <div className="flex flex-col justify-between">
              <Link
                className="text-brand-500 line-clamp-1 text-sm/[18px] font-semibold md:line-clamp-2 md:text-base/[24px]"
                href={`/detail-jobs/${job.slug}-${job.id}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
                target="_blank"
              >
                {job?.title}
              </Link>
              <Link
                className="text-text-500 line-clamp-1 text-xs/[16px] font-medium"
                href={`${job?.company?.detail_url}?src=${srcPageQueryParam}&medium=${mediumPageQueryParam}`}
                target="_blank"
              >
                {job?.company?.display_name}
              </Link>
            </div>
          </div>
          <div className="md:mt-2">
            <span className="text-brand-500 flex items-center gap-[6px] text-sm font-semibold md:text-base/[18px]">
              <span className="inline-block">
                <MoneyIcon />
              </span>{" "}
              <span className="line-clamp-1">{job?.salary?.value}</span>
            </span>
            <div className="mb-2 mt-2 grid grid-cols-1 gap-[6px] md:mb-3 md:grid-cols-2 md:gap-1">
              <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium md:text-sm/[14px]">
                <div className="block h-[14px] w-[14px]">
                  <Location />
                </div>
                <span className="line-clamp-1">
                  {job?.addresses?.sort_addresses}
                </span>
              </span>
              <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium md:text-sm/[14px]">
                <LevelUp />{" "}
                <span className="line-clamp-1 ">{job?.job_levels_str}</span>
              </span>
              <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium md:text-sm/[14px]">
                <Clock /> Fulltime
              </span>
              <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium md:text-sm/[14px]">
                <Calendar />{" "}
                {job?.months_of_experience && job?.months_of_experience > 12
                  ? `${job?.months_of_experience / 12}+ years exp`
                  : job?.months_of_experience + " months exp"}
              </span>
            </div>
            <div className="border-t-text-100 flex items-center justify-center border-t pt-[6px] md:justify-between md:pt-[7px]">
              <span className="text-text-500 flex items-center gap-1 text-xs md:text-sm">
                {job?.published?.since}
              </span>
              <div className="hidden items-center gap-[6px] md:flex">
                <FavoriteButton id={job?.id} isFollow={job?.is_followed} />
                <Button className="bg-brand-500 text-brand-50 h-6 rounded-[64px] px-3 py-2 text-[10px]/[24px] font-bold uppercase hover:bg-brand-600">
                  apply now
                </Button>
              </div>
            </div>
          </div>
        </div>
        {isTopJob &&
          pos &&
          createPortal(
            <div
              className="animate-fade-in border-text-200 z-10 flex h-80 cursor-pointer flex-col items-center gap-2 rounded-xl 
                 border bg-white p-4 shadow-[0px_4px_4px_0px_#3659B340] transition-all 
                 duration-300 ease-in-out"
              style={{
                position: "absolute",
                top: pos.top,
                left: pos.left,
                width: pos.width,
                zIndex: 9999,
              }}
            >
              <Image
                src={job?.company?.image_logo ?? ""}
                alt="job-image"
                className="rounded-lg p-1"
                width={108}
                height={40}
                style={{
                  width: "108px",
                  height: "40px",
                  objectFit: "contain",
                }}
              />
              <span className="text-text-500 text-center text-xs font-medium">
                {job.company?.display_name}
              </span>
              <Link
                href={job?.detail_url ?? "#"}
                className="text-brand-500 line-clamp-2 text-center font-semibold"
              >
                {job?.title}
              </Link>
              <div className="flex w-full flex-col gap-2">
                <span className="text-brand-500 line-clamp-1 flex items-center gap-[6px] text-base/[18px] font-semibold">
                  <MoneyIcon /> {job?.salary?.value}
                </span>
                <span className="text-text-500 flex items-center gap-1 text-xs/[12px] font-medium md:text-sm/[14px]">
                  <div className="block h-[14px] w-[14px]">
                    <Location />
                  </div>
                  <span className="line-clamp-1">
                    {job?.addresses?.sort_addresses}
                  </span>
                </span>
              </div>

              <div className="absolute bottom-5 right-4">
                <div className="mt-14 flex w-full items-end justify-between">
                  <div className="flex flex-col gap-[2px] pl-4">
                    <div className="flex flex-wrap gap-[2px]">
                      {job?.benefits_v2?.slice(0, 3)?.map((benefit) => (
                        <span
                          key={benefit?.name}
                          className="block h-fit w-fit bg-[#F8FBFF] p-1 text-[10px]/[12px] text-[#0A5D27]"
                        >
                          {benefit.name}
                        </span>
                      ))}
                    </div>
                    <span className="flex h-fit w-fit items-center gap-1 whitespace-nowrap bg-[#F8FBFF] px-2 text-[10px]/[16px] text-[#9E370E]">
                      <BenefitBrown />
                      Top 100 Largest Global Companies
                    </span>
                  </div>
                  {job?.company?.image_cover ? (
                    <Image
                      src={job?.company?.image_cover}
                      alt="job-image"
                      className="rounded-full bg-white"
                      width={80}
                      height={80}
                      style={{
                        width: "80px",
                        height: "80px",
                        objectFit: "cover",
                      }}
                    />
                  ) : (
                    <CiImageOn className="h-20 w-20" />
                  )}
                </div>
              </div>
            </div>,
            document.body,
          )}
      </CardContent>
    </Card>
  );
};

export default CardJob;
