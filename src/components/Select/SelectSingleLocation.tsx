"use client";
import { LocationType } from "@/types/search";
import { getCurrentLocale } from "@/utils/locale";
import { FC, MouseEvent, useEffect, useRef, useState } from "react";
import { FaCaretDown } from "react-icons/fa";
import { HiCheck } from "react-icons/hi2";
import { classNames } from "@/utils";

interface SelectSingleLocationType {
  value: LocationType | null;
  placeholder?: string;
  options: LocationType[];
  onSelect: (value: LocationType) => void;
  device?: "mobile" | "desktop";
}

const SelectSingleLocation: FC<SelectSingleLocationType> = (props) => {
  const { value, options, onSelect, placeholder, device = "desktop" } = props;
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener("click", handleClickContext);
    return () => {
      window.removeEventListener("click", handleClickContext);
    };
  }, []);

  const handleClickContext = (event: globalThis.MouseEvent) => {
    if (!dropdownRef.current?.contains(event.target as Element)) {
      setIsOpen(false);
    }
  };

  const handleToggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const getValue = () => {
    try {
      if (!value) {
        return "Data is invalid!";
      }
      const valueFiltered = options.find((item) => item.id === value.id);
      if (!valueFiltered) {
        return "Data is invalid!";
      }
      return getCurrentLocale() === "vi"
        ? valueFiltered.text_vi
        : valueFiltered.text_en;
    } catch (error) {
      return "Data is invalid!";
    }
  };

  const handleSelectItem = (
    event: MouseEvent<HTMLLIElement>,
    selectedItem: LocationType,
  ) => {
    setIsOpen(false);
    onSelect(selectedItem);
  };

  return (
    <div ref={dropdownRef} className="relative select-none whitespace-nowrap">
      <div
        className={classNames(
          "relative flex cursor-pointer items-center justify-between rounded border border-solid px-4 py-2 transition-all",
          value !== null && value?.slug
            ? "border-gray-600 bg-gray-600 text-white"
            : isOpen
            ? "border-gray-200 bg-white"
            : "border-white bg-white",
          device === "desktop" ? "h-12" : "h-9 text-sm",
        )}
        onClick={handleToggleDropdown}
      >
        <span className="flex-1">
          {value !== null ? getValue() : placeholder}
        </span>
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </div>
      <div
        className={classNames(
          "absolute left-0 top-full z-10 w-full translate-y-1 rounded bg-white py-4 shadow-md transition-all ease-out",
          isOpen ? "visible opacity-100" : "invisible opacity-0",
        )}
      >
        <ul>
          {options.map((optionItem) => {
            return (
              <li
                key={optionItem.id}
                onClick={(event) => handleSelectItem(event, optionItem)}
                className="cursor-pointer"
              >
                <div
                  className={classNames(
                    "flex items-center justify-between gap-2 p-4 transition-all hover:bg-gray-100",
                    value?.id === optionItem.id ? "text-primary" : "",
                  )}
                >
                  <span className="line-clamp-1 flex-1">
                    {getCurrentLocale() === "vi"
                      ? optionItem.text_vi
                      : optionItem.text_en}
                  </span>
                  {value?.id === optionItem.id && (
                    <span className="inline-flex h-5 w-5 items-center justify-center">
                      <HiCheck />
                    </span>
                  )}
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default SelectSingleLocation;
