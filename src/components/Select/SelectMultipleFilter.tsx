import { TaxonomyType } from "@/types/taxonomy";
import { classNames } from "@/utils";
import { getCurrentLocale } from "@/utils/locale";
import {
  FC,
  MouseEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { FaCaretDown } from "react-icons/fa";

interface SelectMultipleFilterType {
  value: TaxonomyType[];
  options: TaxonomyType[];
  onSelect: (values: TaxonomyType[]) => void;
  placeholder?: string;
  width?: number;
  prefix: string;
  device?: "mobile" | "desktop";
}

const SelectMultipleFilter: FC<SelectMultipleFilterType> = (props) => {
  const {
    onSelect,
    value,
    options,
    placeholder,
    prefix,
    device = "desktop",
  } = props;
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    window.addEventListener("click", handleClickContext);
    return () => window.removeEventListener("click", handleClickContext);
  }, []);

  const handleClickContext = (event: globalThis.MouseEvent) => {
    if (!dropdownRef.current?.contains(event.target as Element)) {
      setIsOpen(false);
    }
  };

  const handleToggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const handleSelectItem = (
    event: MouseEvent<HTMLLIElement>,
    selectedItem: TaxonomyType,
  ) => {
    if (value.length > 0) {
      const isExist = value.find((item) => item.id === selectedItem.id);
      if (isExist) {
        onSelect(value.filter((item) => item.id !== selectedItem.id));
      } else {
        onSelect([...value, selectedItem]);
      }
    } else {
      onSelect([selectedItem]);
    }
  };

  const getStatusChecked = (optionsItem: TaxonomyType) => {
    return value.length > 0 && value.find((item) => item.id === optionsItem.id)
      ? true
      : false;
  };

  const getTitleFromOptions = (id: number) => {
    try {
      const result = options.find((item) => item.id === id);
      return result
        ? getCurrentLocale() === "vi"
          ? result.text_vi
          : result.text_en
        : "";
    } catch (error) {
      return "Data is invalid!";
    }
  };

  const isExistValue = useCallback(() => {
    return value.length > 0 ? true : false;
  }, [value]);

  return (
    <div ref={dropdownRef} className="relative select-none whitespace-nowrap">
      <div
        className={classNames(
          "relative flex cursor-pointer items-center justify-between rounded border border-solid px-4 py-2 transition-all",
          isExistValue()
            ? "border-gray-600 bg-gray-600 text-white"
            : isOpen
            ? "border-gray-200 bg-white"
            : "border-white bg-white",
          device === "desktop" ? "h-12" : "h-9 text-sm",
        )}
        onClick={handleToggleDropdown}
      >
        <span className="flex-1">
          {isExistValue()
            ? value.length === 1
              ? getTitleFromOptions(value[0].id)
              : `${prefix} (+${value && value.length})`
            : placeholder}
        </span>
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </div>
      <div
        className={classNames(
          "absolute left-0 top-full z-10 w-full translate-y-1 rounded bg-white py-4 shadow-md transition-all ease-out",
          isOpen ? "visible opacity-100" : "invisible opacity-0",
        )}
      >
        <ul>
          {options.map((optionItem) => {
            return (
              <li
                key={optionItem.id}
                onClick={(event) => handleSelectItem(event, optionItem)}
                className="cursor-pointer"
              >
                <div
                  className={classNames(
                    "flex items-center justify-between gap-2 p-4 text-sm transition-all hover:bg-gray-100 lg:text-base",
                  )}
                >
                  <input
                    type="checkbox"
                    className="h-5 w-5 rounded outline-none ring-0 checked:bg-primary checked:text-primary checked:accent-primary focus:border-none focus:shadow-transparent focus:outline-none focus:ring-0"
                    checked={getStatusChecked(optionItem)}
                    readOnly
                  />
                  <span className="line-clamp-1 flex-1">
                    {getCurrentLocale() === "vi"
                      ? optionItem.text_vi
                      : optionItem.text_en}
                  </span>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default SelectMultipleFilter;
