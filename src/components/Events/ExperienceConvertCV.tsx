"use client";
import { classNames } from "@/utils";
import { getCookie, setCookie } from "@/utils/cookies";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { HiX } from "react-icons/hi";
import { useSearchParams } from "next/navigation";

const ExperienceConvertCV = () => {
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations();
  const searchParams = useSearchParams();

  useEffect(() => {
    const checkVisible = !!getCookie("event-convert-cv");
    if (!checkVisible) {
      setCookie("event-convert-cv", "1", 1);
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
  }, [isOpen]);

  const handleCloseEvent = () => {
    setIsOpen(false);
  };

  const isDisablePopup = searchParams.get("disablePopup");

  return !isDisablePopup ? (
    <div
      className={classNames(
        "fixed bottom-0 left-0 right-0 top-0 z-[1000] flex items-center justify-center bg-black/30 lg:z-40",
        isOpen ? "fixed" : "hidden",
      )}
    >
      <div className="relative mx-auto w-screen max-w-[22.5rem] overflow-hidden rounded-lg bg-white lg:max-w-xl">
        <div className="flex justify-end">
          <button
            className="ml-auto inline-flex h-12 w-12 items-center justify-center rounded-lg hover:bg-gray-light"
            type="button"
            onClick={handleCloseEvent}
          >
            <HiX />
          </button>
        </div>
        <div className="px-6 pb-6 text-center lg:px-8 lg:pb-8">
          <Image
            src="/v4/assets/images/events/bg-convert-cv.webp"
            alt="Experience Convert CV"
            width={340}
            height={385}
            loading="lazy"
            className="mx-auto h-auto max-w-[12.75rem] lg:max-w-full"
          />
          <div
            className="mt-4 text-2xl font-bold lg:text-4xl"
            dangerouslySetInnerHTML={{
              __html: t("experience_convert_cv_pop_up_title"),
            }}
          ></div>
          <p className="mt-3 text-sm lg:text-base">
            {t("experience_convert_cv_pop_up_description")}
          </p>
          <Link
            onClick={() => handleCloseEvent()}
            href="https://topdev.vn/tao-cv-online?src=topdev.vn&medium=popup"
            className="mt-4 inline-flex h-14 items-center justify-center rounded-lg bg-primary px-8 font-bold text-white"
          >
            {t("experience_convert_cv_pop_up_try_now")}
          </Link>
        </div>
      </div>
    </div>
  ) : (
    ""
  );
};

export default ExperienceConvertCV;
