"use client";
import { classNames, gtag } from "@/utils";
import { getCookie, setCookie } from "@/utils/cookies";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { isAndroid } from "react-device-detect";
import { HiXMark } from "react-icons/hi2";
const SmartBanner = () => {
  const [imgBanner, setImgBanner] = useState("");
  const [linkBanner, setLinkBanner] = useState("");
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!getCookie("hidden_smartbanner")) {
      setIsVisible(true);
      document.body.style.paddingTop = "80px";
    }

    if (isAndroid) {
      const img =
        document.head
          .querySelector("[rel='android-touch-icon']")
          ?.getAttribute("href") ?? "";
      setImgBanner(img);
      const appId =
        document.head
          .querySelector("[name='google-play-app']")
          ?.getAttribute("content")
          ?.split("=")[1] ?? "";
      const link = `https://play.google.com/store/apps/details?id=${appId}`;
      setLinkBanner(link);
    } else {
      const img =
        document.head
          .querySelector("[rel='apple-touch-icon']")
          ?.getAttribute("href") ?? "";
      setImgBanner(img);
      const appId =
        document.head
          .querySelector("[name='apple-itunes-app']")
          ?.getAttribute("content")
          ?.split("=")[1] ?? "";
      const link = `https://apps.apple.com/app/id${appId}`;
      setLinkBanner(link);
    }
  }, []);

  const handleCloseBanner = () => {
    setCookie("hidden_smartbanner", "true", 30 * 24 * 60 * 60);
    setIsVisible(false);
    document.body.style.paddingTop = "unset";
  };

  return (
    <div
      className={classNames(
        "left-0 top-0 z-[999] flex h-20 w-full items-center gap-3 px-2.5 text-white shadow-[inset_0_4px_0_#88b131]",
        isVisible ? "absolute" : "hidden",
      )}
      style={{
        background:
          "#3d3d3d url(data:image/gif;base64,R0lGODlhCAAIAIABAFVVVf///yH5BAEHAAEALAAAAAAIAAgAAAINRG4XudroGJBRsYcxKAA7)",
      }}
    >
      <button
        type="button"
        onClick={() => handleCloseBanner()}
        className="inline-flex h-4 w-4 items-center justify-center rounded-full bg-black text-sm text-white"
      >
        <HiXMark />
      </button>
      {imgBanner && (
        <Image
          src={imgBanner}
          width={56}
          height={56}
          className="h-14 w-14 rounded-[10px]"
          alt="download app"
          loading="lazy"
        />
      )}
      <div className="flex-1">
        <div className="line-clamp-1 text-sm font-bold">
          TopDev - Tìm Việc Làm IT
        </div>
        <div className="line-clamp-1 text-xs">Applancer</div>
        <div className="line-clamp-1 text-xs">
          FREE - {isAndroid ? "In Google Play" : "On the App Store"}
        </div>
      </div>
      {linkBanner && (
        <Link href={linkBanner}>
          <button
            id="btn_download_app_cta"
            className="inset-px inline-block border border-solid border-white bg-gradient-to-b from-[#42b6c9] to-[#39a9bb] px-3 py-1 text-sm font-bold uppercase text-gray-200 outline-1 outline-black"
          >
            OPEN
          </button>
        </Link>
      )}
    </div>
  );
};

export default SmartBanner;
