"use client";
import React from "react";
import { Switch } from "@headlessui/react";
import { classNames } from "@/utils";

interface Props {
  checked?: boolean;
  defaultChecked?: boolean;
  name?: string;
  id?: string;
  value?: string;
  disabled?: boolean;
  size?: string | undefined | "small" | "large";
  style?: string | undefined | "blue" | "red";
  onChange?(checked: boolean): void;
}

// Custom size for Toggle
const toggleSize = (size: string | undefined) => {
  let sizeClass = {
    widthSwitch: "w-11",
    heightSwitch: "h-6",
    widthDots: "w-4",
    heightDots: "h-4",
    translate: "translate-x-6",
  };

  switch (size) {
    case "small":
      sizeClass = {
        widthSwitch: "w-10",
        heightSwitch: "h-5",
        widthDots: "w-3",
        heightDots: "h-3",
        translate: "translate-x-6",
      };
      break;

    case "large":
      sizeClass = {
        widthSwitch: "w-16",
        heightSwitch: "h-8",
        widthDots: "w-6",
        heightDots: "h-6",
        translate: "translate-x-9",
      };
      break;

    default:
      break;
  }

  return sizeClass;
};

// Custom color Toggle
const ToggleStyle = (style: string | undefined) => {
  let styleClass = "bg-primary";

  switch (style) {
    case "blue":
      styleClass = "bg-[#0c8ce9]";
      break;

    case "red":
      styleClass = "bg-[#ff0000]";
      break;

    default:
      break;
  }

  return styleClass;
};

//Component
const Toggle: React.FC<Props> = ({
  style,
  size,
  checked,
  defaultChecked,
  name,
  id,
  value,
  disabled,
  onChange,
}) => {
  return (
    <Switch
      id={id ?? "switchToggle"}
      checked={checked}
      defaultChecked={defaultChecked}
      name={name}
      value={value}
      disabled={disabled}
      onChange={onChange}
      className={classNames(
        checked ? ToggleStyle(style) : "bg-gray-300",
        toggleSize(size)?.widthSwitch,
        toggleSize(size)?.heightSwitch,
        "relative inline-flex items-center rounded-full outline-none transition duration-200 ease-in-out",
      )}
    >
      <span
        className={classNames(
          checked ? toggleSize(size)?.translate : "translate-x-1",
          toggleSize(size)?.widthDots,
          toggleSize(size)?.heightDots,
          "inline-block transform rounded-full bg-white transition duration-200 ease-in-out",
        )}
      />
    </Switch>
  );
};

export default Toggle;
