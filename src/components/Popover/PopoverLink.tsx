"use client";

import { JobType } from "@/types/job";
import Link from "@/components/Link/Link";
import React, {
  FC,
  ReactNode,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { createPortal } from "react-dom";
import { useHover } from "usehooks-ts";
import CardJ<PERSON><PERSON><PERSON> from "../Card/Job/CardJobHover";
import classNames from "@/utils/classNames";

interface PopoverLinkType {
  data: JobType;
  children: ReactNode;
}

const PopoverLink: FC<PopoverLinkType> = ({ data, children }) => {
  const linkRef = useRef<HTMLAnchorElement>(null);
  const isLinkRefHover = useHover(linkRef);
  const portalRef = useRef<HTMLDivElement>(null);
  const isPortalRefHover = useHover(portalRef);
  const [isShow, setIsShow] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  useLayoutEffect(() => {
    if (isLinkRefHover && linkRef.current) {
      const left = linkRef.current.getBoundingClientRect().x;
      const top = linkRef.current.getBoundingClientRect().y;
      const timeout = setTimeout(() => {
        setIsShow(true);
        clearTimeout(timeout);
        setPosition({
          top,
          left,
        });
      }, 500);
    }
    if (isShow && !isPortalRefHover && !isLinkRefHover) {
      setPosition({
        top: 0,
        left: 0,
      });
      setIsShow(false);
    }
    return () => {
      setIsShow(false);
      setPosition({
        top: 0,
        left: 0,
      });
    };
  }, [isLinkRefHover, isPortalRefHover]);

  return (
    <>
      <Link href={data.detail_url} ref={linkRef}>
        {children}
      </Link>
      {isShow &&
        createPortal(
          <div
            ref={portalRef}
            className={classNames("fixed z-10", isShow ? "block" : "hidden")}
            style={{ top: position.top + "px", left: position.left + "px" }}
          >
            <CardJobHover job={data} />
          </div>,
          document.body,
        )}
    </>
  );
};

export default PopoverLink;
