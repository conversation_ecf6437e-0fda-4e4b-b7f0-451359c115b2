import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "../Button";

export default function ResultNotFoundPayment() {
  const t = useTranslations();

  return (
    <div
      data-cy="not-found"
      className="m-auto flex w-full max-w-[778px] flex-col gap-4 md:gap-10 py-12 text-center"
    >
      <Image
        src="/v4/assets/images/online-payment/icon-order-notfound.png"
        alt="Not found"
        width="184"
        height="200"
        className="m-auto"
      />
      <h3 className="text-xl font-bold text-gray-600 md:text-3xl">
        {t("carts_status_status_title_notfound")}
      </h3>
      <p className="leading-6 text-center">{t("carts_status_description_notfound")}</p>
      {/* <div className="m-auto w-full max-w-[220px]">
        <Button
          onClick={() =>
            (window.location.href = process.env
              .NEXT_PUBLIC_EMPLOYER_DASH_URL as string)
          }
          accent="primary"
        >
          {t("carts_status_order_view_cart_history")}
        </Button>
      </div> */}
      <div className="border-t border-gray-200 pt-4 text-center text-gray-500">
        <p>
          {t.rich("carts_status_for_support", {
            a: (chunk) => (
              <a
                className="font-semibold text-blue-800"
                href={`mailto:${chunk}`}
              >
                {chunk}
              </a>
            ),
          })}
        </p>
        <p className="mt-1">
          {t.rich("carts_status_call_hotline", {
            a: (chunk) => (
              <a className="font-bold text-blue-800" href={`tel:${chunk}`}>
                {chunk}
              </a>
            ),
          })}
        </p>
      </div>
    </div>
  );
}
