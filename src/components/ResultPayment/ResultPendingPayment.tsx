import { Player } from "@lottiefiles/react-lottie-player";
import { useTranslations } from "next-intl";
import pendingConvertAnimation from "@/assets/animations/pending-convert.json";

export default function ResultPendingPayment() {
  const t = useTranslations();

  return (
    <div data-cy="pending" className="h-[33vh]">
      <div className="peding-payment fixed left-0 top-0 flex h-screen w-full items-center justify-center bg-[rgba(41,41,41,0.5)]">
        <div className="flex w-full max-w-[502px] flex-col gap-5 rounded bg-white px-5 pb-5 pt-5 text-center shadow-md md:px-10 md:pb-12 ">
          <Player
            autoplay
            loop
            src={pendingConvertAnimation}
            style={{ height: "192px", width: "192px" }}
          ></Player>
          <div className="flex flex-col gap-2">
            <h3 className="text-xl font-bold">
              {t("carts_status_pending_title")}
            </h3>
            <p className="leading-6">{t("carts_status_peding_description")}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
