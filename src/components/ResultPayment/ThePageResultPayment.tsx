"use client";

import { useState, useEffect } from "react";
import { ResultProps } from "@/types/cart";
import { getResultPayment } from "@/services/cartAPI";
import ResultStatusPayment from "@/components/ResultPayment/ResultStatusPayment";
import ResultNotFoundPayment from "@/components/ResultPayment/ResultNotFoundPayment";
import ResultPendingPayment from "@/components/ResultPayment/ResultPendingPayment";
import { RESULT_STATUS } from "@/contansts/payment";

export default function ThePageResultPayment({
  orderCode,
}: {
  orderCode: string;
}) {
  const [loading, setLoading] = useState<boolean>(false);
  const [resultPayment, setResultPayment] = useState<ResultProps>({
    amount: 0,
    code: "",
    created_at: "",
    id: 0,
    paid_at: "",
    payment_method: "",
    status: null,
  });

  useEffect(() => {
    getResultPayment(orderCode)
      .then((data) => {
        if (data.status == 200) {
          setResultPayment(data.data.data);
        }
      })
      .catch((err) => console.error(err))
      .finally(() => setLoading(true));
  }, [orderCode]);

  useEffect(() => {
    if (resultPayment.status === RESULT_STATUS.PENDING) {
      const timeRecall = setInterval(() => {
        getResultPayment(orderCode)
          .then((data) => {
            if (data.status == 200) {
              setResultPayment(data.data.data);

              if (data.data.data.status !== RESULT_STATUS.PENDING) {
                clearInterval(timeRecall);
              }
            }
          })
          .catch((err) => {
            clearInterval(timeRecall);
            console.error(err);
          })
          .finally(() => {
            if (resultPayment.status !== RESULT_STATUS.PENDING) {
              clearInterval(timeRecall);
            }
            setLoading(true);
          });
      }, 10000);
    }
  }, [resultPayment.status]);

  const isNotPending = !!resultPayment && resultPayment.status !== RESULT_STATUS.PENDING;

  return (
    <>
      <div id="result" className="bg-gray-100">
        {loading ? (
          !!resultPayment && !!resultPayment.code ? (
            isNotPending ? (
              <ResultStatusPayment
                amount={resultPayment.amount}
                code={resultPayment.code}
                created_at={resultPayment.created_at}
                id={resultPayment.id}
                paid_at={resultPayment.paid_at}
                payment_method={resultPayment.payment_method}
                status={resultPayment.status}
              />
            ) : (
              <ResultPendingPayment />
            )
          ) : (
            <ResultNotFoundPayment />
          )
        ) : (
          <div className="flex w-full justify-center py-5 md:py-10">
            <button
              type="button"
              className="shadow inline-flex cursor-not-allowed items-center rounded-lg bg-primary px-4 py-2 text-sm font-semibold leading-6 text-white transition duration-150 ease-in-out"
              disabled
            >
              <svg
                className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </button>
          </div>
        )}
      </div>
    </>
  );
}
