import Image from "next/image";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
import { Button } from "@/components/Button";
import { MethodProps, ResultProps } from "@/types/cart";
import { formatPriceVND } from "@/utils";
import { RESULT_STATUS } from "@/contansts/payment";
import { useAppSelector } from "@/store";
import { getMothodPayment } from "@/services/cartAPI";

export default function ResultStatusPayment({
  status,
  amount,
  code,
  created_at,
  id,
  paid_at,
  payment_method,
}: ResultProps) {
  const t = useTranslations();
  const [paymentMethodName, setPaymentMethodName] = useState<string>("");
  const user = useAppSelector((state) => state?.user?.user);
  const [isClient, setIsLient] = useState<Boolean>(false);

  useEffect(() => {
    setIsLient(true);
  }, []);

  useEffect(() => {
    getMothodPayment()
      .then((data) => {
        if (data.data.error == false && data.data.data.length > 0) {
          const paymentName = data.data.data.find(
            (payment: any) => payment.type === payment_method,
          );
          setPaymentMethodName(paymentName?.name);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  }, [payment_method]);

  const isSuccess = status == RESULT_STATUS.SUCCESS;
  const isFailed = status == RESULT_STATUS.FAILED;

  return (
    <div data-cy="status" className="py-7 md:py-10">
      <div className="m-auto flex w-full max-w-[618px] flex-col gap-4 rounded-xl bg-white pb-4 pt-6 text-center shadow-sm md:gap-10">
        {isSuccess && (
          <Image
            src="/v4/assets/images/online-payment/success-icon.png"
            alt="icon result"
            width={98}
            height={98}
            className="m-auto"
          />
        )}

        {isFailed && (
          <Image
            src="/v4/assets/images/online-payment/error-icon.png"
            alt="icon result"
            width={98}
            height={98}
            className="m-auto"
          />
        )}

        <div className="flex flex-col gap-2">
          <h3 className="text-xl font-bold md:text-3xl">
            {isSuccess
              ? t("carts_status_success_title")
              : t("carts_status_failed_title")}
          </h3>
          <p className="desc-status-payment m-auto max-w-[510px]">
            {isSuccess
              ? t.rich("carts_status_success_description", {
                b: (chunk) => <b>{chunk}</b>,
                email: user?.email,
              })
              : t("carts_status_failed_description")}
          </p>
        </div>
        <div className="px-3 flex justify-between gap-4 flex-col">
          <div className="m-auto flex w-full max-w-[530px] flex-col gap-4 rounded-xl bg-gray-100 p-6">
            <div className="flex flex-wrap items-center justify-between md:flex-nowrap">
              <span className="text-lg text-gray-400">
                {t("carts_status_order_code")}
              </span>
              <b className="w-full text-right text-gray-600 md:w-auto md:text-lg">
                {code}
              </b>
            </div>
            {isFailed ? (
              <div className="flex flex-wrap items-center justify-between md:flex-nowrap">
                <span className="text-lg text-gray-400">
                  {t("carts_status_time_order")}
                </span>
                <b className="w-full text-right text-gray-600 md:w-auto md:text-lg">
                  {isClient && created_at}
                </b>
              </div>
            ) : (
              <div className="flex flex-wrap items-center justify-between md:flex-nowrap">
                <span className="text-lg text-gray-400">
                  {t("carts_status_payment_time")}
                </span>
                <b className="w-full text-right text-gray-600 md:w-auto md:text-lg">
                  {paid_at &&
                    (isClient && isFailed ? t("carts_status_fail") : paid_at)}
                </b>
              </div>
            )}

            <div className="flex flex-wrap items-center justify-between md:flex-nowrap">
              <span className="text-lg text-gray-400">
                {t("carts_status_payment_methods")}
              </span>
              <b className="w-full text-right capitalize text-gray-600 md:w-auto md:text-lg">
                {paymentMethodName}
              </b>
            </div>
            <div className="flex flex-wrap items-center justify-between md:flex-nowrap">
              <span className="text-lg text-gray-400">
                {t("carts_status_order_value")}
              </span>
              <b className="w-full text-right text-gray-600 md:w-auto md:text-lg">
                {formatPriceVND(amount ?? 0)}
              </b>
            </div>
          </div>

          {isSuccess && (
            <div className="m-auto flex w-full max-w-[530px] flex-col gap-4 rounded-xl">
              <div
                className="flex items-start bg-blue-light py-2 px-4 text-gray-400 border-b border-blue-dark"
                role="alert"
              >
                <svg
                  className="h-5 w-5 flex-shrink-0 text-blue-dark"
                  aria-hidden="true"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
                </svg>
                <div className="ms-3 leading-5 text-left text-sm">
                  <h3 className="mb-2">{t("carts_status_title_for_status_success")}</h3>
                  <ul className="list-disc pl-3">
                    <li>
                      {t.rich("carts_status_content_for_status_success1", {
                        b: (chunk) => <b>{chunk}</b>,
                      })}
                    </li>
                    <li>
                      {t.rich("carts_status_content_for_status_success2", {
                        b: (chunk) => <b>{chunk}</b>,
                      })}
                    </li>
                    <li>
                      {t.rich("carts_status_content_for_status_success3", {
                        b: (chunk) => <b>{chunk}</b>,
                      })}
                    </li>
                    <li>
                      {t.rich("carts_status_content_for_status_success4", {
                        b: (chunk) => <b>{chunk}</b>,
                      })}
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>

        <div>
          <div className="m-auto flex flex-wrap items-center justify-center gap-4 md:flex-nowrap">
            <button
              onClick={() => (window.location.href = "/products")}
              className="inline-flex h-9 items-center justify-center gap-1 rounded border border-solid border-black bg-transparent px-4 text-sm font-semibold text-gray-500 transition-all hover:bg-gray-100 disabled:cursor-not-allowed dark:border-white dark:text-white lg:h-12 lg:gap-3 lg:px-6 lg:text-base"
            >
              {t("carts_status_go_to_products_page")}
            </button>
            {isSuccess && (
              <Button
                accent="primary"
                onClick={() =>
                (window.location.href =
                  process.env.NEXT_PUBLIC_BASE_URL_EMPLOYER_DASH +
                  "/unlock-candidate-management")
                }
              >
                {t("carts_status_order_activate_candidate_search_package")}
              </Button>
            )}
          </div>
          {isSuccess && (
            <div className="mt-5 text-center text-gray-500">
              {t.rich("carts_status_navigation_to_employer", {
                a: (chunk) => (
                  <a
                    className="underline hover:text-primary"
                    href={
                      process.env.NEXT_PUBLIC_BASE_URL_EMPLOYER_DASH +
                      "/company/profile?src=topdev.vn&medium=submenu"
                    }
                  >
                    {chunk}
                  </a>
                ),
              })}
            </div>
          )}
        </div>
        <div
          data-cy="footer"
          className="border-t border-gray-200 pt-4 text-center text-gray-500"
        >
          <p>
            {t.rich("carts_status_for_support", {
              a: (chunk) => (
                <a
                  className="font-semibold text-blue-800 hover:underline"
                  href={`mailto:${chunk}`}
                >
                  {chunk}
                </a>
              ),
            })}
          </p>
          <p className="mt-1">
            {t.rich("carts_status_call_hotline", {
              a: (chunk) => (
                <a
                  className="font-bold text-blue-800 hover:underline"
                  href={`tel:${chunk}`}
                >
                  {chunk}
                </a>
              ),
            })}
          </p>
        </div>
      </div>
    </div>
  );
}
