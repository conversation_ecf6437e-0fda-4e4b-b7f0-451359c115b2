"use client";

import Image from "next/image";
import { Tooltip } from "flowbite-react";
import { useTranslations } from "next-intl";
import { getFileCvUploadPreviewUrl } from "@/utils";
import { useState } from "react";
import { downloadUserProfileCv, getUserProfile } from "@/services/userAPI";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { HiEye } from "react-icons/hi2";
import { useAppSelector } from "@/store";
const SelectedCvApplyAllJobs = ({
  selectedCv,
  fileCV,
  userProfile,
}: {
  selectedCv: any;
  fileCV: any;
  userProfile?: string;
}) => {
  const newestCandidate = useAppSelector(
    (state) => state?.user?.newest_candidate,
  );
  const eventData = useAppSelector((state) => state?.setting?.eventData);
  const [isDownloading, setIsDownloading] = useState(false);
  const t = useTranslations();
  const handleDownloadClick = async () => {
    if (isDownloading) return;

    setIsDownloading(true);
    getUserProfile()
      .then((response) => {
        downloadUserProfileCv(response.data, true).finally(() =>
          setIsDownloading(false),
        );
      })
      .catch(() => setIsDownloading(false));
  };
  if (selectedCv?.type == "upload") {
    return (
      <div className="mt-3 flex items-center justify-between bg-gray-100 p-[12px]">
        <div className="flex items-center">
          <Image
            width="21"
            height="25"
            className="mr-3"
            alt="topdev"
            src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-pdf.png"
          />
          {selectedCv?.name}
        </div>
        <Tooltip
          content={t("detail_job_page_apply_popover_preview_cv")}
          theme={{
            style: {
              dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
            },
          }}
        >
          <a href={getFileCvUploadPreviewUrl(fileCV)} target="_blank">
            <Image
              src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-eye.png"
              width="16"
              height="12"
              alt="Topdev"
            />
          </a>
        </Tooltip>
      </div>
    );
  }
  else if (userProfile !== "null" && eventData?.user_profile_id != "null") {
    return (
      <div className="mt-3 flex items-center justify-between bg-gray-100 p-[12px]">
        <div className="flex items-center">
          <Image
            width="21"
            height="25"
            className="mr-3"
            alt="topdev"
            src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-pdf.png"
          />
          <span className="text-sm md:text-base">
            {t("detail_job_my_topdev_cv")}
          </span>
        </div>
        <Tooltip
          content={t("detail_job_page_apply_popover_preview_cv")}
          className="fade-in"
          theme={{
            style: {
              dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
            },
          }}
        >
          {isDownloading ? (
            <AiOutlineLoading3Quarters className="text-brand h-[18px] w-[18px] animate-spin text-brand-600" />
          ) : (
            <HiEye
              onClick={handleDownloadClick}
              title={t("detail_job_cv_type_action_view")}
              className="h-[18px] w-[18px] cursor-pointer text-neutral-400"
            />
          )}
        </Tooltip>
      </div>
    );
  }  else if (eventData?.type == "cvbuilder" || eventData?.type == "media") {
    return (
      <div className="mt-3 flex items-center justify-between bg-gray-100 p-[12px]">
        <div className="flex items-center">
          <Image
            width="21"
            height="25"
            className="mr-3"
            alt="topdev"
            src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-pdf.png"
          />
          <span className="text-sm md:text-base">{eventData?.file_name}</span>
        </div>
        <Tooltip
          content={t("detail_job_page_apply_popover_preview_cv")}
          theme={{
            style: {
              dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
            },
          }}
        >
          <a
            href={
              eventData?.type == "cvbuilder"
                ? `${process.env.NEXT_PUBLIC_BASE_URL}/users/view-cv/${eventData?.media_id}`
                : newestCandidate?.cv_media
            }
            target="_blank"
          >
            <Image
              src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-eye.png"
              width="16"
              height="12"
              alt="Topdev"
              className="ml-2"
            />
          </a>
        </Tooltip>
      </div>
    );
  } else {
    return <></>;
  }
};
export default SelectedCvApplyAllJobs;
