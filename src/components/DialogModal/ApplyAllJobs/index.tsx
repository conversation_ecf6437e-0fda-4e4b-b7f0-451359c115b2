"use client";

import "simplebar-react/dist/simplebar.min.css";
import dynamic from "next/dynamic";
import { isMobile } from "react-device-detect";
import { ChangeEvent, useEffect, useState } from "react";
import { IoMdClose } from "react-icons/io";
import { BsCash } from "react-icons/bs";
import { FaCloudUploadAlt } from "react-icons/fa";
import { useTranslations } from "next-intl";
import { classNames, getFormData, gtag } from "@/utils";
import { useAppDispatch, useAppSelector } from "@/store";
import Image from "next/image";
import SimpleBar from "simplebar-react";
import {
  setDataDialogNotification,
  setEventsState,
  showDialogNotification,
} from "@/store/slices/settingSlice";
import { JobType } from "@/types/job";
import { UserResumes } from "@/types/resume";
import { applyJobsApi } from "@/services/jobAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { VscLoading } from "react-icons/vsc";
import { Link } from "@/navigation";
//Component SelectedCvApplyAllJobs
const SelectedCvApplyAllJobs = dynamic(
  () => import("@/components/DialogModal/ApplyAllJobs/SelectedCvApplyAllJobs"),
);
//End Component SelectedCvApplyAllJobs

//Component button
const Button = dynamic(() => import("@/components/Button/Button"));
//End Component button

interface FileCvValidateProps {
  uploadError: boolean;
  message: string;
  icon: string;
}

const ApplyAllJobs = () => {
  const dispatch = useAppDispatch();
  const t = useTranslations();
  const user = useAppSelector((state) => state?.user?.user);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const eventData = useAppSelector((state) => state?.setting?.eventData);
  const events = useAppSelector((state) => state?.setting?.events);
  const [fileCV, setFileCV] = useState<any>(null);
  const [selectedCv, setSelectedCv] = useState<UserResumes>();
  const [listJobsSelected, setListJobsSelected] = useState<Array<number>>([]);
  const [isFetching, setIsFetching] = useState<boolean>(true);
  const [isNotCheckJob, setIsNotCheckJob] = useState<boolean>(false);
  const [fileCvValidate, setFileCvValidate] = useState<
    FileCvValidateProps | any
  >([]);
  //Show data notification for dialog
  const handleCacel = () => {
    const eventSuggestJobsElement = document.getElementById("EventSuggestJobs");
    if (eventSuggestJobsElement) {
      eventSuggestJobsElement.style.display = "none";
    }
    dispatch(setDataDialogNotification({ event: [""] }));
    dispatch(setEventsState({ events: [] }));
    if (typeof document !== "undefined")
      document.body.classList.remove("layer-popup");
  };
  const newestCandidate = useAppSelector(
    (state) => state?.user?.newest_candidate,
  );
  useEffect(() => {
    if (events?.includes("EventSuggestJobs")) {
      document.body.classList.add("layer-popup");
    }
    setIsFetching(false);
  }, [events]);

  useEffect(() => {
    if (newestCandidate) {
      const updateCandidates = {
        id: newestCandidate?.media_id,
        name: newestCandidate?.file_name,
        type: newestCandidate?.collection_name ?? "files_topdev_cv",
        url: newestCandidate?.cv_media,
      };
      setSelectedCv(updateCandidates);
    }
  }, [newestCandidate, events]);
  useEffect(() => {
    const jobsId = eventData?.similar_jobs?.map((item: any) => item.id);
    if (!!jobsId && Number(jobsId.length) > 0) setListJobsSelected(jobsId);
  }, [eventData]);

  const handleSelecteJob = (event: ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    const jobId = Number(event.target.value);
    if (!listJobsSelected) return;

    if (!checked) {
      const newListJobs = listJobsSelected?.filter(
        (id: number) => id !== jobId,
      );
      setListJobsSelected(newListJobs);
    } else {
      setListJobsSelected([...listJobsSelected, jobId]);
    }
  };

  useEffect(() => {
    if (!!listJobsSelected && listJobsSelected.length == 0) {
      setIsNotCheckJob(true);
    } else {
      setIsNotCheckJob(false);
    }
  }, [listJobsSelected]);

  /**
   * Upload file and check file
   * @param event
   * @returns
   */
  const handleChangeFile = (event: ChangeEvent<HTMLInputElement>) => {
    const file = (event.target as any).files[0];
    if (!file) return;
    const { type, size } = file;

    /**
     * Check file type
     */
    const fileType =
      type === "application/pdf" ||
      type ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
      type === "application/msword";

    if (!fileType) {
      setFileCvValidate({
        ...fileCvValidate,
        uploadError: true,
        message: t("detail_job_page_apply_validate_type_file_popup"),
        icon: "triangle",
      });
      return;
    }

    /**
     * 5MB file
     * Check file size
     */
    const fileSize = size / 1024 / 1024 < 5;
    if (!fileSize) {
      setFileCvValidate({
        ...fileCvValidate,
        uploadError: true,
        message: t("detail_job_page_apply_validate_type_file_popup"),
        icon: "triangle",
      });
      return;
    }

    setFileCV(file);
    setFileCvValidate({ ...fileCvValidate, uploadError: false });
    setSelectedCv({
      name: file.name,
      type: "upload",
    });
  };

  const handleApplyOrSaveAllJobs = async (follow = false) => {
    const { full_name, email, phone } = user;
    let userProfileId = null;
    let filesCv = null;
    let mediaId = null;
    let cvBuilderId = null;
    let eventType = eventData?.type;
    let fileName = eventData?.file_name;
    if (fileCV) {
      filesCv = fileCV;
      fileName = selectedCv?.name;
    } else if (eventType == "cvbuilder") {
      cvBuilderId = eventData?.media_id;
    } else if (eventData?.user_profile_id == "null") {
      mediaId = eventData?.media_id;
    } else if (eventData?.user_profile_id != "null") {
      userProfileId = eventData?.user_profile_id;
    }
    //Check choosed job
    if (listJobsSelected.length == 0) {
      setIsNotCheckJob(true);
      return;
    }
    const params = new URLSearchParams(window.location.href);
    const SID =
      params.has("sid") &&
      (typeof params.get("sid") === "string"
        ? params.get("sid")
        : params.get("sid")?.[0]);

    let data: any = {
      display_name: full_name,
      file_name: fileName,
      email,
      phone,
      job_id: [...listJobsSelected],
      files_cv: filesCv,
      case: isLoggedIn ? "ApplyLogined" : "ApplyNonLogin",
      media_id: mediaId,
      cvbuilder_id: cvBuilderId,
      user_profile_id: userProfileId,
      sid: SID ?? null,
      utm_source: params.has("utm_source") ? params.get("utm_source") : null,
      utm_medium: params.has("utm_medium") ? params.get("utm_medium") : null,
      utm_campaign: params.has("utm_campaign")
        ? params.get("utm_campaign")
        : null,
      upload_from: "UploadFromApplyToAll",
      source: "ApplyToAll",
      device_apply: isMobile ? "MobileWeb" : "PC",
      query_src: params.has("src") ? params.get("src") : null,
      query_medium: params.has("medium") ? params.get("medium") : null,
    };

    if (follow) {
      data = { ...data, is_follow: true };
    }

    setIsFetching(true);

    const formData = getFormData(data);

    try {
      const { data } = await applyJobsApi(formData);
      if (!data.success) {
        if (data.message == "applied") {
          ToastNotification({
            icon: "error",
            title: t("common_error"),
            description: t("detail_job_page_apply_you_have_applied_for_job"),
          });

          return;
        }

        console.error(data.message);
      }

      if (follow) {
        dispatch(
          setDataDialogNotification({
            email: "",
            status: data.success,
            comebackUrl: "",
            theme: "theme_noti_follow",
            event: [],
          }),
        );
        handleCacel();
        return;
      }

      listJobsSelected.forEach(async (id: any) => {
        const recommendJob: any = eventData?.similar_jobs?.map(
          (job: JobType) => job.id === id,
        );

        if (!!recommendJob) {
          gtag({
            event: "apply-to-all-completed",
            ecommerce: {
              checkout: {
                actionField: { list: "ApplyCV" },
                products: [
                  {
                    name: recommendJob.detail_url,
                    id: recommendJob.id,
                    brand: recommendJob?.company?.display_name,
                    category: "Job",
                    variant: recommendJob.job_types_str,
                  },
                ],
              },
            },
          });
        }
      });

      //Show event notification
      if (isLoggedIn && !!data.data) {
        const newData = { ...data.data, events: [] };
        setTimeout(() => {
          dispatch(setEventsState(newData));
        }, 3000);
      }
      handleCacel();
    } catch (error) {
      console.error(error);
      ToastNotification({
        icon: "error",
        title: t("common_sorry"),
        description: t("detail_job_page_apply_notification_apply_failed"),
      });
      setIsFetching(false);
    } finally {
      setIsFetching(false);
    }
  };
  useEffect(() => {
    dispatch(showDialogNotification(false));
  }, [dispatch]);

  if (!events?.includes("EventSuggestJobs")) return <></>;

  return (
    <>
      <section
        id="EventSuggestJobs"
        className={classNames(
          "popup-applyAllJob left=0 fixed top-0 z-[100] flex h-full w-full max-w-full items-center rounded p-3 transition-all md:left-1/2 md:top-1/2 md:h-auto  md:max-w-[800px] md:-translate-x-1/2 md:-translate-y-1/2",
        )}
      >
        <div className="scrollbar-primary box-popup-apply flex min-h-[100%] w-full flex-col overflow-y-auto overscroll-contain bg-white p-2 md:min-h-[660px] md:p-6">
          <div
            className={classNames(
              isFetching ? "flex" : "hidden",
              "load fixed left-1/2 top-1/2 z-50 h-full w-full max-w-[800px] -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded bg-[rgba(0,0,0,.35)]",
            )}
          >
            <div className="flex">
              <div className="h-4 w-4 animate-bounce rounded-full bg-white"></div>
              <div className="mx-2 h-4 w-4 animate-bounce rounded-full bg-white"></div>
              <div className="h-4 w-4 animate-bounce rounded-full bg-white"></div>
            </div>
          </div>
          <div className="headerDialog mb-5 sm:flex-[1]">
            <div className="-mb-3 text-right">
              <span
                onClick={() => handleCacel()}
                className="-mt-[9px] inline-block h-10 w-10 cursor-pointer text-center leading-10"
              >
                <IoMdClose className="inline-block h-5 w-5" />
              </span>
            </div>
            <h3
              id="titleHeadingSuggestJob"
              className="text-center text-2xl font-semibold text-primary"
            >
              {t("detail_job_page_your_are_apply_success_position")}
            </h3>
          </div>
          <SimpleBar
            className="scrollbar-primary flex-[8] overflow-y-auto sm:max-h-[384px]"
            autoHide={false}
            scrollbarMaxSize={3}
          >
            <div id="listJobRecomend">
              {!!eventData?.similar_jobs &&
                eventData?.similar_jobs.length > 0 &&
                eventData?.similar_jobs.map((job: JobType, index: number) => {
                  return (
                    <div
                      key={index}
                      className="itemJob flex flex-row gap-4 border-b border-gray-300 p-4 pl-0 lg:mb-3"
                    >
                      <label
                        htmlFor={job.id + "_checkbox"}
                        className="block w-6 cursor-pointer pt-1"
                      >
                        <input
                          type="checkbox"
                          className="checkbox-input hidden"
                          value={job.id}
                          defaultChecked={true}
                          id={job.id + "_checkbox"}
                          onClick={(event) => handleSelecteJob(event as any)}
                        />
                        <div className="checkbox-group relative flex h-4 w-4 items-center justify-center rounded border border-gray-300">
                          <span className="checkbox-checked"></span>
                        </div>
                      </label>
                      <div className="flex w-full flex-col md:flex-row">
                        <div className="w-full md:w-4/6">
                          <h2>
                            <Link
                              className="text-bray-600 text-sm font-semibold leading-normal"
                              href={{
                                pathname: "/detail-jobs/[slug]",
                                params: { slug: job.slug + "-" + job.id },
                              }}
                              title={job.title}
                              target="_blank"
                            >
                              {job.title}
                            </Link>
                          </h2>
                          <h3 className="text-sm leading-normal text-primary">
                            {(job as any)?.company_name}
                          </h3>
                          <p className="text-bray-600 text-sm leading-normal">
                            {job.addresses.sort_addresses}
                          </p>
                        </div>
                        <div className="w-full text-right md:w-2/6">
                          <p className="mb-1 inline-block text-sm text-primary">
                            <BsCash className="mr-1 inline-block h-4 w-4 align-middle" />
                            {job.salary.value}
                          </p>
                          <p className="flex flex-wrap justify-end">
                            {job.skills_arr.map(
                              (skill: string, index: number) => {
                                return (
                                  <span
                                    className="mb-1 ml-1 border border-gray-200 px-[8%] py-1 text-xs leading-none"
                                    key={index}
                                  >
                                    {skill}
                                  </span>
                                );
                              },
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </SimpleBar>
          <div className="footer-popup sm:flex-[2]">
            {isNotCheckJob && (
              <div className="mt-3 flex w-full items-center rounded bg-brand-100 px-3 py-1 font-semibold text-primary">
                <span className="mr-2 border-r border-[#ff9785] pr-2">
                  <Image
                    src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-alert-circle.png"
                    alt="icon circle"
                    width="20"
                    height="21"
                  />
                </span>
                {t("detail_job_page_please_choose_at_least")}
              </div>
            )}
            <div className="flex items-center justify-between pt-2 text-sm text-gray-600">
              <span className="font-bold">
                {t("detail_job_page_apply_fields_select_cv")}
              </span>
              <div className="flex">
                <input
                  className="hidden"
                  id="pushCV"
                  type="file"
                  onChange={handleChangeFile}
                />
                <label id="cvFile" className="cursor-pointer" htmlFor="pushCV">
                  <FaCloudUploadAlt className="mr-1 inline-block h-4 w-4" />
                  {t("detail_job_page_choose_file_new")}
                </label>
              </div>
            </div>
            {fileCvValidate && fileCvValidate.uploadError && (
              <div className="mt-3 flex w-full items-center rounded bg-brand-100 px-3 py-1 font-semibold text-primary">
                <span className="mr-2 border-r border-[#ff9785] pr-2">
                  {fileCvValidate.icon == "circle" ? (
                    <Image
                      src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-alert-circle.png"
                      alt="icon circle"
                      width="20"
                      height="21"
                    />
                  ) : (
                    <Image
                      src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-alert-triangle.png"
                      alt="icon triangle"
                      width="23"
                      height="19"
                    />
                  )}
                </span>
                {fileCvValidate.message}
              </div>
            )}
            <SelectedCvApplyAllJobs
              fileCV={fileCV}
              selectedCv={selectedCv}
              userProfile={eventData.user_profile_id}
            />
            <div className="mt-5 flex gap-[10px]">
              <Button
                accent={isFetching ? "disable" : "outline"}
                size="lg"
                isBlock
                onClick={() => !isFetching && handleApplyOrSaveAllJobs(true)}
              >
                {isFetching ? (
                  <VscLoading className="h-6 w-6 animate-spin text-primary-400" />
                ) : (
                  t("common_save")
                )}
              </Button>
              <Button
                accent={isFetching ? "disable" : "primary"}
                size="lg"
                isBlock
                onClick={() => !isFetching && handleApplyOrSaveAllJobs()}
              >
                {isFetching ? (
                  <VscLoading className="h-6 w-6 animate-spin text-primary-400" />
                ) : (
                  t("detail_job_page_apply_to_postion")
                )}
              </Button>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default ApplyAllJobs;
