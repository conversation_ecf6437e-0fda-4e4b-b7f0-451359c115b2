"use client";

import { useTranslations } from "next-intl";
import { useAppSelector, useAppDispatch } from "@/store";
import {
  setDataDialogNotification,
  showDialogNotification,
} from "@/store/slices/settingSlice";
import TheNotificationFollowingJob from "@/components/DialogModal/NotificationSuccess/TheNotificationFollowingJob";
import TheNotificationApplySuccessWithoutLogin from "@/components/DialogModal/NotificationSuccess/TheNotificationApplySuccessWithoutLogin";
import TheNotificationApplySuccessLogin from "@/components/DialogModal/NotificationSuccess/TheNotificationApplySuccessLogin";
import PromotionPopup from "@/components/PromotionPopup";
import { useEffect, useState } from "react";
import { getPromotionInfo } from "@/services/promotion";
import { Promotion } from "@/types/applyButton";
import dayjs from "dayjs";

const VOUCHER_10K = "https://c.topdevvn.com/uploads/2025/06/02/voucher_10k.png";
const VOUCHER_MACBOOK =
  "https://c.topdevvn.com/uploads/2025/06/02/voucher_macbook.png";

const NotificationSuccess = () => {
  const t = useTranslations();
  const [promotion, setPromotion] = useState<Promotion>();

  const dispatch = useAppDispatch();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const isVisibleDialogNotification = useAppSelector(
    (state) => state.setting.isVisibleDialogNotification,
  );

  const dataPopupNotification = useAppSelector(
    (state) => state.setting.dataPopupNotification,
  );

  const handleClose = () => {
    dispatch(showDialogNotification(false));
    dispatch(
      setDataDialogNotification({
        email: "",
        status: "",
        comebackUrl: "",
        theme: "",
        event: [],
      }),
    );

    if (!isLoggedIn) {
      document.location.href =
        document.location.origin + "/" + t("slug_it_jobs");
      return;
    }
  };
  useEffect(() => {
    const fetchPromotion = async () => {
      try {
        const request = await getPromotionInfo();
        if (request?.data) {
          if (request?.data) {
            setPromotion(request?.data?.data);
          }
        }
      } catch (err) {
        console.log(err);
      }
    };
    fetchPromotion();
  }, []);

  const isValidVoucher =
    promotion?.started_at &&
    promotion?.ended_at &&
    dayjs().isAfter(dayjs(promotion.started_at)) &&
    dayjs().isBefore(dayjs(promotion.ended_at));
  const isSecondStep =
    (promotion?.has_reward_available === true &&
      promotion?.has_user_already_received_reward === true) ||
    promotion?.has_reward_available === false;

  if (!isVisibleDialogNotification) return <></>;

  if (isValidVoucher) {
    return (
      <PromotionPopup
        isOpen={isVisibleDialogNotification}
        onClose={() => dispatch(showDialogNotification(false))}
        imageUrl={isSecondStep ? VOUCHER_MACBOOK : VOUCHER_10K}
        imageAlt="Special Promotion"
      />
    );
  }

  return (
    <>
      {/* Show UI Notification */}
      {dataPopupNotification.theme != "theme_noti_follow" ? (
        !!isLoggedIn ? (
          <TheNotificationApplySuccessLogin handleClose={handleClose} />
        ) : (
          <TheNotificationApplySuccessWithoutLogin handleClose={handleClose} />
        )
      ) : (
        <TheNotificationFollowingJob handleClose={handleClose} />
      )}
    </>
  );
};

export default NotificationSuccess;
