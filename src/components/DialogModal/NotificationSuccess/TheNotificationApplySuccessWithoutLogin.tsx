import Image from "next/image";
import { useTranslations } from "next-intl";
import { useAppSelector } from "@/store";
import { openLoginPopup } from "@/utils";
import { IoMdClose } from "react-icons/io";
import { FC } from "react";

const TheNotificationApplySuccessWithoutLogin: FC<{
  handleClose(): void;
}> = ({ handleClose }) => {
  const t = useTranslations();
  const dataPopupNotification = useAppSelector(
    (state) => state.setting.dataPopupNotification,
  );

  let textTitleForApplay: string = dataPopupNotification.status
    ? "detail_job_page_title_notification_success_popup"
    : "common_error";

  let descriptionForApplay: string = dataPopupNotification.status
    ? "detail_job_page_description_notification_success_popup"
    : "detail_job_page_cv_applied";

  return (
    <>
      <section
        id="Apply_CV_Success"
        data-cy="TheNotificationApplySuccessWithoutLogin"
        className="popup-notification fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-[rgba(51,51,51,.3)] p-3 transition-all"
      >
        <div className="w-full max-w-[562px] rounded bg-white">
          {/* header DialogModal */}
          <div className="headerDialog p-2 text-right">
            <span
              onClick={() => handleClose()}
              className="-mt-[9px] inline-block h-8 w-8 cursor-pointer text-center leading-10"
            >
              <IoMdClose className="inline-block h-5 w-5" />
            </span>
          </div>
          {/* End header DialogModal */}
          <div className={"px-4 pb-4 md:px-9 md:pb-14"}>
            <div>
              <div className="header-notification-success-popup text-center">
                <h1 className="text-2xl font-extrabold text-primary">
                  {t(textTitleForApplay)}
                </h1>
                <h2 className="mt-2.5 text-lg">{t(descriptionForApplay)}</h2>
              </div>
              <div className="py-6 text-center">
                <Image
                  className="mx-auto inline-block h-auto max-w-full"
                  width="160"
                  height="100"
                  priority
                  src="https://cdn.topdev.vn/v4/assets/images/apply_job/layer-notification.png"
                  alt="Topdev"
                />
              </div>
              {/* Show khi applay job success và không login */}
              <div className="content text-center">
                <h3 className="leading-5 text-gray-500">
                  {t(
                    "detail_job_page_content_email_notification_success_popup",
                  )}
                  <br />
                  <b>{dataPopupNotification?.email}</b>
                </h3>
                <b className="my-4 block text-xl font-semibold text-primary">
                  {t(
                    "detail_job_page_applay_faster_notification_success_popup",
                  )}
                </b>
                <button
                  onClick={() => openLoginPopup()}
                  type="button"
                  className="min-w-[260px] rounded bg-primary px-9 py-5 text-white transition-all duration-100 hover:bg-primary-400"
                >
                  {t("detail_job_page_sign_up_now")}
                </button>
              </div>
            </div>
            <div className="list-notification mt-6 px-5">
              <h3 className="mb-3 font-bold">
                {t("detail_job_page_title_list_benifit")}
              </h3>
              <ul>
                <li className="mb-3 flex items-center">
                  <span className="w-[52px]">
                    <Image
                      width="33"
                      height="28"
                      src="https://cdn.topdev.vn/v4/assets/images/apply_job/benifit-1.png"
                      alt="Topdev"
                      className="align-middle"
                    />
                  </span>
                  <span className="flex-1">
                    {t("detail_job_page_benifit1")}
                  </span>
                </li>
                <li className="mb-3 flex items-center">
                  <span className="w-[52px]">
                    <Image
                      width="33"
                      height="32"
                      src="https://cdn.topdev.vn/v4/assets/images/apply_job/benifit-2.png"
                      alt="Topdev"
                      className="align-middle"
                    />
                  </span>
                  <span className="flex-1">
                    {t("detail_job_page_benifit2")}
                  </span>
                </li>
                <li className="flex items-center">
                  <span className="w-[52px]">
                    <Image
                      width="33"
                      height="30"
                      src="https://cdn.topdev.vn/v4/assets/images/apply_job/benifit-3.png"
                      alt="Topdev"
                      className="align-middle"
                    />
                  </span>
                  <span className="flex-1">
                    {t("detail_job_page_benifit3")}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
export default TheNotificationApplySuccessWithoutLogin;
