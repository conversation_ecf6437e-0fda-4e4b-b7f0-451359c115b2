import BannerNotification from "@/assets/images/icons/banner-notification.svg";
import IconExplore from "@/assets/images/icons/icon-explore.svg";
import IconSuccessNotification from "@/assets/images/icons/icon-success-notification.svg";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { FC } from "react";
import { IoMdClose } from "react-icons/io";

const TheNotificationApplySuccessLogin: FC<{
  handleClose(): void;
}> = ({ handleClose }) => {
  const t = useTranslations();
  return (
    <>
      <section
        id="Apply_CV_Success"
        data-cy="TheNotificationApplySuccessLogin"
        className="popup-notification fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-[rgba(51,51,51,.3)] p-3 transition-all"
      >
        <div className="relative w-full max-w-[723px] overflow-hidden rounded-[10px] bg-white shadow-lg">
          {/* header DialogModal */}
          <span
            onClick={() => handleClose()}
            className="absolute right-[10px] top-[10px] z-10 inline-block h-8 w-8 cursor-pointer rounded text-center leading-8 hover:bg-primary hover:text-white"
          >
            <IoMdClose className="inline-block h-5 w-5" />
          </span>
          {/* End header DialogModal */}
          <div className="flex flex-col items-center gap-2 bg-gradient-notification-apply pt-6 text-center md:-mx-[131px] md:w-[985px]">
            <h3 className="font-bold text-primary md:text-4xl">
              {t("notification_title_popup_success_login")}
            </h3>
            <p className="text-sm text-gray-600 md:text-xl">
              {t("notification_desc_popup_success_login")}
            </p>
            <div className="text-center">
              <Image
                className="md:max-w-auto max-w-[542px]"
                src={BannerNotification}
                alt="banner notification"
              />
            </div>
          </div>
          <div className="p-3 md:p-6">
            <div className="flex flex-col items-start justify-end gap-3 md:flex-row md:gap-8">
              <div className="align-self-stretch flex w-full flex-col items-start justify-between gap-2 sm:min-h-[360px] md:gap-4 md:px-4 md:py-6">
                <div className="flex w-full flex-col items-start gap-2 md:gap-4">
                  <div className="flex items-center justify-center gap-4">
                    <Image src={IconExplore} alt="Icon Explore" />
                    <h3 className="font-bold text-gray-600 md:text-lg">
                      {t("notification_title_explore_more_popup_success_login")}
                    </h3>
                  </div>
                  <div className="flex items-start gap-2 text-sm text-gray-500 md:min-h-[40px] md:text-base">
                    <Image
                      src={
                        "/v4/assets/images/apply_job/icon-double-checked.png"
                      }
                      alt="Icon Explore"
                      width={22}
                      height={13}
                      className="mt-1"
                    />{" "}
                    {t("notification_info_explore_more_popup_success_login_1")}
                  </div>
                  <div className="flex items-start gap-2 text-sm text-gray-500 md:min-h-[40px] md:text-base">
                    <Image
                      src={
                        "/v4/assets/images/apply_job/icon-double-checked.png"
                      }
                      alt="Icon Explore"
                      width={22}
                      height={13}
                      className="mt-1"
                    />{" "}
                    {t("notification_info_explore_more_popup_success_login_2")}
                  </div>
                </div>
                <button
                  onClick={() => (window.location.href = "/it-jobs")}
                  className="inline-flex h-12 w-full items-center justify-center gap-1 rounded border border-solid border-primary bg-transparent px-4 font-semibold text-primary transition-all hover:bg-primary-100 disabled:cursor-not-allowed dark:border-white dark:text-white lg:h-14 lg:gap-3 lg:px-8"
                >
                  {t("notification_button_explore_more")}
                </button>
              </div>
              <div className="align-self-stretch flex w-full flex-col items-start justify-between gap-2 rounded-xl bg-gray-100 p-6 sm:min-h-[360px] md:gap-4">
                <div className="flex items-center justify-center gap-4">
                  <Image
                    src={IconSuccessNotification}
                    alt="Icon Success Notification"
                  />
                  <h3 className="font-bold text-gray-600 md:text-lg">
                    {t("notification_title_constitutive_popup_success_login")}
                  </h3>
                </div>
                <div className="flex items-start gap-2 text-sm text-gray-500 md:text-base">
                  <Image
                    src={"/v4/assets/images/apply_job/icon-double-checked.png"}
                    alt="Icon Explore"
                    width={22}
                    height={13}
                    className="mt-1"
                  />
                  {t("notification_info_constitutive_popup_success_login_1")}
                </div>
                <div className="flex items-start gap-2 text-sm text-gray-500 md:text-base">
                  <Image
                    src={"/v4/assets/images/apply_job/icon-double-checked.png"}
                    alt="Icon Explore"
                    width={22}
                    height={13}
                    className="mt-1"
                  />
                  {t("notification_info_constitutive_popup_success_login_2")}
                </div>

                <button
                  className="inline-flex h-12 w-full items-center justify-center gap-1 rounded border border-solid border-primary bg-primary px-4 text-sm font-semibold text-white transition-all hover:border-primary-400 hover:bg-primary-400 disabled:cursor-not-allowed disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 lg:h-14 lg:gap-3 lg:px-8 lg:text-base"
                  onClick={() =>
                    (window.location.href = "/users/job-management")
                  }
                >
                  {t("notification_button_constitutive")}
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
export default TheNotificationApplySuccessLogin;
