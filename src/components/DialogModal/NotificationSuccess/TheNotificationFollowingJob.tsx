import Image from "next/image";
import { useTranslations } from "next-intl";
import { useAppSelector } from "@/store";
import { FC } from "react";
import { IoMdClose } from "react-icons/io";

const TheNotificationFollowingJob: FC<{
  handleClose(): void;
}> = ({ handleClose }) => {
  const t = useTranslations();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);

  const openExploreMore = () => {
    if (isLoggedIn) {
      //with login
      document.location.href =
        document.location.origin + "/" + t("slug_it_jobs");
      return;
    }

    return;
  };

  return (
    <>
      <section
        id="Apply_CV_Success"
        data-cy="TheNotificationFollowingJob"
        className="popup-notification fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-[rgba(51,51,51,.3)] p-3 transition-all"
      >
        <div className="w-full max-w-[562px] rounded bg-white">
          {/* header DialogModal */}
          <div className="headerDialog p-2 text-right">
            <span
              onClick={() => handleClose()}
              className="-mt-[9px] inline-block h-8 w-8 cursor-pointer text-center leading-10"
            >
              <IoMdClose className="inline-block h-5 w-5" />
            </span>
          </div>
          {/* End header DialogModal */}
          <div className={"px-4 pb-4 md:px-9 md:pb-14"}>
            <div className="text-center">
              <Image
                width="335"
                height="135"
                src="https://cdn.topdev.vn/v4/assets/images/apply_job/layer-save-jobs.png"
                alt="Topdev"
                className="inline-block"
              />
            </div>
            <div className="content pt-6 text-center">
              <b className="mb-3 block text-2xl font-bold leading-5 text-primary">
                {t("detail_job_page_save_jobs_succesed")}
              </b>
              <h3 className="font-normal text-gray-500">
                {t("detail_job_page_content_job_information_following")}
                <a
                  className="transition-all duration-100 hover:text-primary"
                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/jobs-followed`}
                  title="Following Job"
                >
                  <b>
                    <u>Following Job</u>
                  </b>
                </a>
              </h3>
              <button
                onClick={openExploreMore}
                type="button"
                className="mt-6 min-w-[260px] rounded bg-primary px-9 py-5 text-white transition-all duration-100 hover:bg-primary-400"
              >
                {t("detail_job_page_explore_more")}
              </button>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default TheNotificationFollowingJob;
