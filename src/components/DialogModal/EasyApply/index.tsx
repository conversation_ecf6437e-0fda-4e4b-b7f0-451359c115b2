"use client";

import { useAppDispatch, useAppSelector } from "@/store";
import { JobType } from "@/types/job";
import { isMobile } from "react-device-detect";
import { TOKEN_DEVICE } from "@/utils/enums";
import { Tooltip } from "flowbite-react";
import { useTranslations } from "next-intl";
import { getFormData } from "@/utils";
import { applyJobsApi } from "@/services/jobAPI";
import Swal from "sweetalert2";
import ToastNotification from "@/components/Swal/ToastNotification";
import { pushAppliedJob, setNewestCandidate } from "@/store/slices/userSlice";
import { setActiveAppliedJob } from "@/store/slices/jobSlice";
import {
  setDataDialogNotification,
  setEventsState,
  showDialogNotification,
} from "@/store/slices/settingSlice";

import { useCallback, useEffect, useState } from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

const EasyApply = ({
  job,
  size,
}: {
  job: JobType;
  size?: "small" | "default";
}) => {
  const t = useTranslations();
  const user = useAppSelector((state) => state?.user?.user);
  const dispatch = useAppDispatch();
  const [countdown, setCountdown] = useState(3);
  const [isWaiting, setIsWaiting] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const appliedJobs = useAppSelector((state) => state.user.applied_jobs);
  const eventData = useAppSelector((state) => state?.setting?.eventData);
  const isJobApplied = useCallback(
    () => appliedJobs?.includes(job.id),
    [appliedJobs, job.id],
  );
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const newestCandidate = useAppSelector(
    (state) => state?.user?.newest_candidate,
  );
  const handleClearEvent = () => {
    setIsSubmitting(false);
    setIsWaiting(false);
  };

  const onClickEasyApplyBtn = () => {
    if (!isWaiting) {
      setIsWaiting(true);
    }
  };
  useEffect(() => {
    if (isWaiting) {
      const timeWaiting = setInterval(() => {
        setCountdown((currentCountdown) => {
          if (currentCountdown === 1) {
            // Khi countdown đạt 0, thực hiện apply job và dừng đếm ngược
            setIsSubmitting(true);
            handleApplyJob();
            setIsWaiting(false);
            clearInterval(timeWaiting);
            return currentCountdown; // Giữ nguyên giá trị countdown tại 0
          } else {
            return currentCountdown - 1; // Giảm countdown
          }
        });
      }, 1000);
      return () => {
        clearInterval(timeWaiting);
        return;
      };
    }
  }, [isWaiting]);
  const handleApplyJob = async () => {
    const url = new URL(window.location.href);
    const params = url.searchParams;
    let userProfileId = null;
    let mediaId = null;
    let cvBuilderId = null;
    let eventType = eventData?.type;
    let fileName = null;
    // Nope event Data, check newestCandidate
    if (eventType == undefined) {
      fileName = newestCandidate?.file_name;
      let collectionName =
        newestCandidate?.collection_name ?? "files_topdev_cv";
      if (collectionName == "files_cvbuilder") {
        cvBuilderId = newestCandidate?.cvbuilder_id;
      } else {
        mediaId = newestCandidate?.media_id;
      }
    } else {
      // has event Data
      fileName = eventData?.file_name;
      let eventType = eventData?.type;
      if (eventType == "cvbuilder") {
        cvBuilderId = eventData?.media_id;
      } else {
        mediaId = eventData?.media_id;
      }   
    }
    const SID =
      params.has("sid") &&
      (typeof params.get("sid") === "string"
        ? params.get("sid")
        : params.get("sid")?.[0]);

    const data: any = {
      display_name: user.display_name,
      file_name: fileName,
      email: user.email,
      tnc: null,
      cover_letter: "",
      phone: user.phone,
      job_id: job.id || null,
      files_cv: null,
      case: "ApplyLogined",
      media_id: mediaId,
      user_profile_id: userProfileId,
      cvbuilder_id: cvBuilderId,
      token_device: localStorage.getItem(TOKEN_DEVICE) ?? null,
      sid: SID ?? null,
      utm_source: params.has("utm_source") ? params.get("utm_source") : null,
      utm_medium: params.has("utm_medium") ? params.get("utm_medium") : null,
      utm_campaign: params.has("utm_campaign")
        ? params.get("utm_campaign")
        : null,
      upload_from: "UploadFromTopdev",
      source: params.has("source") ? params.get("source") : "ApplyNow",
      device_apply: isMobile ? "MobileWeb" : "PC",
      query_src: params.has("src") ? params.get("src") : null,
      query_medium: params.has("medium") ? params.get("medium") : null,
      easy_apply: 1, // flag to easy apply (apply 1 click)
    };

    const formData = getFormData(data);
    try {
      const { data } = await applyJobsApi(formData);

      if (data.message === "applied" && data.success === false) {
        Swal.fire({
          title: t("common_error"),
          text: t("detail_job_page_apply_you_have_applied_for_job"),
          icon: "error",
          confirmButtonColor: "#DD3F24",
        });
        handleClearEvent();
        return;
      }

      if (!data.success) {
        ToastNotification({
          icon: "error",
          title: t("common_error"),
          description: data.message,
        });

        handleClearEvent();
        return;
      }

      const comebackUrl = data?.data?.comeback_url;

      // Run when not suggest job or is not login and show apply to all
      if (!!data.data && !data.data.events?.includes("EventSuggestJobs")) {
        //Show notification for dialog
        dispatch(showDialogNotification(true));
      }

      //Show data notification for dialog
      dispatch(
        setDataDialogNotification({
          email: user.email,
          status: data.success,
          comebackUrl: comebackUrl,
          theme: "theme_noti_applay",
          event: !!data.data ? data.data.events : [],
        }),
      );

      //Show data job for job applied
      dispatch(
        setActiveAppliedJob({
          jobId: job.id,
          status: true,
        }),
      );

      /**
       * Push current job to applied jobs of current user
       */
      dispatch(pushAppliedJob(job.id));
      handleClearEvent();

      //Show event notification
      if (isLoggedIn && !!data.data) {
        setTimeout(() => {
          dispatch(setEventsState(data.data));
        }, 3000);
      }
    } catch (error) {
      ToastNotification({
        icon: "error",
        title: t("common_sorry"),
        description: t("detail_job_page_apply_notification_apply_failed"),
      });
      handleClearEvent();
      console.error(error);
    }
  };

  const handleCancelEvent = () => {
    if (isWaiting && !isSubmitting) {
      setCountdown(3);
      setIsWaiting(false);
    }
    return;
  };

  //style button
  let styleSubmitApply = size === "small" ? "lg:h-9 h-9" : "lg:h-14 h-14";

  if (isJobApplied()) {
    return (
      <button
        className={`h-11 w-full rounded border-gray-200 bg-gray-200 font-semibold text-gray-600 hover:border-gray-300 hover:bg-gray-300 dark:border-white dark:text-white lg:h-14 ${styleSubmitApply}`}
      >
        {t("detail_job_page_applied")}
      </button>
    );
  }

  if (isWaiting || isSubmitting) {
    return (
      <button
        onClick={() => handleCancelEvent()}
        id="cancelEasyApplyCvBtn"
        className={`h-11 w-full rounded border border-neutral-700 bg-white  font-semibold text-neutral-700 hover:border-gray-300 hover:bg-gray-300 dark:border-white dark:text-white lg:h-14 ${styleSubmitApply}`}
      >
        <AiOutlineLoading3Quarters className="mr-2 mt-0 inline-block h-4 w-4 animate-spin" />
        {t("detail_job_easy_apply_button_cancel_application")}
        <span className="ml-2">{countdown > 0 ? `(${countdown}s)` : ""}</span>
      </button>
    );
  }

  return (
    <div className="flex w-full">
      <Tooltip
        content={t("detail_job_easy_apply_button_tooltip")}
        theme={{
          target: "w-full",
          style: {
            dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
          },
        }}
      >
        <button
          onClick={() => onClickEasyApplyBtn()}
          id="easyApplyCvBtn"
          className={`hover:border-primary-700 hover:bg-primary-700 w-full rounded border-primary bg-primary font-semibold text-white disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 ${styleSubmitApply}`}
        >
          {t("detail_job_easy_apply_button")}
        </button>
      </Tooltip>
    </div>
  );
};

export default EasyApply;
