"use client";

import dayjs from "dayjs";
import Image from "next/image";
import { Tooltip } from "flowbite-react";
import { UserResumes } from "@/types/resume";
import { classNames, getFileCvUploadPreviewUrl } from "@/utils";
import { <PERSON><PERSON><PERSON>, HiOutlineEye, HiPencilSquare } from "react-icons/hi2";
import Link from "next/link";
import Profile from "@/types/profile";
import BagdeStatus from "@/components/User/Common/BagdeStatus";
import "@/assets/styles/pages/profile.scss";
import { JobType } from "@/types/job";
import { useRouter, useSearchParams } from "next/navigation";
import { downloadUserProfileCv, getUserProfile } from "@/services/userAPI";
import { useEffect, useLayoutEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { HiChevronRight } from "react-icons/hi";
import { useAppSelector } from "@/store";
import { isMobile } from "react-device-detect";
import BagePercent from "@/components/User/Common/BagePercent";
interface ListResumesApplyJobProps {
  resumes: Array<UserResumes>;
  userProfile?: Profile;
  fileCV: any;
  transition: any;
  valueCheck: any;
  isLoggedIn: boolean;
  job: JobType;
  isShowApplyJob?: boolean;
  handleClearEvent: () => void;
  onChangeResume: (resumeId: any) => void;
}

const ListResumesApplyJobDetail = ({
  resumes,
  fileCV,
  transition,
  isLoggedIn,
  userProfile,
  job,
  handleClearEvent,
  onChangeResume,
}: ListResumesApplyJobProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params: string = searchParams.toString();
  const paramJob = new URLSearchParams(params);
  const jobTitle = paramJob.get("job_title")
    ? paramJob.get("job_title")
    : job?.title;
  const jobUrl = paramJob.get("job_url")
    ? paramJob.get("job_url")
    : job?.detail_url;
  const jobCompany = paramJob.get("job_company")
    ? paramJob.get("job_company")
    : job?.company?.display_name;
  const jobId = paramJob.get("job_id") ? paramJob.get("job_id") : job?.id;
  const [isDownloading, setIsDownloading] = useState(false);
  const [isJustApply, setIsJustApply] = useState("files_topdev_cv");
  const [processedResumes, setProcessedResumes] = useState(Array<UserResumes>);
  const t = useTranslations();
  const handleDownloadClick = async () => {
    if (isDownloading) return;

    setIsDownloading(true);
    getUserProfile()
      .then((response) => {
        downloadUserProfileCv(response.data, true).finally(() =>
          setIsDownloading(false),
        );
      })
      .catch(() => setIsDownloading(false));
  };
  const navigateToProfile = (action: string) => {
    let string = `&isShowBasicInfoModal=1`;
    if (action === "edit") {
      string = "";
    }
    router.push(
      `/users/profile?topdev_detailjob&medium=popupapply&job_title=${encodeURIComponent(
        jobTitle as string,
      )}&job_url=${jobUrl}&job_company=${jobCompany}&job_id=${jobId}${string}`,
    );
    handleClearEvent();
  };
  const newestCandidate = useAppSelector(
    (state) => state?.user?.newest_candidate,
  );
  // Maxlength Show Name
  const maxLength = isMobile ? 20 : 35;
  const checkAndUpdateResumes = () => {
    let index = -1;
    setIsJustApply("files_topdev_cv");
    // Create a copy of the resumes array
    const resumesCopy = [...resumes];

    // Move the element with type 'upload' to the beginning if it exists
    const uploadIndex = resumesCopy.findIndex(
      (resume) => resume.type === "upload",
    );
    if (uploadIndex > -1) {
      const [uploadResume] = resumesCopy.splice(uploadIndex, 1);
      resumesCopy.unshift(uploadResume);
    }

    // Move the element with the matching id or type to the correct position if needed
    if (newestCandidate?.collection_name !== "files_topdev_cv") {
      const mediaId = newestCandidate?.media_id;
      const builderId = newestCandidate?.cvbuilder_id;

      // Determine the index based on the collection_name
      index = resumesCopy.findIndex((resume) =>
        newestCandidate?.collection_name == "files_cvbuilder"
          ? resume?.id == builderId
          : resume?.id == mediaId,
      );
      if (index > -1) {
        // Set the appropriate flag based on the collection_name
        setIsJustApply(
          newestCandidate?.collection_name == "files_cvbuilder"
            ? "cvbuilder"
            : "media",
        );

        // Remove the matching resume and insert it at the correct position
        const [matchingResume] = resumesCopy.splice(index, 1);
        const position = uploadIndex > -1 ? 1 : 0;
        resumesCopy.splice(position, 0, matchingResume);
      }
    }

    // Check if 'files_topdev_cv' exists in resumesCopy
    const hasFilesTopdevCv = resumesCopy.some(
      (resume) => resume.type === "files_topdev_cv",
    );
    const hasUpload = resumesCopy.some((resume) => resume.type === "upload");

    // Add newResume if 'files_topdev_cv' does not exist and userProfile is available
    if (!hasFilesTopdevCv && userProfile) {
      const newResume = {
        id: userProfile.id,
        type: "files_topdev_cv",
        created_at: userProfile.updated_times,
        last: newestCandidate?.collection_name === "files_topdev_cv" || false,
      };

      // Determine position for newResume
      let position = 0;

      const isUpload = hasUpload;
      const isLast = newResume.last;
      const isIndexValid = index > -1;

      if (isUpload) {
        // If there's an upload and it's the last resume, position is 1
        // If there's an upload but index is not found, position is 1
        // Otherwise, position is 2
        position = isLast || !isIndexValid ? 1 : 2;
      } else {
        // If there's no upload and it's the last resume, position is 0
        // If there's no upload but index is not found, position is 0
        // Otherwise, position is 1
        position = isLast || !isIndexValid ? 0 : 1;
      }
      resumesCopy.splice(position, 0, newResume);
    }
    setProcessedResumes(resumesCopy);
  };

  useEffect(() => {
    checkAndUpdateResumes();
  }, [resumes, userProfile, newestCandidate]);

  useEffect(() => {
    checkAndUpdateResumes();
  }, []);
  //User Profile CV
  if (isLoggedIn) {
    return (
      <>
        <div
          className={classNames(
            userProfile && processedResumes?.length >= 5 ? "" : "",
            "scroll-bar-list-user-cv h-auto max-h-[318px] overflow-y-auto rounded bg-neutral-light px-4 pt-3 md:h-auto md:max-h-[250px]",
          )}
        >
          {processedResumes &&
            userProfile &&
            processedResumes?.length >= 0 &&
            processedResumes.map((resume, index) => {
              if (resume.type === "files_topdev_cv") {
                return (
                  <div
                    key={index}
                    className={`rounded ${
                      index === 0 && userProfile.complete_percent === 100
                        ? "border border-brand-300"
                        : ""
                    } div-resumes data-${
                      resume.id
                    } max-h-auto min-h-auto relative mb-3 flex h-auto w-full cursor-pointer flex-wrap last:pb-0 md:flex md:w-full md:flex-col md:flex-nowrap ${
                      (userProfile?.complete_percent ?? 0) < 100
                        ? "bg-neutral-100"
                        : "bg-white"
                    }`}
                  >
                    <div className="relative flex flex-row px-2 py-4 rounded md:h-auto md:w-full">
                      <div className="pt-1">
                        <input
                          defaultChecked={
                            index === 0 && userProfile.complete_percent === 100
                          }
                          data-type={"files_topdev_cv"}
                          type="radio"
                          name="resume"
                          disabled={(userProfile.complete_percent ?? 0) < 100}
                          id={`resume-${userProfile?.id}`}
                          value={userProfile.id as number}
                          onClick={(e) => onChangeResume(resume.id)}
                          className={`${
                            (userProfile.complete_percent ?? 0) < 100
                              ? "bg-neutral-100"
                              : ""
                          } border border-neutral-300 absolute h-4 w-4 cursor-pointer rounded-full text-brand-600 focus:ring-transparent`}
                        />
                      </div>
                      <label
                        className="relative left-0 flex flex-col flex-wrap w-full h-auto ml-6 cursor-pointer"
                        htmlFor={`resume-${userProfile?.id}`}
                      >
                        <div className="flex flex-row flex-wrap w-full text-sm rounded">
                          <div className="flex gap-0 md:flex-row md:items-center">
                            <span className="mr-1 min-w-[93px] max-w-[117px] flex-shrink-0 font-bold text-neutral-950">
                              {t("detail_job_my_topdev_cv")}
                            </span>
                            <span className="w-full md:w-auto">
                              <BagePercent
                                complete_percent={
                                  userProfile?.complete_percent ?? 0
                                }
                              />
                            </span>
                          </div>
                          {isJustApply === "files_topdev_cv" &&
                          userProfile.complete_percent === 100 &&
                          resume.last ? (
                            <div className="my-1 max-w-[150px] rounded bg-blue-50 px-2 py-0.5 md:my-0 md:ml-1 md:w-auto">
                              <span className="text-xs font-normal text-blue-700">
                                {transition(
                                  "detail_job_cv_most_recent_job_application",
                                )}
                              </span>
                            </div>
                          ) : (
                            ""
                          )}
                          <div className="flex flex-row ml-0 md:absolute md:right-14 md:ml-40">
                            <span className="flex-wrap mb-2 ml-1 text-xs text-neutral-400 md:ml-8">
                              {t("detail_job_choose_last_update")}{" "}
                              {userProfile?.updated_at}
                            </span>
                          </div>
                        </div>
                        <div className="w-full rounded md:justify-between">
                          <div className="mt-1 w-auto rounded-lg text-sm text-neutral-400 md:flex md:max-w-[698px]">
                            {(userProfile.complete_percent ?? 0) < 100 ? (
                              <Link
                                href="/users/profile?src=topdev_detailjob&medium=popupapply"
                                onClick={(e) => e.preventDefault()}
                                className="text-sm font-normal text-neutra-400"
                              >
                                <span onClick={() => navigateToProfile("add")}>
                                  {t.rich(
                                    "detail_job_inprove_inteview_chances",
                                    {
                                      strong: (chunk) => (
                                        <strong className="inline-flex items-center text-sm underline text-neutral-950">
                                          {chunk}
                                          <span>
                                            <HiChevronRight
                                              width={20}
                                              height={20}
                                              className="w-5 h-5 text-sm"
                                            />
                                          </span>
                                        </strong>
                                      ),
                                    },
                                  )}
                                </span>
                              </Link>
                            ) : (
                              <>
                                <div>
                                  <span className="text-sm font-normal text-neutra-400">
                                    {t(
                                      "detail_job_inprove_inteview_chances_standand",
                                    )}
                                  </span>
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      </label>
                      <div className="absolute flex gap-2 right-2 md:right-2">
                        <Tooltip
                          content={transition("detail_job_cv_type_action_edit")}
                          className="fade-in"
                          theme={{
                            style: {
                              dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                            },
                          }}
                        >
                          <Link
                            href={
                              "/users/profile?src=topdev_detailjob&medium=popupapply"
                            }
                            onClick={(e) => e.preventDefault()}
                          >
                            <HiPencilSquare
                              onClick={() => navigateToProfile("edit")}
                              className="ml-2 h-[18px] w-[18px] cursor-pointer text-neutral-400"
                              width={18}
                              height={18}
                            />
                          </Link>
                        </Tooltip>
                        <Tooltip
                          content={transition(
                            "detail_job_page_apply_popover_preview_cv",
                          )}
                          className="fade-in"
                          theme={{
                            style: {
                              dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                            },
                          }}
                        >
                          {isDownloading ? (
                            <AiOutlineLoading3Quarters className="text-brand h-[18px] w-[18px] animate-spin text-brand-600" />
                          ) : (
                            <HiEye
                              onClick={handleDownloadClick}
                              title={t("detail_job_cv_type_action_view")}
                              className="h-[18px] w-[18px] cursor-pointer text-neutral-400"
                            />
                          )}
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                );
              } else if (resume.type == "cvbuilder" || resume.type == "media") {
                return (
                  <div
                    key={index}
                    className={`item-resume rounded ${
                      index === 0 ? "border border-brand-300" : ""
                    } div-resumes data-${
                      resume.id
                    } cvbuilder_media mb-3 cursor-pointer flex-wrap bg-white px-2 last:pb-0 md:relative`}
                  >
                    <input
                      type="radio"
                      name="resume"
                      id={`resume-${resume?.id}`}
                      value={resume.id as string | number | undefined}
                      className="hidden"
                      defaultChecked={index === 0}
                      onClick={(e) => onChangeResume(resume.id)}
                      data-type={resume.type}
                      data-name={resume.name}
                    />
                    <label
                      className="relative flex flex-wrap justify-end py-2 pl-6 cursor-pointer"
                      htmlFor={`resume-${resume?.id}`}
                    >
                      <div className="flex flex-col flex-wrap w-full px-0 text-sm bg-white rounded md:flex-row">
                        <div className="flex flex-wrap items-center justify-start flex-grow truncate w-60 md:w-40">
                          <div className="flex flex-col md:flex-row md:items-center">
                            <div className="flex-shrink min-w-0 overflow-hidden text-ellipsis whitespace-nowrap">
                              {resume?.name && resume?.name.length > maxLength
                                ? `${resume?.name.slice(0, maxLength)}...`
                                : resume?.name}
                            </div>
                            {(isJustApply === "media" &&
                              resume?.id == newestCandidate?.media_id) ||
                            (resume?.id == newestCandidate?.cvbuilder_id &&
                              isJustApply == "cvbuilder") ? (
                              <div className="my-1 max-w-[150px] rounded bg-blue-50 px-2 py-0.5 md:my-0 md:ml-1 md:w-auto">
                                <span className="text-xs font-normal text-blue-700">
                                  {transition(
                                    "detail_job_cv_most_recent_job_application",
                                  )}
                                </span>
                              </div>
                            ) : (
                              ""
                            )}
                          </div>
                        </div>
                        <div className="flex flex-wrap items-center w-auto mr-0">
                          <span className="mr-2 text-xs font-semibold text-neutral-400">
                            {resume.type == "cvbuilder"
                              ? transition("detail_job_cv_type_convert")
                              : transition(
                                  "detail_job_page_apply_fields_upload_at",
                                )}
                          </span>
                          <span className="text-xs text-neutral-400 md:mr-8">
                            {dayjs
                              .unix(resume.created_at as number)
                              .format("HH:mm DD/MM/YYYY")}
                          </span>
                          <div className="md:absolute md:right-0">
                            <Tooltip
                              content={transition(
                                "detail_job_page_apply_popover_preview_cv",
                              )}
                              theme={{
                                style: {
                                  dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                                },
                              }}
                            >
                              {resume.type == "cvbuilder" ? (
                                <a
                                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/view-cv/${resume.id}`}
                                  className="group/tooltip absolute right-2 top-[8px] ml-[40px] h-[12px] w-[16px] md:relative md:right-0 md:top-0 md:ml-0"
                                  target="_blank"
                                >
                                  <HiEye
                                    title={t("detail_job_cv_type_action_view")}
                                    className="ml-2 h-[18px] w-[18px] cursor-pointer text-neutral-400"
                                    width={18}
                                    height={18}
                                  />
                                </a>
                              ) : (
                                <>
                                  {!!resume &&
                                    resume?.assets &&
                                    resume?.assets.length > 0 && (
                                      <Link
                                        href={resume?.assets?.[0]?.download_url}
                                        className="group/tooltip absolute right-2 top-[8px] ml-[40px] h-[12px] w-[16px] md:relative md:right-0 md:top-0 md:ml-0"
                                        target="_blank"
                                      >
                                        <HiEye
                                          title={t(
                                            "detail_job_cv_type_action_view",
                                          )}
                                          className="ml-2 h-[18px] w-[18px] cursor-pointer text-neutral-400"
                                          width={18}
                                          height={18}
                                        />
                                      </Link>
                                    )}
                                </>
                              )}
                            </Tooltip>
                          </div>
                        </div>
                      </div>
                    </label>
                  </div>
                );
              } else if (resume.type == "upload") {
                return (
                  <div
                    key={
                      newestCandidate?.collection_name === "files_topdev_cv"
                        ? index
                        : -1
                    }
                    className={`item-resume rounded ${
                      index === 0 ? "border border-brand-300" : ""
                    } div-resumes data-${
                      resume.id
                    }  cvbuilder_media mb-3 cursor-pointer flex-wrap bg-white px-2 last:pb-0 md:relative`}
                  >
                    <input
                      type="radio"
                      name="resume"
                      id={`resume-${index}`}
                      value={resume.id as number}
                      className="hidden"
                      onClick={(e) => onChangeResume(0)}
                      defaultChecked={index === 0}
                      data-type={"upload"}
                      data-name={resume.name}
                    />
                    <label
                      className="relative flex flex-wrap justify-end py-2 pl-6 cursor-pointer"
                      htmlFor={`resume-${index}`}
                    >
                      <div className="flex flex-col flex-wrap w-full px-0 text-sm bg-white rounded md:flex-row">
                        <div className="flex flex-wrap items-center justify-start flex-grow truncate w-60 md:w-40">
                          <div className="flex flex-col md:flex-row md:items-center">
                            <div className="flex-shrink min-w-0 overflow-hidden text-ellipsis whitespace-nowrap">
                              {resume?.name && resume?.name.length > maxLength
                                ? `${resume?.name.slice(0, maxLength)}...`
                                : resume?.name}
                            </div>
                            <div className="my-1 max-w-[105px] rounded bg-blue-50 px-2 py-0.5 md:my-0 md:ml-1 md:w-auto">
                              <span className="text-xs font-normal text-blue-700">
                                {transition("detail_job_just_uploaded")}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-wrap items-center w-auto mr-0">
                          <span className="mr-2 text-xs font-semibold text-neutral-400">
                            {transition(
                              "detail_job_page_apply_fields_upload_at",
                            )}
                          </span>
                          <span className="text-xs text-neutral-400 md:mr-8">
                            {resume.created_at}
                          </span>
                          <div className="md:absolute md:right-0">
                            <Tooltip
                              content={transition(
                                "detail_job_page_apply_popover_preview_cv",
                              )}
                              theme={{
                                style: {
                                  dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                                },
                              }}
                            >
                              <a
                                href={getFileCvUploadPreviewUrl(fileCV)}
                                className="group/tooltip absolute right-2 top-[8px] ml-[40px] h-[12px] w-[16px] md:relative md:right-0 md:top-0 md:ml-0"
                                target="_blank"
                              >
                                <HiEye
                                  title={t("detail_job_cv_type_action_view")}
                                  className="ml-2 h-[18px] w-[18px] cursor-pointer text-neutral-400"
                                  width={18}
                                  height={18}
                                />
                              </a>
                            </Tooltip>
                          </div>
                        </div>
                      </div>
                    </label>
                  </div>
                );
              }
            })}
        </div>
      </>
    );
  }
};

export default ListResumesApplyJobDetail;
