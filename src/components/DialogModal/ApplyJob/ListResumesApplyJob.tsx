"use client";

import dayjs from "dayjs";
import Image from "next/image";
import { Tooltip } from "flowbite-react";
import { UserResumes } from "@/types/resume";
import { classNames, getFileCvUploadPreviewUrl } from "@/utils";
import { useEffect } from "react";

interface ListResumesApplyJobProps {
  resumes: Array<UserResumes>;
  fileCV: any;
  transition: any;
  valueCheck: any;
  isLoggedIn: boolean;
  onChangeResume: (resumeId: any) => void;
  layoutType?: string;
}

const ListResumesApplyJob = ({
  resumes,
  fileCV,
  transition,
  valueCheck,
  isLoggedIn,
  onChangeResume,
  layoutType = 'apply_job'
}: ListResumesApplyJobProps) => {
  const isConvertCV = layoutType === "convert_cv"

  useEffect(() => {
    if (resumes?.length > 0 && resumes[0].type == "media" && isConvertCV && isLoggedIn) {
      onChangeResume(resumes[0].id);
    }
  }, [isConvertCV, isLoggedIn, onChangeResume, resumes]);

  if (isLoggedIn) {
    return (
      <div
        className={classNames(
          resumes?.length >= 5 ? "pr-3" : "",
          "scrollbar-primary max-h-[120px] overflow-y-auto",
        )}
      >
        {resumes &&
          resumes?.length > 0 &&
          resumes.map((resume, index) => {
            if (resume.type == "cvbuilder" || resume.type == "media") {
              return (
                <div
                  key={index}
                  className={classNames(
                    isConvertCV ? "pb-[10px] pl-2" : "pb-[5px]",
                    "item-resume cursor-pointer last:pb-0",
                  )}
                >
                  <input
                    type="radio"
                    name="resume"
                    id={`resume-${resume?.id}`}
                    value={resume.id as number}
                    className="hidden"
                    defaultChecked={resume?.id == valueCheck?.id}
                    onChange={(e) => onChangeResume(e.target.value)}
                  />
                  <label
                    className="relative flex cursor-pointer items-center justify-between pl-6"
                    htmlFor={`resume-${resume?.id}`}
                  >
                    <div className="max-w-[490px] flex-1 text-sm">
                      {`${resume?.name} ${
                        resume.type == "cvbuilder"
                          ? transition(
                              "detail_job_page_apply_fields_created_at",
                            ) + " TopDev CV"
                          : transition("detail_job_page_apply_fields_upload_at")
                      } ${dayjs
                        .unix(resume.created_at as number)
                        .format("HH:mm DD/MM/YYYY")}`}
                    </div>
                    <Tooltip
                      content={transition(
                        "detail_job_page_apply_popover_preview_cv",
                      )}
                      theme={{
                        style: {
                          dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                        },
                      }}
                    >
                      {resume.type == "cvbuilder" ? (
                        <a
                          href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/view-cv/${resume.id}`}
                          className="group/tooltip relative inline-block"
                          target="_blank"
                        >
                          <Image
                            src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-eye.png"
                            width="16"
                            height="12"
                            alt="Topdev"
                            className="ml-2"
                          />
                        </a>
                      ) : (
                        <>
                          {!!resume &&
                            resume?.assets &&
                            resume?.assets.length > 0 && (
                              <a
                                href={resume?.assets?.[0]?.download_url}
                                className="group/tooltip relative ml-2 inline-block h-[12px] w-[16px]"
                                target="_blank"
                              >
                                <Image
                                  src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-eye.png"
                                  width="16"
                                  height="12"
                                  alt="Topdev"
                                />
                              </a>
                            )}
                        </>
                      )}
                    </Tooltip>
                  </label>
                </div>
              );
            } else if (resume.type == "upload") {
              return (
                <div
                  key={index}
                  className={classNames(
                    isConvertCV ? "pb-[10px] pl-2" : "pb-[5px]",
                    "item-resume cursor-pointer last:pb-0",
                  )}
                >
                  <input
                    type="radio"
                    name="resume"
                    id={`resume-${resume?.id}`}
                    value={resume.id as number}
                    className="hidden"
                    defaultChecked={resume.id == valueCheck.id}
                    onChange={(e) => onChangeResume(e.target.value)}
                  />
                  <label
                    className="relative flex cursor-pointer items-center justify-between pl-6"
                    htmlFor={`resume-${resume?.id}`}
                  >
                    <div className="max-w-[490px] flex-1 text-sm">
                      {`${resume?.name} ${transition(
                        "detail_job_page_apply_fields_upload_at",
                      )} ${resume.created_at}`}
                    </div>
                    <Tooltip
                      content={transition(
                        "detail_job_page_apply_popover_preview_cv",
                      )}
                      theme={{
                        style: {
                          dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                        },
                      }}
                    >
                      <a
                        href={getFileCvUploadPreviewUrl(fileCV)}
                        className="group/tooltip relative inline-block"
                        target="_blank"
                      >
                        <Image
                          src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-eye.png"
                          width="16"
                          height="12"
                          alt="Topdev"
                          className="ml-2"
                        />
                      </a>
                    </Tooltip>
                  </label>
                </div>
              );
            } else {
              return (
                <div
                  key={index}
                  className={classNames(
                    isConvertCV ? "pb-[10px] pl-2" : "pb-[5px]",
                    "item-resume cursor-pointer last:pb-0",
                  )}
                >
                  <input
                    type="radio"
                    name="resume"
                    id={`resume-${resume?.id}`}
                    value={resume.id as number}
                    className="hidden"
                    onChange={(e) => onChangeResume(e.target.value)}
                  />
                  <label
                    className="relative flex cursor-pointer items-center justify-between"
                    htmlFor={`resume-${resume?.id}`}
                  >
                    <div className="max-w-[490px] flex-1 text-sm">
                      {`${resume?.name} ${transition(
                        "detail_job_page_apply_fields_created_at",
                      )} ${resume.created_at}`}
                    </div>
                  </label>
                </div>
              );
            }
          })}
      </div>
    );
  }

  return (
    <div className="resumes">
      {resumes &&
        resumes?.length > 0 &&
        resumes.map((resume, index) => {
          return (
            <div key={index}>
              {resume.type == "upload" ? (
                <>
                  <input
                    type="radio"
                    name="resume"
                    id={`resume-${resume?.id}`}
                    value={resume as any}
                    className="hidden"
                  />
                  <label
                    className="flex cursor-pointer items-center justify-between"
                    htmlFor={`resume-${resume?.id}`}
                  >
                    <div className="flex items-center">
                      <Image
                        width="21"
                        height="25"
                        className="mr-3"
                        alt="topdev"
                        src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-pdf.png"
                      />
                      <span className="line-clamp-1 max-w-[535px] text-sm">
                        {resume?.name}
                      </span>
                    </div>
                    <Tooltip
                      content={transition(
                        "detail_job_page_apply_popover_preview_cv",
                      )}
                      theme={{
                        style: {
                          dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                        },
                      }}
                    >
                      <a
                        href={getFileCvUploadPreviewUrl(fileCV)}
                        className="group/tooltip relative inline-block"
                        target="_blank"
                      >
                        <Image
                          src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-eye.png"
                          width="16"
                          height="12"
                          alt="Topdev"
                          className="ml-2"
                        />
                      </a>
                    </Tooltip>
                  </label>
                </>
              ) : (
                <>
                  <input
                    type="radio"
                    name="resume"
                    id={`resume-${resume?.id}`}
                    value={resume as any}
                    className="hidden"
                  />
                  <label
                    className="flex cursor-pointer items-center justify-between"
                    htmlFor={`resume-${resume?.id}`}
                  >
                    <div className="flex flex-wrap items-center">
                      <Image
                        width="21"
                        height="25"
                        className="mr-3"
                        alt="topdev"
                        src="https://cdn.topdev.vn/v4/assets/images/apply_job/icon-pdf.png"
                      />
                      <span className="line-clamp-1 max-w-[535px] text-sm">
                        {resume?.name}
                      </span>
                    </div>
                  </label>
                </>
              )}
            </div>
          );
        })}
    </div>
  );
};

export default ListResumesApplyJob;
