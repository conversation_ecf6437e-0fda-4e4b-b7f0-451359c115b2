"use client";

import "simplebar-react/dist/simplebar.min.css";
import Swal from "sweetalert2";
import dynamic from "next/dynamic";
import dayjs from "dayjs";
import { ChangeEvent, useEffect, useState } from "react";
import { isDesktop, isMobile } from "react-device-detect";
import { useFormik } from "formik";
import { useTranslations } from "next-intl";
import { IoMdClose } from "react-icons/io";
import { VscLoading } from "react-icons/vsc";
import { FaExclamationCircle, FaCloudUploadAlt } from "react-icons/fa";
import { usePathname, useSearchParams } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/store";
import { HiMiniExclamationCircle } from "react-icons/hi2";
import {
  setDataDialogNotification,
  setIsShowApplyJob,
} from "@/store/slices/settingSlice";
import {
  pushAppliedJob,
  setProfile,
  setResumesUser,
} from "@/store/slices/userSlice";
import { setActiveAppliedJob } from "@/store/slices/jobSlice";
import {
  setEventsState,
  showDialogNotification,
} from "@/store/slices/settingSlice";
import { classNames, getFormData, gtag } from "@/utils";
import { fetchResumesApi } from "@/services/resumeAPI";
import { applyJobsApi } from "@/services/jobAPI";
import { tracking } from "@/services/activity";
import { UserResumes } from "@/types/resume";
import { schemaFormApply } from "@/schemas";
import { TOKEN_DEVICE } from "@/utils/enums";
import openLoginPopup from "@/utils/openLoginPopup";
import { getCookie, setCookie } from "@/utils/cookies";
import ToastNotification from "../../Swal/ToastNotification";
import TextEditor from "../../TextEditor";
import { JobType } from "@/types/job";
import useUserProfile from "@/hooks/useUserProfile";
import { getUserProfile } from "@/services/userAPI";
import Profile from "@/types/profile";
import ListResumesApplyJobDetail from "@/components/DialogModal/ApplyJob/ListResumesApplyJobDetail";
//Component button
const Button = dynamic(() => import("@/components/Button/Button"));
//End Component button

interface FormDataProps {
  display_name: string;
  email: string;
  phone: number | string;
  cover_letter: string;
  resume: UserResumes;
  tnc: boolean;
}

export interface FileCvVaidateProps {
  uploadError: boolean;
  message: string;
  icon: string;
}

const ApplyWithCV = ({ job }: { job: JobType }) => {
  const isShowApplyJob = useAppSelector(
    (state) => state?.setting?.isShowApplyJob,
  );
  const currentJob = useAppSelector((state) => state?.job?.currentJob);
  const dispatch = useAppDispatch();
  const pathName = usePathname();
  const t = useTranslations();
  const [isFetching, setIsFetching] = useState<boolean>(true);
  const [chooseCvValid, setChooseCvValid] = useState<boolean>(false);
  const [fileCV, setFileCV] = useState<any>(null);
  const [fileCvVaidate, setFileCvVaidate] = useState<FileCvVaidateProps | any>(
    [],
  );
  const appliedJobs = useAppSelector((state) => state.user.applied_jobs);

  const [jobState, setJobState] = useState<JobType>(job);
  const [resumes, setResumes] = useState<Array<UserResumes>>([]);
  const user = useAppSelector((state) => state?.user?.user);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const searchParams = useSearchParams();
  const [userProfile, setUserProfile] = useUserProfile();

  //Effects is client
  useEffect(() => {
    if (
      user.roles &&
      user.roles[0] !== "employer" &&
      searchParams.get("isShowApplyJob") === "1"
    ) {
      document.body.classList.add("layer-popup");
      dispatch(setIsShowApplyJob(true));
    }
  }, [user.roles]);

  //Define formik
  const {
    setFieldValue,
    setSubmitting,
    setFieldError,
    resetForm,
    handleSubmit,
    isSubmitting,
    values: valuesForm,
    errors: errorForm,
    touched: touchedForm,
  } = useFormik<FormDataProps>({
    initialValues: {
      display_name: user.display_name as string,
      email: user.email as string,
      phone: !!user.phone ? String(user.phone) : "",
      cover_letter: user.cover_letter as string,
      resume: {},
      tnc: (user.tnc as boolean) ?? false,
    },
    validationSchema: schemaFormApply(t, isLoggedIn), // TODO: Check islogged in here
    onSubmit: (values: FormDataProps) => {
      handleApplyJob(values).then(() => {
        if (appliedJobs?.length > 0) {
          gtag({
            event: "already_applied",
          });
        }
        if (isMobile) {
          gtag({
            event: "apply_recommenedjob_mw",
          });
        }
      });
    },
  });

  //Params Job
  const params: string = searchParams.toString();
  const paramJob = new URLSearchParams(params);
  const jobTitle = paramJob.get("job_title");
  const jobCompany = paramJob.get("job_company");
  const jobId = paramJob.get("job_id");

  //Call api user profile
  useEffect(() => {
    getUserProfile().then((response) => {
      setUserProfile(response.data as Profile);
    });
  }, [isLoggedIn]);
  //Effect change job
  useEffect(() => {
    const data = currentJob?.id > 0 ? currentJob : job;
    setJobState(data);
  }, [currentJob, job]);

  useEffect(() => {
    if (currentJob?.id === 0) {
      return;
    }
    setJobState(currentJob);
  }, [currentJob]);

  //Effect data resumes
  useEffect(() => {
    const fetchData = async () => {
      try {
        const params = {
          ready_to_apply: true,
          page_size: 10,
        };

        const data = await fetchResumesApi(params);
        if (data?.status === 200) {
          const dataFilterApply = data?.data?.data?.filter(
            (resume: UserResumes) => resume?.features?.apply,
          );
          setResumes(dataFilterApply);
          dispatch(setResumesUser(dataFilterApply as Array<UserResumes>));
          setIsFetching(false);
        }
        if (!!userProfile) {
          setFieldValue("display_name", userProfile?.display_name);
          setFieldValue("email", userProfile?.email);
          setFieldValue(
            "phone",
            userProfile?.phone !== null && userProfile?.phone !== undefined
              ? userProfile.phone
              : "",
          );
          setFieldValue("cover_letter", user?.cover_letter);
          setFieldValue("tnc", Boolean(user?.tnc));
        }
      } catch (error) {
        setIsFetching(false);
      }
    };
    if (!!isShowApplyJob) {
      fetchData();
      setChooseCvValid(false);
    } else setIsFetching(false);
  }, [isShowApplyJob, user, setFieldValue, userProfile, dispatch]);
  //End effect data resumes
  //Clear popup if is show false
  if (!isShowApplyJob) {
    if (typeof document !== "undefined")
      document.body.classList.remove("layer-popup");
    return <></>;
  }
  //End Clear popup if is show false

  /**
   * Upload file and check file
   * @param event
   * @returns
   */
  const handleChangeFile = (event: ChangeEvent<HTMLInputElement>) => {
    const file = (event.target as any).files[0];
    if (!file) return;
    const { type, size } = file;

    /**
     * Check file type
     */
    const fileType =
      type === "application/pdf" ||
      type ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
      type === "application/msword";

    if (!fileType) {
      setFileCvVaidate({
        ...fileCvVaidate,
        uploadError: true,
        message: t("detail_job_page_apply_validate_type_file_popup"),
        icon: "triangle",
      });
      return;
    }

    /**
     * 5MB file
     * Check file size
     */
    const fileSize = size / 1024 / 1024 < 5;
    if (!fileSize) {
      setFileCvVaidate({
        ...fileCvVaidate,
        uploadError: true,
        message: t("detail_job_page_apply_validate_type_file_popup"),
        icon: "triangle",
      });
      return;
    }
    setFileCV(file);
    setFileCvVaidate({ ...fileCvVaidate, uploadError: false });
    // Hiden Alert Error
    setChooseCvValid(false);
    const newListResumes = resumes?.filter(
      (resume: any) => resume.type !== "upload",
    );
    newListResumes.unshift({
      id: 0,
      name: file.name,
      type: "upload",
      created_at: dayjs().format("HH:mm DD/MM/YYYY"),
      last: true,
    });
    if (newListResumes.length === 0) return;
    setResumes(newListResumes);
    handleChangeResume(0);
    setSubmitting(false);
  };
  /**
   *  @returns
   */

  const handleClearEvent = () => {
    if (typeof document !== "undefined")
      document.body.classList.remove("layer-popup");
    dispatch(setIsShowApplyJob(false));

    setFileCvVaidate({});
    setFileCV("");
    setResumes([]);
    resetForm();
  };

  const handleCancel = () => {
    handleClearEvent();

    gtag([
      "event",
      "page_view",
      {
        send_to: "AW-*********",
        dynx_itemid: jobState.id,
        dynx_pagetype: "conversionintent",
        job_id: jobState.id,
        job_locid: jobState?.addresses?.address_region_list,
        job_pagetype: "conversionintent",
      },
    ]);
  };

  const handleChangeResume = (resumeId: number) => {
    const elementForm = document.getElementById(`resume-${resumeId}`);
    if (elementForm) {
      elementForm.click();
    }
    const elements = document.querySelectorAll('[class*="div-resumes"]');
    elements.forEach((element) => {
      // Remove class 'border', 'border-brand-300'
      element.classList.remove("border", "border-brand-300");

      // if have list-resumes-resumeId, add border and border-300
      if (element.classList.contains(`data-${resumeId}`)) {
        element.classList.add("border", "border-brand-300");
      }
    });
    const resume =
      !!resumes &&
      resumes.find((resume: UserResumes) => resume.id === resumeId);
    setFieldValue("resume", resume);
    setChooseCvValid(false);
    setFileCvVaidate({ uploadError: false });
  };
  const handleApplyJob = async (values: FormDataProps) => {
    let filesCv = null;
    let mediaId = null;
    let cvBuilderId = null;
    let userProfileId = userProfile.id;
    let resumeId = null;
    let dataType = null;
    let dataName = null;
    const checkedElement =
      (document.querySelector(
        '[class*="div-resumes"] input[type="radio"]:checked',
      ) as HTMLInputElement) ?? null;
    if (checkedElement) {
      resumeId = checkedElement.value;
      dataType = checkedElement.dataset.type;
      dataName = checkedElement.dataset.name;
    }
    if (resumeId != userProfileId) {
      filesCv = dataType == "upload" ? fileCV : null;
      mediaId = dataType == "media" ? resumeId : null;
      cvBuilderId = dataType == "cvbuilder" ? resumeId : null;
      userProfileId = null;
    }
    if (!checkedElement) {
      setChooseCvValid(true);
      setFileCvVaidate({ uploadError: false });
      setSubmitting(false);
      return;
    }
    if (!isLoggedIn && !values?.tnc) {
      setFieldError("tnc", t("detail_job_page_apply_required_tnc_apply"));
      setSubmitting(false);
      return;
    }

    const url = new URL(window.location.href);
    const params = url.searchParams;
    const SID =
      params.has("sid") &&
      (typeof params.get("sid") === "string"
        ? params.get("sid")
        : params.get("sid")?.[0]);

    const data: any = {
      display_name: values.display_name,
      email: values.email,
      tnc: values.tnc ?? null,
      cover_letter: values.cover_letter || "",
      phone: values.phone,
      job_id: jobState.id ? jobState.id : jobId || null,
      files_cv: filesCv,
      case: isLoggedIn ? "ApplyLogined" : "ApplyNonLogin",
      media_id: mediaId,
      cvbuilder_id: cvBuilderId,
      user_profile_id: userProfileId,
      file_name: dataName,
      token_device: localStorage.getItem(TOKEN_DEVICE) ?? null,
      sid: SID ?? null,
      utm_source: params.has("utm_source") ? params.get("utm_source") : null,
      utm_medium: params.has("utm_medium") ? params.get("utm_medium") : null,
      utm_campaign: params.has("utm_campaign")
        ? params.get("utm_campaign")
        : null,
      upload_from: "UploadFromTopdev",
      source: params.has("source") ? params.get("source") : "ApplyNow",
      device_apply: isMobile ? "MobileWeb" : "PC",
      query_src: params.has("src") ? params.get("src") : null,
      query_medium: params.has("medium") ? params.get("medium") : null,
      tracking_variant: "forced-login",
      tracking_login: isLoggedIn ? 1 : 0,
      tracking_signup: getCookie("just_signup") ? 1 : 0,
      tracking_event_location: getCookie("referring_name"),
    };

    const formData = getFormData(data);
    try {
      const { data } = await applyJobsApi(formData);

      if (data.message === "applied" && data.success === false) {
        Swal.fire({
          title: t("common_error"),
          text: t("detail_job_page_apply_you_have_applied_for_job"),
          icon: "error",
          confirmButtonColor: "#DD3F24",
        });

        setSubmitting(false);
        handleClearEvent();
        return;
      }

      if (!data.success) {
        ToastNotification({
          icon: "error",
          title: t("common_error"),
          description: data.message,
        });

        setSubmitting(false);
        handleClearEvent();
        return;
      }

      const comebackUrl = data?.data?.comeback_url;

      // GTM event eec.sendCV
      gtag({
        event: "eec.sendCV",
        ecommerce: {
          checkout: {
            actionField: { list: "ApplyCV" },
            products: [
              {
                name: process.env.NEXT_PUBLIC_BASE_URL + "/" + pathName,
                id: jobState.id,
                brand: jobState.company.display_name,
                category: "Job",
                variant: jobState.job_types_str,
              },
            ],
          },
        },
      });

      gtag(["js", new Date()]);
      gtag(["config", "AW-*********"]);

      // GTM event page_view
      gtag([
        "event",
        "page_view",
        {
          send_to: "AW-*********",
          dynx_itemid: jobState.id,
          dynx_pagetype: "conversion",
          job_id: jobState.id,
          job_locid: "HCM",
          job_pagetype: "conversion",
        },
      ]);

      setCookie("recently_apply", String(jobState.id), 1);

      tracking({
        job_ids: jobState.id,
        collection: "click-ok-when-sent-cv",
      });

      // update newest_candidate

      //Show data notification for dialog
      dispatch(
        setDataDialogNotification({
          email: values.email,
          status: data.success,
          comebackUrl: comebackUrl,
          theme: "theme_noti_applay",
          event: !!data.data ? data.data.events : [],
          promotion_reward: data?.data?.promotion_reward ?? null,
        }),
      );
      if (!!data.data) {
        //Show notification for dialog
        dispatch(showDialogNotification(true));
      }
      //Show data job for job applied
      dispatch(
        setActiveAppliedJob({
          jobId: jobState.id,
          status: true,
        }),
      );

      /**
       * Push current job to applied jobs of current user
       */
      dispatch(pushAppliedJob(jobState.id));

      handleClearEvent();

      //Show event notification
      if (isLoggedIn && !!data.data) {
        setTimeout(() => {
          data.data?.events.includes("EventSuggestJobs") &&
            dispatch(showDialogNotification(false));
          dispatch(setEventsState(data.data));
        }, 3000);
      }
    } catch (error) {
      setSubmitting(false);
      ToastNotification({
        icon: "error",
        title: t("common_sorry"),
        description: t("detail_job_page_apply_notification_apply_failed"),
      });
      handleClearEvent();
      console.error(error);
    }
  };

  //Set content editor
  const setContent = (value: string) => {
    const htmlNode = document.createElement("div");
    htmlNode.innerHTML = value;
    htmlNode?.querySelectorAll("*").forEach(function (node) {
      for (let i = 0; i < node.attributes.length; i++) {
        node.removeAttribute(node.attributes[i].name);
      }
    });
    if (htmlNode.innerHTML) {
      setFieldValue("cover_letter", htmlNode.innerHTML);
    } else {
      setFieldValue("cover_letter", null);
    }
  };

  //Check validated
  const validateDisplayName =
    touchedForm.display_name && errorForm.display_name;
  const validateEmail = touchedForm.email && errorForm.email;
  const validatePhone = touchedForm.phone && errorForm.phone;
  const validateTnc = touchedForm.tnc && errorForm.tnc;

  //Template input form
  const TemplateInputForm = (
    nameAndIdInput: string,
    placeholderInput: string,
    validatedInput: boolean,
    valuesForm: any,
  ) => {
    return (
      <>
        <input
          type="text"
          name={nameAndIdInput}
          id={nameAndIdInput}
          className={classNames(
            validatedInput ? "border-primary" : "border-neutral-200",
            `mt-1 w-full rounded border px-3 py-4 text-sm text-neutral-950 placeholder:text-neutral-300 disabled:border-neutral-200 disabled:bg-neutral-50 disabled:text-black md:px-4 md:py-4`,
          )}
          style={{
            color: `${isLoggedIn && nameAndIdInput == "email" ? "black" : ""}`,
          }}
          onChange={(e) => setFieldValue(nameAndIdInput, e.target.value)}
          value={valuesForm?.[nameAndIdInput] ?? ""}
          placeholder={placeholderInput}
          disabled={isLoggedIn && nameAndIdInput == "email"}
        />
        <FaExclamationCircle
          className={classNames(
            validatedInput ? "" : "hidden",
            "absolute right-[8px] top-[20px] z-10 text-lg text-primary active:block md:right-[15px] md:top-[17px]",
          )}
        />
        {validatedInput ? (
          <div className="mb-2 block">
            <small className="mt-1 block text-xs text-primary">
              <span>{(errorForm as any)?.[nameAndIdInput]}</span>
            </small>
          </div>
        ) : (
          <>
            <div className="mb-2"></div>
          </>
        )}
      </>
    );
  };
  return (
    <section
      id="popup"
      className={classNames(
        "fixed left-0 right-0 top-0 z-[51] mx-4 flex h-modal w-auto items-center justify-center overflow-y-auto overflow-x-hidden dark:bg-opacity-80 md:inset-0 md:mx-auto md:h-full md:w-[1046px]",
      )}
    >
      <div className="md:scroll-bar-resume-list-user scroll-bar-resume-list-user box-popup-apply md:max-h-auto max-h-auto relative w-full rounded-lg bg-white sm:overflow-y-auto md:max-h-screen md:min-w-[1046px]">
        {/* Loading DialogModal */}
        <div
          className={classNames(
            isFetching ? "flex" : "hidden",
            "load absolute left-0 top-0 z-50 h-full w-full max-w-[776px] items-center justify-center rounded bg-[rgba(0,0,0,.35)]",
          )}
        >
          <div className="flex">
            <div className="h-4 w-4 animate-bounce rounded-full bg-white"></div>
            <div className="mx-2 h-4 w-4 animate-bounce rounded-full bg-white"></div>
            <div className="h-4 w-4 animate-bounce rounded-full bg-white"></div>
          </div>
        </div>
        {/* End Loading DialogModal */}

        {/* header DialogModal */}
        <div className="headerDialog relative mb-2 flex items-start justify-between border-b border-gray-300 p-4 md:mb-6 md:p-6">
          <div className="flex flex-col">
            <span className="w-auto text-sm font-bold leading-6 text-neutral-500 md:text-sm md:leading-normal">
              {t("detail_job_apply_position")}
            </span>
            <div>
              <span className="mr-1 w-auto text-lg font-bold text-primary md:text-xl">
                {jobTitle ? jobTitle : jobState.title}
              </span>
              <span className="text-lg font-bold text-neutral-950 md:text-xl">
                {t("detail_job_apply_position_at")}{" "}
                {jobCompany ? jobCompany : jobState.company.display_name}
              </span>
            </div>
          </div>
          <span
            onClick={() => handleCancel()}
            className="-mt-[9px] inline-block h-10 w-10 cursor-pointer text-center leading-10"
          >
            <IoMdClose className="inline-block h-6 w-6" />
          </span>
        </div>
        {/* End header DialogModal */}

        {/* Form DialogModal */}
        <form
          onSubmit={handleSubmit}
          id="formApply"
          className="relative h-auto min-h-[624px] w-auto"
        >
          <div
            className={`${
              isDesktop
                ? "scroll-bar-resume-list-user absolute z-10 h-[624px] w-[98%] px-2 md:mr-2 md:px-6"
                : "absolute z-10 h-[624px] px-4"
            } `}
          >
            <div className="flex w-full flex-col flex-wrap gap-3 md:flex md:flex-row md:flex-nowrap">
              <span className="w-[150px] text-lg font-bold text-neutral-900">
                {isDesktop
                  ? t.rich("detail_job_basic_information", {
                      span: (chunks) => <span>{chunks}</span>,
                      br: () => <br />,
                    })
                  : t("detail_job_basic_information_mobile")}
              </span>
              <div className="w-full flex-wrap rounded bg-neutral-light p-3 md:-mr-5 md:flex md:w-[98%] md:flex-row md:flex-nowrap md:p-3">
                {/* input name */}
                <div className="mr-4 flex w-full flex-col flex-wrap gap-1 md:w-1/3 md:flex-nowrap">
                  <div className="w-[150px] md:mr-3">
                    <label className="text-base font-bold text-neutral-600">
                      {t("detail_job_page_apply_fields_full_name")}
                    </label>
                    <span className="ml-1 text-red-500">*</span>
                  </div>
                  <div className="relative md:w-auto">
                    {TemplateInputForm(
                      "display_name",
                      t("detail_job_fullname"),
                      validateDisplayName as boolean,
                      valuesForm,
                    )}
                  </div>
                </div>
                {/* End input name */}

                {/* input phone */}
                <div className="mr-4 flex w-full flex-col flex-wrap md:mt-0 md:w-1/3 md:flex-nowrap">
                  <div className="w-auto">
                    <label className="text-base font-bold text-neutral-600">
                      {t("detail_job_phone_number")}
                    </label>
                  </div>
                  <div className="relative mt-1 md:w-auto">
                    {TemplateInputForm(
                      "phone",
                      t("detail_job_page_apply_placeholder_phone"),
                      validatePhone as boolean,
                      valuesForm,
                    )}
                  </div>
                </div>

                {/* End input phone */}
                {/* input Email */}
                <div className="flex w-full flex-col flex-wrap gap-1 md:mt-0 md:w-1/3 md:flex-nowrap">
                  <div className="w-auto">
                    <label className="text-base font-bold text-neutral-600">
                      {t("detail_job_page_apply_fields_email")}
                    </label>
                    <span className="ml-1 text-red-500">*</span>
                  </div>
                  <div className="relative w-auto text-neutral-950 disabled:text-neutral-950">
                    {TemplateInputForm(
                      "email",
                      t("detail_job_page_apply_placeholder_email"),
                      validateEmail as boolean,
                      valuesForm,
                    )}
                  </div>
                </div>
                {/* End input Email */}
              </div>
            </div>

            {/* list resume */}
            <div
              id="chooseCv"
              className="mt-6 flex flex-row flex-wrap items-start gap-1 md:-mr-[18px] md:mt-4 md:flex-nowrap"
            >
              <div className="w-auto min-w-[144px]">
                <label className="text-lg font-bold text-neutral-900">
                  {t("detail_job_choose_cv")}
                </label>
              </div>
              <div className="w-full">
                <div className="list-resumes">
                  <div className="choose_file flex h-auto w-auto flex-wrap items-center">
                    {resumes.length >= 0 && (
                      <>
                        <div
                          className={`
                            ${
                              isLoggedIn ? "bg-neutral-light" : " "
                            }                                  
                          ${fileCV ? "bg-neutral-light" : " "} 
                          list-resumes-cv relative w-full rounded md:w-full`}
                        >
                          <ListResumesApplyJobDetail
                            resumes={resumes}
                            fileCV={fileCV}
                            transition={t}
                            valueCheck={valuesForm?.resume}
                            isLoggedIn={isLoggedIn}
                            onChangeResume={(resumeId: number) =>
                              handleChangeResume(resumeId)
                            }
                            userProfile={userProfile}
                            job={jobState}
                            handleClearEvent={handleClearEvent ?? null}
                          />

                          {chooseCvValid && (
                            <div
                              className={`${
                                isLoggedIn ? "px-4 md:pb-2" : "md:pb-2"
                              }`}
                            >
                              <div className="h-15 flex w-full items-center rounded border-b-2 border-b-brand-600 bg-red-light px-4 py-[10px]  md:h-10">
                                <span className="mr-2 text-2xl font-normal text-brand-600">
                                  <HiMiniExclamationCircle
                                    width={6}
                                    height={6}
                                  />
                                </span>

                                <span className="ml-2 w-full text-sm font-bold text-neutral-950 md:w-auto">
                                  {t("detail_job_page_apply_please_upload")}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </>
                    )}
                    <div
                      className={`${
                        isLoggedIn
                          ? "mt-2 w-full md:flex md:flex-row"
                          : "mt-2 w-full md:flex md:flex-col"
                      }`}
                    >
                      <div className="flex flex-col md:flex md:flex-row md:items-center">
                        <label
                          className="flex w-full cursor-pointer justify-center rounded bg-gray-500 py-2 text-sm text-white sm:w-auto md:px-4 md:py-[10px]"
                          htmlFor="choose_cv"
                        >
                          <FaCloudUploadAlt className="mr-1 mt-[1px] inline-block h-4 w-[18px] align-middle" />
                          {t("detail_job_choose_file")}
                        </label>
                        <input
                          onChange={handleChangeFile}
                          type="file"
                          id="choose_cv"
                          className="hidden px-4"
                        />
                        <div className="flex flex-col">
                          {!isLoggedIn && (
                            <>
                              <div className="mt-2 flex w-full flex-row items-center gap-2 text-xs sm:text-left md:mt-0">
                                <span className="mt-0 block sm:inline-block md:ml-2 md:mt-0">
                                  {t("common_or")}
                                </span>
                                <button
                                  type="button"
                                  className="text-sm text-primary-300 underline"
                                  onClick={() => openLoginPopup()}
                                >
                                  {t(
                                    "detail_job_page_apply_login_to_use_uploaded_cv",
                                  )}
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      </div>

                      <div
                        className={`${
                          isLoggedIn ? " md:ml-2" : "md:ml-0 md:mt-2"
                        } mt-2 pl-0 pt-0 text-xs text-neutral-500 sm:text-left md:my-auto`}
                      >
                        {t("detail_job_choose_cv_format")}
                      </div>
                    </div>
                    {fileCvVaidate.uploadError &&
                      fileCvVaidate.icon != "circle" && (
                        <div className="mt-2 flex w-full text-sm text-primary">
                          <span className="mr-2 text-sm font-normal">(*)</span>

                          <span className="w-full max-w-xs text-sm font-bold md:w-auto md:max-w-full">
                            {fileCvVaidate.message}
                          </span>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            </div>
            {/* End list resume */}

            {/* textarea form */}
            <div
              id="coverletter"
              className="mt-6 flex flex-row flex-wrap items-start gap-1 pr-2 md:-mr-6 md:ml-0 md:mt-4 md:min-w-[860px] md:flex-nowrap"
            >
              <div className="w-auto min-w-[144px]">
                <label className="text-lg font-bold">
                  {t("detail_job_apply_cover_letter")}
                </label>
              </div>
              <div className="h-full w-full md:max-w-[860px]">
                <TextEditor
                  onChange={setContent}
                  placeholder={t("detail_job_apply_cover_letter_placeholder")}
                  showLoading={true}
                  value={valuesForm.cover_letter ?? ""}
                />

                {!isLoggedIn && (
                  <div className="boxCheckboxTnc mt-3">
                    <input
                      name="tnc"
                      type="checkbox"
                      id="checkboxTnc"
                      className="hidden"
                      onChange={(e) => setFieldValue("tnc", e.target.checked)}
                    />
                    <label
                      htmlFor="checkboxTnc"
                      className="custom-control-label relative cursor-pointer pl-8 text-sm md:text-base"
                    >
                      {t("detail_job_page_apply_tnc_apply_with")}
                      <a
                        className="text-black hover:text-primary"
                        target="_blank"
                        title={t(
                          "detail_job_page_apply_tnc_apply_term_of_services",
                        )}
                        href={`${process.env.NEXT_PUBLIC_BASE_URL}/term-of-services`}
                      >
                        <b>
                          {t(
                            "detail_job_page_apply_tnc_apply_term_of_services",
                          )}
                        </b>
                      </a>
                      <span className="inline-block px-1">
                        {t("common_and")}
                      </span>
                      <a
                        className="text-black hover:text-primary"
                        target="_blank"
                        title={t(
                          "detail_job_page_apply_tnc_apply_privacy_policy",
                        )}
                        href={`${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`}
                      >
                        <b>
                          {t("detail_job_page_apply_tnc_apply_privacy_policy")}
                        </b>
                      </a>
                      <span className="inline-block pl-1">
                        {t("detail_job_page_apply_tnc_apply_of_topdev")}
                      </span>
                    </label>
                    <br />
                    {validateTnc && (
                      <small className="mb-2 mt-1 block text-xs text-primary">
                        <span>{errorForm.tnc}</span>
                      </small>
                    )}
                  </div>
                )}
              </div>
            </div>
            {isMobile && (
              <div className="mb-6 mt-6 flex w-full justify-between gap-4 pb-6">
                <Button
                  onClick={() => handleCancel()}
                  size={isMobile ? "md" : "lg"}
                  accent="cancelMobile"
                  fontSize="text-sm"
                >
                  {t("detail_job_page_apply_cancel")}
                </Button>
                <Button
                  type={isSubmitting ? "button" : "submit"}
                  size={isMobile ? "md" : "lg"}
                  id="applySendProcessBtn"
                  accent={isSubmitting ? "disableMobile" : "applyMobile"}
                  fontSize="text-sm"
                  form={"formApply"}
                >
                  {isSubmitting ? (
                    <VscLoading className="h-6 w-6 animate-spin text-primary-400" />
                  ) : (
                    t("detail_job_page_apply_send_cv")
                  )}
                </Button>
              </div>
            )}
            {/* End textarea form */}
          </div>

          {/* Footer form DialogModal */}
          {isDesktop ? (
            <div className="absolute inset-x-0 bottom-0 z-[30] flex h-25 w-full items-center justify-between bg-white shadow-md shadow-black sm:absolute sm:bottom-0 sm:w-full md:absolute md:bottom-0 md:w-full">
              <div className="mx-auto flex w-full justify-between px-2 md:m-0 md:mr-6 md:justify-end md:px-0">
                <Button
                  onClick={() => handleCancel()}
                  size={isMobile ? "md" : "lg"}
                  accent="cancelMobile"
                  fontSize="text-sm"
                >
                  {t("detail_job_cancel_apply_button")}
                </Button>
                <div>
                  <Button
                    type={isSubmitting ? "button" : "submit"}
                    size={isMobile ? "md" : "lg"}
                    id="applySendProcessBtn"
                    accent={isSubmitting ? "disableMobile" : "applyMobile"}
                    fontSize="text-sm"
                    form={"formApply"}
                  >
                    {isSubmitting ? (
                      <VscLoading className="h-6 w-6 animate-spin text-primary-400" />
                    ) : (
                      t("detail_job_send_cv")
                    )}
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <></>
          )}

          {/* End footer form DialogModal */}
        </form>
        {/* End form DialogModal */}

        {/* Footer apply */}
        {/* End footer apply */}
      </div>
    </section>
  );
};
export default ApplyWithCV;
