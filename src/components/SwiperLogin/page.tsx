"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";

// Import Swiper modules
import { isDesktop } from "react-device-detect";
import Write from "../Icons/Write";
import { Autoplay, Grid, Navigation, Pagination } from "swiper/modules";
import { useTranslations } from "next-intl";

const SlideOne = () => {
  const t = useTranslations();

  return (
    <div className="flex flex-col items-center gap-7">
      <span className="w-fit text-4xl font-bold text-[#292929]">
        {t("auth_slide_one_title")}
      </span>
      <Image
        src="https://c.topdevvn.com/uploads/2025/07/23/find-sum.png"
        width={466}
        height={419}
        alt="find-sum"
      />
      <ul className="max-w-[470px] space-y-2">
        {[1, 2, 3, 4].map((i) => (
          <li key={i} className="flex gap-2">
            <span className="block h-8 w-8">
              <Write />
            </span>
            {t(`auth_slide_one_item_${i}`)}
          </li>
        ))}
      </ul>
    </div>
  );
};

const SlideTwo = () => {
  const t = useTranslations();

  return (
    <div className="flex flex-col items-center gap-7">
      <span className="text-4xl font-bold text-[#292929]">
        {t("auth_slide_two_title")}
      </span>
      <Image
        src="https://c.topdevvn.com/uploads/2025/07/23/job-post.png"
        width={466}
        height={419}
        style={{ height: "419px" }}
        alt="job-post"
      />
      <ul className="max-w-[470px] space-y-2">
        {[1, 2, 3, 4].map((i) => (
          <li key={i} className="flex gap-2">
            <span className="block h-8 w-8">
              <Write />
            </span>
            {t(`auth_slide_two_item_${i}`)}
          </li>
        ))}
      </ul>
    </div>
  );
};

const SlideThree = () => {
  const t = useTranslations();

  return (
    <div className="flex flex-col items-center gap-7">
      <span className="text-4xl font-bold text-[#292929]">
        {t("auth_slide_three_title")}
      </span>
      <Image
        src="https://c.topdevvn.com/uploads/2025/07/23/easy-apply.png"
        width={466}
        height={419}
        alt="easy"
      />
      <ul className="max-w-[470px] space-y-2">
        {[1, 2, 3, 4].map((i) => (
          <li key={i} className="flex gap-2">
            <span className="block h-8 w-8">
              <Write />
            </span>
            {t(`auth_slide_three_item_${i}`)}
          </li>
        ))}
      </ul>
    </div>
  );
};

const SwiperLogin = () => {
  return (
    <div className="w-1/2">
      <Swiper
        grabCursor={true}
        slidesPerView={1}
        centeredSlides={true}
        spaceBetween={isDesktop ? 24 : 12}
        autoplay={{
          delay: 5000, // Delay between transitions in ms
          disableOnInteraction: false, // Continue autoplay after user interaction
        }}
        grid={{
          rows: isDesktop ? 1 : 2,
          fill: "row",
        }}
        className="w-full"
        modules={
          isDesktop
            ? [Grid, Pagination, Navigation, Autoplay]
            : [Grid, Pagination, Autoplay]
        }
        pagination={{ clickable: true, el: ".pagination" }}
        navigation={
          isDesktop
            ? {
                nextEl: ".nextEl",
                prevEl: ".preEl",
              }
            : false
        }
      >
        {[SlideOne, SlideTwo, SlideThree].map((item, index) => {
          return <SwiperSlide key={index}>{item}</SwiperSlide>;
        })}
        <div className={"swiper-footer flex items-center justify-center gap-1"}>
          <div
            className={
              "preEl absolute left-0 top-1/2 z-10 inline-flex h-9 w-9 items-center justify-center rounded-full bg-primary-100 lg:static lg:block lg:h-auto lg:w-auto lg:bg-transparent"
            }
          ></div>
          {isDesktop ? <div className={"pagination"}></div> : null}
          <div
            className={
              "nextEl absolute right-0 top-1/2 z-10 inline-flex h-9 w-9 items-center justify-center rounded-full bg-primary-100 lg:static lg:block lg:h-auto lg:w-auto lg:bg-transparent"
            }
          ></div>
        </div>
      </Swiper>
    </div>
  );
};

export default SwiperLogin;
