import React, { useState } from "react";
import { Editor } from "@tinymce/tinymce-react";
import { VscLoading } from "react-icons/vsc";

export default function TextEditor({
  value,
  showLoading,
  placeholder,
  height = 150,
  onChange,
}: {
  value: string;
  showLoading?: boolean;
  placeholder?: string;
  height?: number;
  onChange: (newValue: string) => void;
}) {
  const [loading, setLoading] = useState<boolean>(true);

  return (
    <>
      <div className={`${showLoading ? "min-h-[150px]" : ""} relative`}>
        {showLoading && loading && (
          <div className="absolute z-10 flex h-full w-full items-center justify-center bg-[rgba(0,0,0,0.4)]">
            <VscLoading className="h-6 w-6 animate-spin" />{" "}
          </div>
        )}

        <Editor
          tinymceScriptSrc={"https://assets.topdev.vn/tinymce/tinymce.min.js"}
          value={value}
          onInit={() => setLoading(false)}
          onEditorChange={(newValue, editor) => onChange(newValue)}
          init={{
            height: height,
            menubar: false,
            plugins: ["lists", "wordcount"],
            branding: false,
            resize: false,
            elementpath: false,
            oninit: "setPlainText",
            toolbar: "bold italic underline | numlist bullist",
            paste_as_text: true,
            content_css: false,
            skin: "tinymce-5",
            placeholder: !!placeholder ? placeholder : "",
            content_style:"body p { margin-top: 0px; margin-bottom: 0.5rem;}"
          }}
        />
      </div>
    </>
  );
}
