"use client";

import React, { FC } from "react";
import Image from "next/image";
import { <PERSON> } from "@/navigation";
import dynamic from "next/dynamic";
import { BRAND_LOGO } from "@/utils/image";
import { CompanyType } from "@/types/company";
import IndustryTag from "@/components/Tag/IndustryTag";
import useTaxonomy from "@/utils/taxonomies";
import { Lang } from "@/types/page";
import { useTranslations } from "next-intl";

const JobOpeningsButton = dynamic(
  () => import("@/components/Button/JobOpeningsButton"),
);

const FollowCompanyButton = dynamic(
  () => import("@/components/Button/FollowCompanyButton"),
);

//Skill Tag
const SkillTag = dynamic(() => import("@/components/Tag/SkillTag"));

//Skill Tag Taxonomy
const SkillTagTaxonomy = dynamic(
  () => import("@/components/Tag/SkillTagTaxonomy"),
);

interface Props {
  companyData: CompanyType;
  locale: Lang;
}

const CardCompanyList: FC<Props> = ({ companyData, locale }) => {
  const name = companyData.display_name ?? "";
  const taxonomy = useTaxonomy();
  const t = useTranslations();
  return (
    <Link
      href={{
        pathname: "/companies/[slug]",
        params: { slug: companyData.slug + "-" + companyData.id },
        query: { src: "topdev_search", medium: "searchresult" },
      }}
      target="_blank"
    >
      <div className="card-company-list group flex max-w-[832px] cursor-pointer items-start gap-4 rounded border border-white bg-white p-4 transition-all hover:border-primary-200 hover:bg-primary-100 hover:shadow-md">
        <div className="card-company-list-info-logo h-28 w-40 items-center justify-items-center">
          <Link
            href={{
              pathname: "/companies/[slug]",
              params: { slug: companyData.slug + "-" + companyData.id },
              query: { src: "topdev_search", medium: "searchresult" },
            }}
            target="_blank"
          >
            <Image
              src={companyData.image_logo ?? ""}
              alt={name}
              width={BRAND_LOGO.company.logo.large.with}
              height={BRAND_LOGO.company.logo.large.height}
              loading="lazy"
              className="inline-block h-28 w-40 rounded-xl bg-white object-contain p-2"
            />
          </Link>
        </div>
        <div className="card-company-list-info-content flex-1">
          <Link
            href={{
              pathname: "/companies/[slug]",
              params: { slug: companyData.slug + "-" + companyData.id },
              query: { src: "topdev_search", medium: "searchresult" },
            }}
            target="_blank"
          >
            <h3 className="line-clamp-1 text-lg font-bold transition-all">
              {name}
            </h3>
            {companyData.tagline && (
              <p className="tag-line mt-1 line-clamp-1 min-h-[24px] text-lg">
                {companyData.tagline}
              </p>
            )}
            {(!!companyData.addresses || !!companyData.num_employees) && (
              <div className="card-company-list-info-address-employees mt-2 flex flex-wrap items-start text-base capitalize text-gray-dark">
                {companyData.addresses.address_region_array.length > 1
                  ? companyData.addresses.address_short_region_list
                  : companyData.addresses.sort_addresses}

                {!!companyData.num_employees &&
                  taxonomy(companyData.num_employees, "num_employees") && (
                    <span className="whitespace-nowrap">
                      <span className="mx-5 inline-block h-[6px] w-[6px] rounded-full bg-gray-dark align-middle"></span>
                      {locale == "en"
                        ? taxonomy(companyData.num_employees, "num_employees")
                            ?.text_en
                        : taxonomy(companyData.num_employees, "num_employees")
                            ?.text}{" "}
                      {t("company_num_employees_text")}
                    </span>
                  )}
              </div>
            )}
            {companyData.industries_ids?.length > 0 && (
              <ul className="card-company-list-info-industry text-base capitalize text-gray-dark">
                {companyData.industries_ids.length > 3 ? (
                  <>
                    {companyData.industries_ids.map(
                      (industryId: number, index: number) => {
                        if (index < 3) {
                          const industry = taxonomy(industryId, "industries");
                          return (
                            <li className="inline-block" key={index}>
                              <IndustryTag
                                title={
                                  locale == "en"
                                    ? industry?.text_en
                                    : industry?.text
                                }
                                className="pl-2 first:pl-0"
                              />
                              {Number(
                                companyData.industries_ids.slice(0, 3)?.length,
                              ) >
                                index + 1 && (
                                <span className="mx-2 inline-block">-</span>
                              )}
                            </li>
                          );
                        }
                      },
                    )}
                    <li className="inline-block">
                      <IndustryTag
                        quantity={companyData.industries_ids.length - 3}
                      />
                    </li>
                  </>
                ) : (
                  companyData.industries_ids.map(
                    (id: number, index: number) => {
                      const industry = taxonomy(id, "industries");
                      return (
                        <li
                          className="inline-block pl-2 first:pl-0"
                          key={index}
                        >
                          <IndustryTag
                            title={
                              locale == "en"
                                ? industry?.text_en
                                : industry?.text
                            }
                          />
                          {Number(companyData.industries_ids?.length) >
                            index + 1 && " - "}
                        </li>
                      );
                    },
                  )
                )}
              </ul>
            )}
          </Link>
          {!!companyData.skills_ids && (
            <div className="mt-3 line-clamp-1">
              {companyData.skills_ids.slice(0, 6).map((skillItem, index) => {
                return <SkillTagTaxonomy skillId={skillItem} key={index} />;
              })}
            </div>
          )}
          <div className="mt-2">
            <JobOpeningsButton
              companyId={companyData.id}
              companySlug={companyData.slug}
              numberJobs={companyData.num_job_openings}
            />
          </div>
        </div>
        <div className="card-company-list-info-bookmark text-right">
          <span
            className="inline-block select-none text-2xl text-gray-dark"
            onClick={(event) => event.preventDefault()}
          >
            <FollowCompanyButton
              companyName={companyData.display_name}
              active={companyData.is_followed}
              companyId={companyData.id}
            />
          </span>
        </div>
      </div>
    </Link>
  );
};
export default CardCompanyList;
