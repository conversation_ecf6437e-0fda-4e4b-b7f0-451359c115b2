"use client";

import React, { FC } from "react";
import Image from "next/image";
import { <PERSON> } from "@/navigation";
import dynamic from "next/dynamic";
import { BRAND_LOGO } from "@/utils/image";
import { CompanyType } from "@/types/company";
import IndustryTag from "@/components/Tag/IndustryTag";
import SkillTag from "@/components/Tag/SkillTag";
import useTaxonomy from "@/utils/taxonomies";
import { Lang } from "@/types/page";
import { useTranslations } from "next-intl";

// Job Openings Button
const JobOpeningsButton = dynamic(
  () => import("@/components/Button/JobOpeningsButton"),
);

//Follow Company Button
const FollowCompanyButton = dynamic(
  () => import("@/components/Button/FollowCompanyButton"),
);

//Skill Tag Taxonomy
const SkillTagTaxonomy = dynamic(
  () => import("@/components/Tag/SkillTagTaxonomy"),
);

interface Props {
  companyData: CompanyType;
  locale: Lang;
}

const CardCompanyListMobile: FC<Props> = ({ companyData, locale }) => {
  const name = companyData.display_name ?? "";
  const taxonomy = useTaxonomy();
  const t = useTranslations();

  return (
    <Link
      href={{
        pathname: "/companies/[slug]",
        params: { slug: companyData.slug + "-" + companyData.id },
        query: { src: "topdev_search", medium: "searchresult" },
      }}
    >
      <div className="card-company-list-mobile group relative flex min-h-[186px] w-full cursor-pointer flex-wrap items-start gap-2 rounded border border-white bg-white p-4 transition-all hover:border-primary-200 hover:bg-primary-100 hover:shadow-md">
        <div className="card-company-list-info-logo h-[3.13rem] w-[4.5rem] items-center justify-items-center">
          <Link
            href={{
              pathname: "/companies/[slug]",
              params: { slug: companyData.slug + "-" + companyData.id },
              query: { src: "topdev_search", medium: "searchresult" },
            }}
          >
            <Image
              src={companyData.image_logo ?? ""}
              alt={name}
              width={BRAND_LOGO.company.logo.small.with}
              height={BRAND_LOGO.company.logo.small.height}
              loading="lazy"
              className="inline-block h-[3.13rem] w-[4.5rem] object-contain"
            />
          </Link>
        </div>
        <div className="card-company-list-info-content flex-1 pr-3">
          <Link
            href={{
              pathname: "/companies/[slug]",
              params: { slug: companyData.slug + "-" + companyData.id },
              query: { src: "topdev_search", medium: "searchresult" },
            }}
          >
            <h3 className="text-gray line-clamp-1 text-sm font-bold transition-all">
              {name}
            </h3>
            <p className="tag-line text-gray mb-4 mt-1 line-clamp-1 min-h-[18px] text-xs">
              {companyData.tagline}
            </p>
          </Link>
        </div>
        <div className="basis-full">
          {(!!companyData.addresses || !!companyData.num_employees) && (
            <div className="card-company-list-info-address-employees flex flex-wrap text-xs capitalize text-gray-dark">
              <p>
                {companyData.addresses &&
                companyData.addresses.address_region_array.length == 1
                  ? companyData.addresses.sort_addresses
                  : companyData.addresses.address_region_array[0] +
                    ` (+${
                      companyData.addresses.address_region_array.length - 1
                    })`}
              </p>
              {!!companyData.num_employees &&
                taxonomy(companyData.num_employees, "num_employees") && (
                  <p>
                    <span className="mx-4 inline-block h-[3px] w-[3px] rounded-full bg-gray-dark align-middle"></span>
                    {locale == "en"
                      ? taxonomy(companyData.num_employees, "num_employees")
                          ?.text_en
                      : taxonomy(companyData.num_employees, "num_employees")
                          ?.text}{" "}
                    {t("company_num_employees_text")}
                  </p>
                )}
            </div>
          )}
          {companyData.industries_ids?.length > 0 && (
            <ul className="card-company-list-info-industry text-xs capitalize text-gray-dark">
              {companyData.industries_ids.length > 2 ? (
                <>
                  {companyData.industries_ids.map(
                    (id: number, index: number) => {
                      if (index < 2) {
                        const industry = taxonomy(id, "industries");
                        return (
                          <li className="inline-block" key={index}>
                            <IndustryTag
                              title={
                                locale == "en"
                                  ? industry?.text_en
                                  : industry?.text
                              }
                            />
                            {Number(
                              companyData.industries_ids?.slice(0, 2)?.length,
                            ) >
                              index + 1 && <span className="mx-2">-</span>}
                          </li>
                        );
                      }
                    },
                  )}{" "}
                  <li className="inline-block">
                    <IndustryTag
                      quantity={companyData.industries_ids.length - 2}
                    />
                  </li>
                </>
              ) : (
                companyData.industries_ids.map((id: number, index: number) => {
                  const industry = taxonomy(id, "industries");
                  return (
                    <li className="ml-2 inline-block first:ml-0" key={index}>
                      <IndustryTag
                        title={
                          locale == "en" ? industry?.text_en : industry?.text
                        }
                      />
                      {Number(companyData.industries_ids?.length) > index + 1 &&
                        " - "}
                    </li>
                  );
                })
              )}
            </ul>
          )}
          {companyData.skills_ids.length > 0 && (
            <div className="mt-3 line-clamp-1 text-gray-dark">
              {companyData.skills_ids.slice(0, 6).map((skillItem, index) => {
                return <SkillTagTaxonomy skillId={skillItem} key={index} />;
              })}
            </div>
          )}
          {companyData.num_job_openings > 0 && (
            <div className="mt-2 text-right">
              <JobOpeningsButton
                companyId={companyData.id}
                companySlug={companyData.slug}
                numberJobs={companyData.num_job_openings}
              />
            </div>
          )}
        </div>
        <span
          className="absolute right-4 top-4 z-10 inline-block select-none text-2xl text-gray-dark"
          onClick={(event) => event.preventDefault()}
        >
          <FollowCompanyButton
            companyName={companyData.display_name}
            active={companyData.is_followed}
            companyId={companyData.id}
          />
        </span>
      </div>
    </Link>
  );
};
export default CardCompanyListMobile;
