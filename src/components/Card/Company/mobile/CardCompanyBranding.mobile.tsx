import React, { <PERSON> } from "react";
import Image from "next/image";
import { Link } from "@/navigation";
import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";
import { CompanyType } from "@/types/company";
import { BRAND_LOGO } from "@/utils/image";
import { isDesktop } from "react-device-detect";

//Job Openings Button
const JobOpeningsButton = dynamic(
  () => import("@/components/Button/JobOpeningsButton"),
);

//Follow Job Button
const FollowCompanyButton = dynamic(
  () => import("@/components/Button/FollowCompanyButton"),
);

interface Props {
  companyData: CompanyType;
}

const CardCompanyBrandingMobile: FC<Props> = ({ companyData }) => {
  const t = useTranslations();
  const name = companyData.display_name ?? "";

  return (
    <div className="company-card-branding-mobile group cursor-pointer">
      <div className="company-card-branding-mobile-cover block rounded transition-all group-hover:shadow-md">
        <Image
          src={companyData.image_cover ?? ""}
          alt={name}
          width={BRAND_LOGO.company.image_cover.small_branding.with}
          height={BRAND_LOGO.company.image_cover.small_branding.height}
          loading="lazy"
          className="min-h-[168px] object-contain"
        />
      </div>
      <div className="company-card-branding-mobile-info relative z-10 mx-auto -mt-20 flex min-h-[148px] max-w-[305px] flex-wrap rounded bg-white p-2 shadow-md transition-all group-hover:shadow-lg">
        <div className="company-card-branding-mobile-info-logo flex flex-initial basis-1/4 items-center justify-items-center">
          <Link
            href={{
              pathname: "/companies/[slug]",
              params: { slug: companyData.slug + "-" + companyData.id },
            }}
          >
            <Image
              src={companyData.image_logo ?? ""}
              alt={name}
              width={BRAND_LOGO.company.logo.small.with}
              height={BRAND_LOGO.company.logo.small.height}
              loading="lazy"
              className="object-contain"
            />
          </Link>
        </div>
        <div className="company-card-branding-mobile-info-content flex-initial basis-4/6 pl-2">
          <Link
            href={{
              pathname: "/companies/[slug]",
              params: { slug: companyData.slug + "-" + companyData.id },
            }}
          >
            <h3 className="line-clamp-2 text-sm font-bold transition-all group-hover:text-red-500">
              {name}
            </h3>
            {!!companyData.tagline && (
              <p className="tag-line mt-1 line-clamp-1 text-sm">
                {companyData.tagline}
              </p>
            )}
          </Link>
        </div>
        <div className="company-card-branding-mobile-info-bookmark flex-initial basis-5 text-right">
          <span className="inline-block select-none text-2xl text-gray-dark">
            <FollowCompanyButton
              companyId={companyData.id}
              companyName={companyData.display_name}
              active={companyData.is_followed}
            />
          </span>
        </div>
        <div className="company-card-branding-mobile-info-bottom mt-2 flex-initial basis-full">
          {!!companyData.description_str && (
            <div className="description line-clamp-2 text-xs text-gray-300">
              {companyData.description_str}
            </div>
          )}
          <div className="mt-2 text-right">
            {companyData.num_job_openings ? (
              <JobOpeningsButton
                companyId={companyData.id}
                companySlug={companyData.slug}
                numberJobs={companyData.num_job_openings}
              />
            ) : (
              <Link
                className="font-medium text-primary-300"
                target={isDesktop ? "" : "_blank"}
                href={{
                  pathname: "/companies/[slug]",
                  params: { slug: companyData.slug + "-" + companyData.id },
                }}
                title={t("home_view_more")}
              >
                {t("home_view_more")}
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
export default CardCompanyBrandingMobile;
