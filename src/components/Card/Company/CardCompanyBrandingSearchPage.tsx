"use client";

import SkillTagLoading from "@/components/Loading/SkillTagLoading";
import { Link } from "@/navigation";
import { CompanyType } from "@/types/company";
import { Lang } from "@/types/page";
import { TaxonomiesType } from "@/types/taxonomy";
import { BRAND_LOGO } from "@/utils/image";
import useTaxonomy from "@/utils/taxonomies";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import { FC } from "react";
const SkillTagTaxonomy = dynamic(
  () => import("@/components/Tag/SkillTagTaxonomy"),
  {
    loading: () => <SkillTagLoading />,
    ssr: false,
  },
);

//Button jobs open
const JobOpeningsButton = dynamic(
  () => import("@/components/Button/JobOpeningsButton"),
);

//Follow Company Button
const FollowCompanyButton = dynamic(
  () => import("@/components/Button/FollowCompanyButton"),
);

interface Props {
  companyData: CompanyType;
  taxonomies: TaxonomiesType;
  locale: Lang;
}

const CardCompanyBrandingSearchPage: FC<Props> = ({
  companyData,
  taxonomies,
  locale,
}) => {
  const name = companyData.display_name ?? "";
  const taxonomy = useTaxonomy();
  const t = useTranslations();

  return (
    <Link
      href={{
        pathname: "/companies/[slug]",
        params: { slug: companyData.slug + "-" + companyData.id },
        query: { src: "topdev_search", medium: "highlight" },
      }}
    >
      <div className="company-card-branding-search-page group min-h-[278px] max-w-[404px] overflow-hidden rounded bg-white shadow-sm hover:shadow-md">
        <div className="company-card-branding-search-page-cover overflow-hidde block rounded transition-all">
          <Image
            src={
              companyData.image_cover ??
              "https://cdn.topdev.vn/v4/assets/images/company/cover_8.webp"
            }
            alt={name}
            width={BRAND_LOGO.company.image_cover.small_grid.with}
            height={BRAND_LOGO.company.image_cover.small_grid.height}
            loading="lazy"
            className="inline-block h-[150px] w-full max-w-[404px] rounded-tl rounded-tr object-cover object-center"
          />
        </div>
        <div className="company-card-branding-search-page-info relative z-10 rounded p-4 pt-0 transition-all">
          <div className="company-card-branding-search-page-info-logo relative mt-[-2rem] inline-block overflow-hidden rounded bg-white shadow-md">
            <Link
              href={{
                pathname: "/companies/[slug]",
                params: { slug: companyData.slug + "-" + companyData.id },
                query: { src: "topdev_search", medium: "highlight" },
              }}
            >
              <Image
                src={companyData.image_logo ?? ""}
                alt={name}
                width={BRAND_LOGO.medium.width}
                height={BRAND_LOGO.medium.height}
                loading="lazy"
                className="inline-block h-[70px] w-[120px] bg-white object-contain p-2"
              />
            </Link>
          </div>
          <div className="company-card-branding-search-page-info-content mt-2.5">
            <Link
              href={{
                pathname: "/companies/[slug]",
                params: { slug: companyData.slug + "-" + companyData.id },
                query: { src: "topdev_search", medium: "highlight" },
              }}
            >
              <h3
                title={name}
                className="text-gray line-clamp-1 text-lg font-bold transition-all hover:text-primary-300"
              >
                {name}
              </h3>
              {!!companyData.tagline && (
                <p className="tag-line text-gray mt-1 line-clamp-1 text-base">
                  {companyData.tagline}
                </p>
              )}
            </Link>
            <ul className="mt-2 list-disc pl-5 text-base capitalize text-gray-500">
              {(!!companyData.addresses || !!companyData.num_employees) && (
                <>
                  <li className="mt-1 first:mt-0">
                    {companyData.addresses &&
                    companyData.addresses.address_region_array.length == 1
                      ? companyData.addresses.sort_addresses
                      : companyData.addresses.address_region_array[0] +
                        ` (+${
                          companyData.addresses.address_region_array.length - 1
                        })`}
                  </li>
                  {!!companyData.num_employees &&
                    taxonomy(companyData.num_employees, "num_employees") && (
                      <li className="mt-1 first:mt-0">
                        {locale == "en"
                          ? taxonomy(companyData.num_employees, "num_employees")
                              ?.text_en
                          : taxonomy(companyData.num_employees, "num_employees")
                              ?.text}{" "}
                        {t("company_num_employees_text")}
                      </li>
                    )}
                </>
              )}
              {companyData.industries_ids?.length > 0 && (
                <li className="mt-1 first:mt-0">
                  <div className="line-clamp-1">
                    <span>
                      {companyData.industries_ids.map(
                        (industryId: number, index: number) => {
                          if (index < 2) {
                            const industry = taxonomy(industryId, "industries");
                            const industryName =
                              locale === "en"
                                ? industry?.text_en
                                : industry?.text;

                            return `${industryName}${
                              companyData.industries_ids.length - 1 > index
                                ? ", "
                                : ""
                            }`;
                          }
                        },
                      )}
                    </span>
                    {companyData.industries_ids.length > 2 ? (
                      <span> (+{companyData.industries_ids.length - 2})</span>
                    ) : null}
                  </div>
                </li>
              )}
            </ul>
            {companyData.skills_ids && companyData.skills_ids.length > 0 && (
              <div className="card-company-list-info-list-skills mt-2 line-clamp-1 items-center gap-2 text-base capitalize text-gray-dark">
                {companyData.skills_ids
                  .slice(0, 6)
                  .map((value, index: number) => {
                    return <SkillTagTaxonomy skillId={value} key={index} />;
                  })}
              </div>
            )}
            {!!companyData.num_job_openings && (
              <div className="mt-4 text-center">
                <JobOpeningsButton
                  companyId={companyData.id}
                  companySlug={companyData.slug}
                  numberJobs={companyData.num_job_openings}
                  mediumPage={"highlight"}
                />
              </div>
            )}
          </div>
          <span
            className="absolute right-4 top-4 inline-block select-none text-2xl text-gray-dark"
            onClick={(event) => event.preventDefault()}
          >
            <FollowCompanyButton
              companyName={companyData.display_name}
              active={companyData.is_followed}
              companyId={companyData.id}
            />
          </span>
        </div>
      </div>
    </Link>
  );
};
export default CardCompanyBrandingSearchPage;
