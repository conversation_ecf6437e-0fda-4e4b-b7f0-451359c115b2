import FollowCompanyButton from "@/components/Button/FollowCompanyButton";
import { Link } from "@/navigation";
import { CompanyType } from "@/types/company";
import { BRAND_LOGO } from "@/utils/image";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import { FC } from "react";
import { isDesktop } from "react-device-detect";
import { RiArrowRightSLine } from "react-icons/ri";

const JobOpeningsButton = dynamic(
  () => import("@/components/Button/JobOpeningsButton"),
);

interface Props {
  companyData: CompanyType;
  srcPage?: string;
}

const KICC_COMPANY_ID = 83771;

const CardCompanyBranding: FC<Props> = ({ companyData, srcPage }) => {
  const t = useTranslations();
  const name = companyData.display_name ?? "";
  const isKICC = companyData.id === KICC_COMPANY_ID;
  const linkTarget = isDesktop && !isKICC ? "" : "_blank";
  const KICC_LINK = `${t("slug_group")}/korean-it-companies-${KICC_COMPANY_ID}`;
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;

  return (
    <div className="company-card-branding group relative cursor-pointer">
      <div className="company-card-branding-cover block overflow-hidden rounded text-center transition-all group-hover:shadow-lg">
        <Link
          href={{
            pathname: "/companies/[slug]",
            params: {
              slug: isKICC
                ? KICC_LINK
                : companyData.slug + "-" + companyData.id,
            },
            query: { src: srcPageQueryParam, medium: "superspotlight" },
          }}
          target={linkTarget}
        >
          <Image
            src={companyData.image_cover ?? ""}
            alt={name}
            width={
              isDesktop
                ? BRAND_LOGO.company.image_cover.large.with
                : BRAND_LOGO.company.image_cover.small_branding.with
            }
            height={
              isDesktop
                ? BRAND_LOGO.company.image_cover.large.height
                : BRAND_LOGO.company.image_cover.small_branding.height
            }
            className={`${
              isDesktop ? "h-[190px] lg:h-[250px]" : "h-[168px] lg:h-[168px]"
            } inline-block w-full max-w-full rounded object-cover`}
            priority
          />
        </Link>
      </div>
      <Link
        href={{
          pathname: "/companies/[slug]",
          params: {
            slug: isKICC ? KICC_LINK : companyData.slug + "-" + companyData.id,
          },
          query: { src: srcPageQueryParam, medium: "superspotlight" },
        }}
        target={linkTarget}
      >
        <div className="company-card-branding-info absolute bottom-0 z-10 mx-2.5 min-h-[136px] translate-y-1/2 flex-row rounded bg-white p-4 shadow-sm transition-all group-hover:shadow-lg lg:mx-6 lg:shadow-lg">
          <div className={"flex items-start gap-2"}>
            <div
              className={
                "company-card-branding-info-logo flex-none bg-white p-2"
              }
            >
              <Link
                href={{
                  pathname: "/companies/[slug]",
                  params: {
                    slug: isKICC
                      ? KICC_LINK
                      : companyData.slug + "-" + companyData.id,
                  },
                  query: { src: srcPageQueryParam, medium: "superspotlight" },
                }}
                target={linkTarget}
              >
                <Image
                  src={companyData.image_logo ?? ""}
                  alt={name}
                  width={
                    isDesktop
                      ? BRAND_LOGO.company.logo.large.with
                      : BRAND_LOGO.company.logo.small.with
                  }
                  height={
                    isDesktop
                      ? BRAND_LOGO.company.logo.large.height
                      : BRAND_LOGO.company.logo.small.height
                  }
                  loading="lazy"
                  className={`${isDesktop ? "w-[160px]" : "w-[72px]"} ${
                    isDesktop ? "h-[112px]" : "h-[50px]"
                  } object-contain`}
                />
              </Link>
            </div>

            <div className="company-card-branding-info-content flex grow flex-col gap-1 lg:pl-8 lg:pr-4">
              <Link
                target={linkTarget}
                href={{
                  pathname: "/companies/[slug]",
                  params: {
                    slug: isKICC
                      ? KICC_LINK
                      : companyData.slug + "-" + companyData.id,
                  },
                  query: { src: srcPageQueryParam, medium: "superspotlight" },
                }}
              >
                <h3 className="card-title line-clamp-2 text-sm font-bold transition-all lg:line-clamp-1 lg:text-lg">
                  {name}
                </h3>
              </Link>

              <Link
                target={linkTarget}
                href={{
                  pathname: "/companies/[slug]",
                  params: {
                    slug: isKICC
                      ? KICC_LINK
                      : companyData.slug + "-" + companyData.id,
                  },
                  query: { src: srcPageQueryParam, medium: "superspotlight" },
                }}
              >
                <p className="tag-line line-clamp-1 min-h-[1.25rem] text-sm lg:my-1 lg:text-lg">
                  {companyData.tagline}
                </p>
              </Link>

              <div className={"hidden lg:block"}>
                {!!companyData.description_str && (
                  <Link
                    target={linkTarget}
                    href={{
                      pathname: "/companies/[slug]",
                      params: {
                        slug: isKICC
                          ? KICC_LINK
                          : companyData.slug + "-" + companyData.id,
                      },
                      query: {
                        src: srcPageQueryParam,
                        medium: "superspotlight",
                      },
                    }}
                  >
                    <p className="description line-clamp-2 text-base text-gray-500">
                      {companyData.description_str}
                    </p>
                  </Link>
                )}
              </div>

              <div className="mt-2 hidden text-right lg:block">
                {companyData.num_job_openings > 0 ? (
                  <JobOpeningsButton
                    companySlug={companyData.slug}
                    companyId={companyData.id}
                    numberJobs={companyData.num_job_openings}
                    srcPage={srcPage}
                    mediumPage={"superspotlight"}
                  />
                ) : (
                  <>
                    <Link
                      className="text-sm font-medium text-primary-300 underline"
                      target={linkTarget}
                      href={{
                        pathname: "/companies/[slug]",
                        params: {
                          slug: isKICC
                            ? KICC_LINK
                            : companyData.slug + "-" + companyData.id,
                        },
                        query: {
                          src: srcPageQueryParam,
                          medium: "superspotlight",
                        },
                      }}
                      title={t("home_view_more")}
                    >
                      {t("home_view_more")}
                    </Link>
                  </>
                )}
              </div>
            </div>
            <div className="company-card-branding-info-bookmark flex-none">
              <span
                className="inline-block select-none text-xl text-gray-dark lg:text-2xl"
                onClick={(e) => e.stopPropagation()}
              >
                <FollowCompanyButton
                  companyId={companyData.id as number}
                  companyName={companyData.display_name}
                  active={companyData.is_followed || false}
                  size={isDesktop ? "md" : "sm"}
                />
              </span>
            </div>
          </div>
          <div className={"flex flex-col lg:hidden"}>
            {!!companyData.description_str && (
              <Link
                target={linkTarget}
                href={{
                  pathname: "/companies/[slug]",
                  params: {
                    slug: isKICC
                      ? KICC_LINK
                      : companyData.slug + "-" + companyData.id,
                  },
                  query: { src: srcPageQueryParam, medium: "superspotlight" },
                }}
              >
                <p className="description line-clamp-1 text-xs text-gray-300">
                  {companyData.description_str}
                </p>
              </Link>
            )}

            <div className="mt-0 text-right lg:mt-2">
              {!!companyData.num_job_openings ? (
                <JobOpeningsButton
                  companySlug={companyData.slug}
                  companyId={companyData.id}
                  numberJobs={companyData.num_job_openings}
                />
              ) : (
                <>
                  <Link
                    className="inline-flex gap-1 text-xs text-primary-300 underline lg:text-base lg:font-medium"
                    target={linkTarget}
                    href={{
                      pathname: "/companies/[slug]",
                      params: {
                        slug: isKICC
                          ? KICC_LINK
                          : companyData.slug + "-" + companyData.id,
                      },
                      query: {
                        src: srcPageQueryParam,
                        medium: "superspotlight",
                      },
                    }}
                    title={t("home_view_more")}
                  >
                    {t("home_view_more")}
                    <RiArrowRightSLine className="ml-2 inline-block" />
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};
export default CardCompanyBranding;
