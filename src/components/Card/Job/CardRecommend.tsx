// src/components/Card/Job/CardRecommend.tsx
import Follow<PERSON><PERSON><PERSON><PERSON>on from "@/components/Button/FollowJobButton";
import SalaryLoading from "@/components/Loading/SalaryLoading";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import { JobType } from "@/types/job";
import dynamic from "next/dynamic";
import { Link } from "@/navigation";
import { FC } from "react";
import Image from "next/image";
import { BRAND_LOGO } from "@/utils/image";
import { PiDotOutlineFill } from "react-icons/pi";
import { useTranslations } from "next-intl";

const SalaryInfo = dynamic(() => import("./SalaryInfo"), {
  ssr: false,
  loading: () => <SalaryLoading />,
});

interface CardRecommendProps {
  job: JobType;
  device?: string;
  srcPage?: string;
  mediumPage?: string;
}

const CardRecommend: FC<CardRecommendProps> = ({
  job,
  device = "desktop",
  srcPage = "detailcompany",
  mediumPage = "openingjobs",
}) => {
  const srcPageQueryParam = `topdev_${srcPage}`;
  const t = useTranslations();

  if (device === "mobile") {
    return (
      <div
        className="rounded border border-solid border-gray-200 bg-white p-4 transition-all hover:shadow-sm">
        <div className="flex items-start gap-2">
          <div className="job-details w-full">
            <div className="flex items-start align-middle gap-2">
              <div className="flex-1">
                <h3 className="line-clamp-2 text-sm font-bold">
                  <Link href={{
                    pathname: "/detail-jobs/[slug]",
                    params: { slug: job.slug + "-" + job.id },
                    query: { src: srcPageQueryParam, medium: mediumPage },
                  }} className="transition-all hover:text-primary text-base">
                    {job.title}
                  </Link>
                </h3>
              </div>
              <div>
                <FollowJobButton jobId={job?.id ?? 0}
                                 active={job?.is_followed ?? false}
                                 jobTitle={job?.title ?? ""} />
              </div>
            </div>
            <div className="line-clamp-1 flex align-middle items-start gap-1">
              <Link href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job?.slug + "-" + job.id },
                query: { src: srcPageQueryParam, medium: mediumPage },
              }} className="block" target="_blank">
                {job.company.image_logo && (
                  <Image src={job.company.image_logo}
                         alt={job.company?.display_name}
                         width={BRAND_LOGO.large.width}
                         height={BRAND_LOGO.large.height}
                         className="h-8 w-14 max-w-full rounded-xl bg-white object-contain p-2"
                         loading="lazy" />
                )}
              </Link>
              <div className="h-8 flex items-center">
                <Link href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job?.slug + "-" + job.id },
                  query: { src: srcPageQueryParam, medium: mediumPage },
                }} className="text-gray-400 transition-all hover:text-primary">
                  {job.company?.display_name}
                </Link>
              </div>
            </div>
            <div className="mt-1">
              <div className="text-primary">
                <SalaryInfo value={job.salary.value} />
              </div>
            </div>
            <div className="mt-1">
              <div className="text-gray-400">
                {job.addresses && job.addresses.address_region_array.length == 1
                  ? job.addresses.sort_addresses
                  : job.addresses.address_region_array[0] +
                  ` (+${job.addresses.address_region_array.length - 1})`}{" "} {job.job_types_str && (
                <span className="ml-2">({job.job_types_str})</span>
              )}
              </div>
            </div>
            <ul className="mt-2 flex items-center justify-between">
              <div className="line-clamp-1">
                {job.skills_ids.map((skillItem, index) => {
                  return (
                    <span key={index} className="mr-2">
                    <SkillTagTaxonomy skillId={skillItem} srcPage={srcPage} />
                  </span>
                  );
                })}
              </div>
            </ul>
            <div className="mt-1 text-right">
              <p className="whitespace-nowrap text-sm text-gray-400">
                {t("search_page_posted")} {job.refreshed.since}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="rounded border border-solid border-gray-200 bg-white p-4 transition-all hover:shadow-sm">
      <div className="flex items-start gap-2">
        <div className="cpmpany-logo">
          <Link href={{
            pathname: "/detail-jobs/[slug]",
            params: { slug: job?.slug + "-" + job.id },
            query: { src: srcPageQueryParam, medium: mediumPage },
          }} className="block h-[4rem] w-[7rem]" target="_blank">
            {job.company.image_logo && (
              <Image src={job.company.image_logo}
                     alt={job.company?.display_name}
                     width={BRAND_LOGO.large.width} height={BRAND_LOGO.large.height}
                     className="h-24 w-35 max-w-full rounded-xl bg-white object-contain p-2"
                     loading="lazy" />
            )}
          </Link>
        </div>
        <div className="job-details w-full">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <h3 className="line-clamp-1 text-sm font-bold lg:text-base">
                <Link href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job.slug + "-" + job.id },
                  query: { src: srcPageQueryParam, medium: mediumPage },
                }} className="transition-all hover:text-primary">
                  {job.title}
                </Link>
              </h3>
            </div>
            <div>
              <FollowJobButton jobId={job?.id ?? 0}
                               active={job?.is_followed ?? false}
                               jobTitle={job?.title ?? ""} />
            </div>
          </div>
          <div className="line-clamp-1">
            <Link href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job?.slug + "-" + job.id },
              query: { src: srcPageQueryParam, medium: mediumPage },
            }} className="text-gray-400 transition-all hover:text-primary">
              {job.company?.display_name}
            </Link>
          </div>
          <div className="mt-1 flex items-center justify-start gap-5">
            <div className="text-primary">
              <SalaryInfo value={job.salary.value} />
            </div>
            <div className="text-gray-600">
              <PiDotOutlineFill />
            </div>
            <div className="text-gray-400">
              {job.addresses && job.addresses.address_region_array.length == 1
                ? job.addresses.sort_addresses
                : job.addresses.address_region_array[0] +
                ` (+${job.addresses.address_region_array.length - 1})`} {job.job_types_str &&
              <span className="ml-2">({job.job_types_str})</span>}
            </div>
          </div>
          <ul className="mt-2 flex items-center justify-between">
            <div className="line-clamp-1">
              {job.skills_ids.map((skillItem, index) => {
                return (
                  <span key={index} className="mr-2">
                    <SkillTagTaxonomy skillId={skillItem} srcPage={srcPage} />
                  </span>
                );
              })}
            </div>
            <p className="whitespace-nowrap text-sm text-gray-400">
              {t("search_page_posted")} {job.refreshed.since}
            </p>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CardRecommend;
