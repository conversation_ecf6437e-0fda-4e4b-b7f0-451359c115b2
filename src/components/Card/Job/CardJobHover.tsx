import { FollowButton } from "@/components/Button";
import ChipTag from "@/components/Tag/ChipTag";
import { JobType } from "@/types/job";
import { BRAND_LOGO } from "@/utils/image";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";
import { HiChevronRight } from "react-icons/hi2";

interface Props {
  job: JobType;
}

const CardJobHover: FC<Props> = ({ job }) => {
  return (
    <div className="relative w-full max-w-lg rounded-xl border border-solid border-gray-300 bg-white p-6 shadow-md">
      <div className="flex items-start gap-2">
        <Link
          href={{
            pathname: "/companies/[slug]",
            params: { slug: job?.company?.slug + "-" + job?.company?.id },
          }}
          className="inline-block"
          target="_blank"
        >
          {job.company.image_logo && (
            <Image
              width={BRAND_LOGO.medium.width}
              height={BRAND_LOGO.medium.height}
              className="h-[70px] w-[100px] rounded-xl object-contain"
              src={job.company.image_logo}
              alt="Company name"
              loading="lazy"
            />
          )}
        </Link>
        <div className="flex-1">
          <h3 className="line-clamp-2 font-bold">
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
              }}
              target="_blank"
            >
              {job.title}
            </Link>
          </h3>
          <div className="mt-1 line-clamp-1">
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job?.company?.slug + "-" + job?.company?.id },
              }}
              className="font-bold text-primary"
              target="_blank"
            >
              {job.company.display_name}
            </Link>
          </div>
          <p className="mt-1 line-clamp-1 text-gray-500">
            {job.addresses.sort_addresses}
          </p>
          <p className="mt-1 line-clamp-1 text-gray-500">{job.salary.value}</p>
        </div>
        <div className="">
          <FollowButton isActive={false} />
        </div>
      </div>
      <hr className="my-4" />
      <div className="flex">
        <div className="flex-1">
          <p className="font-bold">Years of experience</p>
          <p className="text-gray-500">Information not provided</p>
        </div>
        <div className="flex-1">
          <p className="font-bold">Job Type</p>
          <p className="text-gray-500">Hybrid</p>
        </div>
      </div>
      <div className="mt-4">
        <p className="font-bold">Skills</p>
        <div className="flex flex-wrap gap-2">
          <div className="line-clamp-none">
            {Array.from({ length: 8 }).map((item, index) => {
              return (
                <a key={index} className="mr-2 mt-2 inline-block">
                  <ChipTag title="Javascript" size="sm" />
                </a>
              );
            })}
          </div>
        </div>
      </div>
      <div className="mt-4 h-[12.5rem] overflow-y-hidden rounded bg-gray-100 p-4">
        <div className="h-full overflow-y-auto pr-2 scrollbar scrollbar-track-gray-200 scrollbar-thumb-primary scrollbar-track-rounded scrollbar-thumb-rounded scrollbar-w-1">
          <div>
            <p className="font-bold">Your role & responsibilities</p>
            <ul className="ml-5 list-disc">
              <li>
                Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Architecto, doloribus.
              </li>
              <li>
                Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Architecto, doloribus.
              </li>
              <li>
                Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Architecto, doloribus.
              </li>
            </ul>
          </div>
          <div className="mt-2">
            <p className="font-bold">Job benefit</p>
            <ul className="ml-5 list-disc">
              <li>
                Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Architecto, doloribus.
              </li>
              <li>
                Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Architecto, doloribus.
              </li>
              <li>
                Lorem ipsum dolor, sit amet consectetur adipisicing elit.
                Architecto, doloribus.
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div className="mt-4 text-center">
        <a className="inline-flex items-center gap-2 font-semibold text-primary">
          <span className="underline">View more</span>
          <HiChevronRight />
        </a>
      </div>
    </div>
  );
};

export default CardJobHover;
