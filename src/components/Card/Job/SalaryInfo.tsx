"use client";
import { useAppSelector } from "@/store";
import { classNames, openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Link from "next/link";
import React, { FC, HTMLAttributes } from "react";
import { forceLogin, isRunningInApp } from "@/services/connector";

interface SalaryInfoType {
  value: string | null;
  containerClass?: string;
  loginClass?: string;
  srcPage?: string;
  screenView?: string;
}

const SalaryInfo: FC<SalaryInfoType> = ({
  value,
  containerClass,
  loginClass,
  srcPage,
  screenView,
}) => {
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);
  const t = useTranslations();

  return (
    <p className={containerClass}>
      {isLoggedIn ? (
        value ? (
          value
        ) : (
          t("detail_job_negotiable")
        )
      ) : (
        <span
          onClick={(e) => {
            e.preventDefault();
            screenView === "mobile" && isRunningInApp()
              ? forceLogin()
              : openLoginPopup();
          }}
          className={classNames(
            "cursor-pointer transition-all hover:underline",
            loginClass ?? "text-primary",
          )}
        >
          {t("search_page_login_to_view_salary")}
        </span>
      )}
    </p>
  );
};

export default SalaryInfo;
