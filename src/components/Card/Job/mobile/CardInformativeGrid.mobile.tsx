import { FollowButton } from "@/components/Button";
import SalaryLoading from "@/components/Loading/SalaryLoading";
import SkillTagLoading from "@/components/Loading/SkillTagLoading";
import { JobType } from "@/types/job";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";
const SalaryInfo = dynamic(() => import("../SalaryInfo"), {
  ssr: false,
  loading: () => <SalaryLoading size="sm" />,
});
const SkillTagTaxonomy = dynamic(
  () => import("@/components/Tag/SkillTagTaxonomy"),
  {
    ssr: false,
    loading: () => <SkillTagLoading size="xs" />,
  },
);
interface CardInformativeGridType {
  outline?: boolean;
  highlight?: boolean;
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}

const CardInformativeGridMobile: FC<CardInformativeGridType> = (props) => {
  const {
    outline = false,
    highlight = false,
    job ,
    srcPage = "home",
    mediumPage = "popularcompanies",
  } = props;
  const t = useTranslations();

  return (
    <div
      className={classNames(
        "rounded border border-solid bg-white p-4",
        outline ? "border-gray-200" : " border-transparent",
      )}
    >
      <div className="flex items-start justify-between just">
        <Link
          href={{
            pathname: "/detail-jobs/[slug]",
            params: { slug: job.slug + "-" + job.id },
            query: { src: "topdev_" + srcPage, medium: mediumPage, view: "app" },
          }}
          target="_blank"
        >
          {job.company.image_logo && (
            <Image
              src={job.company.image_logo}
              width={72}
              height={50}
              className="h-[66px] w-[88px] max-w-full bg-white object-contain p-2"
              alt={job.company.display_name}
              loading="lazy"
            />
          )}
        </Link>
        <div>
          <FollowButton isActive={false} size="sm" screenView="mobile" />
        </div>
      </div>
      <div className="mt-2">
        <h3 className={classNames("line-clamp-2 h-9 text-sm font-bold")}>
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id },
              query: { src: "topdev_" + srcPage, medium: mediumPage, view: "app" },
            }}
            target="_blank"
          >
            {job.title}
          </Link>
        </h3>
        <div className={classNames("mt-1 line-clamp-1 text-sm text-gray-500")}>
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id },
              query: { src: "topdev_" + srcPage, medium: mediumPage, view: "app" },
            }}
            target="_blank"
          >
            {job.company.display_name}
          </Link>
        </div>
        <div className="text-xs h-9">
          {!highlight && (
            <>
              <div className="mt-2 line-clamp-1 text-primary-300">
                <SalaryInfo value={job.salary.value} screenView={"mobile"}/>
              </div>
              <p className="text-gray-500 line-clamp-2">
                {job.addresses && job.addresses.address_region_array.length == 1
                  ? job.addresses.sort_addresses
                  : job.addresses.address_region_array[0] +
                    ` (+${job.addresses.address_region_array.length - 1})`}
              </p>
            </>
          )}
        </div>
        <div className="mt-2">
          <div className="line-clamp-1">
            {job.skills_ids.map((skillItem, index) => {
              return <SkillTagTaxonomy skillId={skillItem} key={index} />;
            })}
          </div>
        </div>
        <p className="mt-2 text-xs text-right text-gray-400">
          {t("search_page_posted")} {job.refreshed.since}
        </p>
      </div>
    </div>
  );
};

export default CardInformativeGridMobile;
