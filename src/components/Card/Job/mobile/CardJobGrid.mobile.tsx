import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import { JobType } from "@/types/job";
import { BRAND_LOGO } from "@/utils/image";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";

interface Props {
  job: JobType;
}

const CardJobGridMobile: FC<Props> = (props) => {
  const { job } = props;
  return (
    <div className="block rounded border border-solid border-white bg-white p-4">
      <div className="flex items-center justify-center">
        <Link
          href={{
            pathname: "/detail-jobs/[slug]",
            params: { slug: job.slug + "-" + job.id , view: "app" } as any,
          }}
          target="_blank"
        >
          {job.company.image_logo && (
            <Image
              src={job.company.image_logo}
              alt={"Company name"}
              width={BRAND_LOGO.small.width}
              height={BRAND_LOGO.small.height}
              className="mx-auto h-[66px] w-[88px] max-w-full bg-white object-contain p-2"
              loading="lazy"
            />
          )}
        </Link>
      </div>
      <div className="mt-4 text-center">
        <div className="line-clamp-1 text-sm text-gray-500">
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id, view: "app" } as any,
            }}
            target="_blank"
          >
            {job.company.display_name}
          </Link>
        </div>
        <h2 className="mt-2 line-clamp-1 text-sm font-bold">
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id, view: "app" } as any,
            }}
            target="_blank"
          >
            {job.title}
          </Link>
        </h2>
        <div className="line-clamp-2">
          {job.skills_ids.map((skillItem, index) => {
            return <SkillTagTaxonomy skillId={skillItem} key={index} />;
          })}
        </div>
      </div>
    </div>
  );
};

export default CardJobGridMobile;
