import FollowJobButton from "@/components/Button/FollowJobButton";
import SalaryLoading from "@/components/Loading/SalaryLoading";
import SkillTagLoading from "@/components/Loading/SkillTagLoading";
import HotJobTag from "@/components/Tag/HotJobTag";
import { Link } from "@/navigation";
import { JobType } from "@/types/job";
import { classNames } from "@/utils";
import { BRAND_LOGO } from "@/utils/image";
import { isHotLabel } from "@/utils/jobDetection";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import { FC } from "react";
import { extractItems } from "../CardInformativeList";
const SkillTagTaxonomy = dynamic(
  () => import("@/components/Tag/SkillTagTaxonomy"),
  {
    ssr: false,
    loading: () => <SkillTagLoading size="xs" />,
  },
);
const SalaryInfo = dynamic(() => import("../SalaryInfo"), {
  ssr: false,
  loading: () => <SalaryLoading size="sm" />,
});

interface CardInformativeListType {
  job: JobType;
}

const CardInformativeListMobile: FC<CardInformativeListType> = (props) => {
  const { job } = props;
  const t = useTranslations();
  const isValidBenefitsOriginal = job?.benefits_original?.[0]?.value ?? false;
  const isEmptyBenefitsJob =
    job?.benefits?.length === 0 && !isValidBenefitsOriginal;
  const benefitCompany = job?.company?.benefits
    ? extractItems(job.company.benefits)
    : [];
  const extractList = isValidBenefitsOriginal
    ? job?.benefits_original
    : job?.benefits;
  const benefitJob = !isEmptyBenefitsJob
    ? extractItems(extractList as any[])
    : [];
  return (
    <Link
      href={{
        pathname: "/detail-jobs/[slug]",
        params: { slug: job.slug + "-" + job.id },
        query: { src: "topdev_search", medium: "searchresult", view: "app" },
      }}
      target="_blank"
    >
      <div
        className={classNames(
          "relative rounded border border-solid p-4 text-sm",
          job.is_distinction || isHotLabel(job.features)
            ? "border-primary"
            : "border-transparent",
          job.is_distinction ? "bg-primary-100" : "bg-white",
        )}
      >
        <div className="flex items-start justify-between">
          <div>
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: {
                  src: "topdev_search",
                  medium: "searchresult",
                  view: "app",
                },
              }}
              target="_blank"
            >
              {job.company.image_logo && (
                <Image
                  src={job.company.image_logo}
                  width={BRAND_LOGO.medium.width}
                  height={BRAND_LOGO.medium.height}
                  loading="lazy"
                  alt={job.company.display_name}
                  className="h-[66px] w-[88px] max-w-full bg-white object-contain p-2"
                />
              )}
            </Link>
          </div>
          <div>
            <FollowJobButton
              jobId={job.id}
              jobTitle={job.title}
              active={job.is_followed}
              screenView={"mobile"}
            />
          </div>
        </div>
        <div className="relative z-10">
          <h3
            className={classNames(
              "mt-2 line-clamp-2 text-sm font-bold",
              !job.is_basic ? "text-primary" : "hover:text-primary",
            )}
          >
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: {
                  src: "topdev_search",
                  medium: "searchresult",
                  view: "app",
                },
              }}
              target="_blank"
              className="text-base"
            >
              {job.title}
            </Link>
          </h3>
          <div>
            <div className="mt-2 line-clamp-1 font-bold text-gray-500">
              <Link
                href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job.slug + "-" + job.id },
                  query: { view: "app" },
                }}
                target="_blank"
              >
                {job.company.display_name}
              </Link>
            </div>
            <div className="text-primary-300">
              <SalaryInfo value={job.salary.value} screenView={"mobile"} />
            </div>
            <p className="line-clamp-2 text-gray-500">
              {job.addresses && job.addresses.address_region_array.length == 1
                ? job.addresses.sort_addresses
                : job.addresses.address_region_array[0] +
                  ` (+${job.addresses.address_region_array.length - 1})`}
            </p>
          </div>
          {!isEmptyBenefitsJob && job?.is_distinction && (
            <>
              {isValidBenefitsOriginal ? (
                <div className="mt-2">
                  <ul className="ml-6 list-disc text-gray-600">
                    {benefitJob.map((benefitItem, index) => {
                      return (
                        <li key={index}>
                          <p
                            className="line-clamp-1"
                            dangerouslySetInnerHTML={{ __html: benefitItem }}
                          ></p>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              ) : (
                <div className="mt-2">
                  <ul className="ml-6 list-disc text-gray-600">
                    {job.benefits.slice(0, 3).map((benefitItem, index) => {
                      return (
                        <li key={index}>
                          <p className="line-clamp-1">
                            {benefitItem.description ||
                              benefitItem?.value ||
                              benefitItem?.name}
                          </p>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              )}
            </>
          )}

          {isEmptyBenefitsJob &&
            job?.is_distinction &&
            (job.company.benefits.length > 1 ? (
              <div className="mt-2">
                <ul className="ml-6 list-disc text-gray-600">
                  {job.company.benefits
                    .slice(0, 3)
                    .map((benefitItem, index) => {
                      return (
                        <li key={index}>
                          <p className="line-clamp-1">{benefitItem.value}</p>
                        </li>
                      );
                    })}
                </ul>
              </div>
            ) : (
              <div className="mt-2">
                <ul className="ml-6 list-disc text-gray-600">
                  {benefitCompany.map((benefitItem, index) => {
                    return (
                      <li key={index}>
                        <p
                          className="line-clamp-1"
                          dangerouslySetInnerHTML={{ __html: benefitItem }}
                        ></p>
                      </li>
                    );
                  })}
                </ul>
              </div>
            ))}
          <div className="mt-2">
            <div className="line-clamp-1">
              {job.skills_ids.map((skillItem, index) => {
                return <SkillTagTaxonomy skillId={skillItem} key={index} />;
              })}
            </div>
          </div>
          <div className="mt-2 flex items-center justify-end">
            <p className="text-xs text-gray-400">
              {t("search_page_posted")} {job.refreshed.since}
            </p>
          </div>
        </div>
        {isHotLabel(job.features) && (
          <div className="absolute bottom-0 left-0">
            <HotJobTag size="sm" />
          </div>
        )}
      </div>
    </Link>
  );
};

export default CardInformativeListMobile;
