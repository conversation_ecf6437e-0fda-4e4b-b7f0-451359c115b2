import ChipTag from "@/components/Tag/ChipTag";
import { JobType } from "@/types/job";
import { TaxonomiesType } from "@/types/taxonomy";
import { BRAND_LOGO } from "@/utils/image";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";

interface Props {
  job: JobType;
  taxonomies: TaxonomiesType;
}

const CardJobListMobile: FC<Props> = (props) => {
  const { job, taxonomies } = props;
  return (
    <div className="flex gap-2 rounded border border-solid border-white bg-white p-4">
      <Link
        href={{
          pathname: "/detail-jobs/[slug]",
          params: { slug: job.slug + "-" + job.id, view: "app" } as any,
        }}
        className="inline-block"
        target="_blank"
      >
        {job.company.image_logo && (
          <Image
            src={job.company.image_logo}
            alt="Company name"
            width={BRAND_LOGO.small.width}
            height={BRAND_LOGO.small.height}
            className="mx-auto h-[66px] w-[88px] max-w-full bg-white object-contain p-2"
            loading="lazy"
          />
        )}
      </Link>
      <div className="flex-1">
        <h2 className="line-clamp-1 text-sm font-bold">
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id, view: "app" } as any,
            }}
            target="_blank"
          >
            {job.title}
          </Link>
        </h2>
        <div className="line-clamp-1 text-sm text-gray-500">
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id, view: "app" } as any,
            }}
            target="_blank"
          >
            {job.company.display_name}
          </Link>
        </div>
        <div className="mt-2">
          <div className="line-clamp-1">
            {Array.from({ length: 10 }).map((item, index) => {
              return (
                <a href={"#"} key={index} className="mr-2 inline-block">
                  <ChipTag title="Javascript" size="xs" />
                </a>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardJobListMobile;
