import dynamic from "next/dynamic";
import { Link } from "@/navigation";
import { JobType } from "@/types/job";
import SalaryLoading from "@/components/Loading/SalaryLoading";

//Salary Info
const SalaryInfo = dynamic(() => import("@/components/Card/Job/SalaryInfo"), {
  ssr: false,
  loading: () => <SalaryLoading />,
});

//Follow button
const FollowJobButton = dynamic(
  () => import("@/components/Button/FollowJobButton"),
);

//Skill Tag Taxonomy
const SkillTagTaxonomy = dynamic(
  () => import("@/components/Tag/SkillTagTaxonomy"),
);

const CardRecommendMobile = ({ job }: { job: JobType }) => {
  return (
    <div className="p-4 bg-white border border-gray-200 border-solid rounded">
      <div className="flex items-start justify-between gap-2">
        <div className="flex-1">
          <h3 className="text-sm font-bold line-clamp-2">
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: { view: "app" },
              }}
            >
              {job?.title}
            </Link>
          </h3>
        </div>
        <div>
          <FollowJobButton
            jobId={job?.id ?? 0}
            active={job?.is_followed ?? false}
            jobTitle={job?.title ?? ""}
            screenView={"mobile"}
          />
        </div>
      </div>
      <div className="mt-1 text-xs text-primary">
        <SalaryInfo value={job.salary.value} screenView={'mobile'}/>
      </div>
      <div className="mt-2">
        <div className="line-clamp-1">
          {job?.skills_ids.map((item) => {
            return <SkillTagTaxonomy skillId={item} key={item} />;
          })}
        </div>
      </div>
    </div>
  );
};

export default CardRecommendMobile;
