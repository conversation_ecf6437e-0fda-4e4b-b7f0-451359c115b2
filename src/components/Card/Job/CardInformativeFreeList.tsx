import { JobType } from "@/types/job";
import { classNames } from "@/utils";
import { BRAND_LOGO } from "@/utils/image";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";
import FollowJob<PERSON>reeButton from "@/components/Button/FollowJobFreeButton";

interface CardInformativeFreeListType {
  job: JobType;
  hideImg?: boolean;
  isDesktop?: boolean;
}

const stripHTML = (html: string) => {
  const text = html.replace(/<\/?[^>]+>/gi, "");

  if (typeof window !== "undefined") {
    const textarea = document.createElement("textarea");
    textarea.innerHTML = text;
    return textarea.value;
  } else {
    return text
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .replace(/&apos;/g, "'")
      .replace(/&nbsp;/g, " ") // Handle non-breaking space
      .replace(/&#160;/g, " ") // Handle numeric entity for non-breaking space
      .replace(/&#(\d+);/g, (match, num) => String.fromCharCode(num)) // Decimal numeric entities
      .replace(/&#x([0-9A-Fa-f]+);/g, (match, hex) =>
        String.fromCharCode(parseInt(hex, 16)),
      ); // Hexadecimal numeric entities
  }
};
export const extractItems = (benefits: any[]) => {
  const values = benefits.map((item) => item.value).join("\n");
  const regex = /<li[^>]*>(.*?)<\/li>/g;
  const items = [];
  let match;

  while ((match = regex.exec(values)) !== null) {
    items.push(stripHTML(match[1].trim()));
  }

  return items.slice(0, 3);
};
const CardInformativeFreeList: FC<CardInformativeFreeListType> = (props) => {
  const { job, hideImg, isDesktop = false } = props;

  return (
    <Link
      href={{
        pathname: "/detail-jobs/[slug]",
        params: { slug: job?.slug + "-" + job.id },
        query: { src: "topdev_search", medium: "searchresult" },
      }}
      className={classNames(isDesktop ? "w-1/2" : "")}
      target="_blank"
    >
      <div
        className={classNames(
          "relative rounded border border-solid border-transparent bg-white transition-all hover:border-primary-200 hover:bg-primary-100 hover:shadow-md",
        )}
      >
        <div className="flex items-start justify-between gap-4 px-4 py-[0.92rem]">

          <div className="flex-1">
            <h3 className={classNames("line-clamp-2")}>
              <Link
                href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job?.slug + "-" + job.id },
                  query: { src: "topdev_search", medium: "searchresult" },
                }}
                target="_blank"
                className={classNames(
                  "text-[16px] font-bold text-[#292929] transition-all hover:text-primary",
                )}
              >
                {job.title}
              </Link>
            </h3>
            <div className="mt-1 line-clamp-1">
              <Link
                href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job?.slug + "-" + job.id },
                  query: { src: "topdev_search", medium: "searchresult" },
                }}
                target="_blank"
                className="text-[14px] font-bold text-[#5d5d5d] transition-all hover:text-primary"
              >
                {job.company?.display_name}
              </Link>
            </div>
          </div>
          <div>
            <FollowJobFreeButton
              jobId={job.id}
              jobTitle={job.title}
              active={job.is_followed}
              fontSize="!text-[20px]"
            />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CardInformativeFreeList;
