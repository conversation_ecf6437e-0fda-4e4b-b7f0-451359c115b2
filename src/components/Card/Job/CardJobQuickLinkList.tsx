"use client";

import React, { FC } from "react";

import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";

import IconUp from "@/assets/images/icons/arrow-up.svg";
import Image from "next/image";
import { classNames } from "@/utils";
import { useTranslations, useLocale } from "next-intl";
import Link from "next/link";

interface Props {
  title: string;
  values: { title: string; url_vi: string; url_en: string }[];
  collapse?: boolean;
  onCollapse?: (event: React.MouseEvent) => void;
  srcPage?: string;
  mediumPage?: string;
}

const CardJobQuickLinkList: FC<Props> = ({
  title,
  values,
  collapse = false,
  onCollapse,
  srcPage = "home",
  mediumPage = "quicksearch",
}) => {
  const t = useTranslations();
  const locale = useLocale();

  const getUrl = (value: { url_vi: string; url_en: string }) => {
    return locale == "vi" ? value.url_vi : value.url_en;
  }

  return (
    <div className={"overflow-hidden md:rounded"}>
      <div className={"flex bg-stone-800 p-4 text-white"} onClick={onCollapse}>
        <h3 className={"grow font-bold"}>{title}</h3>

        <div className={"flex-none md:hidden"}>
          <button>
            <Image
              src={IconUp}
              alt={""}
              className={classNames(collapse ? "rotate-180" : "")}
            />
          </button>
        </div>
      </div>

      <div
        className={classNames(
          "bg-gray-500 p-4 md:block",
          collapse ? "hidden" : "block",
        )}
      >
        <SimpleBar
          className={"max-h-[244px]"}
          autoHide={false}
          scrollbarMaxSize={50}
        >
          <ul className={"leading-9 text-white"}>
            {values.map((value, index) => (
              <li key={index}>
                <Link
                  href={{
                    pathname: getUrl(value),
                    query: { src: "topdev_" + srcPage, medium: mediumPage },
                  }}
                  className={"hover:text-primary"}
                >
                  {t("home_" + value.title)}
                </Link>
              </li>
            ))}
          </ul>
        </SimpleBar>
      </div>
    </div>
  );
};

export default CardJobQuickLinkList;
