import FollowJobButton from "@/components/Button/FollowJobButton";
import SalaryLoading from "@/components/Loading/SalaryLoading";
import HotJobTag from "@/components/Tag/HotJobTag";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import { JobType } from "@/types/job";
import { classNames } from "@/utils";
import { BRAND_LOGO } from "@/utils/image";
import { isHotLabel } from "@/utils/jobDetection";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";
import { PiDotOutlineFill } from "react-icons/pi";
const SalaryInfo = dynamic(() => import("./SalaryInfo"), {
  ssr: false,
  loading: () => <SalaryLoading />,
});

interface CardInformativeListType {
  job: JobType;
}

const stripHTML = (html: string) => {
  const text = html.replace(/<\/?[^>]+>/gi, "");

  if (typeof window !== "undefined") {
    const textarea = document.createElement("textarea");
    textarea.innerHTML = text;
    return textarea.value;
  } else {
    return text
      .replace(/&amp;/g, "&")
      .replace(/&lt;/g, "<")
      .replace(/&gt;/g, ">")
      .replace(/&quot;/g, '"')
      .replace(/&apos;/g, "'")
      .replace(/&nbsp;/g, " ") // Handle non-breaking space
      .replace(/&#160;/g, " ") // Handle numeric entity for non-breaking space
      .replace(/&#(\d+);/g, (match, num) => String.fromCharCode(num)) // Decimal numeric entities
      .replace(/&#x([0-9A-Fa-f]+);/g, (match, hex) =>
        String.fromCharCode(parseInt(hex, 16)),
      ); // Hexadecimal numeric entities
  }
};
export const extractItems = (benefits: any[]) => {
  if (benefits?.length > 0) {
    const values = benefits.map((item) => item.value).join("\n");
    const valuesMap = benefits.map((item) => item.value).filter((item) => item);
    const regex = /<li[^>]*>(.*?)<\/li>/g;
    const items = [];
    let match;

    while ((match = regex.exec(values)) !== null) {
      items.push(stripHTML(match[1].trim()));
    }
    return items?.length > 0 ? items.slice(0, 3) : valuesMap?.slice(0, 3);
  }
  return [];
};
const CardInformativeList: FC<CardInformativeListType> = (props) => {
  const { job } = props;
  const t = useTranslations();
  const isValidBenefitsOriginal = job?.benefits_original?.[0]?.value ?? false;
  const isEmptyBenefitsJob =
    job?.benefits?.length === 0 && !isValidBenefitsOriginal;
  const benefitCompany = job?.company?.benefits
    ? extractItems(job.company.benefits)
    : [];
  const extractList = isValidBenefitsOriginal
    ? job?.benefits_original
    : job?.benefits;
  const benefitJob = !isEmptyBenefitsJob
    ? extractItems(extractList as any[])
    : [];
  return (
    <Link
      href={{
        pathname: "/detail-jobs/[slug]",
        params: { slug: job?.slug + "-" + job.id },
        query: { src: "topdev_search", medium: "searchresult" },
      }}
      target="_blank"
    >
      <div
        className={classNames(
          "relative rounded border border-solid transition-all hover:shadow-md",
          job.is_basic
            ? `${
                isHotLabel(job.features)
                  ? "border-b-4 border-primary"
                  : "border-transparent hover:border-primary-200"
              } bg-white hover:bg-primary-100`
            : "",
          job.is_basic_plus
            ? `${
                isHotLabel(job.features)
                  ? "border-b-4 border-primary"
                  : "border-transparent hover:border-primary-200"
              } bg-white hover:bg-primary-100`
            : "",
          job.is_distinction
            ? `${
                isHotLabel(job.features)
                  ? "border-b-4 border-primary"
                  : "border-primary"
              } bg-primary-100`
            : "",
        )}
      >
        {isHotLabel(job.features) && (
          <div className="absolute -bottom-1 -left-px">
            <HotJobTag />
          </div>
        )}
        <div className="flex items-start justify-between gap-6 p-4">
          <div>
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job?.slug + "-" + job.id },
                query: { src: "topdev_search", medium: "searchresult" },
              }}
              className="block h-[7.5rem] w-[10rem]"
              target="_blank"
            >
              {job.company?.image_logo && (
                <Image
                  src={job.company.image_logo}
                  alt={job.company?.display_name}
                  width={BRAND_LOGO.large.width}
                  height={BRAND_LOGO.large.height}
                  className="h-28 w-40 max-w-full rounded-xl bg-white object-contain p-2"
                  loading="lazy"
                />
              )}
            </Link>
          </div>
          <div className="flex-1">
            <h3 className={classNames("line-clamp-1")}>
              <Link
                href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job?.slug + "-" + job.id },
                  query: { src: "topdev_search", medium: "searchresult" },
                }}
                target="_blank"
                className={classNames(
                  "text-lg font-bold transition-all",
                  !job.is_basic ? "text-primary" : "hover:text-primary",
                )}
              >
                {job.title}
              </Link>
            </h3>
            <div className="mt-1 line-clamp-1">
              <Link
                href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job?.slug + "-" + job.id },
                  query: { src: "topdev_search", medium: "searchresult" },
                }}
                target="_blank"
                className="text-gray-600 transition-all hover:text-primary"
              >
                {job.company?.display_name}
              </Link>
            </div>
            <div className="mt-2 flex items-center justify-start gap-5">
              <div className="text-primary">
                <SalaryInfo value={job.salary.value} />
              </div>
              <div className="text-gray-600">
                <PiDotOutlineFill />
              </div>
              <div>
                <p className="text-gray-500">{job.job_levels_str}</p>
              </div>
            </div>
            <div className="flex flex-wrap items-end gap-2 text-gray-500">
              <p>
                {job.addresses && job.addresses.address_region_array.length == 1
                  ? job.addresses.sort_addresses
                  : job.addresses.address_region_array[0] +
                    ` (+${job.addresses.address_region_array.length - 1})`}
              </p>
              {job.job_types_str && <p>({job.job_types_str})</p>}
            </div>

            {!isEmptyBenefitsJob && job?.is_distinction && (
              <>
                {isValidBenefitsOriginal ? (
                  <div className="mt-2">
                    <ul className="ml-6 list-disc text-gray-600">
                      {benefitJob.map((benefitItem, index) => {
                        return (
                          <li key={index}>
                            <p
                              className="line-clamp-1"
                              dangerouslySetInnerHTML={{ __html: benefitItem }}
                            ></p>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                ) : (
                  <div className="mt-2">
                    <ul className="ml-6 list-disc text-gray-600">
                      {job.benefits.slice(0, 3).map((benefitItem, index) => {
                        return (
                          <li key={index}>
                            <p className="line-clamp-1">
                              {benefitItem.description ||
                                benefitItem?.value ||
                                benefitItem?.name}
                            </p>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                )}
              </>
            )}
            {isEmptyBenefitsJob &&
              job?.is_distinction &&
              (job.company.benefits.length > 1 ? (
                <div className="mt-2">
                  <ul className="ml-6 list-disc text-gray-600">
                    {job.company.benefits
                      .slice(0, 3)
                      .map((benefitItem, index) => {
                        return (
                          <li key={index}>
                            <p className="line-clamp-1">{benefitItem?.value}</p>
                          </li>
                        );
                      })}
                  </ul>
                </div>
              ) : (
                <div className="mt-2">
                  <ul className="ml-6 list-disc text-gray-600">
                    {benefitCompany.map((benefitItem, index) => {
                      return (
                        <li key={index}>
                          <p
                            className="line-clamp-1"
                            dangerouslySetInnerHTML={{ __html: benefitItem }}
                          ></p>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              ))}
            <hr className="mt-2 h-px w-full bg-gray-200" />
            <div className="mt-4 flex items-center justify-between">
              <div className="line-clamp-1">
                {job.skills_ids.slice(0, 6).map((skillItem, index) => {
                  return <SkillTagTaxonomy skillId={skillItem} key={index} />;
                })}
              </div>
              <p className="whitespace-nowrap text-sm text-gray-400">
                {t("search_page_posted")} {job.refreshed.since}
              </p>
            </div>
          </div>
          <div>
            <FollowJobButton
              jobId={job.id}
              jobTitle={job.title}
              active={job.is_followed}
            />
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CardInformativeList;
