import React, { FC, ReactNode } from "react";

interface Props {
  children: ReactNode;
  className?: string;
}

const CardJobWrapper: FC<Props> = ({ children, ...props }) => {
  return (
    <div
      className={
        props.className
          ? props.className
          : "block cursor-pointer rounded border border-solid border-white bg-white p-4 transition-all hover:border-primary-200 hover:shadow-md"
      }
    >
      {children}
    </div>
  );
};

export default CardJobWrapper;
