import FollowJobButton from "@/components/Button/FollowJobButton";
import SalaryLoading from "@/components/Loading/SalaryLoading";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import { DeviceType } from "@/types/device";
import { JobType } from "@/types/job";
import classNames from "@/utils/classNames";
import { BRAND_LOGO } from "@/utils/image";
import dynamic from "next/dynamic";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";
const SalaryInfo = dynamic(() => import("./SalaryInfo"), {
  ssr: false,
  loading: () => <SalaryLoading />,
});

interface CardInformativeGridType {
  outline?: boolean;
  display?: "horizontal" | "vertical" | "highlight";
  highlight?: boolean;
  job: JobType;
  home?: boolean;
  classMinHeight?: string;
  device?: DeviceType;
  className?: string;
  index: any;
  srcPage?: string;
  mediumPage?: string;
}

const CardInformativeGrid: FC<CardInformativeGridType> = (props) => {
  const {
    classMinHeight,
    outline = false,
    display = "horizontal",
    highlight = false,
    home = false,
    job,
    device = "desktop",
    index,
    srcPage = "home",
    mediumPage = "popularcompanies",
  } = props;

  return (
    <Link
      href={{
        pathname: "/detail-jobs/[slug]",
        params: { slug: job.slug + "-" + job.id },
        query: { src: "topdev_" + srcPage, medium: mediumPage },
      }}
      target="_blank"
    >
      <div
        className={classNames(
          "rounded border border-solid bg-white p-4 transition-all hover:border-primary-200 hover:shadow-md",
          outline ? "border-gray-200" : " border-transparent",
          classMinHeight as string,
          props.className ? props.className : "",
        )}
      >
        <div className="just flex items-start justify-between">
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id },
              query: { src: "topdev_" + srcPage, medium: mediumPage },
            }}
            target="_blank"
            title={job.title}
          >
            {job.company?.image_logo && (
              <Image
                src={job.company?.image_logo as string}
                width={
                  device === "desktop"
                    ? BRAND_LOGO.medium.width
                    : BRAND_LOGO.small.width
                }
                height={
                  device === "desktop"
                    ? BRAND_LOGO.medium.height
                    : BRAND_LOGO.small.height
                }
                className="h-[66px] w-[88px] max-w-full bg-white object-contain md:h-[70px] md:w-[100px]"
                alt={job.company?.display_name as string}
                loading="lazy"
              />
            )}
          </Link>
          <div onClick={(e) => e.preventDefault()}>
            <FollowJobButton
              jobId={props.job.id}
              active={props.job.is_followed}
              jobTitle={props.job.title}
            />
          </div>
        </div>

        <div className="mt-2">
          <h3
            className={classNames(
              "text-sm font-bold md:text-lg",
              display === "horizontal"
                ? "line-clamp-1"
                : "line-clamp-2 h-10 md:h-12",
            )}
          >
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: { src: "topdev_" + srcPage, medium: mediumPage },
              }}
              target="_blank"
              title={job.title}
              className="transition-all hover:text-primary"
            >
              {job.title}
            </Link>
          </h3>
          <div
            className={classNames(
              "mt-1 line-clamp-1 text-gray-500 md:mt-2",
              display === "horizontal"
                ? "text-sm font-semibold md:text-base"
                : "text-sm md:text-lg",
            )}
          >
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: { src: "topdev_" + srcPage, medium: mediumPage },
              }}
              target="_blank"
              className="transition-all hover:text-primary"
            >
              {index <= 7 ? (
                <h3>{job.company?.display_name}</h3>
              ) : (
                <>{job.company?.display_name}</>
              )}
            </Link>
          </div>
          {highlight && (
            <>
              <div className="mt-2 line-clamp-1 text-xs font-normal text-primary-300 lg:text-base">
                <SalaryInfo value={job.salary.value} />
              </div>
              <p className="line-clamp-1 text-sm text-gray-500 lg:text-base">
                {job.addresses && job.addresses.address_region_array.length == 1
                  ? job.addresses.sort_addresses
                  : job.addresses.address_region_array[0] +
                    ` (+${job.addresses.address_region_array.length - 1})`}
              </p>
            </>
          )}
          <div className="mt-2">
            <div className="line-clamp-1 gap-2">
              {props?.job?.skills_ids.map((item) => {
                return (
                  <SkillTagTaxonomy
                    skillId={item}
                    key={item}
                    srcPage={srcPage}
                  />
                );
              })}
            </div>
          </div>
          {display === "vertical" && !home && (
            <p className="mt-2 hidden text-right text-gray-400 md:block">
              {job.refreshed ? job.refreshed.since : ""}
            </p>
          )}
        </div>
      </div>
    </Link>
  );
};

export default CardInformativeGrid;
