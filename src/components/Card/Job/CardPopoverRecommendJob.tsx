"use client";
import FollowJ<PERSON>Button from "@/components/Button/FollowJobButton";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Link } from "@/navigation";

import SalaryInfo from "./SalaryInfo";
import { useMemo } from "react";
import { TextEditorResponse } from "@/types/job";

interface PopoverProps {
  isVisible: boolean;
  slug?: string;
  jobId: number;
  logo: string;
  title: string;
  company: string;
  salary: string;
  location: string;
  skills: number[];
  detailUrl: string;
  benefits?: string[];
  jobTypesStr: string;
  responsibilities?: string | TextEditorResponse;
  experiences_str?: string[] | string;
  position: { top: number; left: number } | null;
  onMouseLeave: () => void;
  onMouseEnter: () => void;
  srcPage: string;
  mediumPage: string;
}

const CardPopoverRecommendJob: React.FC<PopoverProps> = ({
  isVisible,
  slug,
  jobId,
  logo,
  title,
  company,
  salary,
  location,
  skills,
  benefits,
  jobTypesStr,
  responsibilities,
  experiences_str,
  position,
  srcPage,
  mediumPage,
  onMouseLeave,
  onMouseEnter,
}) => {
  const t = useTranslations();
  const queryObject = useMemo(() => {
    let queryObj: { [key: string]: string } = {};
    if (srcPage) {
      queryObj["src"] = `topdev_${srcPage}`;
    }
    if (mediumPage) {
      queryObj["medium"] = mediumPage;
    }
    return queryObj;
  }, [srcPage, mediumPage]);

  if (!position) return null;

  return (
    <div
      data-popover
      id="popover-default"
      role="tooltip"
      style={{ top: position.top, left: position.left }}
      className={`text-smshadow-sm absolute z-30 inline-block h-auto w-auto min-w-[512px] max-w-[512px] rounded-lg border bg-white p-6 transition-opacity duration-200 ${
        isVisible ? "visible opacity-100" : "invisible opacity-0"
      } dark:border-gray-600  dark:text-gray-400`}
      onMouseLeave={onMouseLeave}
      onMouseEnter={onMouseEnter}
    >
      <div data-popper-arrow></div>
      <div className="flex grid-cols-2 flex-row">
        <div className="col-span-1 flex items-center bg-white object-contain md:h-[70px] md:max-w-[100px]">
          <Image
            src={logo ?? ""}
            alt={title}
            width={100}
            height={70}
            loading="lazy"
            className="max-h-[66px] max-w-[88px] bg-white object-contain md:max-h-[70px] md:max-w-[100px]"
          />
        </div>
        <div className="col-span-2 ml-4 flex flex-col">
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: slug + "-" + jobId },
              query: queryObject,
            }}
            className="min-h-11 text-base font-bold text-neutral-950 md:min-h-[44px] md:min-w-[316px]"
          >
            {title}
          </Link>
          <Link
            target="_blank"
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: slug + "-" + jobId },
              query: queryObject,
            }}
          >
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: slug + "-" + jobId },
                query: queryObject,
              }}
              className="md:min-h-auto text-base font-bold text-brand-600 md:h-[22px]"
            >
              {company}
            </Link>
          </Link>
          <span className="line-clamp-1 text-base font-normal text-neutral-600">
            {location.length > 25
              ? `${location.substring(0, 25)}...`
              : location}
          </span>
          <span className="text-base font-normal text-brand-600">
            <SalaryInfo value={salary} />
          </span>
        </div>
        <div className="col-span-1 ml-2">
          <FollowJobButton jobId={jobId} active={true} jobTitle={title} />
        </div>
      </div>
      <div className="mb-2 mt-4 border"></div>
      <div className="flex flex-col pt-2">
        <div className="flex flex-row justify-start">
          <div className="flex flex-col">
            <span className="w-full text-base font-bold text-neutral-950 md:min-w-[232px]">
              {t("recommend_experience")}
            </span>
            <span className="text-sm font-normal text-neutral-600">
              {Array.isArray(experiences_str) && experiences_str.length > 0 && (
                <span className="text-sm font-normal text-neutral-600">
                  {experiences_str[0]}
                </span>
              )}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-base font-bold text-neutral-950">
              {t("recommend_job_type")}
            </span>
            <span className="text-sm font-normal text-neutral-600">
              {jobTypesStr}
            </span>
          </div>
        </div>
        <div className="mt-4 w-[464px] md:min-w-[464px]">
          <span className="text-base font-bold text-neutral-950">
            {t("recommend_job_skills")}
          </span>
          <div className="mt-2 flex w-full flex-row flex-wrap gap-2">
            {skills?.map((skill, index) => {
              return <SkillTagTaxonomy skillId={skill} key={index} />;
            })}
          </div>
        </div>
        <div className="scrollbar-primary mb-4 mt-4 w-[464px] overflow-y-auto bg-neutral-50 pb-4 last:mb-0 last:border-0 md:max-h-[200px] md:min-h-[200px] md:max-w-[464px]">
          <div className="p-4">
            {!!responsibilities && (
              <div>
                <h2 className="mb-2 text-base font-bold text-black">
                  {t("recommend_job_resposibility")}
                </h2>
                <div
                  className="prose max-w-full text-sm text-black lg:text-base"
                  dangerouslySetInnerHTML={{
                    __html: responsibilities,
                  }}
                ></div>
              </div>
            )}
            {!!benefits && benefits.length > 0 ? (
              <div>
                <h2 className="mb-2 text-base font-bold text-black">
                  {t("recommend_job_benefit")}
                </h2>
                {benefits.length > 1 ? (
                  <ul>
                    {benefits.map((benefit, index) => (
                      <li
                        key={index}
                        dangerouslySetInnerHTML={{ __html: benefit }}
                      />
                    ))}
                  </ul>
                ) : (
                  <div
                    className="prose max-w-full text-sm text-black lg:text-base"
                    dangerouslySetInnerHTML={{
                      __html: benefits.join("<br/>"),
                    }}
                  ></div>
                )}
              </div>
            ) : null}
          </div>
        </div>
        <Link
          href={{
            pathname: "/detail-jobs/[slug]",
            params: { slug: slug + "-" + jobId },
            query: queryObject,
          }}
          target="_blank"
          className="mx-auto mt-4 flex cursor-pointer text-sm font-semibold text-brand-600 underline"
        >
          {t("detail_job_view_more")}
        </Link>
      </div>
    </div>
  );
};

export default CardPopoverRecommendJob;
