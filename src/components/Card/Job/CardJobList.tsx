import { useTranslations } from "next-intl";

const SkillTagTaxonomy = dynamic(
  () => import("@/components/Tag/SkillTagTaxonomy"),
  {
    ssr: false,
    loading: () => <SkillTagLoading size="sm" />,
  },
);
import SkillTagLoading from "@/components/Loading/SkillTagLoading";
import { JobType } from "@/types/job";
import { BRAND_LOGO } from "@/utils/image";
import dynamic from "next/dynamic";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";

interface Props {
  job: JobType;
  classMinHeight?: string;
  srcPage?: string;
  mediumPage?: string;
}

const CardJobList: FC<Props> = ({
  job,
  classMinHeight,
  srcPage,
  mediumPage,
}) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "search"}`;
  const mediumQueryParam = mediumPage ?? "jobsfitforyou";

  return (
    <Link
      href={{
        pathname: "/detail-jobs/[slug]",
        params: { slug: job.slug + "-" + job.id },
        query: { src: srcPageQueryParam, medium: mediumQueryParam },
      }}
      target="_blank"
    >
      <div
        className={`${classMinHeight} CardJobList block rounded border border-solid border-white bg-white p-4 transition-all hover:shadow-md`}
      >
        <div className="flex items-start gap-2">
          <div className="w-[88px] bg-white">
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: { src: srcPageQueryParam, medium: mediumQueryParam },
              }}
              className="inline-block"
              target="_blank"
            >
              {job.company?.image_logo && (
                <Image
                  src={job?.company?.image_logo as string}
                  alt={job?.company?.display_name as string}
                  width={BRAND_LOGO.small.width}
                  height={BRAND_LOGO.small.height}
                  className="CardJobList__Company__Image mx-auto h-[66px] w-[88px] max-w-full object-contain p-2"
                  loading="lazy"
                />
              )}
            </Link>
          </div>
          <div className="flex-1">
            <h3 className="line-clamp-1 text-sm font-bold md:text-lg">
              <Link
                href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job.slug + "-" + job.id },
                  query: { src: srcPageQueryParam, medium: mediumQueryParam },
                }}
                className="transition hover:text-primary"
                target="_blank"
              >
                {job.title}
              </Link>
            </h3>
            <div className="line-clamp-1">
              <Link
                href={{
                  pathname: "/detail-jobs/[slug]",
                  params: { slug: job.slug + "-" + job.id },
                  query: { src: srcPageQueryParam, medium: mediumQueryParam },
                }}
                target="_blank"
                className="text-sm text-gray-500 transition-all hover:text-primary md:text-base"
              >
                {job.company?.display_name}
              </Link>
            </div>
            <div className="mt-2 flex items-end">
              <div className="line-clamp-1">
                {job.skills_ids
                  ? job.skills_ids.slice(0, 3).map((item) => {
                      return (
                        <SkillTagTaxonomy
                          srcPage={srcPage}
                          skillId={item}
                          key={item}
                        />
                      );
                    })
                  : null}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CardJobList;
