"use client";

import React, { useEffect, useMemo, useState } from "react";
import Image from "next/image";
import { Link } from "@/navigation";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import ApplyJobButton from "@/components/Button/ApplyJobButton";
import FollowJobButton from "@/components/Button/FollowJobButton";
import { JobRecommendData, JobType, TextEditorResponse } from "@/types/job";
import SalaryInfo from "./SalaryInfo";
import { useAppSelector } from "@/store";
import EasyApply from "@/components/DialogModal/EasyApply";
import { Tooltip } from "flowbite-react";
import { useTranslations } from "next-intl";
import { useRouter } from "@/navigation";

type CardRecommendJobProps = {
  srcPage?: string;
  mediumPage?: string;
  jobId: number;
  logo: string;
  title: string;
  company: string;
  salary: string;
  location: string;
  skills: number[];
  detailUrl: string;
  statusDisplay: string;
  isApplied: boolean;
  jobTypesStr: string;
  benefits?: string[];
  responsibilities?: string | TextEditorResponse;
  jobPosted: string;
  experiences_str?: string[] | string;
  onHover: (data: JobRecommendData | null, event: React.MouseEvent) => void;
  onMouseLeave: () => void;
  job: JobType;
};

const CardRecommendJob: React.FC<CardRecommendJobProps> = ({
  jobId,
  logo,
  title,
  company,
  salary,
  location,
  skills,
  detailUrl,
  jobPosted,
  benefits,
  responsibilities,
  statusDisplay,
  isApplied,
  jobTypesStr,
  experiences_str,
  srcPage,
  mediumPage,
  job,
  onHover,
  onMouseLeave,
}) => {
  const router = useRouter();
  const queryObject = useMemo(() => {
    let queryObj: { [key: string]: string } = {};
    if (srcPage) {
      queryObj["src"] = `topdev_${srcPage}`;
    }
    if (mediumPage) {
      queryObj["medium"] = mediumPage;
    }

    return queryObj;
  }, [srcPage, mediumPage]);
  const t = useTranslations();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const user = useAppSelector((state) => state?.user?.user);
  const newestCandidate = useAppSelector(
    (state) => state?.user?.newest_candidate,
  );
  const [isEasyApply, setIsEasyApply] = useState<boolean>(false);

  useEffect(() => {
    if (isLoggedIn && user && newestCandidate && newestCandidate.id) {
      setIsEasyApply(true);
    }
  }, [isLoggedIn, user, newestCandidate]);

  return (
    <div
      className="flex h-full w-full cursor-pointer flex-col justify-between rounded bg-white p-3 px-4 pb-2 pt-4 transition-all hover:border-brand-100 hover:shadow-md md:h-full md:w-full"
      onMouseLeave={onMouseLeave}
    >
      <div className="flex gap-3">
        <div className="flex items-baseline bg-white object-contain">
          <div className="flex h-[66px] items-center justify-center">
            <Image
              src={logo ?? ""}
              alt={title}
              width={100}
              height={70}
              loading="lazy"
              className="max-h-[66px] max-w-[88px] bg-white object-contain md:max-h-[70px] md:max-w-[100px]"
            />
          </div>
        </div>
        <div className="flex flex-col justify-between">
          <div>
            <div className="flex flex-row items-start justify-between gap-2">
              <div className="flex-1">
                <Link
                  target="_blank"
                  href={{
                    pathname: "/detail-jobs/[slug]",
                    params: { slug: job.slug + "-" + job.id },
                    query: queryObject,
                  }}
                  onMouseEnter={(event) =>
                    onHover(
                      {
                        jobId,
                        logo,
                        title,
                        company,
                        salary,
                        location,
                        skills,
                        detailUrl,
                        benefits,
                        responsibilities,
                        jobTypesStr,
                        experiences_str,
                        srcPage,
                        mediumPage,
                        job,
                      },
                      event,
                    )
                  }
                  className="flex"
                >
                  <Link
                    href={{
                      pathname: "/detail-jobs/[slug]",
                      params: { slug: job.slug + "-" + job.id },
                      query: queryObject,
                    }}
                    className="text-lg font-bold text-black hover:text-brand-600 md:h-12 md:min-h-[auto]"
                  >
                    {title.length > 35 ? title.slice(0, 35) + "..." : title}
                  </Link>
                </Link>
              </div>
              {jobId && title && (
                <Tooltip
                  content={t("detail_job_recommended_job_save_job_tooltip")}
                  theme={{
                    style: {
                      dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                    },
                  }}
                >
                  <FollowJobButton
                    jobId={jobId}
                    active={true}
                    jobTitle={title}
                  />
                </Tooltip>
              )}
            </div>
            <Tooltip
              content={t("detail_job_recommended_job_company_tooltip")}
              theme={{
                style: {
                  dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
                },
              }}
            >
              <Link
                target="_blank"
                href={{
                  pathname: "/companies/[slug]",
                  params: { slug: job?.company?.slug + "-" + job?.company?.id },
                  query: queryObject,
                }}
              >
                <p className="flex-nowrap whitespace-nowrap text-base text-neutral-900 md:h-[22px]">
                  {company.length > 25
                    ? company.slice(0, 25) + "...."
                    : company}
                </p>
              </Link>
            </Tooltip>
            <p className="md:min-h-auto truncate text-base text-brand-600 md:h-[22px]">
              <SalaryInfo value={salary} />
            </p>
            <span className="line-clamp-1 text-base font-normal text-neutral-600">
              {location.length > 25 ? `${location.slice(0, 25)}...` : location}
            </span>
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: queryObject,
              }}
              className="flex w-60 flex-row overflow-hidden truncate"
            >
              {skills?.map((skill, index) => {
                if (index < 3) {
                  return <SkillTagTaxonomy skillId={skill} key={index} />;
                }
                if (index === 3) {
                  return <div key={index}>...</div>;
                }
                return null;
              })}
            </Link>
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <div>
          {jobPosted && (
            <span className="sticky my-2 flex items-end justify-end text-sm font-normal text-neutral-600">
              {t("detail_job_posted_date")} {jobPosted}
            </span>
          )}
        </div>
        <div className="md:min-h-13 min-h-auto mt-2 flex flex-row items-center gap-2 ">
          <div className="flex w-full flex-1 flex-grow">
            {!isEasyApply ? (
              <ApplyJobButton
                size="small"
                statusDisplay={statusDisplay}
                isApplied={isApplied}
                jobId={jobId}
                detailUrl={detailUrl}
                companyDisplayName={company}
                jobTypesStr={jobTypesStr}
                job={job}
              />
            ) : (
              <EasyApply job={job} size="small" />
            )}
          </div>
          <Tooltip
            content={t("detail_job_recommended_job_detail_job_tooltip")}
            theme={{
              target: "w-fit h-full flex items-center justify-center",
              style: {
                dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
              },
            }}
          >
            <button
              onClick={() => {
                router.push({
                  pathname: "/detail-jobs/[slug]",
                  query: queryObject,
                  params: { slug: job.slug + "-" + job.id },
                });
              }}
              className="flex h-full w-full items-center justify-center rounded border border-primary bg-white px-4 font-semibold text-primary hover:bg-primary-100"
            >
              {t("detail_job_recommended_job_view_detail_job")}
            </button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default CardRecommendJob;
