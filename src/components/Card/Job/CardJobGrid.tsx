"use client";
import ChipTag from "@/components/Tag/ChipTag";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import { JobType } from "@/types/job";
import { BRAND_LOGO } from "@/utils/image";
import Image from "next/image";
import { Link } from "@/navigation";
import { FC } from "react";
import CardJobWrapper from "./CardJobWrapper";
import { isMobile } from "react-device-detect";

interface Props {
  job: JobType;
  srcPage?: string;
  mediumPage?: string;
}

const CardJobGrid: FC<Props> = ({ job, srcPage, mediumPage }) => {
  const srcPageQueryParam = `topdev_${srcPage ?? "home"}`;
  const mediumPageQueryParam = mediumPage ?? "superhotjobs";

  return (
    <CardJobWrapper>
      <Link
        href={{
          pathname: "/detail-jobs/[slug]",
          params: { slug: job.slug + "-" + job.id },
          query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
        }}
        target="_blank"
      >
        <div className="flex items-center justify-center">
          <Link
            href={{
              pathname: "/detail-jobs/[slug]",
              params: { slug: job.slug + "-" + job.id },
              query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
            }}
            className="inline-block"
            target="_blank"
          >
            {job.company.image_logo && (
              <Image
                src={job.company.image_logo as string}
                alt={job?.company?.display_name as string}
                width={
                  isMobile ? BRAND_LOGO.small.width : BRAND_LOGO.medium.width
                }
                height={
                  isMobile ? BRAND_LOGO.small.height : BRAND_LOGO.medium.height
                }
                className="mx-auto h-[66px] w-[88px] bg-white object-contain lg:h-[70px] lg:w-[100px]"
                loading="lazy"
              />
            )}
          </Link>
        </div>
        <div className="mt-4 text-center">
          <div className="line-clamp-1 text-sm font-normal text-gray-500 md:text-lg">
            <Link
              href={{
                pathname: "/companies/[slug]",
                params: { slug: job?.company?.slug + "-" + job?.company?.id },
                query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
              }}
              title={job.company.display_name}
              target="_blank"
              className="transition-all hover:text-primary"
            >
              {job.company.display_name}
            </Link>
          </div>
          <h3 className="mt-2 line-clamp-1 text-sm font-bold md:line-clamp-2 md:h-[50px] md:text-lg">
            <Link
              href={{
                pathname: "/detail-jobs/[slug]",
                params: { slug: job.slug + "-" + job.id },
                query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
              }}
              target="_blank"
              title={job.title}
              className="transition-all hover:text-primary"
            >
              {job.title}
            </Link>
          </h3>
          <div className="line-clamp-2 h-[4.25rem] md:h-[4.5rem] lg:h-[5.75rem]">
            {job.skills_ids.slice(0, 6).map((skillItem, index) => {
              return (
                <span
                  key={index}
                  className="mt-2 inline-block"
                  onClick={(e) => e.preventDefault()}
                >
                  <SkillTagTaxonomy
                    skillId={skillItem}
                    size="md"
                    srcPage={srcPage ?? "home"}
                  />
                </span>
              );
            })}
          </div>
        </div>
      </Link>
    </CardJobWrapper>
  );
};

export default CardJobGrid;
