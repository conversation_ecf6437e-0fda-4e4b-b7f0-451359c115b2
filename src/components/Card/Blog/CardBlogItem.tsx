import { useTranslations } from "next-intl";
import React, { FC, useMemo } from "react";
import { BlogType } from "@/types/blog";
import Image from "next/image";
import Link from "@/components/Link/Link";
interface Props {
  post: BlogType;
  srcPage?: string;
  mediumPage?: string;
}
const CardBlogItem: FC<Props> = ({ post, srcPage, mediumPage }) => {
  const t = useTranslations();

  const queryObject = useMemo(() => {
    let queryObj: { [key: string]: string } = {};
    if (srcPage) {
      queryObj["src"] = `topdev_${srcPage}`;
    }
    if (mediumPage) {
      queryObj["medium"] = mediumPage;
    }
    return queryObj;
  }, [srcPage, mediumPage]);

  return (
    <Link
      href={{
        pathname: post.permalink,
        query: queryObject,
      }}
      target="_blank"
    >
      <article className={"flex gap-5"}>
        {post.image ? (
          <Image
            src={post.image ?? ""}
            alt={post.post_title}
            height={128}
            width={208}
            loading="lazy"
            className={
              "h-24 w-36 rounded bg-white object-cover object-center lg:h-32 lg:w-52"
            }
          />
        ) : (
          ""
        )}
        <div className={"flex flex-col gap-1 text-sm lg:text-base"}>
          <h3 className={"line-clamp-2"}>
            <span className="font-bold transition-all hover:text-primary">
              {post.post_title}
            </span>
          </h3>
          <p className={"line-clamp-2 break-all"}>{post.post_excerpt}</p>

          <span
            className={
              "text-sm text-primary underline transition-all hover:text-primary-400"
            }
          >
            {t("home_read_more")}
          </span>
        </div>
      </article>
    </Link>
  );
};

export default CardBlogItem;
