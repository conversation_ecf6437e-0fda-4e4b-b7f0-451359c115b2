import { useTranslations } from "next-intl";
import React, { FC, useMemo } from "react";
import Image from "next/image";
import Link from "@/components/Link/Link";
import { BlogType } from "@/types/blog";
interface Props {
  post: BlogType;
  srcPage?: string;
  mediumPage?: string;
}
const CardBlogMain: FC<Props> = ({ post, srcPage, mediumPage }) => {
  const t = useTranslations();

  const queryObject = useMemo(() => {
    let queryObj: { [key: string]: string } = {};
    if (srcPage) {
      queryObj["src"] = `topdev_${srcPage}`;
    }
    if (mediumPage) {
      queryObj["medium"] = mediumPage;
    }
    return queryObj;
  }, [srcPage, mediumPage]);

  return (
    <Link
      href={{
        pathname: post.permalink,
        query: queryObject,
      }}
      className={"flex-none"}
      target="_blank"
    >
      <article className={"flex flex-row gap-4 md:flex-col"}>
        <div>
          {post.image ? (
            <Image
              src={post.image}
              height={600}
              width={282}
              loading="lazy"
              unoptimized
              alt={post.post_title}
              className={
                "h-[120px] w-[120px] rounded bg-white object-cover object-center md:h-[282px] md:w-full"
              }
            />
          ) : (
            ""
          )}
        </div>

        <div className={"flex flex-col gap-1"}>
          <h3
            className={
              "line-clamp-2 text-base font-bold transition-all hover:text-primary lg:text-xl"
            }
          >
            {post.post_title}
          </h3>

          <p className={"line-clamp-2 text-gray-500"}>{post.post_excerpt}</p>

          <span
            className={
              "text-sm text-primary underline transition-all hover:text-primary-400"
            }
          >
            {t("home_read_more")}
          </span>
        </div>
      </article>
    </Link>
  );
};

export default CardBlogMain;
