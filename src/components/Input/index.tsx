import React from "react";
import { classNames } from "@/utils";

// Use for typeScript
export type TypeOnChangeInput = React.ChangeEvent<HTMLInputElement>;
export type TypeOnFocusInput = React.FocusEvent<HTMLInputElement>;

//Interface for input
interface Props {
  type?: string;
  name?: string;
  id?: string;
  placeholder?: string;
  value?: string | number | undefined;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  onChange?(event: TypeOnChangeInput): void;
  onFocus?(event: TypeOnFocusInput): void;
}

//Component
const Input: React.FC<Props> = ({
  type,
  name,
  value,
  id,
  disabled,
  className,
  placeholder,
  onChange,
  onFocus,
}) => {
  const classDisabled: string = String(
    !!disabled &&
      "disabled:opacity-75 bg-gray-100 text-gray-300 border-gray-100",
  );

  return (
    <input
      type={type ?? "text"}
      name={name}
      value={value}
      disabled={disabled}
      onChange={onChange}
      onFocus={onFocus}
      id={id}
      className={classNames(className ?? "", "block w-full", classDisabled)}
      placeholder={placeholder}
    />
  );
};
export default Input;
