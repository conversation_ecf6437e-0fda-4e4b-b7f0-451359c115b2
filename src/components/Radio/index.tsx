import React, { useState, useEffect } from "react";
import { classNames } from "@/utils";

export type TypeOnChangeRadio = React.ChangeEvent<HTMLInputElement>;

interface Props {
  id?: string;
  name?: string;
  label?: string | undefined;
  value?: string | number | undefined;
  inline?: boolean;
  checked?: boolean;
  disabled?: boolean;
  onChange?(event: TypeOnChangeRadio): void;
}

const Radio: React.FC<Props> = ({
  id,
  value,
  name,
  label,
  inline,
  checked,
  disabled,
  onChange,
}) => {
  const [checkedState, setCheckedState] = useState<boolean>(false);

  useEffect(() => {
    setCheckedState(checked ?? checkedState);
  }, [checkedState, checked]);

  return (
    <div className={classNames(!!inline ? "" : "w-full", "flex items-center")}>
      <input
        id={id}
        name={name}
        value={value ?? ""}
        checked={checkedState}
        disabled={disabled}
        type="radio"
        onChange={onChange}
        className="h-5 w-5 cursor-pointer border-gray-300 text-primary hover:ring-primary-100 focus:ring-primary-100"
      />
      {!!label && (
        <label
          htmlFor={id}
          className="ml-3 block cursor-pointer text-sm font-medium leading-6"
        >
          {label}
        </label>
      )}
    </div>
  );
};
export default Radio;
