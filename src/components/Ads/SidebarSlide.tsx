"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/pagination";
import Image from "next/image";
import { Pagination } from "swiper/modules";

const SidebarSlide = () => {
  return (
    <div className="relative">
      <Swiper
        modules={[Pagination]}
        pagination={{
          el: "#ads-pagination",
          bulletActiveClass: "custom-pagination-bullet-active",
          bulletClass: "custom-pagination-bullet",
        }}
        spaceBetween={16}
        slidesPerView={1}
      >
        <SwiperSlide>
          <div>TopDev</div>
        </SwiperSlide>
        <SwiperSlide>
          <div>TopDev</div>
        </SwiperSlide>
        <SwiperSlide>
          <div>TopDev</div>
        </SwiperSlide>
        <SwiperSlide>
          <div>TopDev</div>
        </SwiperSlide>
      </Swiper>
      <div
        id="ads-pagination"
        className="flex items-center justify-center gap-2 py-2"
      ></div>
    </div>
  );
};

export default SidebarSlide;
