"use client";
import React, { useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  decrement,
  decrementByAmount,
  increment,
  incrementByAmount,
} from "@/store/slices/couterSlice";
import { useTranslations } from "next-intl";
import Toggle from "@/components/Toggle";
import Input, { TypeOnFocusInput, TypeOnChangeInput } from "@/components/Input";
import Radio, { TypeOnChangeRadio } from "@/components/Radio";

import { classNames } from "@/utils";

const Counter = () => {
  const count = useAppSelector((state) => state.counter.value);
  const [checked, setChecked] = useState(false);
  const [value, setValue] = useState("");
  const dispatch = useAppDispatch();
  const t = useTranslations();

  const handleChangeData = (e: any) => {
    setChecked(e);
  };

  const handleChangeDataInput = (e: TypeOnChangeInput) => {
    setValue(e?.target?.value);
  };

  const handleFocusDataInput = (e: TypeOnFocusInput) => {
    console.log(e);
  };

  const handleChangeDataRadio = (e: TypeOnChangeRadio) => {
    console.log(e?.target?.value);
  };

  return (
    <div>
      <h2 className="mb-2">Toggle</h2>
      <div className="mb-5 flex items-center">
        <label htmlFor="toggle" className="mr-3">
          View Job
        </label>
        <Toggle onChange={handleChangeData} checked={checked} id="toggle" />
        <div className="pl-2">
          State:{" "}
          <span
            className={classNames(checked ? "text-green-500" : "text-red-500")}
          >
            {checked ? "on" : "off"}
          </span>
        </div>
      </div>
      <h2 className="mb-2">Input</h2>
      <div className="mb-5 flex flex-wrap items-center">
        <label htmlFor="inputText" className="mr-3">
          Name
        </label>
        <Input
          id="inputText"
          name="name"
          placeholder="Nhập họ và tên"
          onChange={handleChangeDataInput}
          onFocus={handleFocusDataInput}
          className="rounded-md border border-gray-300 px-5 py-2 font-normal text-gray-900 shadow-sm outline-none placeholder:text-gray-300 focus:ring-1 focus:ring-inset focus:ring-primary-300 sm:text-sm sm:leading-6"
        />
        <span className="mt-2 block">State: {value}</span>
      </div>
      <h2 className="mb-2">Radio</h2>
      <div className="mb-5 flex flex-wrap items-center">
        <Radio
          checked
          id="radio1"
          name="data"
          label="Email"
          onChange={handleChangeDataRadio}
          value="<EMAIL>"
        />
        <Radio
          id="radio2"
          name="data"
          label="Phone"
          onChange={handleChangeDataRadio}
          value="0333445566"
        />
      </div>
      <h2 className="mb-2">Store</h2>
      <div className="overflow-auto pb-10">
        <h2 className="inline-flex aspect-square items-center justify-center rounded bg-primary px-3 text-3xl text-white">
          {count}
        </h2>
        <div className="mt-5 flex items-center gap-5">
          <button
            type="button"
            className="rounded bg-primary px-4 py-2 text-white transition-all hover:bg-primary-500"
            onClick={() => dispatch(increment())}
          >
            Increment
          </button>
          <button
            type="button"
            className="rounded bg-primary px-4 py-2 text-white transition-all hover:bg-primary-500"
            onClick={() => dispatch(decrement())}
          >
            Decrement
          </button>
          <button
            type="button"
            className="rounded bg-primary px-4 py-2 text-white transition-all hover:bg-primary-500"
            onClick={() => dispatch(incrementByAmount(5))}
          >
            Increment by amount +5
          </button>
          <button
            type="button"
            className="rounded bg-primary px-4 py-2 text-white transition-all hover:bg-primary-500"
            onClick={() => dispatch(decrementByAmount(5))}
          >
            Decrement by amount -5
          </button>
        </div>
      </div>
    </div>
  );
};

export default Counter;
