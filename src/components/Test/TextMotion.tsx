"use client";
import { useTransition, animated } from "@react-spring/web";
import React, { useState } from "react";
const slides = [
  "Hello world",
  "Good morning",
  "Goodbye",
  "You are my sunshine",
  "Keep it up",
];
const TextMotion = () => {
  const [index, set] = useState(0);
  const transitions = useTransition(index, {
    key: index,
    from: { opacity: 0, transform: "translateY(10px)" },
    enter: { opacity: 1, transform: "translateY(0px)" },
    leave: { opacity: 0, transform: "translateY(-10px)" },
    config: { duration: 2000 },
    onRest: (_a, _b, item) => {
      if (index === item) {
        set((state) => (state + 1) % slides.length);
      }
    },
    exitBeforeEnter: true,
  });
  return (
    <div>
      {transitions((style, index) => {
        return (
          <animated.div
            className="text-3xl text-black ease-out"
            style={{ ...style }}
          >
            {slides[index]}
          </animated.div>
        );
      })}
    </div>
  );
};

export default TextMotion;
