"use client";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination } from "swiper/modules";
import Image from "next/image";
import { isMobile } from "react-device-detect";
import { useEffect, useState } from "react";
import { fetchBlogs } from "@/services/techcombank";
interface MagazineBlogsProps {
  permalink: string;
  post_title: string;
  image: string;
  terms: {
    post_tag: Array<{
      name: string;
    }>;
  };
  post_excerpt: string;
}

const SectionMagazineBlogs = () => {
  const [blogs, setBlogs] = useState<Array<MagazineBlogsProps>>([]);

  useEffect(() => {
    const fetchData = async () => {
      await fetchBlogs()
        .then((res: any) => !!res && setBlogs(res?.data?.data))
        .catch((error) => {
          console.error(error);
        });
    };
    fetchData();
  }, []);

  return (
    <div id="section-magazine-blogs" className="relative z-20">
      <div className="container relative">
        <div className="title-website mb-11 text-center text-xl font-bold leading-[44px] text-white md:text-4xl">
          <span className="inline-block px-11">E-Magazines and Blog</span>
        </div>
        <div className="box-magazine-blogs relative m-auto max-w-[1050px]">
          <Swiper
            modules={[Pagination, Navigation]}
            pagination={{ clickable: true, el: ".pagination" }}
            navigation={{
              nextEl: ".nextEl",
              prevEl: ".preEl",
            }}
            loop={false}
            slidesPerView={1}
            slidesPerGroup={1}
            spaceBetween={18}
            initialSlide={1}
            centeredSlidesBounds={true}
            autoplay={{
              delay: 5000,
            }}
            breakpoints={{
              320: {
                slidesPerView: 1,
                spaceBetween: 10,
              },
              480: {
                slidesPerView: 1,
                spaceBetween: 10,
              },
              540: {
                slidesPerView: 1,
                spaceBetween: 15,
              },
              640: {
                slidesPerView: 2,
                spaceBetween: 15,
              },
              1025: {
                slidesPerView: 3,
                spaceBetween: 18,
              },
            }}
            className="swiper-container-custom"
          >
            {!!blogs &&
              blogs.map((blog: MagazineBlogsProps, index: number) => {
                return (
                  <SwiperSlide key={index} className="item-magazine-blogs-card">
                    <div
                      className="item-magazine-blogs-card swiper-slide"
                      key={index}
                    >
                      <a
                        href={`${blog.permalink}?src=topdev_techcombank&medium=magazine`}
                        title={blog.post_title}
                        target="_blank"
                        className="inline-block overflow-hidden rounded-[16px] bg-[#415172] text-white"
                      >
                        <Image
                          width={338}
                          height={178}
                          src={blog.image.replace("-300x158", "")}
                          alt={blog.post_title}
                          className="card-img-top rounded-3 max-h-[178px] w-full"
                        />
                        <div className="card-body p-4">
                          {!!blog.terms.post_tag &&
                            blog?.terms?.post_tag?.length > 0 &&
                            !!blog.terms.post_tag && (
                              <div
                                className="tags-blogs mb-3 gap-2"
                                aria-label="tags blogs"
                                role="tags-blogs"
                              >
                                {blog.terms.post_tag.map((tag) => {
                                  return (
                                    <a
                                      role="tags"
                                      title={tag.name}
                                      key={tag.name}
                                      className="bg-[rgba(16, 36, 78, 0.2)] mb-[2px] mr-[2px] px-2 text-xs font-medium capitalize leading-[22px] last:mr-0"
                                    >
                                      {tag.name}
                                    </a>
                                  );
                                })}
                              </div>
                            )}
                          <h5 className="mb-2 line-clamp-2 min-h-[42px] text-sm font-semibold capitalize leading-5">
                            {blog.post_title}
                          </h5>
                          <p className="line-clamp-2">{blog.post_excerpt}</p>
                        </div>
                      </a>
                    </div>
                  </SwiperSlide>
                );
              })}
            {!!blogs && blogs.length > 3 && !isMobile && (
              <div className="swiper-footer items-center justify-center lg:flex">
                <div className="preEl absolute left-0 top-40 z-10">
                  <Image
                    src={"/v4/assets/images/techcombank/arrow-prev-blog.png"}
                    alt={"Previous"}
                    width={26}
                    height={42}
                  />
                </div>
                <div className="pagination justify-center"></div>
                <div className="nextEl absolute right-0 top-40 z-10">
                  <Image
                    src={"/v4/assets/images/techcombank/arrow-next-blog.png"}
                    alt={"Next"}
                    width={26}
                    height={42}
                  />
                </div>
              </div>
            )}
          </Swiper>
        </div>
      </div>
    </div>
  );
};

export default SectionMagazineBlogs;
