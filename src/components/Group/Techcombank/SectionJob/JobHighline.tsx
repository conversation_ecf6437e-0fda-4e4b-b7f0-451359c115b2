"use client";

import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import useTaxonomy from "@/utils/taxonomies";
import { ReactNode } from "react";
import {
  BsGeoAlt,
  BsCurrencyDollar,
  BsBriefcase,
  BsInfoCircle,
} from "react-icons/bs";
import { JobsTechcombankProps } from "@/types/job";

const JobHighline = ({
  jobs,
  handleReplace,
}: {
  jobs: Array<JobsTechcombankProps>;
  handleReplace(text: string): ReactNode;
}) => {
  const taxonomy = useTaxonomy();
  return (
    <div className="top-job-highline">
      <Swiper
        slidesPerView={3}
        spaceBetween={25}
        autoplay={{
          delay: 10000,
          disableOnInteraction: false,
        }}
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
          480: {
            slidesPerView: 2,
            spaceBetween: 10,
          },
          540: {
            slidesPerView: 2,
            spaceBetween: 15,
          },
          640: {
            slidesPerView: 2,
            spaceBetween: 15,
          },
          1025: {
            slidesPerView: 2,
            spaceBetween: 25,
          },
        }}
        className="swiper-container-custom"
      >
        {!!jobs &&
          jobs.map((job, index) => {
            const minExperience = job?.experiences_ids?.[0] as number;
            const maxLevels = job?.job_levels_ids?.pop() as number;
            const valueExperience = taxonomy(minExperience, "experiences");
            const valueLevels = taxonomy(maxLevels, "job_levels");
            return (
              <SwiperSlide key={index} className="item-job-highline">
                <a
                  href={`${job.detail_url}?src=topdev_techcombank&medium=jobs_highlight`}
                  title={job.title}
                  target="_blank"
                  className="d-block text-white"
                >
                  <div className="text-center">
                    {job.image_thumbnail ? (
                      <Image
                        src={job.image_thumbnail}
                        alt={job.title}
                        width={370}
                        height={220}
                        className="m-auto min-h-[220px] rounded-[20px]"
                      />
                    ) : (
                      <Image
                        src="https://assets.topdev.vn/images/2023/04/10/TopDev-image-45-**********.png"
                        alt={job.title}
                        width={370}
                        height={220}
                        className="m-auto min-h-[220px] rounded-[20px]"
                      />
                    )}
                  </div>
                  <div className="shadow-[0px 4px 4px rgba(0, 0, 0, 0.25), 3px 3px 50px #8faad0] mt-1 rounded-xl border-2 border-[#baf2fe] bg-white p-4">
                    <h5 className="min-h-[48px] text-lg font-semibold capitalize leading-6 text-[#ff0004]">
                      {job.title}
                    </h5>
                    <ul className="card-text ps-0">
                      <li className="d-block mb-3 leading-6 text-[#040c1b]">
                        <BsCurrencyDollar className="mr-2 inline-block h-5 w-5 align-middle" />
                        Negotiable
                      </li>
                      {job.addresses?.address_region_list?.length > 0 && (
                        <li className="d-block  mb-3 leading-6 text-[#040c1b]">
                          <BsGeoAlt className="mr-2 inline-block h-5 w-5 align-middle" />
                          {handleReplace(job.addresses.address_region_list)}
                        </li>
                      )}
                      {job.experiences_ids?.length > 0 && (
                        <li className="d-block mb-3 leading-6 text-[#040c1b]">
                          <BsInfoCircle className="mr-2 inline-block h-5 w-5 align-middle" />
                          {valueExperience?.text}
                        </li>
                      )}
                      {job.job_levels_ids?.length > 0 && (
                        <li className="d-block mb-3 leading-6 text-[#040c1b]">
                          <BsBriefcase className="mr-2 inline-block h-5 w-5 align-middle" />
                          {valueLevels?.text}
                        </li>
                      )}
                    </ul>
                  </div>
                </a>
              </SwiperSlide>
            );
          })}
      </Swiper>
    </div>
  );
};

export default JobHighline;
