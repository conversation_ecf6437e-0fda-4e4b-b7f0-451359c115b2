"use client";

import { Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import {
  BsGeoAlt,
  BsCurrencyDollar,
  BsBriefcase,
  BsInfoCircle,
} from "react-icons/bs";
import { JobsTechcombankProps } from "@/types/job";
import { ReactNode } from "react";
import useTaxonomy from "@/utils/taxonomies";

const TempalateJobs = ({
  jobs,
  handleReplace,
}: {
  jobs: Array<JobsTechcombankProps>;
  handleReplace(text: string): ReactNode;
}) => {
  const taxonomy = useTaxonomy();
  if (jobs.length === 0) return <></>;

  return(
    jobs.map((job, index) => {
      const minExperience = job?.experiences_ids?.[0] as number;
      const maxLevels = job?.job_levels_ids?.pop() as number;
      const valueExperience = taxonomy(minExperience, "experiences");
      const valueLevels = taxonomy(maxLevels, "job_levels");

      return (
        <div
          key={index}
          className="item-job-card mb-5 rounded-[20px] bg-white last:mb-0"
        >
          <a
            href={`${job.detail_url}?src=topdev_techcombank&medium=jobs_highlight`}
            title={job.title}
            target="_blank"
            className="d-block text-white"
          >
            <div className="bg-white p-6">
              <h5 className="line-clamp-1 mb-2 text-lg font-semibold capitalize leading-6 text-[#ff0004]">
                {job.title}
              </h5>
              <ul className="flex flex-wrap items-center justify-between gap-3 ps-0">
                {job.addresses.address_region_list?.length > 0 && (
                  <li className="d-block mb-3 w-1/2 leading-6 text-[#040c1b]">
                    <BsGeoAlt className="mr-1 inline-block h-4 w-4 align-middle" />
                    {handleReplace(job.addresses.address_region_list)}
                  </li>
                )}
                {job.experiences_ids?.length > 0 && (
                  <li className="d-block mb-3 line-clamp-1 w-[44%] leading-6 text-[#040c1b]">
                    <BsInfoCircle className="mr-1 inline-block h-4 w-4 align-middle" />
                    {valueExperience?.text}
                  </li>
                )}
                <li className="d-block w-1/2 leading-6 text-[#040c1b]">
                  <BsCurrencyDollar className="mr-1 inline-block h-4 w-4 align-middle" />
                  Negotiable
                </li>
                {job.job_levels_ids?.length > 0 && (
                  <li className="d-block w-1/2 leading-6 text-[#040c1b]">
                    <BsBriefcase className="mr-1 inline-block h-4 w-4 align-middle" />
                    {valueLevels?.text}
                  </li>
                )}
              </ul>
            </div>
          </a>
        </div>
      );
    })
  )
};

const AllJob = ({
  chunkJobs,
  handleReplace,
}: {
  chunkJobs: any;
  handleReplace(text: string): ReactNode;
}) => {
  if (!!chunkJobs && chunkJobs.length == 0) return <></>;

  return (
    <div className="all-jobs mt-4">
      <Swiper
        slidesPerView={3}
        spaceBetween={25}
        autoplay={{
          delay: 10000,
          disableOnInteraction: false,
        }}
        breakpoints={{
          320: {
            slidesPerView: 1,
            spaceBetween: 10,
          },
          480: {
            slidesPerView: 2,
            spaceBetween: 10,
          },
          540: {
            slidesPerView: 2,
            spaceBetween: 15,
          },
          640: {
            slidesPerView: 2,
            spaceBetween: 15,
          },
          1025: {
            slidesPerView: 3,
            spaceBetween: 25,
          },
        }}
        pagination={{ clickable: true, el: ".pagination" }}
        modules={[Pagination]}
        className="swiper-container-custom"
      >
        {!!chunkJobs &&
          chunkJobs.map(
            (listJobs: Array<JobsTechcombankProps>, index: number) => {
              return (
                <SwiperSlide key={index}>
                  <TempalateJobs
                    jobs={listJobs}
                    handleReplace={handleReplace}
                  />
                </SwiperSlide>
              );
            },
          )}

        <div className="swiper-footer items-center justify-center w-full !mt-5 lg:flex">
          <div className="pagination justify-center"></div>
        </div>
      </Swiper>
    </div>
  );
};
export default AllJob;
