"use client";

import { useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { isMobile } from "react-device-detect";
import { fetchJobsOfCategories, fetchJobsForIds } from "@/services/techcombank";
import { chunkSubMenu, classNames } from "@/utils";
import useTaxonomy from "@/utils/taxonomies";
import { JobsTechcombankProps } from "@/types/job";

const JobHighline = dynamic(
  () => import("@/components/Group/Techcombank/SectionJob/JobHighline"),
);

const AllJob = dynamic(
  () => import("@/components/Group/Techcombank/SectionJob/AllJob"),
);

interface categoriesJobPros {
  key: number;
}

const SectionJob = () => {
  const taxonomy = useTaxonomy();
  const listIdsJobs: string = "2030056,2030479,2030468";
  const [idActive, setIdActive] = useState<number | null>(0);
  const [totalJobs, setTotalJobs] = useState<number>(0);
  const [categoriesJob, setCategoriesJob] = useState<Array<categoriesJobPros>>(
    [],
  );
  const [jobs, setJobs] = useState<Array<JobsTechcombankProps>>([]);
  const [josFlexible, setJosFlexible] = useState<Array<JobsTechcombankProps>>(
    [],
  );
  const [chunkJob, setChunkJob] = useState<Array<JobsTechcombankProps>>([]);

  useEffect(() => {
    if (!jobs) return;

    let chunk = chunkSubMenu(jobs, 2);

    if (jobs.length === 2 || jobs.length === 3) {
      chunk = chunkSubMenu(jobs, 1);
    }

    setChunkJob(chunk);
  }, [jobs]);

  const fetchData = async (categoriesIds: number | null) => {
    await fetchJobsOfCategories(categoriesIds, 20)
      .then((res: any) => {
        if (!res && res.data.data === undefined) {
          console.error(
            "Fail to fetch Job from API, please check response bellow",
          );
          return;
        }

        setCategoriesJob(res.data.aggregations.categories);
        setJobs(res.data.data);
        setTotalJobs(res.data.meta.total);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  useEffect(() => {
    const fetchDataJosFlexible = async () => {
      await fetchJobsForIds(listIdsJobs)
        .then((res: any) => {
          if (res.data.data) setJosFlexible(res.data.data);
        })
        .catch((error) => {
          console.error(error);
        });
    };

    fetchData(null);
    fetchDataJosFlexible();
  }, []);

  const handleReplace = (text: string) => {
    let stringReplace = "";
    stringReplace = text.replaceAll("Thành phố", "");
    stringReplace = stringReplace.replaceAll("Hà Nội", "Ha Noi");
    stringReplace = stringReplace.replaceAll("Hồ Chí Minh", "Ho Chi Minh");

    return stringReplace;
  };

  /**
   * Handle change categories click
   */
  const handleFilterCategory = (categoriesIds: number | null) => {
    setIdActive(categoriesIds);
    fetchData(categoriesIds);
  };

  if (
    categoriesJob.length === 0 &&
    josFlexible.length === 0 &&
    jobs.length === 0
  )
    return;

  return (
    <section id="section-job" className="relative z-20 mt-10 scroll-mt-12">
      <div className="container">
        <div className="title-website mb-11 text-center text-xl font-bold leading-[44px] text-white md:text-4xl">
          <span className="inline-block px-11">Jobs at Techcombank</span>
        </div>
        {!!categoriesJob &&
          categoriesJob.length > 0 &&
          (isMobile ? (
            <div id="box-filter-select" className="filter-select text-center">
              <select
                name="filter-category"
                id="filter-category"
                className="mb-3"
                aria-placeholder="All Department"
                placeholder="All Department"
                onChange={(event) =>
                  handleFilterCategory(
                    !!event && ((event.target as any).value as number),
                  )
                }
              >
                <option value="">All Department</option>
                {!!categoriesJob &&
                  categoriesJob.map(
                    (category: categoriesJobPros, index: number) => {
                      const value = taxonomy(category.key, "categories");
                      return (
                        <option value={category.key} key={index}>
                          {value?.text}
                        </option>
                      );
                    },
                  )}
              </select>
            </div>
          ) : (
            <ul
              id="filterBar"
              className="filterBar mb-6 flex items-center justify-center"
            >
              <li
                className={classNames(
                  idActive == null ? "active" : "",
                  "relative m-1 ml-0 flex cursor-pointer flex-wrap items-center justify-center whitespace-nowrap rounded-[20px] px-6 py-[26px] pb-[13px] pt-[13px] text-center capitalize leading-6 text-white",
                )}
                onClick={() => handleFilterCategory(null)}
              >
                <div className="relative z-10 font-bold">
                  All
                  <span className="mt-1 block w-full font-medium">
                    {totalJobs} Jobs Available
                  </span>
                </div>
              </li>
              {!!categoriesJob &&
                categoriesJob.map(
                  (category: categoriesJobPros, index: number) => {
                    const value = taxonomy(category.key, "categories");
                    return (
                      <li
                        key={index}
                        className={classNames(
                          idActive == category.key ? "active" : "",
                          "relative m-1 flex cursor-pointer flex-wrap items-center justify-center whitespace-nowrap rounded-[20px] px-6 py-[26px] text-center capitalize leading-6 text-white",
                        )}
                        onClick={() => handleFilterCategory(category.key)}
                      >
                        <div className="relative">{value?.text}</div>
                      </li>
                    );
                  },
                )}
            </ul>
          ))}
        <JobHighline jobs={josFlexible} handleReplace={handleReplace} />
        <AllJob chunkJobs={chunkJob} handleReplace={handleReplace} />
      </div>
    </section>
  );
};
export default SectionJob;
