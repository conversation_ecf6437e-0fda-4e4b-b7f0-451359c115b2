"use client";

import Image from "next/image";
import Link from "@/components/Link/Link";
import { useEffect } from "react";
import { isMobile } from "react-device-detect";
export default function SectionHeader() {

  const handleActiveMenu = (event: any) => {
    const list = document.getElementsByClassName(
      "item-menu-navbar",
    ) as HTMLCollection;
    for (let i = 0; i < list.length; i++) {
      if (list[i].classList.contains("active")) {
        list[i].classList.remove("active");
      }
    }
    event.target.classList.add("active");
  };

  useEffect(() => {
    window?.addEventListener("scroll", function (e) {
      const headerTechcombank = document.getElementById(
        "header-techcombank",
      ) as HTMLElement;

      headerTechcombank.classList.remove("fix-menu");
      let pageHeight = window.pageYOffset || document.documentElement.scrollTop;

      if (pageHeight >= 12) {
        headerTechcombank.classList.add("fix-menu");
      }
    });
  }, []);

  return (
    <header id="header-techcombank" className="sticky top-0 z-30 py-[5px]">
      <div className="container flex items-center justify-between flex-wrap md:flex-nowrap">
        <Link className="logo block md:inline-block" href="/group/techcombank">
          <Image
            className=" min-h-[34px] max-w-[255px]"
            src="/v4/assets/images/techcombank/logo-techcombank.png"
            width="255"
            height="34"
            alt="Topdev"
          />
        </Link>
        <nav id="navbar-menu" className="flex flex-nowrap justify-start py-2">
          <ul className="flex flex-wrap">
            <li className="pl-12 first:pl-0">
              <Link
                className="item-menu-navbar font-bold md:text-base text-sm text-white"
                href="#section-job"
                title="Jobs"
                onClick={handleActiveMenu}
              >
                Jobs
              </Link>
            </li>
            <li className="pl-3 md:pl-12">
              <Link
                className="item-menu-navbar font-bold md:text-base text-sm text-white"
                href="#section-magazine-blogs"
                title="Blogs"
                onClick={handleActiveMenu}
              >
                Blogs
              </Link>
            </li>
            <li className="pl-3 md:pl-12">
              <Link
                className="item-menu-navbar font-bold md:text-base text-sm text-white"
                href="#section-about"
                title="About us"
                onClick={handleActiveMenu}
              >
                About us
              </Link>
            </li>
            <li className="pl-3 md:pl-12">
              <Link
                className="item-menu-navbar font-bold md:text-base text-sm text-white"
                href="#section-media"
                title="Media"
                onClick={handleActiveMenu}
              >
                Media
              </Link>
            </li>
          </ul>
        </nav>
      </div>
    </header>
  );
}
