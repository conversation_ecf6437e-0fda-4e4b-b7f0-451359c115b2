"use client";

import { useEffect, useState } from "react";
import { BsXLg } from "react-icons/bs";
import Image from "next/image";

const ModalViewBlog = () => {
  const [show, setShow] = useState<boolean>(false);
  useEffect(() => {
    document.body.classList.remove("overflow-hidden");
    if (!!show) {
      document.body.classList.add("overflow-hidden");
    }
  }, [show]);

  useEffect(() => {
    setTimeout(() => {
      document.body.classList.add("overflow-hidden");
      setShow(true);
    }, 10000);
  }, []);

  return (
    <>
      {show && (
        <div className="fixed bottom-0 left-0 right-0 top-0 z-40 h-full w-full bg-[rgba(0,0,0,.7)]"></div>
      )}
      {show && (
        <div
          id="modalViewBlog"
          className="shadow-[0 4px 4px rgba(0,0,0,.25), 3px 3px 50px #8faad0] fixed left-1/2 top-1/2 z-50 h-[240px] w-full max-w-[440px] rounded-[20px] border border-[#baf2fe]"
        >
          <div id="content-modal-view-blog">
            <div className="modal-view-blog-body position-relative">
              <span
                className="close-btn absolute right-4 top-2 z-[1] cursor-pointer text-[22px] text-white"
                onClick={() => setShow(false)}
              >
                <BsXLg />
              </span>
              <div className="card border-0 bg-transparent text-center text-white">
                <div className="card-body px-8 py-7">
                  <p className="card-text text-2xl font-bold">
                    Giải mã bài toán
                    <br />
                    Công nghệ &amp; Dữ liệu
                    <br />
                    trong số hoá ngân hàng
                  </p>
                  <div className="mt-6">
                    <a
                      href="/blog/techcombank/"
                      className="trackinng-[0.5px] m-auto flex max-w-[195px] items-center gap-3 rounded-[20px] border-2 border-[#baf2fe] bg-[#ff0004] px-6 text-xl font-bold leading-[46px] text-white"
                      target="_blank"
                    >
                      <Image
                        width={31}
                        height={26}
                        src="/v4/assets/images/techcombank/arrow-view-blog.png"
                        alt="arrow View Blog"
                        className="align-middle"
                      />
                      View Blog
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <span className="star one absolute"></span>
          <span className="star two absolute"></span>
          <span className="star three absolute"></span>
        </div>
      )}

      <button
        className="btn-modal-view-blog trackinng-[0.5px] fixed bottom-5 right-5 z-30 flex items-center gap-3 rounded-[20px] border-2 border-[#baf2fe] bg-[#ff0004] px-6 text-xl font-bold leading-[46px] text-white"
        onClick={() => setShow(true)}
      >
        <Image
          width={31}
          height={26}
          src="/v4/assets/images/techcombank/arrow-view-blog.png"
          alt="arrow View Blog"
          className="align-middle"
        />
        View Blog
      </button>
    </>
  );
};
export default ModalViewBlog;
