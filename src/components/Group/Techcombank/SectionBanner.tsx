"use client";

import Image from "next/image";
import Link from "next/link";
import dayjs from "dayjs";
import { isMobile } from "react-device-detect";
import { Swiper, SwiperSlide, useSwiper } from "swiper/react";
import { useAppSelector } from "@/store";
import { FC } from "react";
import { openLoginPopup } from "@/utils";
import { useState, useEffect } from "react";
import { fetchJobsOfCategoriesInFeatures } from "@/services/techcombank";

interface JobsSectionBannerProps {
  title: string;
  detail_url: string;
  content_str: string;
  expires: {
    date?: string;
  };
}

const SectionBanner: FC = () => {
  const swiper = useSwiper();
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);
  const [jobs, setJobs] = useState<Array<JobsSectionBannerProps>>([]);
  const [isClient, setIsClient] = useState<boolean>(false);

  useEffect(() => {
    const fecthData = async () => {
      await fetchJobsOfCategoriesInFeatures(2)
        .then((res: any) => {
          if (res.data.data !== undefined) {
            setJobs(res.data.data);
          }
        })
        .catch((error) => {
          console.error(error);
        });
    };

    fecthData();
    setIsClient(true);
  }, []);

  const formatDate = (date: any) => {
    if (date) {
      return dayjs(date, "DD-MM-YYYY").format("DD/MM");
    }
  };

  const allJobsSlideToNext = () => swiper.slideNext();

  return (
    <section id="seciton-banner" className="relative z-20">
      <div className="container max-w-[1488px]">
        <div className="flex flex-wrap md:flex-nowrap md:gap-3">
          <div className={`${isMobile && isClient ? "w-full" : "w-3/4"}`}>
            <Image
              src="https://assets.topdev.vn/uploads/2023/04/17/banner%20resize%20Techcom-01.jpg"
              alt="Topdev"
              width="1087"
              height="552"
              className="md:min-h-[552px] w-full max-w-[1087px]"
            />
          </div>
          {isMobile && isClient ? (
            <>
              <div className="seciton-banner-news mt-4 relative z-10 w-full bg-[#dde3ea]">
                <div className="p-4 text-[#333]">
                  <h4 className="mb-4 text-lg font-semibold text-[#000]">
                    Sign up member or Create CV
                  </h4>
                  <p className="leading-6">
                    Unlock Your Career: Get Techcombank&#39;s Latest Job
                    Opportunities Now!
                  </p>
                  <div className="mt-3 text-end">
                    {isLoggedIn ? (
                      <a
                        className="inline-block text-[18px] font-bold leading-10 text-[#000] underline"
                        onClick={() => openLoginPopup()}
                      >
                        Sign in
                      </a>
                    ) : (
                      <a className="inline-block text-[18px] font-bold leading-10 text-[#000] underline">
                        Sign up
                      </a>
                    )}
                    <a
                      href="https://topdev.vn/users/dash?openPopupCreate=1&src=topdev_techcombank&medium=button_createcv"
                      className="ml-5 inline-block text-[18px] font-bold leading-10 text-[#000] underline"
                      target="_blank"
                    >
                      Create CV
                    </a>
                  </div>
                </div>
              </div>
              {!!jobs && jobs.length > 0 && (
                <div className="mt-4 min-h-[270px] w-full">
                  <div className="px-6 py-5">
                    <h4 className="mb-3 text-xl font-bold capitalize text-white">
                      Top News
                    </h4>
                    <ul className="list-group relative">
                      <Swiper
                        slidesPerView={1}
                        loop={false}
                        autoplay={{
                          delay: 10000,
                          disableOnInteraction: false,
                        }}
                      >
                        {!!jobs &&
                          jobs.map(
                            (job: JobsSectionBannerProps, index: number) => {
                              return (
                                <SwiperSlide key={index}>
                                  <li
                                    key={index}
                                    className="mb-0 w-full list-none pl-0"
                                  >
                                    <a
                                      href={job.detail_url}
                                      title={job.title}
                                      target="_blank"
                                    >
                                      <div className="title-top-news mb-1 flex items-start justify-between">
                                        <h3 className="line-clamp-2 min-h-[36px] max-w-[280px] text-base font-semibold leading-6 text-white">
                                          {job.title}
                                        </h3>
                                      </div>
                                      <div
                                        className="desc line-clamp-2 leading-6 text-white"
                                        dangerouslySetInnerHTML={{
                                          __html: job.content_str,
                                        }}
                                      ></div>
                                      <span
                                        className="top-news-time text-right text-[#959595]"
                                        v-if="job.expires.date"
                                      >
                                        {formatDate(job.expires.date)}
                                      </span>
                                    </a>
                                  </li>
                                </SwiperSlide>
                              );
                            },
                          )}
                      </Swiper>
                      {!!jobs && jobs.length > 0 && (
                        <div
                          className="arrow-next-card-top"
                          onClick={() => allJobsSlideToNext()}
                        ></div>
                      )}
                    </ul>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="w-1/4">
              <div className="rounded-[16px] border border-[rgba(0,0,0,0.13)] bg-[#dde3ea]">
                <div className="px-4 py-6 pt-4">
                  <h3 className="mb-4 font-bold text-[#333333]">
                    Sign up member or Create CV
                  </h3>
                  <p className="leading-6 text-[#333]">
                    Unlock Your Career: Get Techcombank&#39;s Latest Job
                    Opportunities Now!
                  </p>
                  <div className="mt-3">
                    {isLoggedIn ? (
                      <a
                        title="Sign in"
                        className="border-color-[#10244e] block w-full cursor-pointer rounded-[16px] border bg-[#10244e] px-3 py-[11px] text-center font-bold text-white transition-all hover:bg-primary"
                      >
                        Sign in
                      </a>
                    ) : (
                      <a
                        href="#"
                        title="Sign up"
                        target="_blank"
                        onClick={() => openLoginPopup()}
                        className="border-color-[#10244e] block w-full rounded-[16px] border bg-[#10244e] px-3 py-[11px] text-center font-bold text-white transition-all hover:bg-primary"
                      >
                        Sign up
                      </a>
                    )}

                    <Link
                      href="https://topdev.vn/users/dash?openPopupCreate=1&src=topdev_techcombank&medium=button_createcv"
                      className="mt-3 block w-full rounded-[16px] border border-[#10244e] px-3 py-[11px] text-center font-bold text-[#10244e] transition-all hover:border-primary hover:text-primary"
                      target="_blank"
                    >
                      Create CV
                    </Link>
                  </div>
                </div>
              </div>
              <div className="mt-4 min-h-[280px] rounded-[16px] border border-[rgba(0,0,0,0.13)] bg-white">
                <div className="p-4">
                  <h3 className="text-xl font-bold capitalize leading-6 text-[#10244e]">
                    Top News
                  </h3>
                  <ul className="list-group">
                    {!!jobs &&
                      jobs.map((job: JobsSectionBannerProps, index: number) => {
                        return (
                          <li
                            key={index}
                            className="list-group-item border-0 p-0 pt-2"
                          >
                            <a
                              href={`${job.detail_url}?src=topdev_techcombank&medium=topnews`}
                              title={job.title}
                              target="_blank"
                            >
                              <div className="title-top-news mb-1 flex items-start justify-between">
                                <h3 className="line-clamp-2 leading-6 min-h-[36px] max-w-[280px] font-semibold text-[#000] hover:text-[#e01f26]">
                                  {job.title}
                                </h3>
                                {job.expires.date && (
                                  <span className="block text-right text-[#959595]">
                                    {formatDate(job.expires.date)}
                                  </span>
                                )}
                              </div>
                              <div
                                className="desc line-clamp-2 leading-4 text-[#000]"
                                dangerouslySetInnerHTML={{
                                  __html: job.content_str,
                                }}
                              ></div>
                            </a>
                          </li>
                        );
                      })}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default SectionBanner;
