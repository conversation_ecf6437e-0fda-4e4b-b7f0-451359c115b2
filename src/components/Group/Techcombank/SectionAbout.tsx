"use client";

import { useEffect, useState } from "react";
import { isMobile } from "react-device-detect";

const SectionAbout = () => {
  const [isClient, setIsClient] = useState<boolean>(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleShowContent = (event: any) => {
    const contentAbout = document.getElementById("contentAbout");
    const boxAbout = document.getElementsByClassName("box-about");

    if (event.target.innerText == "Hide") {
      event.target.innerText = "See more";
      contentAbout?.classList.add("active");
      boxAbout[0].classList.remove("active");
      return;
    }
    event.target.innerText = "Hide";
    contentAbout?.classList.remove("active");
    boxAbout[0].classList.add("active");
  };

  return (
    <section id="section-about" className="relative z-20 mt-20 scroll-my-12">
      <div className="container">
        <div className="title-website mb-11 text-center text-xl font-bold leading-[44px] text-white md:text-4xl">
          <span className="inline-block px-11">About Techcombank</span>
        </div>
        <div className="box-about m-auto max-w-[1032px] rounded-[20px] bg-cover px-3 py-6 leading-6 text-white md:min-h-[414px] md:px-16 md:py-24">
          <div
            id="contentAbout"
            className={`content ${isMobile && isClient ? "active" : ""}`}
          >
            <p>
              Founded in September 1993 and headquartered in Hanoi, Techcombank
              is one of the largest joint-stock commercial banks in Vietnam and
              one of the leading banking institutions in Asia.
            </p>
            <br />
            <p>
              Over the years, we have been awarded by many globally reputable
              organizations such as: EuroMoney, Global Finance, Wells Fargo,
              Bank of New York Mellon, AsiaRisk, Finance Asia, Global Banking
              and Finance Review, etc. We were also honored with many
              prestigious HR awards such as: HR Asia Awards; Top 2 Vietnam’s
              Best places to work in Vietnam in Banking industry for 5
              consecutive years (2016-2020); Vietnam HR Awards; Best Employer of
              Choice by Vietnam Student.....
            </p>
            <br />
            “Be greater”, as it says in the brand positioning, Techcombank is
            dedicated and committed to bringing the best value and offering
            great experience to our clients, partners, and team members.
          </div>
          {!!isMobile && isClient && (
            <div className="text-end">
              <a
                onClick={handleShowContent}
                className="btn-about text-white"
                title="See more"
              >
                See more
              </a>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};
export default SectionAbout;
