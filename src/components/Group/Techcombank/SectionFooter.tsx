import { useTranslations } from "next-intl";
import { FaSquareYoutube } from "react-icons/fa6";
import { FaFacebookSquare, FaLinkedin } from "react-icons/fa";
import Image from "next/image";
import { Link } from "@/navigation";

const SectionFooter = () => {
  const t = useTranslations();

  return (
    <footer
      id="footer"
      className="footerTechcombank overflow-hidden bg-[#04226b] pb-14 pt-5 text-white"
    >
      <div className="container max-w-[1300px]">
        <div className="flex flex-wrap gap-3 md:flex-nowrap">
          <div className="footer-bottom w-full md:w-1/3">
            <div>
              <a href="//topdev.vn">
                <Image
                  src="https://assets.topdev.vn/static/assets/desktop/images/logo-td.png"
                  width={160}
                  height={30}
                  alt="TopDev"
                  className="max-w-[160px]"
                />
              </a>
            </div>
            <div className="td-contact mt-4 text-lg font-normal">
              <p>{t("footer_address")}</p>
              <p>Copyright &copy; CÔNG TY CỔ PHẦN APPLANCER</p>
              <p>
                {t("footer_tel")}:<a href="tel:**********">0888 1555 00</a> -
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </p>
              <p>
                {t("footer_business_registration")} : ************ -
                {t("footer_issues_on")} : 27/11/2014
              </p>
            </div>
          </div>
          <div className="w-full md:w-1/6">
            <p className="pb-1 font-semibold">{t("footer_about_topdev")}</p>
            <ul className="nav flex-column">
              <li className="nav-item">
                <Link
                  href={{
                    pathname: "/about-us",
                  }}
                  className="block p-[3px] text-[15px]"
                >
                  {t("footer_list_about_us")}
                </Link>
              </li>
              <li className="nav-item">
                <h5>
                  <a href="/contact" className="block p-[3px] text-[15px]">
                    {t("footer_list_contact_us")}
                  </a>
                </h5>
              </li>
              <li className="nav-item">
                <a
                  href="/term-of-services"
                  className="block p-[3px] text-[15px]"
                >
                  {t("footer_list_terms_of_service")}
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="/page/topdev-buddies"
                  className="block p-[3px] text-[15px]"
                >
                  {t("footer_list_career_at_topdev")}
                </a>
              </li>
              <li className="nav-item">
                <a href="/privacy-policy" className="block p-[3px] text-[15px]">
                  {t("footer_list_privacy_policy")}
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="/operation-regulation"
                  className="block p-[3px] text-[15px]"
                >
                  {t("footer_list_operation_regulation_of_topdev")}
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="/resolve-complaints"
                  className="block p-[3px] text-[15px]"
                >
                  {t("footer_list_resolve_complaints")}
                </a>
              </li>
            </ul>
          </div>
          <div className="w-full md:w-1/6">
            <p className="pb-1 font-semibold">{t("footer_for_jobseekers")}</p>
            <ul className="nav flex-column">
              <li className="nav-item">
                <a
                  href="/tool/tinh-luong-gross-net"
                  className="block p-[3px] text-[15px]"
                >
                  {t("footer_list_salary_calculation_gross_net")}
                </a>
              </li>
              <li className="nav-item">
                <a href="/tao-cv-online" className="block p-[3px] text-[15px]">
                  {t("footer_list_create_cv")}
                </a>
              </li>
              <li className="nav-item">
                <a href="/it-jobs" className="block p-[3px] text-[15px]">
                  {t("footer_list_browse_all_it_jobs")}
                </a>
              </li>
              <li className="nav-item">
                <a
                  href="/page/trac-nghiem-tinh-cach"
                  className="block p-[3px] text-[15px]"
                >
                  {t("footer_list_personality_test")}
                </a>
              </li>
            </ul>
          </div>
          <div className="w-full md:w-1/6">
            <p className="pb-1 font-semibold">{t("footer_for_employers")}</p>
            <ul className="nav flex-column">
              <li className="nav-item">
                <a href="/recruit" className="block p-[3px] text-[15px]">
                  {t("footer_list_post_a_job")}
                </a>
              </li>
              <li className="nav-item">
                <a href="/page/products" className="block p-[3px] text-[15px]">
                  {t("footer_list_talent_solutions")}
                </a>
              </li>
            </ul>
          </div>
          <div className="w-full md:w-1/6">
            <h5 className="mb-5 text-[15px] font-semibold">
              {t("footer_subscrible_us_on")}
            </h5>
            <ul className="mb-5 flex">
              <li className="nav-item mr-5 text-base">
                <a
                  href="https://www.facebook.com/topdevvietnam"
                  className="facebook"
                >
                  <FaFacebookSquare />
                </a>
              </li>
              <li className="nav-item mr-5 text-base">
                <a
                  href="https://www.linkedin.com/company/topdev-vn/"
                  className="linkedin"
                >
                  <FaLinkedin />
                </a>
              </li>
              <li className="nav-item mr-5 text-base">
                <a
                  href="https://www.youtube.com/channel/UCZedbcmUtab8Y7DEt4ZZv7Q"
                  className="youtube"
                >
                  <FaSquareYoutube />
                </a>
              </li>
            </ul>
            <p className="pb-1 font-semibold">{t("footer_verified_by")}</p>

            <a
              className="mb-3 inline-block"
              href="http://online.gov.vn/HomePage/WebsiteDisplay.aspx?DocId=24677"
            >
              <Image
                src="https://accounts.topdev.vn/asset/images/logo_bocongthuong.jpgx"
                alt="Bo Cong Thuong Logo"
                className="h-[60px] w-[155px] max-w-full"
                loading="lazy"
                width="155"
                height="60"
                unoptimized
              />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default SectionFooter;
