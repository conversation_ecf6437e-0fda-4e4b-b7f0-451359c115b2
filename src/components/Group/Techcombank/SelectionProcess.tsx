import Image from "next/image";

const SelectionProcess = () => {
  return (
    <section id="section-process" className="pt-16 pb-14 md:pb-44 relative z-10">
      <div className="container">
      <div className="title-website text-xl leading-[44px] font-bold md:text-4xl mb-5 text-center text-white">
          <span className="inline-block px-11">Jobs at Techcombank</span>
        </div>
        <div className="flex flex-col md:flex-row">
          <div className="relative z-10 text-center sm:w-full md:w-1/2 lg:w-1/4">
            <div className="cursor-pointer items-process text-white">
              <Image
                width={140}
                height={140}
                src="/v4/assets/images/techcombank/1.png"
                alt="Online Application"
                className="h-[100px] md:h-[140px] w-[100px] md:w-[140px] m-auto"
              />
              <div className="px-4 pt-4">
                <h5 className="capitalize font-extrabold text-base md:text-[22px]">Online Application</h5>
              </div>
            </div>
          </div>
          <div className="relative z-10 text-center sm:w-full md:w-1/2 lg:w-1/4">
            <div className="cursor-pointer items-process text-white">
              <Image
                width={140}
                height={140}
                src="/v4/assets/images/techcombank/2.png"
                alt="Phone Interview"
                className="h-[100px] md:h-[140px] w-[100px] md:w-[140px] m-auto"
              />
              <div className="px-4 pt-4">
                <h5 className="capitalize font-extrabold text-base md:text-[22px]">Phone Interview</h5>
              </div>
            </div>
          </div>
          <div className="relative z-10 text-center sm:w-full md:w-1/2 lg:w-1/4">
            <div className="cursor-pointer items-process text-white">
              <Image
                width={140}
                height={140}
                src="/v4/assets/images/techcombank/3.png"
                alt="Technical Test"
                className="h-[100px] md:h-[140px] w-[100px] md:w-[140px] m-auto"
              />
              <div className="px-4 pt-4">
                <h5 className="capitalize font-extrabold text-base md:text-[22px]">Technical Test</h5>
              </div>
            </div>
          </div>
          <div className="relative z-10 text-center sm:w-full md:w-1/2 lg:w-1/4">
            <div className="cursor-pointer items-process text-white">
              <Image
                width={140}
                height={140}
                src="/v4/assets/images/techcombank/4.png"
                alt="Final Interview"
                className="h-[100px] md:h-[140px] w-[100px] md:w-[140px] m-auto"
              />
              <div className="px-4 pt-4">
                <h5 className="capitalize font-extrabold text-base md:text-[22px]">Final Interview</h5>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SelectionProcess;
