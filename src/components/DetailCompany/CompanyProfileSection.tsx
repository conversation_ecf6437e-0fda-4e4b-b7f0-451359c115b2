import React, { FC } from "react";
const MediaSlider = dynamic(() => import("./MediaSlider"), {
  ssr: false,
  loading: () => <Loading />,
});
import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Loading from "../Common/Loading";
import CompanyTabContent from "./CompanyTab/CompanyTabContent";
const DkiCompanyProfile = dynamic(() => import("./DkiCompanyProfile"), {
  ssr: false,
  loading: () => <Loading />,
});

interface Props {
  company: CompanyType;
  isDkiProfile?: boolean;
}

const CompanyProfileSection: FC<Props> = ({ company,isDkiProfile = false }) => {
  const t = useTranslations();
  return (
    <CompanyTabContent type="company-profile" className="bg-white p-4">
      <>
        {isDkiProfile ? (
            <DkiCompanyProfile />
        ) : (
          <>
            <h2 className="mb-2 font-semibold">{t("company_about_us")}</h2>
            <div
              className="prose max-w-full leading-snug text-gray-500"
              dangerouslySetInnerHTML={{
                __html: company.description,
              }}
            ></div>
          </>
        )}
      </>
      {company.image_galleries &&
        company.image_galleries.length > 0 &&
        !isDkiProfile && (
          <div className="mt-2">
            <div>
              <MediaSlider
                data={company.image_galleries}
                companyName={company.display_name}
              />
            </div>
          </div>
        )}
      {company.benefits &&
        company.benefits.length > 0 &&
        !isDkiProfile && (
          <div className="mt-8">
            <h2 className="font-semibold">{t("company_benefits")}</h2>
            <div className="mt-2">
              {company.benefits.length > 1 ? (
                <ul className="list-disc pl-6 text-[15px]">
                  {company.benefits.map((benefit, index) => (
                    <li
                      key={index}
                      dangerouslySetInnerHTML={{ __html: benefit.value }}
                    />
                  ))}
                </ul>
              ) : (
                <div className="pl-0">
                  {company.benefits.map((benefit, index) => (
                    <div
                      key={index}
                      className="prose max-w-full text-sm text-black lg:text-base"
                      dangerouslySetInnerHTML={{ __html: benefit.value }}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
    </CompanyTabContent>
  );
};

export default CompanyProfileSection;
