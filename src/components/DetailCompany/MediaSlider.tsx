"use client";

import { ImageGalleryType } from "@/types/company";
import Image from "next/image";
import { FC, useCallback, useState } from "react";
import GalleryModal from "./GalleryModal";

interface MediaSliderProps {
  data: ImageGalleryType[];
  numOfSlideShow?: number;
  companyName: string;
}

const MediaSlider: FC<MediaSliderProps> = ({ data, companyName }) => {
  const [isShowModal, setIsShowModal] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);

  const dataSlice = useCallback(
    (num: number) => {
      return data.slice(0, num);
    },
    [data],
  );

  const handleOpenGallery = (index: number) => {
    setIsShowModal(true);
    setActiveIndex(index);
  };
  return (
    <div>
      <div className="grid grid-cols-3 gap-4">
        {dataSlice(3).map((item, index) => {
          return (
            <div
              key={item.id}
              onClick={() => handleOpenGallery(index)}
              className="relative col-span-1 flex items-center justify-center"
            >
              {index === 2 && dataSlice(3).length < data.length && (
                <div className="absolute flex h-full w-full items-center justify-center bg-gray-400/60 text-3xl font-bold text-white">
                  +{data.length - dataSlice(3).length}
                </div>
              )}
              <Image
                src={item.url}
                width={250}
                loading="lazy"
                alt={companyName}
                height={250}
                quality={100}
                unoptimized
                className="aspect-square h-[100px] max-w-full object-cover object-center lg:h-[250px]"
              />
            </div>
          );
        })}
      </div>
      <GalleryModal
        data={data}
        isOpen={isShowModal}
        onToggle={(value) => {
          setIsShowModal(value);
        }}
        activeIndex={activeIndex}
      />
    </div>
  );
};

export default MediaSlider;
