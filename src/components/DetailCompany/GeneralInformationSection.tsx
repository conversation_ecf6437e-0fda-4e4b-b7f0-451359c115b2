"use client";

import Image from "next/image";
import React, { FC } from "react";
import SkillTagTaxonomy from "../Tag/SkillTagTaxonomy";
import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import useTaxonomy from "@/utils/taxonomies";
import { Lang } from "@/types/page";

interface Props {
  company: CompanyType;
  locale?: Lang;
}

const GeneralInformationSection: FC<Props> = ({ company, locale }) => {
  const t = useTranslations();
  const taxonomy = useTaxonomy();

  return (
    <div className="rounded bg-white">
      <div className="border-b border-t border-solid border-gray-200 bg-gray-100 px-4 py-2 lg:border-none lg:bg-white lg:py-4">
        <h2 className="text-base font-bold lg:text-lg">
          {t("search_page_general_information")}
        </h2>
      </div>
      <hr className="hidden lg:block" />
      <div className="p-4">
        {company.industries_ids?.length > 0 && (
          <div>
            <h3 className="font-bold">{t("search_page_industry")}</h3>
            <p className="mt-2">{company.industries_ids.map((id: number, index: number) => {
              const industry = taxonomy(id, "industries");
              const industryName = locale === "en" ? industry?.text_en : industry?.text;

              return `${industryName}${company.industries_ids.length - 1 > index ? ", " : ""}`;
            })}</p>
          </div>
        )}
        {company.num_employees && taxonomy(company.num_employees, 'num_employees') && (
          <div className="mt-4">
            <h3 className="font-bold">{t("search_page_company_size")}</h3>
            <p className="mt-2">{
                locale === 'en'
                  ? taxonomy(company.num_employees, 'num_employees')?.text_en
                  : taxonomy(company.num_employees, 'num_employees')?.text}</p>
          </div>
        )}
        {company.nationalities_arr?.length > 0 && (
          <div className="mt-4">
            <h3 className="font-bold">{t("search_page_nationality")}</h3>
            <ul className="mt-2">
              {company.nationalities_arr.map((national, index) => {
                return (
                  <li key={index} className="mt-1 first:mt-0">
                    <div className="flex gap-2">
                      {national.flag && (
                        <Image
                          src={national.flag}
                          alt={national.national}
                          width={26}
                          height={18}
                          className="h-[18px] w-[26px] max-w-full object-contain"
                        />
                      )}
                      <p>{national.national}</p>
                    </div>
                  </li>
                );
              })}
            </ul>
          </div>
        )}
        {company.skills_ids?.length > 0 && (
          <div className="mt-4">
            <h3 className="font-bold">{t("search_page_tech_stack")}</h3>
            <ul className="mt-1 flex flex-wrap">
              {company.skills_ids.map((skillItem, index) => {
                return (
                  <li key={index} className="mt-2">
                    <SkillTagTaxonomy skillId={skillItem} srcPage={"detailcompany"} />
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default GeneralInformationSection;
