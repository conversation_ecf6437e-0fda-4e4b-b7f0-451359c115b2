import { CompanyType } from "@/types/company";
import { DeviceType } from "@/types/device";
import { useTranslations } from "next-intl";
import { FC } from "react";
import CompanyTabItem from "./CompanyTab/CompanyTabItem";

interface Props {
  company: CompanyType;
  device?: DeviceType;
}

const CompanyTabSection: FC<Props> = ({ company, device = "desktop" }) => {
  const t = useTranslations();
  return (
      <div className="sticky top-14 z-10 flex w-full rounded-tl rounded-tr bg-white lg:top-[12rem]">
        <div className="flex-1">
          <CompanyTabItem
            title={
              device === "desktop"
                ? t("company_company_profile")
                : t("company_company_profile_mobile")
            }
            type="company-profile"
          />
        </div>
        {company.products && company.products.length > 0 && (
          <div className="flex-1">
            <CompanyTabItem
              title={
                device === "desktop"
                  ? t("company_product")
                  : t("company_product_mobile")
              }
              type="product"
            />
          </div>
        )}
        {company.news && company.news.length > 0 && (
          <div className="flex-1">
            <CompanyTabItem
              title={
                device === "desktop"
                  ? t("company_news")
                  : t("company_news_mobile")
              }
              type="news"
            />
          </div>
        )}
        {company.num_job_openings > 0 && (
          <div className="flex-1">
            <CompanyTabItem
              title={
                device === "desktop"
                  ? t("company_opening_jobs")
                  : t("company_opening_jobs_mobile")
              }
              type="opening-jobs"
              numOfJob={company.num_job_openings}
            />
          </div>
        )}
      </div>
  );
};

export default CompanyTabSection;
