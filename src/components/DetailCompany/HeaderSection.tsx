import { CompanyType } from "@/types/company";
import { classNames } from "@/utils";
import { BRAND_LOGO, getRandomCoverCompanyImage } from "@/utils/image";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import { FC } from "react";
import { HiOutlineFolderOpen } from "react-icons/hi2";
import JobOpeningButton from "./JobOpeningButton";
import ShareCompanyButton from "./ShareCompanyButton";
const FollowCompanyButton = dynamic(() => import("./FollowCompanyButton"), {
  loading: () => (
    <>
      <button className="h-12 w-full animate-pulse rounded bg-gray-200"></button>
    </>
  ),
  ssr: false,
});

interface Props {
  company: CompanyType;
}

const HeaderSection: FC<Props> = ({ company }) => {
  const imageCover = company.image_cover;
  const t = useTranslations();
  return (
    <>
      <div id="company-header-section" className={classNames("pt-6")}>
        <div className={"relative"}>
          <Image
            src={imageCover ?? getRandomCoverCompanyImage()}
            width={832}
            height={250}
            alt={company.display_name}
            className={classNames(
              "h-[250px] w-full max-w-full rounded bg-white object-cover",
            )}
            loading="lazy"
          />
          <div>
            <div
              id="common-information"
              className={classNames(
                "w-full",
                "absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-24 px-8",
              )}
            >
              <div className="flex gap-4 rounded bg-white p-4">
                <div>
                  {company.image_logo && (
                    <Image
                      className="h-28 w-40 rounded-xl object-contain p-2"
                      alt={company.display_name}
                      src={company.image_logo}
                      width={BRAND_LOGO.large.width}
                      height={BRAND_LOGO.large.height}
                      loading="lazy"
                    />
                  )}
                </div>
                <div className="flex-1">
                  <h1 className="text-lg font-bold">{company.display_name}</h1>
                  <p
                    className="mt-1 text-lg"
                    dangerouslySetInnerHTML={{ __html: company.tagline }}
                  ></p>
                  {company.num_job_openings > 0 && (
                    <div className="mt-2 flex flex-1 items-center gap-2 text-gray-400">
                      <span className="text-xl">
                        <HiOutlineFolderOpen />
                      </span>
                      <JobOpeningButton
                        numJobOpenings={company.num_job_openings}
                      />
                    </div>
                  )}
                  <div className="mt-4 flex gap-4">
                    <div className="flex-1">
                      <FollowCompanyButton company={company} />
                    </div>
                    <div>
                      <ShareCompanyButton />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default HeaderSection;
