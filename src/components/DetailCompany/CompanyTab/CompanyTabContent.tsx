"use client";

import { CompanyTabType } from "@/types/company";
import React, {
  FC,
  HTMLAttributes,
  ReactNode,
  useContext,
  useEffect,
  useRef,
} from "react";
import { useIntersectionObserver } from "usehooks-ts";
import { DetailCompanyContext } from "../DetailCompanyContext";
// import { TabGroupContext } from "./CompanyTabGroup";

interface Props extends HTMLAttributes<HTMLElement> {
  type: CompanyTabType;
  children: ReactNode;
}
const CompanyTabContent: FC<Props> = (props) => {
  const { type, children, ...attributes } = props;
  const { state, dispatch } = useContext(DetailCompanyContext);
  const sectionRef = useRef<HTMLElement | null>(null);
  const entry = useIntersectionObserver(sectionRef, { threshold: 0.3 });
  const isVisible = !!entry?.isIntersecting;

  useEffect(() => {
    if (isVisible && state.activeTab !== type) {
      dispatch({ type: "UPDATE_ACTIVE_TAB", payload: type });
    }
  }, [isVisible]);
  return (
    <section id={type} {...attributes} ref={sectionRef}>
      {children}
    </section>
  );
};

export default CompanyTabContent;
