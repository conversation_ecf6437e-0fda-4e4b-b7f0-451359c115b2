"use client";
import { classNames } from "@/utils";
import React, { FC, useContext } from "react";
import { TabGroupContext } from "./CompanyTabGroup";
import { CompanyTabType } from "@/types/company";
import { DetailCompanyContext } from "../DetailCompanyContext";

interface TabItemType {
  title: string;
  type: CompanyTabType;
  numOfJob?: number;
}

const CompanyTabItem: FC<TabItemType> = ({ title, type, numOfJob }) => {
  const { state, dispatch } = useContext(DetailCompanyContext);

  const handleMoveToSection = () => {
    dispatch({ type: "UPDATE_ACTIVE_TAB", payload: type });
    const element = document.getElementById(type);
    if (element) {
      const isCenterPosition =
        element.offsetTop - (window.innerHeight / 2) * 0.4;
      window.scrollTo({
        top: isCenterPosition > 0 ? isCenterPosition : 0,
        behavior: "smooth",
      });
    }
  };

  return (
    <button
      onClick={() => handleMoveToSection()}
      type="button"
      className={classNames(
        "relative flex h-[3.25rem] w-full items-center justify-center gap-3 px-2 py-2 transition-all after:absolute  after:bottom-0 after:left-0 after:w-full after:transition-all after:content-[''] lg:h-16",
        state.activeTab === type
          ? "font-bold text-primary after:h-1 after:bg-primary"
          : "after:h-px after:bg-gray-600",
      )}
    >
      <span>{title}</span>
      {numOfJob && (
        <span className="inline-flex h-5 w-5 items-center justify-center rounded-full bg-primary-100 text-sm font-bold text-primary lg:h-7 lg:w-7">
          {numOfJob}
        </span>
      )}
    </button>
  );
};

export default CompanyTabItem;
