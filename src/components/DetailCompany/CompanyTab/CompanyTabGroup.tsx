"use client";
import { CompanyTabType } from "@/types/company";
import { Dispatch, FC, ReactNode, createContext, useReducer } from "react";

interface StateType {
  activeTab: CompanyTabType;
}
type ActionType = { type: "updateActiveTab"; payload: CompanyTabType };

const initialState: StateType = {
  activeTab: "company-profile",
};

const reducer = (state: StateType = initialState, action: ActionType) => {
  switch (action.type) {
    case "updateActiveTab": {
      return {
        ...state,
        activeTab: action.payload,
      };
    }
    default:
      return state;
  }
};

export const TabGroupContext = createContext<{
  state: StateType;
  dispatch: Dispatch<ActionType>;
}>({
  state: initialState,
  dispatch: () => null,
});

interface CompanyTabProps {
  children: ReactNode;
}

const CompanyTabGroup: FC<CompanyTabProps> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <TabGroupContext.Provider value={{ state, dispatch }}>
      {children}
    </TabGroupContext.Provider>
  );
};

export default CompanyTabGroup;
