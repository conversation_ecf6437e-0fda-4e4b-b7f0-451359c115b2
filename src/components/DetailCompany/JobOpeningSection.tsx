import { getListJobOpenings } from "@/services/companyAPI";
import { getLocale, getLocaleForParams } from "@/utils/localeServer";
import { getTranslations } from "next-intl/server";
import { FC } from "react";
import CardRecommend from "../Card/Job/CardRecommend";
import CompanyTabContent from "./CompanyTab/CompanyTabContent";
import { JOB_DEFAULT_PARAMS } from "@/types/job";
import { COMPANY_DEFAULT_PARAMS } from "@/types/company";

interface Props {
  companyId: number;
  device?: string;
}

const JobOpeningSection: FC<Props> = async ({
  companyId,
  device = "desktop",
}) => {
  const translate = await getTranslations();
  const page = 1;
  const params = {
    "fields[job]": JOB_DEFAULT_PARAMS.join(","),
    "fields[company]": COMPANY_DEFAULT_PARAMS.join(","),
    ordering: "newest_job",
    page,
  };
  const listJobOpenings = await getListJobOpenings(companyId, params);

  return (
    <CompanyTabContent
      type="opening-jobs"
      className="mb-6 mt-8 rounded bg-white p-4"
    >
      <h2 className="text-xl font-bold lg:text-2xl">
        {translate("company_opening_jobs")}
      </h2>
      <div className="mt-6">
        <ul>
          {listJobOpenings.data.map((jobItem, index) => {
            return (
              <li key={index} className="mb-4 last:mb-0">
                <CardRecommend job={jobItem} device={device} />
              </li>
            );
          })}
        </ul>
      </div>
    </CompanyTabContent>
  );
};

export default JobOpeningSection;
