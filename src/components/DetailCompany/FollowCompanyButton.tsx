"use client";
import { followCompany } from "@/services/companyAPI";
import { useAppSelector } from "@/store";
import { FC, useContext, useEffect, useState } from "react";
import { HiBookmark, HiOutlineBookmark } from "react-icons/hi2";
import { Button } from "../Button";
import ToastNotification from "../Swal/ToastNotification";
import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import { openLoginPopup } from "@/utils";
import { DetailCompanyContext } from "./DetailCompanyContext";

interface Props {
  company: CompanyType;
  isSticky?: boolean;
}

const FollowCompanyButton: FC<Props> = ({ company, isSticky = false }) => {
  const [isLoading, setIsLoading] = useState(false);
  const user = useAppSelector((state) => state.user.user);
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);
  const t = useTranslations();
  const { state, dispatch } = useContext(DetailCompanyContext);

  useEffect(() => {
    if (company.is_followed) {
      dispatch({ type: "UPDATE_FOLLOW", payload: company.is_followed });
    }
  }, [company.is_followed]);

  const handleFollowCompany = async () => {
    if (!isLoggedIn) {
      openLoginPopup();
      return;
    }
    if (user.roles && user.roles[0] === "employer") {
      dispatch({ type: "UPDATE_FOLLOW", payload: true });
      return;
    }
    if (isLoading) {
      return;
    }

    await followCompany(company.id)
      .then((responseData) => {
        if (responseData.is_followed) {
          dispatch({ type: "UPDATE_FOLLOW", payload: true });
          ToastNotification({
            icon: "success",
            description: t("company_you_have_followed_company", {
              company: company.display_name,
            }),
          });
        } else {
          dispatch({ type: "UPDATE_FOLLOW", payload: false });
          ToastNotification({
            icon: "success",
            description: t("company_you_have_unfollowed_company", {
              company: company.display_name,
            }),
          });
        }
        setIsLoading(false);
      })
      .catch(() => {
        ToastNotification({
          icon: "error",
          description: t("company_follow_company_unsuccessfull"),
        });
        setIsLoading(false);
      });
  };

  if (isSticky) {
    return (
      <button
        type="button"
        onClick={() => handleFollowCompany()}
        className="text-2xl"
      >
        {state.isFollowed ? <HiBookmark /> : <HiOutlineBookmark />}
      </button>
    );
  }

  return (
    <Button
      accent="primary"
      isBlock
      size="md"
      leadingIcon={<HiOutlineBookmark />}
      onClick={() => handleFollowCompany()}
      loading={isLoading}
    >
      {state.isFollowed ? t("company_followed") : t("company_follow")}
    </Button>
  );
};

export default FollowCompanyButton;
