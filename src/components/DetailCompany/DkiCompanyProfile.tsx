import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Inter } from "next/font/google";

const inter = Inter({
  subsets: ["latin"],
  weight: ["400", "500","600", "700"],
});
const DkiCompanyProfile = () => {
  return (
    <div
      className={inter.className}
      style={{
        WebkitFontSmoothing: "antialiased",
      }}
    >
      <div className="pt-4 lg:mx-auto lg:pt-12">
        <div className="px-0 pb-6 lg:px-[3.2rem] lg:pb-12">
          <div className="flex flex-col items-center justify-center">
            <Image
              width={638}
              height={51}
              src="https://c.topdevvn.com/uploads/2025/04/09/topic.webp"
              alt=""
              quality={100}
              className="relative mb-8 hidden h-11 w-[361px] lg:mb-10 lg:block lg:h-[51px] lg:w-[638px]"
            />
            <Image
              width={347}
              height={32}
              src="https://c.topdevvn.com/uploads/2025/04/09/topic-mobile.webp"
              alt=""
              quality={100}
              className="relative mb-8 block h-11 lg:hidden"
            />
            <Image
              width={460}
              height={84}
              src="https://c.topdevvn.com/uploads/2025/04/09/dki.webp"
              alt=""
              quality={100}
              className="relative h-12 w-[264px] lg:h-[84px] lg:w-[460px]"
            />
          </div>
          <div className="mt-8 lg:mt-14">
            <div className="flex justify-center lg:px-8">
              <Image
                width={626}
                height={237}
                src="https://c.topdevvn.com/uploads/2025/04/01/dki.webp"
                alt=""
                className="relative h-36 w-[336px] lg:h-[237px] lg:w-[626px]"
              />
            </div>
            <div className="flex items-center justify-center gap-[14px] lg:gap-10">
              <div className="relative z-[2] flex min-w-[84px] -translate-y-4 flex-col items-center justify-center rounded-[20px] bg-white py-2 shadow-[0_4px_4px_0_#A3A6AD33] lg:w-fit lg:min-w-[178px] lg:-translate-y-[14px] lg:gap-1 lg:rounded-[24px] lg:py-4">
                <div className="w-fit rounded-full bg-[#E7F1FF] p-1 lg:p-[6px]">
                  <Image
                    width={24}
                    height={24}
                    src="https://c.topdevvn.com/uploads/2025/04/01/software.svg"
                    className="h-4 w-4 lg:h-6 lg:w-6"
                    alt=""
                  />
                </div>
                <span className="text-[10px]/[14px] text-[#7B7E87] lg:text-sm">
                  Industry
                </span>
                <span className="text-[11px] font-semibold text-[#131722] lg:text-base">
                  Software
                </span>
              </div>
              <div className="relative z-[2] flex min-w-[157px] -translate-y-4 flex-col items-center justify-center rounded-[20px] bg-white py-2 shadow-[0_4px_4px_0_#A3A6AD33] lg:w-fit lg:min-w-[253px] lg:-translate-y-[14px] lg:gap-1 lg:rounded-[24px] lg:py-4">
                <div className="w-fit rounded-full bg-[#E7F1FF] p-1 lg:p-[6px]">
                  <Image
                    width={24}
                    height={24}
                    src="https://c.topdevvn.com/uploads/2025/04/01/employees.svg"
                    className="h-4 w-4 lg:h-6 lg:w-6"
                    alt=""
                  />
                </div>
                <span className="text-[10px]/[14px] text-[#7B7E87] lg:text-sm">
                  Company size
                </span>
                <span className="text-nowrap text-[11px] font-semibold text-[#131722] lg:text-base">
                  over 5,000 employees
                </span>
              </div>
              <div className="relative z-[2] flex min-w-[84px] -translate-y-4 flex-col items-center justify-center rounded-[20px] bg-white py-2 shadow-[0_4px_4px_0_#A3A6AD33] lg:w-fit lg:min-w-[178px] lg:-translate-y-[14px] lg:gap-1 lg:rounded-[24px] lg:py-4">
                <div className="w-fit rounded-full bg-[#E7F1FF] p-1 lg:p-[6px]">
                  <Image
                    width={24}
                    height={24}
                    src="https://c.topdevvn.com/uploads/2025/04/01/diacau.svg"
                    className="h-4 w-4 lg:h-6 lg:w-6"
                    alt=""
                  />
                </div>
                <span className="text-[10px]/[14px] text-[#7B7E87] lg:text-sm">
                  Nationality
                </span>
                <span className="text-[11px] font-semibold text-[#131722] lg:text-base">
                  South-Korea
                </span>
              </div>
            </div>
            <div className="mt-14 lg:mt-[26px]">
              <h3 className="text-[30px] font-semibold text-[#03509F] lg:text-[32px]">
                About Us
                <div className="relative -bottom-[15px] block h-1 w-64 bg-[#C8C8C8] after:absolute after:inset-0 after:h-full after:w-[128px] after:bg-gradient-dki after:content-[''] lg:w-[432px] after:lg:w-[192px]"></div>
              </h3>
              <div className="mt-10 grid grid-cols-2 gap-4 lg:gap-6">
                <div className="flex flex-col rounded-[24px] bg-[#F7F7FA] px-3 pt-2 pb-4 lg:p-4 lg:flex-row lg:gap-4">
                  <div className="relative flex h-[54px] w-[54px] px-4 lg:h-[40px] lg:w-[40px]">
                    <Image
                      src={"https://c.topdevvn.com/uploads/2025/04/09/number_1.svg"}
                      alt="number_1"
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  </div>
                  <div className="flex flex-col items-center gap-2 lg:items-start">
                    <span className="text-lg font-semibold text-[#363030] lg:text-left lg:text-xl/[24px]">
                      A Leading Force in IT & Finance
                    </span>
                    <span className="text-[#51535B] lg:text-left lg:text-base/[1.2rem]">
                      Founded in <span className="font-semibold">1986</span> as
                      DAOU Tech, we entered finance in{" "}
                      <span className="font-semibold">2000 </span>
                      with{" "}
                      <span className="font-semibold">Kiwoom Securities</span>,
                      Korea’s top securities firm.
                    </span>
                  </div>
                </div>
                <div className="flex flex-col rounded-[24px] bg-[#F7F7FA] px-3 pt-2 pb-4 lg:p-4 lg:flex-row lg:gap-4">
                  <div className="relative flex h-[54px] w-[54px] px-4 lg:h-[40px] lg:w-[40px]">
                    <Image
                      src={"https://c.topdevvn.com/uploads/2025/04/09/dki.svg"}
                      alt="dki"
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  </div>
                  <div className="flex flex-col items-center gap-2 lg:items-start">
                    <span className="text-lg font-semibold text-[#363030] lg:text-left lg:text-xl/[24px]">
                      DaouKiwoom Innovation in Vietnam
                    </span>
                    <span className="text-[#51535B] lg:text-left lg:text-base/[1.2rem]">
                      Founded in <span className="font-semibold">2018 </span> to
                      drive business growth with Korea’s top IT solutions.
                    </span>
                  </div>
                </div>
                <div className="flex flex-col rounded-[24px] bg-[#F7F7FA] px-3 pt-2 pb-4 lg:p-4 lg:flex-row lg:gap-4">
                  <div className="relative flex h-[54px] w-[54px] px-4 lg:h-[40px] lg:w-[40px]">
                    <Image
                      src={"https://c.topdevvn.com/uploads/2025/04/09/global.svg"}
                      alt="global"
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  </div>
                  <div className="flex flex-col  items-start gap-2">
                    <span className="text-left text-lg font-semibold text-[#363030] lg:text-xl/[24px]">
                      Global Presence
                    </span>
                    <span className="text-[#51535B] lg:text-left lg:text-base/[1.2rem]">
                      Operating across{" "}
                      <span className="font-semibold">
                        Korea, Japan, China, France, Indonesia, and Vietnam,
                      </span>{" "}
                      balancing IT and financial expertise.
                    </span>
                  </div>
                </div>
                <div className="flex flex-col rounded-[24px] bg-[#F7F7FA] px-3 pt-2 pb-4 lg:p-4 lg:flex-row lg:gap-4">
                  <div className="relative flex h-[54px] w-[54px] px-4 lg:h-[40px] lg:w-[40px]">
                    <Image
                      src={"https://c.topdevvn.com/uploads/2025/04/09/future.svg"}
                      alt="future"
                      fill
                      style={{ objectFit: "cover" }}
                    />
                  </div>
                  <div className="flex flex-col items-center gap-2 lg:items-start">
                    <span className="text-lg font-semibold text-[#363030] lg:text-left lg:text-xl/[24px]">
                      Advancing R&D for the Future
                    </span>
                    <span className="text-[#51535B] lg:text-left lg:text-base/[1.2rem]">
                      In <span className="font-semibold">2021</span>, we
                      established an{" "}
                      <span className="font-semibold">R&D center</span> in
                      Vietnam for
                      <span className="font-semibold">
                        {" "}
                        fintech, IT outsourcing, and platform innovation.
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="hidden lg:mt-10 lg:block">
              <div className="flex items-center justify-between">
                <Image
                  width={216}
                  height={144}
                  src="https://c.topdevvn.com/uploads/2025/04/01/team-build-1.webp"
                  alt="team-build-1"
                />
                <Image
                  width={216}
                  height={144}
                  src="https://c.topdevvn.com/uploads/2025/04/01/team-build-2.webp"
                  alt="team-build-2"
                />
                <Image
                  width={216}
                  height={144}
                  src="https://c.topdevvn.com/uploads/2025/04/01/team-build-3.webp"
                  alt="team-build-3"
                />
              </div>
              <div className="flex items-center justify-between lg:mt-4">
                <Image
                  width={216}
                  height={144}
                  src="https://c.topdevvn.com/uploads/2025/04/01/team-build-4.webp"
                  className="h-[178px] w-[406px]"
                  alt=""
                />
                <Image
                  width={216}
                  height={144}
                  src="https://c.topdevvn.com/uploads/2025/04/01/team-build-5.webp"
                  className="h-[178px] w-[266px]"
                  alt=""
                />
              </div>
            </div>
            <div className="mt-10 block lg:hidden">
              <div className="grid grid-cols-2 gap-4">
                <div
                  style={{
                    position: "relative",
                    minWidth: "171px",
                    minHeight: "114px",
                  }}
                >
                  <Image
                    width={0}
                    height={0}
                    sizes="100vw"
                    style={{
                      width: "100vw",
                      height: "auto",
                    }}
                    src="https://c.topdevvn.com/uploads/2025/04/01/team-build-1.webp"
                    alt="team-build-1"
                  />
                </div>
                <div
                  style={{
                    position: "relative",
                    minWidth: "171px",
                    minHeight: "114px",
                  }}
                >
                  <Image
                    width={0}
                    height={0}
                    sizes="100vw"
                    style={{
                      width: "100vw",
                      height: "auto",
                    }}
                    src="https://c.topdevvn.com/uploads/2025/04/01/team-build-2.webp"
                    alt="team-build-2"
                  />
                </div>
                <div
                  style={{
                    position: "relative",
                    minWidth: "171px",
                    minHeight: "114px",
                  }}
                >
                  <Image
                    width={0}
                    height={0}
                    sizes="100vw"
                    style={{
                      width: "100vw",
                      height: "auto",
                    }}
                    src="https://c.topdevvn.com/uploads/2025/04/01/team-build-3.webp"
                    alt="team-build-3"
                  />
                </div>
                <div
                  style={{
                    position: "relative",
                    minWidth: "171px",
                    minHeight: "114px",
                  }}
                >
                  <Image
                    alt="Full-width image"
                    src="https://c.topdevvn.com/uploads/2025/04/01/team-build-5.webp"
                    width={0}
                    height={0}
                    sizes="100vw"
                    style={{
                      width: "100vw",
                      height: "auto",
                    }}
                  />
                </div>
              </div>
              <div className="mt-4 flex items-center justify-between">
                <div style={{ position: "relative", width: "100vw" }}>
                  <Image
                    src="https://c.topdevvn.com/uploads/2025/04/01/team-build-4.webp"
                    alt="Full-width image"
                    width={0}
                    height={0}
                    sizes="100vw"
                    style={{
                      width: "100vw",
                      height: "auto",
                    }}
                  />
                </div>
              </div>
            </div>
            <div className="relative mt-10  overflow-hidden rounded-[24px] border border-[#DBDDE3] bg-white px-4 py-6 lg:p-6">
              <h3 className="flex items-center justify-center gap-2 text-2xl font-semibold text-[#03509F]">
                DaouKiwoom Group
                <Image
                  width={30}
                  height={30}
                  src="https://c.topdevvn.com/uploads/2025/04/01/stunk2.svg"
                  alt="stunk2"
                />
              </h3>
              <div className="mt-4 rounded-[16px] bg-[#F2F6FF] px-6 py-3 lg:mt-2 lg:px-5">
                <span className="block text-center text-[15px] text-[#51535B] lg:text-xs">
                  <span className="pr-1 font-semibold">DaouKiwoom Group</span>
                  grew from IT into online
                  <span className="pr-1 font-semibold">
                    {" "}
                    finance, content, and services,
                  </span>
                  becoming a leader in each field. With a strong financial
                  foundation, it has achieved 33.4% annual growth over the past
                  five years.
                </span>
              </div>
              <div className="flex flex-col justify-between pt-6 lg:flex-row lg:gap-6">
                <div>
                  <h3 className="mb-4 text-xl font-semibold">Overview</h3>
                  <div className="mb-6 flex flex-col gap-4 lg:mb-0">
                    <div className="flex gap-3">
                      <Image
                        width={28}
                        height={28}
                        className="h-[54px] w-[54px] lg:h-7 lg:w-7"
                        src="https://c.topdevvn.com/uploads/2025/04/01/asset.svg"
                        alt=""
                      />
                      <div className="flex flex-col lg:gap-1">
                        <span className="text-[15px] text-[#9EA2AA] lg:text-xs">
                          Total Asset
                        </span>
                        <span className="text-[15px] font-semibold text-[#387FC6] lg:text-xs">
                          KRW
                          <span className="pr-1 text-[26px]/[32px] lg:text-[22px]">
                            {" "}
                            53.939
                          </span>
                          trillion
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <Image
                        width={28}
                        height={28}
                        className="h-[54px] w-[54px] lg:h-7 lg:w-7"
                        src="https://c.topdevvn.com/uploads/2025/04/01/revenue.svg"
                        alt="revenue"
                      />
                      <div className="flex flex-col lg:gap-1">
                        <span className="text-[15px] text-[#9EA2AA] lg:text-xs">
                          Total Revenue
                        </span>
                        <span className="text-[15px] font-semibold text-[#387FC6] lg:text-xs">
                          KRW
                          <span className="text-[26px]/[32px] lg:text-[22px]">
                            {" "}
                            10.334{" "}
                          </span>
                          trillion
                        </span>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <Image
                        width={28}
                        height={28}
                        className="h-[54px] w-[54px] lg:h-7 lg:w-7"
                        src="https://c.topdevvn.com/uploads/2025/04/01/profit.svg"
                        alt="profit"
                      />
                      <div className="flex flex-col lg:gap-1">
                        <span className="text-[15px] text-[#9EA2AA] lg:text-xs">
                          Total Operating Profit
                        </span>
                        <span className="text-[15px] font-semibold text-[#387FC6] lg:text-xs">
                          KRW
                          <span className="text-[26px]/[32px] lg:text-[22px]">
                            {" "}
                            667.9{" "}
                          </span>
                          billion
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Image
                        width={28}
                        height={28}
                        className="h-[54px] w-[54px] lg:h-7 lg:w-7"
                        src="https://c.topdevvn.com/uploads/2025/04/01/sub.svg"
                        alt="profit"
                      />
                      <span className="text-[15px] font-semibold text-[#9EA2AA] lg:w-[230px] lg:text-xs">
                        There are
                        <br />
                        <span className="inline-block px-1 text-[26px] text-[#387FC6] lg:text-[22px]">
                          92
                        </span>
                        Subsidiaries & Affiliates
                      </span>
                    </div>
                  </div>
                </div>
                <Image
                  width={100}
                  height={100}
                  src="https://c.topdevvn.com/uploads/2025/04/01/about-us.webp"
                  className="h-[272px] w-[376px] lg:h-[272px] lg:w-[360px]"
                  alt="aboutUS"
                />
              </div>
              <div className="my-5 h-[1px] bg-[#EAEAF1] lg:my-4"></div>
              <Link
                href="https://www.daou.co.kr/"
                className="flex gap-1 text-sm text-[#51535B] lg:text-xs"
              >
                <Image
                  width={16}
                  height={16}
                  src="https://c.topdevvn.com/uploads/2025/04/01/earth.svg"
                  className="h-fit"
                  alt="earth"
                />
                https://www.daou.co.kr/
              </Link>
              <div className="mt-1 flex gap-[2px] lg:gap-1">
                <Image
                  width={16}
                  height={16}
                  src="https://c.topdevvn.com/uploads/2025/04/01/location.svg"
                  className="h-fit w-9 lg:w-4"
                  alt="location"
                />
                <span className="flex gap-1 text-sm text-[#51535B] lg:text-xs">
                  4F, Dawoo Digital Square, 69 Geumto-ro, Sujeong-gu,
                  Seongnam-si, Gyeonggi-do, Republic of Korea
                </span>
              </div>
            </div>
            <div className="relative mt-6 lg:mt-10 overflow-hidden rounded-[24px] border border-[#DBDDE3] bg-white px-4 py-6 lg:p-6">
              <h3 className="flex items-center justify-center gap-2 text-2xl font-semibold text-[#03509F]">
                Daoukiwoom <span className="text-[#E3087F]">Innovation</span>
                <Image
                  width={30}
                  height={30}
                  src="https://c.topdevvn.com/uploads/2025/04/01/stunk.svg"
                  alt="stunk"
                />
              </h3>
              <div className="flex items-center justify-center">
                <span className="mx-auto mt-2 rounded-[24px] bg-[#FFF2F9] px-3 py-2 text-center text-xs font-semibold text-[#183B68] lg:px-4 lg:text-left lg:text-sm">
                  #ITFINANCIAL #MTS #HTS #WTS #SECURITYSOLUTION
                </span>
              </div>
              <div className="mt-6 grid grid-cols-2 gap-6 lg:gap-2">
                <div className="flex flex-col lg:flex-row lg:items-center lg:gap-3">
                  <div className="relative flex h-[40px] w-[40px]">
                    <Image
                      src="https://c.topdevvn.com/uploads/2025/04/01/idea.svg"
                      alt="idea"
                      width={40}
                      height={0}
                      style={{
                        width: "40px",
                        height: "auto",
                      }}
                    />
                  </div>
                  <span className="text-[#51535B] lg:max-w-[233px] lg:text-sm">
                    <span className="font-semibold">
                      Stable & reliable solutions{" "}
                    </span>
                    in Thailand & Indonesia
                  </span>
                </div>
                <div className="flex flex-col lg:flex-row lg:items-center lg:gap-3">
                  <div className="relative flex">
                    <Image
                      src="https://c.topdevvn.com/uploads/2025/04/01/trade.svg"
                      alt="trade"
                      width={0}
                      height={0}
                      style={{
                        width: "40px",
                        height: "auto",
                      }}
                    />
                  </div>
                  <span className="text-[#51535B] lg:max-w-[233px] lg:text-sm">
                    <span className="font-semibold">
                      Applying Korea’s No.1 securities technology{" "}
                    </span>
                    in Vietnam
                  </span>
                </div>
                <div className="flex flex-col lg:flex-row lg:items-center lg:gap-3">
                  <div className="relative flex h-[40px] w-[40px]">
                    <Image
                      src="https://c.topdevvn.com/uploads/2025/04/01/system.svg"
                      alt="system"
                      width={40}
                      height={0}
                      style={{
                        width: "40px",
                        height: "auto",
                      }}
                    />
                  </div>
                  <span className="text-[#51535B] lg:max-w-[233px] lg:text-sm">
                    <span className="font-semibold">
                      Expanding securities ITO{" "}
                    </span>
                    with IT expertise & R&D
                  </span>
                </div>
                <div className="flex flex-col lg:flex-row lg:items-center lg:gap-3">
                  <div className="relative flex h-[40px] w-[40px]">
                    <Image
                      src="https://c.topdevvn.com/uploads/2025/04/01/cloud.svg"
                      alt="system"
                      width={40}
                      height={0}
                      style={{
                        width: "40px",
                        height: "auto",
                      }}
                    />
                  </div>
                  <span className="text-[#51535B] lg:max-w-[233px] lg:text-sm">
                    Providing a
                    <span className="font-semibold">
                      {" "}
                      secure, user-friendly platform
                    </span>
                  </span>
                </div>
              </div>
              <div className="my-5 h-[1px] bg-[#EAEAF1] lg:my-4"></div>
              <a
                href="https://daoukiwoom.vn/"
                className="flex gap-1 text-xs text-[#51535B]"
              >
                <Image
                  width={16}
                  height={16}
                  src="https://c.topdevvn.com/uploads/2025/04/01/earth.svg"
                  className="h-fit"
                  alt="earth"
                />
                https://daoukiwoom.vn/
              </a>
              <div className="mt-1 flex gap-1 lg:gap-1">
                <Image
                  width={16}
                  height={16}
                  src="https://c.topdevvn.com/uploads/2025/04/01/location.svg"
                  className="h-fit w-6 lg:w-4"
                  alt="location"
                />
                <span className="flex gap-1 text-xs text-[#51535B]">
                  AP Tower, 518B Điện Biên Phủ, Phường 21, Quận Bình Thạnh,
                  Thành phố Hồ Chí Minh
                </span>
              </div>
            </div>
          </div>
          <div className="mt-11">
            <h3 className="text-[30px] font-semibold text-[#03509F] lg:text-[32px]/[32px]">
              Benefits
              <div className="relative -bottom-[15px] block h-1 w-64 bg-[#C8C8C8] after:absolute after:inset-0 after:h-full after:w-[128px] after:bg-gradient-dki after:content-[''] lg:w-[432px] after:lg:w-[192px]"></div>
            </h3>
            <div className="relative z-[2] mt-10  overflow-hidden rounded-[24px] border border-[#DBDDE3] bg-white p-4 lg:p-6">
              <div className="relative flex flex-col gap-6">
                <div className="flex items-center gap-4">
                  <Image
                    width={72}
                    height={72}
                    src="https://c.topdevvn.com/uploads/2025/04/01/salary.svg"
                    className="h-[72px] w-[72px]"
                    alt="salary"
                  />
                  <span className="text-[18px] font-semibold text-[#363030] lg:text-base">
                    Competitive Salary & Benefits
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <Image
                    width={72}
                    height={72}
                    src="https://c.topdevvn.com/uploads/2025/04/01/balance.svg"
                    className="h-[72px] w-[72px]"
                    alt="balance"
                  />
                  <span className="text-[18px] font-semibold text-[#363030] lg:text-base">
                    Leave & Work-Life Balance
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <Image
                    width={72}
                    height={72}
                    src="https://c.topdevvn.com/uploads/2025/04/01/grow.svg"
                    className="h-[72px] w-[72px]"
                    alt="grow"
                  />
                  <span className="text-[18px] font-semibold text-[#363030] lg:text-base">
                    Professional Growth & Opportunities
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <Image
                    width={72}
                    height={72}
                    src="https://c.topdevvn.com/uploads/2025/04/01/heath.svg"
                    className="h-[72px] w-[72px]"
                    alt="heath"
                  />
                  <span className="text-[18px] font-semibold text-[#363030] lg:text-base">
                    Health & Well-being
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <Image
                    width={72}
                    height={72}
                    src="https://c.topdevvn.com/uploads/2025/04/01/team.svg"
                    className="h-[72px] w-[72px]"
                    alt="team"
                  />
                  <span className="text-[18px] font-semibold text-[#363030] lg:text-base">
                    Team Activities & Perks
                  </span>
                </div>
              </div>
              <Image
                width={430}
                height={370}
                src="https://c.topdevvn.com/uploads/2025/04/01/benefits.webp"
                className="absolute hidden lg:bottom-0 lg:right-0 lg:block lg:h-[430px] lg:w-[370px]"
                alt="benefits"
              />
              <Image
                width={115}
                height={255}
                src="https://c.topdevvn.com/uploads/2025/04/01/bg-benefit.png"
                className="absolute bottom-0 right-0 z-[-1] block lg:hidden"
                alt="benefits"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DkiCompanyProfile;
