"use client";
import { ProductType } from "@/types/company";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";
import "swiper/css";
import "swiper/css/pagination";
import { Swiper, SwiperSlide } from "swiper/react";

interface ProductSliderProps {
  data: ProductType[];
}

const ProductSlider: FC<ProductSliderProps> = ({ data }) => {
  return (
    <div>
      <Swiper
        spaceBetween={12}
        loop
        slidesPerView={1}
        breakpoints={{
          576: {
            slidesPerView: 1,
            spaceBetween: 12,
          },
          991: {
            slidesPerView: 2,
            spaceBetween: 12,
          },
        }}
      >
        {data.map((productItem, index) => {
          return (
            <SwiperSlide key={index}>
              <div className="flex gap-4 rounded border border-solid border-gray-200 p-4">
                {productItem.image && (
                  <div className="w-25">
                    <Image
                      src={productItem.image}
                      alt={productItem.name}
                      width={100}
                      height={100}
                      className="aspect-square h-auto w-full max-w-full object-cover object-center"
                      loading="lazy"
                    />
                  </div>
                )}
                <div className="scrollbar-primary max-h-42 flex-1 overflow-y-auto">
                  <Link
                    className="line-clamp-1 font-bold"
                    href={productItem.link ?? ""}
                  >
                    {productItem.name}
                  </Link>
                  <div
                    className="prose mt-2 line-clamp-6"
                    dangerouslySetInnerHTML={{
                      __html: productItem.description,
                    }}
                  ></div>
                </div>
              </div>
            </SwiperSlide>
          );
        })}
      </Swiper>
    </div>
  );
};

export default ProductSlider;
