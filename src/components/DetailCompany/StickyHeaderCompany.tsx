"use client";
import { CompanyType } from "@/types/company";
import { classNames, shareFacebook } from "@/utils";
import { FC, useEffect, useRef, useState } from "react";
import { HiShare } from "react-icons/hi2";
import CompanyTabSection from "./CompanyTabSection";
import FollowCompanyButton from "./FollowCompanyButton";

interface Props {
  company: CompanyType;
}
const StickyHeaderCompany: FC<Props> = ({ company }) => {
  const headerRef = useRef<HTMLDivElement>(null);
  const [isSticky, setIsSticky] = useState(false);
  useEffect(() => {
    window.addEventListener("scroll", handleScrollWindow);
    return () => {
      window.removeEventListener("scroll", handleScrollWindow);
    };
  }, []);

  const handleScrollWindow = () => {
    if (headerRef.current) {
      if (headerRef.current.getBoundingClientRect().top <= 84) {
        setIsSticky(true);
      } else {
        setIsSticky(false);
      }
    }
  };

  const handleShareCompany = () => {
    shareFacebook(document.location.pathname);
  };

  return (
    <>
      <div
        ref={headerRef}
        className={classNames(
          "sticky z-10 mt-2 w-full bg-gray-100 pb-2 pt-4 lg:top-[5.25rem]",
          isSticky ? "visible opacity-100" : "invisible opacity-0",
        )}
      >
        <div className="flex items-start gap-6 rounded bg-white p-4">
          <div className="flex-1 text-lg">
            <p className="font-bold">{company.display_name}</p>
            {company.tagline ? (
              <p
                className="mt-1 line-clamp-1"
                dangerouslySetInnerHTML={{ __html: company.tagline }}
              ></p>
            ) : (
              <p className="mt-1 line-clamp-1 h-6"></p>
            )}
          </div>
          <div className="flex items-center gap-6">
            <div className="flex h-6 w-6 items-center justify-center">
              <FollowCompanyButton isSticky={true} company={company} />
            </div>
            <button
              type="button"
              onClick={() => handleShareCompany()}
              className="flex h-6 w-6 items-center justify-center text-2xl"
            >
              <HiShare />
            </button>
          </div>
        </div>
      </div>
      <CompanyTabSection company={company} />
    </>
  );
};

export default StickyHeaderCompany;
