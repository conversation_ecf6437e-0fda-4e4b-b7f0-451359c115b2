"use client";
import { CompanyTabType } from "@/types/company";
import { Dispatch, FC, ReactNode, createContext, useReducer } from "react";

interface StateType {
  activeTab: CompanyTabType;
  isFollowed: boolean;
}
type ActionType =
  | { type: "UPDATE_ACTIVE_TAB"; payload: CompanyTabType }
  | {
      type: "UPDATE_FOLLOW";
      payload: boolean;
    };

const initialState: StateType = {
  activeTab: "company-profile",
  isFollowed: false,
};

const reducer = (state: StateType = initialState, action: ActionType) => {
  switch (action.type) {
    case "UPDATE_ACTIVE_TAB": {
      return {
        ...state,
        activeTab: action.payload,
      };
    }
    case "UPDATE_FOLLOW": {
      return {
        ...state,
        isFollowed: action.payload,
      };
    }
    default:
      return state;
  }
};

export const DetailCompanyContext = createContext<{
  state: StateType;
  dispatch: Dispatch<ActionType>;
}>({
  state: initialState,
  dispatch: () => null,
});

interface DetailCompanyProps {
  children: ReactNode;
}

const DetailCompanyWrapper: FC<DetailCompanyProps> = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <DetailCompanyContext.Provider value={{ state, dispatch }}>
      {children}
    </DetailCompanyContext.Provider>
  );
};

export default DetailCompanyWrapper;
