"use client";
import { ImageGalleryType } from "@/types/company";
import { classNames } from "@/utils";
import Image from "next/image";
import { FC, useEffect, useRef, useState } from "react";
import { HiX } from "react-icons/hi";
import "swiper/css";
import "swiper/css/navigation";
import { Swiper, SwiperSlide } from "swiper/react";

interface Props {
  isOpen: boolean;
  activeIndex: number;
  onToggle: (value: boolean) => void;
  data: ImageGalleryType[];
}

const GalleryModal: FC<Props> = ({
  isOpen,
  onToggle,
  data,
  activeIndex = 0,
}) => {
  const [dataActive, setDataActive] = useState<ImageGalleryType>(
    data[activeIndex],
  );
  const imageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  return (
    <div
      className={classNames(
        "fixed left-0 top-0 z-50 h-screen w-full bg-black/50 transition-all",
        isOpen ? "visible opacity-100" : "invisible opacity-0",
      )}
    >
      <button
        onClick={() => onToggle(false)}
        className="fixed right-10 top-10 z-10 inline-flex h-10 w-10 items-center justify-center rounded-full bg-primary text-2xl text-white transition-all hover:bg-primary-500"
      >
        <HiX />
      </button>
      <div className="flex h-full flex-col gap-10">
        <div className="mx-auto h-full w-full flex-1 p-5 lg:p-10">
          <div className="container h-full">
            <div className="relative h-full w-full">
              <Image
                src={dataActive.url}
                alt={"Company image"}
                fill={true}
                quality={100}
                unoptimized
                objectFit="contain"
                className="h-auto max-w-full object-contain"
                ref={imageRef}
              />
            </div>
          </div>
        </div>
        <div className="bg-black/80 py-5">
          <div className="container">
            <Swiper slidesPerView={"auto"} spaceBetween={16}>
              {data.map((item, index) => {
                return (
                  <SwiperSlide key={index} style={{ width: "200px" }}>
                    <div onClick={() => setDataActive(item)} className="h-40">
                      <Image
                        width={200}
                        height={150}
                        alt="Image"
                        src={item.url}
                        className="h-[150px] max-w-full object-cover object-center"
                      />
                    </div>
                  </SwiperSlide>
                );
              })}
            </Swiper>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GalleryModal;
