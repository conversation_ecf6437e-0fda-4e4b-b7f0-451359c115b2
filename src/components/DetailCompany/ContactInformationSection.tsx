import "@/assets/styles/pages/company.scss";
import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import Link from "@/components/Link/Link";
import { FC } from "react";
import { HiOutlineLocationMarker } from "react-icons/hi";
interface Props {
  company: CompanyType;
}
const ContactInformationSection: FC<Props> = ({ company }) => {
  const t = useTranslations();
  return (
    <>
      <div className="rounded bg-white">
        <div className="p-4">
          <h2 className="text-base font-bold lg:text-lg">
            {t("company_contact_information")}
          </h2>
        </div>
        <hr />
        <div className="p-4 pt-0">
          {company.website ? (
            <div className="mt-4">
              <h3 className="font-bold">{t("company_website")}</h3>
              <Link
                className="mt-2 inline-block break-all text-blue-dark hover:underline"
                href={company.website}
                target="_blank"
              >
                {company.website}
              </Link>
            </div>
          ) : null}
          {company.social_network.length > 0 &&
          company.social_network.filter((item) => item.url && item.social)
            .length > 0 ? (
            <div className="mt-4">
              <h3 className="font-bold">{t("company_social_media")}</h3>
              <ul className="mt-2 flex flex-wrap items-center gap-2 text-xl text-gray-600">
                {company.social_network
                  .filter((item) => item.url && item.social)
                  .map((socialItem, index) => {
                    return (
                      <li key={index}>
                        <Link
                          href={socialItem.url}
                          target="_blank"
                          className="text-2xl text-gray-500 transition-all hover:text-primary"
                        >
                          <i
                            className={`fa fa-${socialItem.social}-square`}
                            aria-hidden="true"
                          ></i>
                        </Link>
                      </li>
                    );
                  })}
              </ul>
            </div>
          ) : null}
          {company.addresses.collection_addresses &&
            company.addresses.collection_addresses.length > 0 && (
              <div className="mt-4">
                <h3 className="font-bold">{t("company_office_address")}</h3>
                <ul className="mt-2">
                  {company.addresses.collection_addresses.map((addressItem) => {
                    return (
                      <li key={addressItem.id}>
                        <div className="rounded border border-solid border-white p-2 transition-all">
                          <div className="flex items-start gap-2">
                            <span className="inline-block text-2xl">
                              <HiOutlineLocationMarker />
                            </span>
                            <p className="flex-1">{addressItem.full_address}</p>
                          </div>
                        </div>
                      </li>
                    );
                  })}
                </ul>
              </div>
            )}
        </div>
      </div>
    </>
  );
};

export default ContactInformationSection;
