"use client";
import { useRouter } from "next/navigation";
import React, { FC } from "react";
import { HiArrowLeft } from "react-icons/hi2";

interface Props {
  companyName: string;
}
const HeaderTitleMobile: FC<Props> = ({ companyName }) => {
  const router = useRouter();
  const handleBackPage = () => {
    router.back();
  };
  return (
    <div className="sticky top-0 z-50 flex w-full items-center gap-6 bg-white px-5 py-4 shadow-sm">
      <button type="button" className="text-2xl" onClick={handleBackPage}>
        <HiArrowLeft />
      </button>
      <div className="flex-1">
        <h1 className="line-clamp-1 text-lg font-bold">{companyName}</h1>
      </div>
    </div>
  );
};

export default HeaderTitleMobile;
