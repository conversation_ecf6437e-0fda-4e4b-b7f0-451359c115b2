import Link from "@/components/Link/Link";
import { CompanyType } from "@/types/company";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { BRAND_LOGO, getRandomCoverCompanyImage } from "@/utils/image";
import Image from "next/image";
import { FC } from "react";
import { HiFolder } from "react-icons/hi2";
import FollowCompanyButton from "../FollowCompanyButton";
import ShareCompanyButton from "../ShareCompanyButton";

interface Props {
  company: CompanyType;
}

const HeaderSectionMobile: FC<Props> = ({ company }) => {
  const imageCover = company.image_cover;
  const t = useTranslations();

  return (
    <div className="relative">
      <div>
        <Image
          src={imageCover ?? getRandomCoverCompanyImage()}
          width={393}
          height={167}
          alt={company.display_name}
          className={classNames(
            "h-[10.5rem] w-full max-w-full object-cover object-center",
          )}
          loading="lazy"
        />
      </div>
      <div className="container -mb-20 -translate-y-20">
        <div className="rounded bg-white px-4 shadow-sm">
          <div className="pt-2">
            <div>
              {company.image_logo && (
                <Image
                  src={company.image_logo}
                  width={BRAND_LOGO.medium.width}
                  height={BRAND_LOGO.medium.height}
                  alt={company.display_name}
                  className="h-[70px] w-[100px] max-w-full rounded object-contain"
                  loading="lazy"
                />
              )}
            </div>
            <div className="mt-2">
              <h2 className="text-base font-bold">{company.display_name}</h2>
              <p className="mt-1">{company.tagline}</p>
              <div className="mt-1 text-gray-400">
                {company.num_job_openings ? (
                  <div className="flex items-center gap-1">
                    <span>
                      <HiFolder />
                    </span>
                    <Link href="#" className="font-semibold underline">
                      {company.num_job_openings}{" "}
                      {company.num_job_openings > 1
                        ? t("company_jobs_opening")
                        : t("company_job_opening")}
                    </Link>
                  </div>
                ) : null}
              </div>
            </div>
            <div className="flex gap-2 pb-4 pt-2">
              <div className="flex-1">
                <FollowCompanyButton company={company} />
              </div>
              <ShareCompanyButton />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderSectionMobile;
