import React, { FC } from "react";
import ProductSlider from "./ProductSlider";
import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import CompanyTabContent from "./CompanyTab/CompanyTabContent";

interface Props {
  company: CompanyType;
}
const CompanyProductSection: FC<Props> = ({ company }) => {
  const t = useTranslations();
  return (
    <CompanyTabContent type="product" className="mt-8 rounded bg-white p-4">
      <h2 className="text-xl font-bold lg:text-2xl">{t("company_product")}</h2>
      <div className="mt-4">
        <ProductSlider data={company.products} />
      </div>
    </CompanyTabContent>
  );
};

export default CompanyProductSection;
