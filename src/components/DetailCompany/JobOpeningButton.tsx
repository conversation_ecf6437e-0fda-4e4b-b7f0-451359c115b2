"use client";
import { useTranslations } from "next-intl";
import React, { FC } from "react";

interface Props {
  numJobOpenings: number;
}
const JobOpeningButton: FC<Props> = ({ numJobOpenings }) => {
  const t = useTranslations();

  const handleScrollToSection = (sectionNameId: string) => {
    const targetScroll = document.getElementById(`${sectionNameId}`);
    if (targetScroll) {
      targetScroll.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  return (
    <button
      onClick={() => handleScrollToSection("opening-jobs")}
      type="button"
      className="font-semibold underline transition-all hover:text-primary"
    >
      {numJobOpenings}{" "}
      {numJobOpenings > 1
        ? t("company_jobs_opening")
        : t("company_job_opening")}
    </button>
  );
};

export default JobOpeningButton;
