import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "@/components/Link/Link";
import React, { FC } from "react";
import CompanyTabContent from "./CompanyTab/CompanyTabContent";

interface Props {
  company: CompanyType;
}

const CompanyNewsSection: FC<Props> = ({ company }) => {
  const t = useTranslations();

  return (
    <CompanyTabContent type="news" className="mt-8 rounded bg-white p-4">
      <h4 className="text-2xl font-bold">{t("company_news")}</h4>
      <div className="mt-2">
        <ul>
          {company.news.map((newsItem, index) => {
            return (
              <li
                key={index}
                className="border-b border-solid border-gray-200 px-4 pb-6 pt-6 first:pt-0 last:border-b-0 last:pb-0"
              >
                <div className="flex gap-5">
                  <div className="w-30">
                    <Image
                      src={newsItem.image}
                      width={120}
                      height={120}
                      className="aspect-square h-auto w-30 max-w-full object-cover"
                      alt={newsItem.name}
                      loading="lazy"
                    />
                  </div>
                  <div className="flex-1">
                    <h4>
                      <Link
                        href={newsItem.link}
                        className="font-bold transition-all hover:text-primary"
                        target="_blank"
                      >
                        {newsItem.name}
                      </Link>
                    </h4>
                    <div
                      className="prose mt-2 line-clamp-1 break-all text-gray-500"
                      dangerouslySetInnerHTML={{
                        __html: newsItem.description,
                      }}
                    ></div>
                    <Link
                      href={newsItem.link}
                      target="_blank"
                      className="mt-2 inline-block text-sm text-primary underline"
                    >
                      Đọc bài viết
                    </Link>
                  </div>
                </div>
              </li>
            );
          })}
        </ul>
      </div>
    </CompanyTabContent>
  );
};

export default CompanyNewsSection;
