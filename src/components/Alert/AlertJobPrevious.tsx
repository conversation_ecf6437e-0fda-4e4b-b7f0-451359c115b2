import { useTranslations } from "next-intl";
import Link from "next/link";
import React, { FC, useMemo, useState } from "react";
import { HiExclamationCircle, HiXMark } from "react-icons/hi2";

type AlertJobPreviousProps = {
  jobId?: string;
  jobCompany?: string;
  jobTitle?: string;
  jobUrl?: string;
  srcPage: string;
  mediumPage: string;
  isShowApplyJob: boolean;
  source?: string;
  closeAlert?: () => void;
};

const AlertJobPrevious: FC<AlertJobPreviousProps> = ({
  jobId,
  jobCompany,
  jobTitle,
  jobUrl,
  srcPage,
  mediumPage,
  source,
  isShowApplyJob = false,
  closeAlert,
}) => {
  const t = useTranslations();
  const queryObject = useMemo(() => {
    let queryObj: { [key: string]: string } = {};
    if (isShowApplyJob === true) {
      queryObj["isShowApplyJob"] = "1";
    }
    if (srcPage) {
      queryObj["src"] = `topdev_${srcPage}`;
    }
    if (mediumPage) {
      queryObj["medium"] = mediumPage;
    }
    if (source) {
      queryObj["source"] = source;
    }
    if (jobTitle) {
      queryObj["job_title"] = jobTitle;
    }
    if (jobCompany) {
      queryObj["job_company"] = jobCompany;
    }
    if (jobUrl) {
      queryObj["job_url"] = jobUrl;
    }
    if (jobId) {
      queryObj["job_id"] = jobId;
    }
    return queryObj;
  }, [srcPage, mediumPage, isShowApplyJob]);

  return (
    <div className="fixed bottom-16 left-0 right-0 md:relative md:top-0 md:w-[939px] z-50">
      <div className="flex h-auto w-full flex-row flex-wrap items-center justify-between gap-2 border-b-2 border-[#1375FD] bg-[#EEF8FF] px-4">
        <div className="flex flex-row items-center gap-2 py-3">
          <span>
            <HiExclamationCircle
              className="h-6 w-6 text-[#1375FD]"
              width={24}
              height={24}
            />
          </span>
          <div className="flex w-auto flex-row flex-nowrap md:w-auto">
            <p className="w-[319px] text-sm font-normal text-neutral-950 md:w-auto">
              {t("detail_job_please_complete_basic_information_and_apply")}
              <span className="ml-1 text-sm font-bold text-neutral-950">
                {jobTitle && jobTitle.length > 0
                  ? jobTitle.length > 25
                    ? `${jobTitle.slice(0, 25)}...`
                    : jobTitle
                  : null}
              </span>
            </p>
          </div>
        </div>
        <div
          id="actionAlertPreviousJob"
          className="mx-auto flex flex-row gap-2 py-2"
        >
          <Link
            href={{
              pathname: jobUrl,
              query: queryObject,
            }}
            className="h-full w-full bg-neutral-200 text-sm font-bold text-neutral-900 md:h-[30px] md:w-[227px]"
          >
            <span className="flex h-auto w-[319px] justify-center rounded-sm py-[5px] text-center md:w-auto">
              {t("detail_job_button_previous_job")}
            </span>
          </Link>
          <span
            className="absolute right-0 top-0 my-auto cursor-pointer p-2 text-neutral-900 md:relative"
            onClick={closeAlert}
          >
            <HiXMark width={18} height={18} className="h-[18px] w-[18px]" />
          </span>
        </div>
      </div>
    </div>
  );
};

export default AlertJobPrevious;
