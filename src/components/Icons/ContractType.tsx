import React from "react";

type ContractTypeProps = {
  size?: number
}

const ContractType = ({ size = 20 } : ContractTypeProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.99968 18.3337C4.0792 18.3337 3.33301 17.5875 3.33301 16.667V7.35866C3.33266 6.91446 3.50964 6.48849 3.82467 6.17533L7.84134 2.15866C8.15451 1.84363 8.58047 1.66664 9.02467 1.66699H14.9997C15.9201 1.66699 16.6663 2.41318 16.6663 3.33366V16.667C16.6663 17.5875 15.9201 18.3337 14.9997 18.3337H4.99968ZM10.416 15.0008C10.6461 15.0008 10.8326 14.8143 10.8326 14.5842V13.7508C10.8326 13.5207 10.6461 13.3342 10.416 13.3342H6.24928C6.01917 13.3342 5.83262 13.5207 5.83262 13.7508V14.5842C5.83262 14.8143 6.01917 15.0008 6.24928 15.0008H10.416ZM14.166 11.2502C14.166 11.4803 13.9794 11.6669 13.7493 11.6669H6.24928C6.01917 11.6669 5.83262 11.4803 5.83262 11.2502V10.4169C5.83262 10.1867 6.01917 10.0002 6.24928 10.0002H13.7493C13.9794 10.0002 14.166 10.1867 14.166 10.4169V11.2502ZM9.16608 3.1834L4.84941 7.50007H8.33275C8.79298 7.50007 9.16608 7.12697 9.16608 6.66673V3.1834Z"
        fill="#888888"
      />
    </svg>
  );
};

export default ContractType;
