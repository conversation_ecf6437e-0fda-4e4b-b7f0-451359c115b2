import React from "react";

type CubeTechProps = {
  className?: string;
  size?: number;
};

const CubeTech: React.FC<CubeTechProps> = (props) => {
  const { size = 24 } = props;
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={props.className}
    >
      <g clipPath="url(#clip0_7095_50495)">
        <path
          d="M12.0839 2L18.5761 5.73586C18.6765 5.76366 18.9229 5.55378 19.0527 5.49818C20.8986 4.70684 22.3918 7.14586 20.874 8.38983C20.7486 8.49282 20.378 8.64004 20.3586 8.74106L20.3391 16.2253L13.7461 19.9956C13.6652 20.0849 13.7235 20.2983 13.7065 20.4255C13.4124 22.6249 10.4046 22.4115 10.228 20.4604C10.2161 20.3284 10.2741 20.0156 10.2233 19.958L3.70966 16.2253L3.69181 8.70895C3.23072 8.50652 2.84779 8.20815 2.65376 7.73241C2.57955 7.55073 2.55217 7.38353 2.50257 7.19793C2.50813 7.12119 2.49463 7.03935 2.50257 6.96339C2.6232 5.80164 3.80291 5.0287 4.92787 5.48761C5.09135 5.55417 5.33221 5.7754 5.47229 5.73586L11.9653 2H12.0839ZM19.1646 8.68899C18.5535 8.4658 18.0928 7.89921 18.0233 7.25588C18.0083 7.11805 18.0642 6.83692 17.9837 6.74803L11.9946 3.37359L6.04369 6.76565C5.93536 6.87059 6.00361 7.10631 5.98576 7.25549C5.90798 7.90744 5.49014 8.40824 4.89692 8.68194L4.88422 15.5542L10.8534 18.9419C11.0645 18.8601 11.2133 18.7262 11.4419 18.6588C12.0549 18.4779 12.6252 18.6205 13.1255 18.9944L19.1626 15.5526L19.1646 8.68859V8.68899Z"
          fill="#888888"
        />
        <path
          d="M15.9249 8.56635L12.0179 10.8264L8.08594 8.56635L11.8453 6.43431L12.0219 6.37793L15.9249 8.56635Z"
          fill="#888888"
        />
        <path
          d="M11.4109 11.8085V16.3028L7.49121 14.0557V9.62012L11.4109 11.8085Z"
          fill="#888888"
        />
        <path
          d="M16.5183 9.62012V14.0557L12.5986 16.3028V11.8085L16.5183 9.62012Z"
          fill="#888888"
        />
      </g>
      <defs>
        <clipPath id="clip0_7095_50495">
          <rect
            width="19"
            height="20"
            fill="white"
            transform="translate(2.5 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CubeTech;
