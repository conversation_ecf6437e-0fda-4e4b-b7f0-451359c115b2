import React from "react";

type ExperienceProps = {
  size?: number;
};

const Experience = ({ size = 20 }: ExperienceProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.776 4.84826H13.8204C14.4784 4.84826 15.4366 5.71786 15.4366 6.38566V7.53394C11.2054 6.79743 7.49593 10.8082 8.62195 14.9673H3.50079C2.88099 14.9673 2.02817 14.0555 2.00593 13.4259L2.00007 6.5008C1.99071 5.75259 2.88723 4.84826 3.61749 4.84826H5.70092V3.34989C5.70092 2.8542 6.40035 2.21489 6.8613 2.09663C7.39835 1.95846 9.64025 1.99476 10.278 2.04472C11.0941 2.10872 11.776 2.84015 11.776 3.66135V4.84826ZM10.2569 4.84826V3.58329L10.1984 3.52474H7.23872L7.18017 3.58329V4.84826H10.2565H10.2569Z"
        fill="#888888"
      />
      <path
        d="M13.9486 8.94648C17.683 8.61589 20.2516 12.5068 18.3633 15.7717C16.5835 18.8492 12.0111 18.704 10.3914 15.5527C8.95467 12.7578 10.807 9.22476 13.9486 8.94648ZM14.2573 10.5003C14.02 10.5491 13.7601 10.8191 13.7261 11.0592C13.6098 11.88 13.7987 12.9619 13.7316 13.81C13.7741 13.9182 13.8256 14.0192 13.8971 14.1117C14.0579 14.3198 14.7827 15.05 14.9884 15.2003C15.5976 15.6456 16.3189 15.1527 16.1671 14.4115C16.1046 14.1075 15.3549 13.5969 15.1714 13.305C15.1043 12.6067 15.2327 11.8183 15.1656 11.1318C15.1211 10.679 14.696 10.4101 14.2573 10.5003Z"
        fill="#888888"
      />
    </svg>
  );
};

export default Experience;
