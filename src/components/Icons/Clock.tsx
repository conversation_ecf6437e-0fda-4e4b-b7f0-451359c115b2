import React from "react";

const Clock = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.877099 0.877314C1.9832 -0.229057 3.75429 -0.297483 4.94241 0.720251C4.99218 0.771025 5.02074 0.838862 5.02227 0.909953C5.02783 0.975544 5.00617 1.04054 4.96238 1.08967L1.0894 4.95361C1.04194 5.00104 0.976763 5.0264 0.90973 5.0235C0.838656 5.02197 0.770836 4.99341 0.720074 4.94362C-0.29741 3.75521 -0.229001 1.98369 0.877099 0.877314ZM17.0005 0.0313315C16.3008 0.0299641 15.6228 0.273708 15.084 0.720251C15.0306 0.769412 14.9983 0.837447 14.9942 0.909953C14.9913 0.977003 15.0166 1.0422 15.064 1.08967L18.937 4.96359C18.9845 5.01103 19.0497 5.03638 19.1167 5.03348C19.1892 5.02931 19.2572 4.99708 19.3063 4.94362C20.0505 4.05002 20.2102 2.80635 19.7161 1.75363C19.2219 0.700904 18.1632 0.0294403 17.0005 0.0313315ZM1.02951 11.0141C1.02951 6.05132 5.05165 2.0282 10.0132 2.0282C14.9748 2.0282 18.9969 6.05132 18.9969 11.0141C18.9969 15.9769 14.9748 20 10.0132 20C5.05165 20 1.02951 15.9769 1.02951 11.0141ZM3.02589 11.0141C3.02589 14.874 6.15422 18.0031 10.0132 18.0031C13.8722 18.0031 17.0005 14.874 17.0005 11.0141C17.0005 7.15416 13.8722 4.02507 10.0132 4.02507C6.15422 4.02507 3.02589 7.15416 3.02589 11.0141ZM10.5422 11.0141C10.6692 11.015 10.7766 10.9205 10.7918 10.7944L11.1911 7.57949C11.2083 7.43706 11.1634 7.2941 11.0679 7.18708C10.9723 7.08006 10.8354 7.01933 10.692 7.02037H9.33444C9.19101 7.01933 9.05407 7.08006 8.95854 7.18708C8.86301 7.2941 8.81813 7.43706 8.83535 7.57949L9.23462 10.7944C9.24985 10.9205 9.35724 11.015 9.48417 11.0141H10.5422ZM9.48417 13.011H10.4824H10.5123C10.7762 13.0268 10.9819 13.2458 10.9815 13.5102V14.5086C10.9815 14.7843 10.758 15.0078 10.4824 15.0078H9.48417C9.20853 15.0078 8.98507 14.7843 8.98507 14.5086V13.5102C8.98507 13.2345 9.20853 13.011 9.48417 13.011Z"
        fill="#4F4F4F"
      />
    </svg>
  );
};

export default Clock;
