import React from "react";

type JobTypeProps = {
  size?: number;
};

const JobType = ({ size = 20 }: JobTypeProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.667 5.83236H14.167V4.16569C14.167 3.24522 13.4208 2.49902 12.5003 2.49902H7.50033C6.57985 2.49902 5.83366 3.24522 5.83366 4.16569V5.83236H3.33366C2.41318 5.83236 1.66699 6.57855 1.66699 7.49902V15.8324C1.66699 16.7528 2.41318 17.499 3.33366 17.499H16.667C17.5875 17.499 18.3337 16.7528 18.3337 15.8324V7.49902C18.3337 6.57855 17.5875 5.83236 16.667 5.83236ZM11.6671 11.6656C11.6671 12.1258 11.294 12.4989 10.8338 12.4989H9.16712C8.70689 12.4989 8.33379 12.1258 8.33379 11.6656V11.2489C8.33379 11.0188 8.52034 10.8322 8.75046 10.8322H11.2505C11.4806 10.8322 11.6671 11.0188 11.6671 11.2489V11.6656ZM7.50059 5.8321H12.5006V4.16543H7.50059V5.8321Z"
        fill="#888888"
      />
    </svg>
  );
};

export default JobType;
