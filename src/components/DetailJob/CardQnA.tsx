"use client";

import { useAppSelector } from "@/store";
import { openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { FC, useMemo } from "react";
import { HiChevronRight } from "react-icons/hi2";
type QnAProps = {
  srcPage?: string;
  mediumPage?: string;
};
const CardQnA: FC<QnAProps> = ({ srcPage, mediumPage }) => {
  const t = useTranslations();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);

  const queryObject = useMemo(() => {
    let queryObj: { [key: string]: string } = {};
    if (srcPage) {
      queryObj["src"] = `topdev_${srcPage}`;
    }
    if (mediumPage) {
      queryObj["medium"] = mediumPage;
    }
    return queryObj;
  }, [srcPage, mediumPage]);

  return (
    <section className="flex w-full flex-col justify-start gap-3 rounded-lg border border-transparent bg-gradient-push-notification-OTW p-4">
      <h2 className="text-lg font-bold text-neutral-950">
        {t("detail_job_card_qna_candidates_supporters")}
      </h2>

      <div className="rounded-lg border-t border-transparent bg-white px-4">
        <Link
          href={{
            pathname: "/interview",
            query: queryObject,
          }}
        >
          <div className="bg-qna-for-interviews flex flex-col gap-2 bg-right-bottom bg-no-repeat py-4">
            <h2 className="text-base font-bold text-black">
              {t("detail_job_card_qna_prepare_for_interviews")}
            </h2>

            <p className="text-sm text-neutral-700">
              {t.rich("detail_job_card_qna_check_topdev_qna_tool", {
                b: (chunk) => <b>{chunk}</b>,
              })}
            </p>

            <Link
              href={{
                pathname: "/interview",
                query: queryObject,
              }}
              className="relative flex flex-row flex-nowrap items-center gap-2 text-base font-semibold text-primary-300 underline"
            >
              {t("detail_job_card_qna_read_qna_for_interviews")}
              <HiChevronRight className="h-4 w-4 stroke-1 text-base font-bold text-primary-300" />
              <Image
                src="https://c.topdevvn.com/uploads/2024/07/03/interview_2x.webp"
                width={70}
                height={66}
                className="absolute -bottom-4 -right-2 h-16 w-[70px]"
                alt="Improve cv"
              />
            </Link>
          </div>
        </Link>
      </div>

      {/* support convert CV  */}

      <div className="rounded-lg border-t border-transparent bg-white px-4">
        <Link
          href={{
            pathname: "/users/profile",
            query: { isShowConvertCVModal: "1", ...queryObject },
          }}
        >
          <div className="flex flex-col gap-2 bg-right-bottom py-4">
            <h2 className="text-base font-bold text-neutral-950">
              {t("detail_job_card_improve_cv_title")}
            </h2>
            <p className="text-sm text-neutral-700">
              {t("detail_job_card_improve_cv_description")}
            </p>
            <Link
              href={{
                pathname: "/users/profile",
                query: {
                  isShowConvertCVModal: "1",
                  referring_name: "support_tool",
                  ...queryObject,
                },
              }}
              className="relative flex flex-row flex-nowrap items-center text-base font-semibold text-primary-300 underline"
            >
              {t("detail_job_card_improve_cv_button_navigate_convert_cv")}
              <HiChevronRight className="h-4 w-4 stroke-1 text-base font-bold text-primary-300" />
              <Image
                src="https://c.topdevvn.com/uploads/2024/07/03/importCV_2x.webp"
                width={70}
                height={66}
                className="absolute -bottom-4 -right-4 h-16 w-[70px]"
                alt="Improve cv"
              />
            </Link>
          </div>
        </Link>
      </div>
    </section>
  );
};

export default CardQnA;
