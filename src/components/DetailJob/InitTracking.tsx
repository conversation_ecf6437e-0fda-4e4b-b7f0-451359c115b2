"use client";

import { useEffect } from "react";
import { useTranslations } from "next-intl";
import { tracking } from "@/services/activity";
import { JobType } from "@/types/job";
import { gtag } from "@/utils";

const InitTracking = ({ jobData }: { jobData: JobType }) => {
  const t = useTranslations();
  const pathName = process.env.NEXT_PUBLIC_BASE_URL;

  //Effect client
  useEffect(() => {
    if (Object.keys(jobData).length > 0) {
      tracking({
        job_ids: jobData?.id,
        collection: "view",
        tracking_variant: 'forced-login',
      });

      gtag([
        "event",
        "page_view",
        {
          send_to: "AW-*********",
          dynx_itemid: jobData?.id,
          dynx_pagetype: "offerdetail",
          job_id: jobData?.id,
          job_locid: jobData?.addresses?.address_region_list,
          job_pagetype: "offerdetail",
        },
      ]);

      gtag({
        event: "eec.detailView",
        nonInteraction: true,
        ecommerce: {
          detail: {
            products: [
              {
                name: jobData.detail_url,
                id: jobData?.id,
                brand: jobData.company?.display_name || "",
                category: "Job",
                variant: jobData.job_types_str,
              },
            ],
          },
        },
      });

      gtag("event", "purchase", {
        send_to: "AW-*********",
        items: [
          {
            id: String(jobData?.id),
            location_id: "Vietnam",
            google_business_vertical: "jobs",
          },
        ],
      });
    }
  }, [jobData]);

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            itemListElement: [
              {
                "@type": "ListItem",
                position: 1,
                name: "TopDev",
                item: pathName,
              },
              {
                "@type": "ListItem",
                position: 2,
                name: "It Jobs",
                item: `${pathName}/${t("slug_it_jobs")}`,
              },
              {
                "@type": "ListItem",
                position: 3,
                name: jobData.title,
                item: jobData.detail_url,
              },
            ],
          }),
        }}
      />

      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: jobData.schema_job_posting ?? "" }}
      />
    </>
  );
};
export default InitTracking;
