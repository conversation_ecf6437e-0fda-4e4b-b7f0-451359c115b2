"use client";

import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store";
import { tracking } from "@/services/activity";
import ToastNotification from "@/components/Swal/ToastNotification";
import { gtag } from "@/utils";
import { JobType } from "@/types/job";
import { JOB_DISPLAY_STATUS_CLOSED } from "@/utils/enums";

const Button = dynamic(() => import("@/components/Button/Button"));

const ApplyJobButton = dynamic(
  () => import("@/components/Button/ApplyJobButton"),
);

const ExpireButton = dynamic(
  () => import("@/components/Button/ExpireButton"),
);

const CardRightApply = ({ job }: { job: JobType }) => {
  const t = useTranslations();
  const dispatch = useAppDispatch();
  const user = useAppSelector((state) => state?.user?.user);
  const currentJob = useAppSelector((state) => state?.job?.currentJob);
  const [isClient, setIsClient] = useState<boolean>(false);
  const [isAppliedJob, setIsAppliedJob] = useState<boolean>(
    job.is_applied as boolean,
  );
  const appliedJobs = useAppSelector((state) => state.user.applied_jobs);
  const isApplied = appliedJobs?.includes(job.id);

  //Effects is client
  useEffect(() => {
    setIsClient(true);
  }, []);

  //Effects
  const { id, is_applied } = currentJob;
  useEffect(() => {
    if (id > 0) {
      setIsAppliedJob(is_applied);
    }
  }, [id, is_applied]);

  //Start handleCreateCVToApply
  const handleCreateCVToApply = (jobId: number) => {
    const BUILDER_URL = process.env.NEXT_PUBLIC_CV_BUILDER_URL;
    if (user.roles && user.roles[0] === "employer") {
      ToastNotification({
        icon: "warning",
        title: t("detail_job_page_not_right_access"),
      });
      return;
    }

    const openCVBuilderAndPopupCreate = (path: any) => {
      window.open(
        BUILDER_URL + "?referring_name=cv_job&openPopupCreate=1&referer=" + encodeURIComponent(path),
        "__blank",
      );
    };

    let path = "/" + t("slug_it_jobs") + "/" + job.slug;

    openCVBuilderAndPopupCreate(path);

    // Tracking apply-now
    tracking({
      job_ids: jobId,
      collection: "apply-with-topdev-cv",
    });

    // Push GTM on click Apply Now
    gtag({
      event: "eec.jobApply",
      ecommerce: {
        add: {
          actionField: { list: "ApplyWithTopDevCV" },
          products: [
            {
              name: job.detail_url,
              id: job.id,
              brand: job?.company?.display_name,
              category: "Job",
              variant: job.job_types_str,
            },
          ],
        },
      },
    });
  };
  //End handleCreateCVToApply

  //Start show button
  return (
    <>
      {
        <ApplyJobButton
          statusDisplay={job.status_display}
          isApplied={job.is_applied}
          jobId={job.id}
          detailUrl={job.detail_url}
          companyDisplayName={job?.company?.display_name}
          jobTypesStr={job.job_types_str}
          job={job}
        />
      }
      {job.status_display !== JOB_DISPLAY_STATUS_CLOSED && <ExpireButton job={job}/>}
      
      {!isApplied && (
        <Button
          onClick={() => handleCreateCVToApply(job.id)}
          id="createTopdevCV"
          isBlock
          size="lg"
          accent="outline"
        >
          {t("detail_job_page_create_cv_to_apply")}
        </Button>
      )}
    </>
  );
  //End show button
};

export default CardRightApply;
