"use client";

import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";
import React, { useState } from "react";
import { IoMdClose } from "react-icons/io";
import { Modal } from "flowbite-react";
import { JobType } from "@/types/job";
import SpinLoadingButton from "../Spin/SpinLoadingButton";
import { sendFeedbackJob } from "@/services/jobAPI";
import ToastNotification from "@/components/Swal/ToastNotification";

const TextEditor = dynamic(() => import("@/components/TextEditor"), {
  ssr: false,
});

type Props = {
  job: JobType;
  isShowModalFeedback?: boolean;
  setIsShowModalFeedback?: Function;
};

const PopupReviewFeedback = ({
  job,
  isShowModalFeedback,
  setIsShowModalFeedback,
}: Props) => {
  const t = useTranslations();
  const MIN_HEIGHT = 576;

  const [loading, setLoading] = useState<boolean>(false);
  const [msgFeedback, setMsgFeedback] = useState<any>();

  const handleCancel = () => {
    setIsShowModalFeedback && setIsShowModalFeedback(false);
  };

  //Set content editor
  const handleChangeData = (value: string) => {
    const htmlNode = document.createElement("div");
    htmlNode.innerHTML = value;
    htmlNode?.querySelectorAll("*").forEach(function (node) {
      for (let i = 0; i < node.attributes.length; i++) {
        node.removeAttribute(node.attributes[i].name);
      }
    });
    if (htmlNode.innerHTML) {
      setMsgFeedback(htmlNode.innerHTML);
    } else {
      setMsgFeedback(null);
    }
  };

  const sendFeedback = async () => {
    const dataReq = {
      job_id: job.id,
      content: msgFeedback,
    };
    setLoading(true);

    try {
      await sendFeedbackJob(dataReq);
      handleCancel();
    } catch (error: any) {
      if (error?.response?.data?.message) {
        ToastNotification({
          icon: "error",
          description: error?.response?.data?.message,
        });
      }
    }
    setLoading(false);
  };

  return (
    <Modal
      className="modal-feedback"
      show={isShowModalFeedback}
      onClose={handleCancel}
      size={"6xl"}
      id="formApply"
    >
      <Modal.Body className="scrollbar-primary relative max-h-[820px] overflow-auto rounded-xl px-2 py-3">
        <div className="flex flex-col">
          <div className="absolute right-6 top-6">
            <span
              onClick={handleCancel}
              className="inline-block rounded cursor-pointer hover:bg-gray-200"
            >
              <IoMdClose className="w-6 h-6" />
            </span>
          </div>

          <div className="flex flex-col p-4">
            <div className="items-start">
              <span className="text-[14px] font-bold text-[#6d6d6d]">
                {t("modal_feedback_hint")}
              </span>
              <p className="text-xl font-bold text-[#dd3f24]">{job?.title}</p>
            </div>
            <div className="items-start w-auto h-auto mt-6">
              <TextEditor
                onChange={handleChangeData}
                placeholder={t("pl_editor")}
                showLoading={true}
                value={msgFeedback ?? ""}
                height={MIN_HEIGHT}
              />
            </div>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer
        className="min-h-[98px] justify-end py-0"
        style={{ boxShadow: "0px -4px 4px 0px #5F2F2626" }}
      >
        <button
          className="px9 relative flex h-[56px] w-[176px] items-center rounded bg-brand-600 hover:bg-primary-400"
          disabled={loading}
          onClick={sendFeedback}
        >
          {loading && <SpinLoadingButton />}

          <span className="mx-auto text-base font-semibold text-white">
            {t("send_feedback")}
          </span>
        </button>
      </Modal.Footer>
    </Modal>
  );
};

export default PopupReviewFeedback;
