"use client";

import { useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import Image from "next/image";
import {
  HiMiniBanknotes,
  HiMapPin,
  HiShare,
  HiMiniClock,
} from "react-icons/hi2";
import { useAppSelector } from "@/store";
import { classNames, openLoginPopup, shareFacebook } from "@/utils";
import { JOB_DISPLAY_STATUS_CLOSED } from "@/utils/enums";
import { CompanyType } from "@/types/company";
import { Link } from "@/navigation";
import FollowJobButton from "@/components/Button/FollowJobButton";

const CardDetailJobHeader = ({
  jobTitle,
  jobAddress,
  jobId,
  company,
  jobSalary,
  statusDisplay,
  jobPosted,
}: {
  jobTitle: string;
  jobAddress: string[];
  jobId: number;
  company: CompanyType;
  jobSalary: string | null;
  statusDisplay: string | null;
  jobPosted?: string | null;
}) => {
  const t = useTranslations();
  const pathName = usePathname();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const [isHidden, setIsHidden] = useState(false);
  const isJobClosed = statusDisplay === JOB_DISPLAY_STATUS_CLOSED;

  //Effect client
  const handleScroll = () => {
    setIsHidden(false);

    if (window.scrollY >= 95) {
      setIsHidden(true);
    }
  };

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <section
      id="detailJobHeader"
      className={classNames(
        isHidden ? "shadow-sm" : "",
        "sticky top-0 z-10 flex items-start gap-6 rounded bg-white p-4",
        // "flex items-start gap-6 rounded bg-white p-4",
      )}
    >
      {/* Logo detail job header */}
      <div
        className={classNames(
          isHidden ? "hidden" : "flex",
          "w-[21%] flex-initial items-center justify-center bg-white",
        )}
      >
        <Link
          className="flex min-h-[112px] items-center justify-center"
          href={{
            pathname: "/companies/[slug]",
            params: { slug: company.slug + "-" + company.id },
            query: { src: "topdev_detailjob", medium: "logo_company" },
          }}
          title={jobTitle}
        >
          <Image
            src={
              !!company?.image_logo
                ? company?.image_logo
                : "/v4/assets/images/td-logo.png"
            }
            alt="TopDev"
            className="h-full max-h-[112px] w-[160px] bg-white object-contain"
            width="160"
            height="112"
            loading="lazy"
          />
        </Link>
      </div>
      {/* End Logo detail job header */}

      {/* Info detail job header */}
      <div
        className={classNames(
          isHidden ? "w-11/12" : "w-3/4",
          "flex flex-initial flex-col",
        )}
      >
        <h1
          className={classNames(
            isHidden ? "line-clamp-1" : "",
            "text-2xl font-bold text-black",
          )}
        >
          {jobTitle}
        </h1>
        <p
          className={classNames(
            isHidden ? "" : "my-1",
            "line-clamp-1 text-base font-bold text-gray-500",
          )}
        >
          {company.display_name}
        </p>
        <div
          className={classNames(
            isHidden ? "hidden" : "",
            "my-2 max-w-[540px] text-base text-gray-500",
          )}
        >
          {!!jobAddress &&
            jobAddress.map((address: string, index: number) => {
              return (
                <div className="mb-2 flex last:mb-0" key={index}>
                  <HiMapPin className="mr-2 h-6 w-6 text-gray-300" />
                  <div className="w-11/12">{address}</div>
                </div>
              );
            })}
        </div>
        <div
          className={classNames(
            isHidden ? "hidden" : "",
            "mb-2 max-w-[540px] text-base text-gray-500",
          )}
        >
          {!!jobPosted && (
            <div className="flex last:mb-0">
              <HiMiniClock className="mr-2 flex h-6 w-6 items-center text-gray-300" />
              <div className="flex w-11/12 items-center text-base text-gray-500">
                {t("detail_job_posted_date")} {jobPosted}
              </div>
            </div>
          )}
        </div>
        <div
          className={classNames(
            isHidden ? "hidden" : "flex",
            "max-w-[540px] items-center",
          )}
        >
          <HiMiniBanknotes className="mr-2 h-6 w-6 text-gray-300" />
          {!!isLoggedIn ? (
            <p className="text-primary">
              {jobSalary ?? t("detail_job_negotiable")}
            </p>
          ) : (
            <button
              type="button"
              onClick={() =>
                openLoginPopup([
                  { name: "referring_name", value: "salary_job" },
                ])
              }
              className="text-base font-semibold text-primary-300 underline"
            >
              {t("detail_job_page_signin_to_view_salary")}
            </button>
          )}
        </div>
      </div>
      {/* End Info detail job header */}

      {/* Handle action */}
      <div className={classNames(isHidden ? "" : "flex-col", "flex gap-6")}>
        {!isJobClosed && <FollowJobButton jobId={jobId} jobTitle={jobTitle} />}

        {!isJobClosed && (
          <div
            onClick={() => shareFacebook(pathName)}
            className="cursor-pointer text-2xl hover:text-primary-300"
            id="shareFB"
          >
            <HiShare />
          </div>
        )}
      </div>
      {/* End handle action */}
    </section>
  );
  //End Tempalte
};
export default CardDetailJobHeader;
