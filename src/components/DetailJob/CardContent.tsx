"use client";

import dynamic from "next/dynamic";
import { Lang } from "@/types/page";
import { classNames } from "@/utils";
import { BenefitType, JobType } from "@/types/job";
import { CardAboutCompany, CardSliderCompany } from "@/components/DetailJob";
import CardContentTabBar from "@/components/DetailJob/CardContentTabBar";
import { HiMiniArrowRight } from "react-icons/hi2";
import { Link } from "@/navigation";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { useAppSelector } from "@/store";
import CardFeedback from "./CardFeedback";

const CardContent = ({
  isMobile,
  jobs,
  locale,
  isPreview
}: {
  isMobile: boolean;
  jobs: JobType[];
  locale: Lang;
  isPreview?: boolean
}) => {
  const t = useTranslations();
  const detailJobIndex = useAppSelector(
    (state) => state.job.detailJob.jobIndex,
  );
  const [job, setJob] = useState<JobType & {content_html?: any}>(jobs[detailJobIndex]);

  const isEmptyBenefitsJob = !job?.benefits
    .map((item) => item.value)
    .filter((element) => element !== null).length;
  const benefits =
    job?.benefits && !isEmptyBenefitsJob
      ? job?.benefits
      : job?.company?.benefits;

  useEffect(() => {
    setJob(jobs[detailJobIndex]);
  }, [detailJobIndex, jobs]);

  return (
    <section id="cardContentDetailJob">
      {/* Tab content detail job */}
      <CardContentTabBar />
      {/* End Tab content detail job */}

      {/* Job Description */}
      <div id="JobDescription" className="p-4 bg-white rounded md:px-6 md:py-4">
        {
          job.content_html ? 
            <div
              className="max-w-full pb-2 mb-4 text-sm border-b border-gray-200 last:mb-0 last:border-0 last:pb-0 lg:text-base"
              dangerouslySetInnerHTML={{ __html: job.content_html }}
            />
          :
          <>
            {!!job.content && (
              <div
                className="max-w-full pb-2 mb-4 text-sm prose border-b border-gray-200 last:mb-0 last:border-0 last:pb-0 lg:text-base"
                dangerouslySetInnerHTML={{ __html: job.content }}
              />
            )}

            {!!job.responsibilities && (
              <div className="pb-4 mb-4 border-b border-gray-200 last:mb-0 last:border-0 last:pb-0">
                <h2 className="mb-2 text-base font-bold text-black">
                  {t("detail_job_page_responsibilities")}
                </h2>
                <div
                  className="max-w-full text-sm prose text-black lg:text-base"
                  dangerouslySetInnerHTML={{
                    __html: job.responsibilities,
                  }}
                />
              </div>
            )}

            {!!job.requirements && (
              <div className="pb-4 mb-4 border-b border-gray-200 last:mb-0 last:border-0 last:pb-0">
                <h2 className="mb-2 text-base font-bold text-black">
                  {t("detail_job_page_requirements")}
                </h2>
                <div
                  className="max-w-full text-sm prose text-black lg:text-base"
                  dangerouslySetInnerHTML={{ __html: job.requirements }}
                />
              </div>
            )}

            {!!benefits && benefits.length > 0 && (
              <div>
                <h2 className="mb-2 text-base font-bold text-black">
                  {t("detail_job_page_benefits_of_this_jobs")}
                </h2>
                <div className="max-w-full text-sm prose text-black lg:text-base">
                  {benefits.length > 1 ? (
                    <ul>
                      {benefits.map((benefit, index) => (
                        <li
                          key={index}
                          dangerouslySetInnerHTML={{ __html: benefit.value }}
                        />
                      ))}
                    </ul>
                  ) : (
                    <div className="pl-0">
                      {benefits.map((benefit, index) => (
                        <div
                          key={index}
                          className="max-w-full text-sm prose text-black lg:text-base"
                          dangerouslySetInnerHTML={{ __html: benefit.value }}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </>
        }
        
      </div>
      {/* End Job Description */}

      {/* About Company */}
      <div
        id="AboutCompany"
        className={classNames(
          isMobile ? "hidden" : "mt-8",
          "rounded bg-white p-2 md:px-6 md:py-4",
        )}
      >
        <CardAboutCompany locale={locale as Lang} company={job?.company} />

        {/* Slide Company */}
        {Number(job?.company?.image_galleries?.length) > 0 && (
          <div id="GalleriesCompany" className="mt-4">
            <div className="overflow-hidden">
              <div className="lg:w-[808px]">
                <CardSliderCompany
                  isClient={true}
                  galleriesCompany={job?.company?.image_galleries}
                />
              </div>
            </div>
          </div>
        )}
        {/* End Slide Company */}

        <div className="mt-4 text-center">
          <Link
            className="text-primary-300 hover:text-primary-400"
            title={t(
              isMobile
                ? "detail_job_page_view_company_page"
                : "detail_job_page_view_compnay",
            )}
            href={{
              pathname: "/companies/[slug]",
              params: {
                slug: job?.company?.slug + "-" + job?.company?.id,
              },
              query: { src: "topdev_detailjob", medium: "viewcompany" },
            }}
          >
            <span className="text-sm font-semibold underline transition-all md:text-base">
              {t(
                isMobile
                  ? "detail_job_page_view_company_page"
                  : "detail_job_page_view_compnay",
              )}
            </span>
            {isMobile && (
              <HiMiniArrowRight className="inline-block w-5 h-5 ml-2 md:hidden" />
            )}
          </Link>
        </div>
      </div>
      {/* End About Company */}

      {/* Show Card Feedback */}
      {isPreview && <div className="mt-4">
        <CardFeedback
          gridCols={4}
          gridLargeCol={3}
          job={job}
          left="left-5"
        />
      </div>}
      
      {/* End Card Feedback */}
    </section>
  );
};

export default CardContent;
