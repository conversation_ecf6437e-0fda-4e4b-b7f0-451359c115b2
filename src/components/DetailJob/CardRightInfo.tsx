"use client";

import { JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { classNames } from "@/utils";
import useTaxonomy from "@/utils/taxonomies";
import { useTranslations } from "next-intl";
import { Fragment, useEffect, useState } from "react";
import { isMobile } from "react-device-detect";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import Link from "next/link";

interface CardRightInfoProps {
  locale: Lang;
  job?: JobType;
  srcPage?: string;
  mediumPage?: string;
}

const CardRightInfo = ({
  locale,
  job,
  srcPage = "detailjob",
  mediumPage = 'general_info'
}: CardRightInfoProps) => {
  const t = useTranslations();
  const taxonomy = useTaxonomy();
  const [isClient, setIsClient] = useState(false);
  const EXP_EXCEPT_IDS = [1640, 1651, 4851];
  const convertSlugTaxonomy = (id: number, slug: string): string => {
    return "/" + t("slug_it_jobs") + "/" + slug + "-kt" + id;
  };

  const recruimentProcess =
    job?.recruiment_process && job?.recruiment_process.length > 0
      ? job?.recruiment_process
      : job?.company.recruitment_process;

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isIncludeExceptId = (id: number) => {
    return EXP_EXCEPT_IDS.includes(id);
  };

  const ExperienceInfo = () => {
    if (!job?.experiences_ids || !job?.experiences_ids.length) return null;
    const fromExperienceId = job.experiences_ids[0];
    const dataTaxonomy = taxonomy(fromExperienceId, "experiences");

    if (!dataTaxonomy) {
      return null;
    }
    const name = locale === "en" ? dataTaxonomy?.text_en : dataTaxonomy?.text;
    return (
      <Link
        className="text-sm hover:text-primary-300 hover:underline md:text-base"
        key={fromExperienceId}
        href={{
          pathname: convertSlugTaxonomy(
            fromExperienceId,
            dataTaxonomy?.slug as string,
          ),
          query: { src: 'topdev_' + srcPage, medium: mediumPage },
        }}
        title={name}
      >
        {isIncludeExceptId(fromExperienceId) ? null : t("detail_job_from")}{" "}
        {name}
      </Link>
    );
  };

  return (
    <>
      <div className="flex flex-wrap">
        {/* experiences */}
        {!!job?.experiences_ids && job?.experiences_ids.length > 0 && (
          <div className="item-card-info mb-2 w-1/2 md:mb-4 md:w-full">
            <h3 className="mb-1 text-sm font-bold text-black md:text-base">
              {t("detail_job_minimum_year_of_experience")}
            </h3>
            <div>
              <ExperienceInfo />
            </div>
          </div>
        )}
        {/* End experiences */}

        {/* job levels */}
        {!!job?.job_levels_ids && job?.job_levels_ids.length > 0 && (
          <div className="item-card-info mb-2 w-1/2 pl-3 md:mb-4 md:w-full md:pl-0">
            <h3 className="mb-1 text-sm font-bold text-black md:text-base">
              {t("detail_job_page_level")}
            </h3>
            <div>
              {job?.job_levels_ids.map((id: number, index: number) => {
                const dataTaxonomy = taxonomy(id, "job_levels");
                const name =
                  locale === "en" ? dataTaxonomy?.text_en : dataTaxonomy?.text;
                return (
                  <Fragment key={index + "-" + id}>
                    <Link
                      className="text-sm hover:text-primary-300 hover:underline md:text-base"
                      href={{
                        pathname: convertSlugTaxonomy(
                          id,
                          dataTaxonomy?.slug as string,
                        ),
                        query: { src: 'topdev_' + srcPage, medium: mediumPage },
                      }}
                      title={name}
                    >
                      {name}
                    </Link>
                    {job?.job_levels_ids?.length - 1 > index ? ", " : ""}
                  </Fragment>
                );
              })}
            </div>
          </div>
        )}

        {/* End job levels */}

        {/* job types */}
        {!!job?.job_types_ids && job?.job_types_ids.length > 0 && (
          <div className="item-card-info mb-2 w-1/2 md:mb-4 md:w-full">
            <h3 className="mb-1 text-sm font-bold text-black md:text-base">
              {t("detail_job_page_job_type")}
            </h3>
            <div>
              {!!job?.job_types_ids &&
                job?.job_types_ids.map((id: number, index: number) => {
                  const dataTaxonomy = taxonomy(id, "job_types");
                  const name =
                    locale === "en"
                      ? dataTaxonomy?.text_en
                      : dataTaxonomy?.text;
                  return (
                    <Fragment key={index + "-" + id}>
                      <Link
                        className="text-sm hover:text-primary-300 hover:underline md:text-base"
                        href={{
                          pathname: convertSlugTaxonomy(
                            id,
                            dataTaxonomy?.slug as string,
                          ),
                          query: { src: 'topdev_' + srcPage, medium: mediumPage },
                        }}
                        title={name}
                      >
                        {name}
                      </Link>
                      {!!job.job_types_ids &&
                      job?.job_types_ids.length - 1 > index
                        ? ", "
                        : ""}
                    </Fragment>
                  );
                })}
            </div>
          </div>
        )}

        {/* End job types */}

        {/* contract types */}

        <div className="item-card-info mb-2 w-1/2 pl-3 md:mb-4 md:w-full md:pl-0">
          <h3 className="mb-1 text-sm font-bold text-black md:text-base">
            {t("detail_job_page_contract_type")}
          </h3>
          <div>
            {!!job?.contract_types_ids &&
              job?.contract_types_ids.map((id: number, index: number) => {
                const dataTaxonomy = taxonomy(id, "contract_types");
                const name =
                  locale === "en" ? dataTaxonomy?.text_en : dataTaxonomy?.text;
                return (
                  <Fragment key={index + "-" + id}>
                    <Link
                      className="text-sm hover:text-primary-300 hover:underline md:text-base"
                      key={index + "-" + id}
                      href={{
                        pathname: convertSlugTaxonomy(
                          id,
                          dataTaxonomy?.slug as string,
                        ),
                        query: { src: 'topdev_' + srcPage, medium: mediumPage },
                      }}
                      title={name}
                    >
                      {name}
                    </Link>
                    {!!job?.contract_types_ids &&
                    (job?.contract_types_ids?.length as number) - 1 > index
                      ? ", "
                      : ""}
                  </Fragment>
                );
              })}
          </div>
        </div>
        {/* End contract types */}
      </div>

      {/* company Skills Ids */}
      <div className="item-card-info mb-4">
        <h3 className="mb-1 text-sm font-bold text-black md:text-base">
          {t("detail_job_page_tech_stack")}
        </h3>
        <div
          className={classNames(
            isMobile ? "max-h-[200px] overflow-y-auto" : "",
          )}
        >
          <div className="flex flex-wrap gap-y-2">
            {job?.skills_ids.map((id: number, index: number) => {
              return (
                <div key={index}>
                  <SkillTagTaxonomy skillId={id} srcPage={"detailjob"} />
                </div>
              );
            })}
          </div>
        </div>
      </div>
      {/* End company Skills Ids */}

      {/* recruiment Process */}
      {!!recruimentProcess && recruimentProcess?.length > 0 && (
        <div className="item-card-info">
          <h3 className="mb-1 text-sm font-bold text-black md:text-base">
            {t("detail_job_page_recruitment_progress")}
          </h3>
          <div>
            <ul className="list-disc pl-5 text-base">
              {recruimentProcess?.map((process: any, index: number) => {
                return (
                  <li
                    key={index}
                    className="mb-1 text-sm last:mb-0 md:text-base"
                  >
                    {t("detail_job_page_round") + " " + (index + 1)}:{" "}
                    {process.name}
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      )}
      {/* End recruiment Process */}
    </>
  );
};

//End Template
export default CardRightInfo;
