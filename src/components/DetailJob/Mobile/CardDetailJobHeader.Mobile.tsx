"use client";

import dynamic from "next/dynamic";
import Image from "next/image";
import { Swiper, SwiperClass, SwiperSlide } from "swiper/react";
import {
  HiMiniArrowLeft,
  HiShare,
  HiOutlineMapPin,
  HiOutlineBanknotes,
  HiOutlineClock,
} from "react-icons/hi2";
import { VscLoading } from "react-icons/vsc";
import { useEffect, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import { JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { openLoginPopup, shareFacebook, gtag } from "@/utils";
import { JOB_DISPLAY_STATUS_CLOSED } from "@/utils/enums";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  createOrUpdateCurrentJob,
  setDetailJobIndex,
} from "@/store/slices/jobSlice";
import ApplyJobButton from "@/components/Button/ApplyJobButton";
import CardHeaderInfoMobile from "@/components/DetailJob/Mobile/CardHeaderInfo.mobile";
import ExpireButton from "@/components/Button/ExpireButton";
const PopupSuggestScrollSideway = dynamic(
  () => import("@/components/DetailJob/Mobile/PopupSuggestScrollSideway"),
);

//Component Follow Button
const FollowJobButton = dynamic(
  () => import("@/components/Button/FollowJobButton"),
);
//End Component Follow Button

//Interface Card Detail Job Header Mobile Props
interface CardDetailJobHeaderMobileProps {
  locale: Lang;
  jobData: JobType;
  jobs: Array<JobType>;
}

const CardDetailJobHeaderMobile = ({
  locale,
  jobData,
  jobs,
}: CardDetailJobHeaderMobileProps) => {
  const t = useTranslations();
  const dispatch = useAppDispatch();
  const swiperRef = useRef(null);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isClient, setIsClient] = useState(false);
  const [job, setJob] = useState<JobType>(jobs[0]);
  const [isShowPopupSuggest, setIsShowPopupSuggest] = useState(false);

  useEffect(() => {
    if (!!jobs && jobs?.length > 0) {
      // Push eec.impressionView GTM
      const impressions: any[] = [];

      jobs.forEach((job, index) => {
        if (index !== 0) {
          impressions.push({
            name: job.detail_url,
            id: job.id,
            brand: job.company?.display_name,
            category: "Job",
            list: "details-job-jbd-more-jobs",
            position: index,
          });
        }
      });
      if (!!impressions && impressions.length > 0) {
        gtag({
          event: "eec.impressionView",
          nonInteraction: true,
          ecommerce: {
            impressions,
          },
        });
      }
    }
  }, [jobs]);

  //Effect client
  useEffect(() => {
    setIsClient(true);
  }, []);

  //isJobClosed is checkjob closed
  const isJobClosed = job?.status_display === JOB_DISPLAY_STATUS_CLOSED;

  const handleChangeJob = (swiper: SwiperClass) => {
    const indexSwiper = swiper?.realIndex as number;

    dispatch(setDetailJobIndex(indexSwiper));
    dispatch(createOrUpdateCurrentJob(jobs[indexSwiper]));

    const job = jobs[indexSwiper];
    setJob(job);
    window.history.pushState(
      {},
      String(job?.title),
      "/" + t("slug_it_jobs_detail") + "/" + job?.slug + "-" + job?.id,
    );

    // GTM event "view_recommenedjob_swipe" scroll sideways to view more jobs
    gtag({
      event: "view_recommenedjob_swipe",
    });
  };

  useEffect(() => {
    const lastShown = localStorage.getItem("popupLastShown");
    const now = new Date().getTime();

    if (!lastShown || now - parseInt(lastShown) > 24 * 60 * 60 * 1000) {
      setIsShowPopupSuggest(true);
      localStorage.setItem("popupLastShown", now.toString());

      const timer = setTimeout(() => {
        setIsShowPopupSuggest(false);
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, []);

  return (
    <>
      <div className="detailJobHeaderMobile sticky left-0 top-0 z-20 flex w-full justify-between gap-2 bg-white px-5 py-4 shadow-sm lg:px-2 lg:py-4">
        <button
          type="button"
          className="inline-block"
          onClick={() => history.back()}
        >
          <HiMiniArrowLeft className="h-6 w-6" />
        </button>
        <h1 className="line-clamp-1 max-w-sm text-lg font-bold text-black">
          {job?.title}
        </h1>
        {!isJobClosed && (
          <div
            onClick={() => shareFacebook(location?.pathname)}
            className="inline-block cursor-pointer hover:text-primary-300"
          >
            <HiShare className="h-6 w-6" />
          </div>
        )}
      </div>

      <div className="py-5">
        <Swiper
          ref={swiperRef}
          slidesPerView="auto"
          centeredSlides={true}
          onSlideChangeTransitionEnd={(swiper) => handleChangeJob(swiper)}
          className="mySwiper"
          id="detailJobSwiperMobile"
        >
          {!!jobs &&
            jobs?.length > 0 &&
            jobs.map((job: JobType, index: number) => {
              return (
                <SwiperSlide
                  className="mx-3 flex !h-full !w-full max-w-[320px] items-center justify-center"
                  key={index}
                >
                  <div className="h-full min-h-[500px] w-full overflow-hidden rounded bg-white px-3 py-3 md:px-5 md:py-4">
                    <div className="headerInfoMobile flex flex-col gap-2 pb-2">
                      <div>
                        <div className="inline-block rounded shadow-sm">
                          <Image
                            width="100"
                            height="70"
                            alt={job?.title}
                            src={String(job?.company?.image_logo)}
                            className="h-[70px] object-contain"
                            loading="lazy"
                          />
                        </div>
                      </div>
                      <h3 className="w-full font-bold text-black">
                        {job?.title}
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        <div>
                          <p className="text-sm font-bold text-gray-500">
                            {job?.company?.display_name}
                          </p>
                        </div>
                        <div>
                          {!!job?.addresses &&
                            !!job?.addresses?.full_addresses &&
                            job?.addresses?.full_addresses?.map(
                              (address: any, index: number) => {
                                return (
                                  <div
                                    key={index}
                                    className="flex text-sm lg:text-base"
                                  >
                                    <HiOutlineMapPin className="mr-2 h-5 w-5" />
                                    <div className="w-11/12">{address}</div>
                                  </div>
                                );
                              },
                            )}
                        </div>
                        <div>
                          <p className="flex flex-row text-sm lg:text-base">
                            <HiOutlineClock className="mr-2 h-5 w-5" />
                            <span className="flex items-center text-sm lg:text-base">
                              {t("detail_job_posted_date")}{" "}
                              {job?.refreshed?.since}
                            </span>
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-primary lg:text-base">
                        <HiOutlineBanknotes className="mr-2 h-5 w-5" />
                        {!!isClient ? (
                          !!isLoggedIn ? (
                            <p>
                              {job?.salary?.value ?? t("detail_job_negotiable")}
                            </p>
                          ) : (
                            <button
                              type="button"
                              onClick={() =>
                                openLoginPopup([
                                  {
                                    name: "referring_name",
                                    value: "salary_job",
                                  },
                                ])
                              }
                              className="text-sm font-semibold underline"
                            >
                              {t("detail_job_page_signin_to_view_salary")}
                            </button>
                          )
                        ) : (
                          <VscLoading className="w-5t h-5 animate-spin text-primary-300" />
                        )}
                      </div>
                    </div>
                    <h3 className="mb-4 border-t border-gray-200 pt-2 font-bold text-black">
                      {t("detail_job_page_general_information")}
                    </h3>
                    {/*Template Header detail job */}
                    <CardHeaderInfoMobile
                      locale={locale as Lang}
                      job={job as JobType}
                      isLoading={isLoading}
                    />
                    {/*End Template Header detail job */}
                  </div>
                </SwiperSlide>
              );
            })}
        </Swiper>
      </div>

      <div className="bottomBarMobileJobDetail fixed bottom-0 left-0 z-20 w-full bg-white shadow-[4px_0px_4px_0px_rgba(95,47,38,0.15)]">
        <div className="flex gap-3 px-5 py-2 pb-6">
          {!isJobClosed && (
            <div className="flex items-center rounded border border-primary bg-transparent px-4 py-2 text-primary hover:bg-primary-100 dark:border-white dark:text-white">
              {job && <FollowJobButton jobId={job?.id} jobTitle={job.title} />}
            </div>
          )}

          {job && (
            <ApplyJobButton
              statusDisplay={job?.status_display}
              isApplied={job?.is_applied}
              jobId={job?.id}
              detailUrl={job?.detail_url}
              companyDisplayName={job?.company.display_name}
              jobTypesStr={job?.job_types_str}
              job={job}
            />
          )}
        </div>
        {job && job.status_display !== JOB_DISPLAY_STATUS_CLOSED && (
          <ExpireButton job={job} size="small" />
        )}
      </div>
      {isShowPopupSuggest && <PopupSuggestScrollSideway />}
    </>
  );
};
export default CardDetailJobHeaderMobile;
