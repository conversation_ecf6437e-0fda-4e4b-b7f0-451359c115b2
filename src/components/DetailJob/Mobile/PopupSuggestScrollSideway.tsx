import { Player } from "@lottiefiles/react-lottie-player";
import React from "react";
import scrollSidewayAnimation from "@/assets/animations/scroll-sideways.json";
import { useTranslations } from "next-intl";

const PopupSuggestScrollSideway = () => {
  const t = useTranslations();
  return (
    <div className="fixed left-0 right-0 z-20 flex h-56 bg-white bg-opacity-50 backdrop-blur-swiperDetailJob bottom-60 mx-7">
      <div className="mt-4 ">
        <Player
          autoplay
          loop
          src={scrollSidewayAnimation}
          style={{ height: "86px", width: "120px" }}
        ></Player>
        <div className="flex w-full mt-2">
          <span className="px-2 text-base font-bold mx-7 text-neutral-900">
            {t("recommend_job_popup_scroll_sideways")}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PopupSuggestScrollSideway;
