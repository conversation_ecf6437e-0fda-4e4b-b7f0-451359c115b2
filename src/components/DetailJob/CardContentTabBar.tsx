"use client";

import React, { useState } from "react";
import { isMobile } from "react-device-detect";
import Tab from "@/components/Tab/Tab";
import { useTranslations } from "next-intl";

export default function CardContentTabBar() {
  const t = useTranslations();
  const [isActive, setIsActive] = useState<string>("JobDescription");

  const handleTab = (type: string) => {
    setIsActive(type);
    const element = document.getElementById(type);
    if (!element) return;

    if (!isMobile) {
      const scrollY = (element?.offsetTop as number) - 160 ?? 0;
      window.scrollTo({
        top: scrollY,
        behavior: "smooth",
      });
    } else {
      if (type === "AboutCompany")
        document.getElementById("JobDescription")?.classList.add("hidden");
      else if (type === "JobDescription")
        document.getElementById("AboutCompany")?.classList.add("hidden");
      element.classList.remove("hidden");
    }
  };

  return (
    <div
      className={
        "sticky top-14 z-10 flex w-full overflow-hidden rounded-t md:top-[84px]"
      }
    >
      <div
        id="tabJobDescription"
        className="w-1/2 cursor-pointer text-sm md:text-base"
        onClick={() => handleTab("JobDescription")}
      >
        <Tab isActive={isActive === "JobDescription"}>
          {t("detail_job_page_job_description")}
        </Tab>
      </div>
      <div
        id="tabAboutCompany"
        className="w-1/2 cursor-pointer text-sm md:text-base"
        onClick={() => handleTab("AboutCompany")}
      >
        <Tab isActive={isActive === "AboutCompany"}>
          {t("detail_job_page_about_company")}
        </Tab>
      </div>
    </div>
  );
}
