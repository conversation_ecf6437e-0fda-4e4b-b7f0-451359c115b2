"use client";
import { gtag, openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import React, { useLayoutEffect, useState } from "react";
import { IoMdClose } from "react-icons/io";
import { Modal } from "flowbite-react";
import { useAppSelector } from "@/store";
import { JobType } from "@/types/job";
import { forceLogin } from "@/services/connector";

const PopupRemindSignIn = ({ job, device }: { job: JobType, device?: string }) => {
  const t = useTranslations();
  const [isShowRemindPopup, setIsShowRemindPopup] = useState<boolean>(false);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const getLocalStorage =
    typeof localStorage != "undefined" ? localStorage : undefined;
  const countVisit = getLocalStorage?.getItem("countVisit" + job?.id) || "0";
  const timeShowRemindPopup =
    getLocalStorage?.getItem("timeShowRemindPopup" + job?.id) || "0";

  useLayoutEffect(() => {
    if (isLoggedIn) {
      localStorage.setItem("countVisit" + job?.id, "0");
      localStorage.setItem("timeShowRemindPopup" + job?.id, "0");
      return;
    }

    const checkTime =
      parseInt(timeShowRemindPopup) > 0
        ? (Date.now() - parseInt(timeShowRemindPopup)) / (1000 * 60 * 60)
        : 24;

    const newCountVisit = () => {
      const count = parseInt(countVisit) + 1;

      if (Number(checkTime) < 24) {
        localStorage.setItem("countVisit" + job?.id, "0");
        return 0;
      }
      localStorage.setItem("countVisit" + job?.id, count.toString());
      return count;
    };

    const timeoutId = setTimeout(() => {
      const count = newCountVisit();

      if (
        count == 2 &&
        !isLoggedIn &&
        (Number(checkTime) >= 24 || !timeShowRemindPopup)
      ) {
        setIsShowRemindPopup(true);
        localStorage.setItem(
          "timeShowRemindPopup" + job?.id,
          Date.now().toString(),
        );
        gtag({
          event: "open_pop_up_remind_sign_up",
        });
      }
    }, 5000);

    // cleanup function to cancel the setTimeout if the component re-renders
    return () => clearTimeout(timeoutId);
  }, [countVisit, isLoggedIn, job?.id, timeShowRemindPopup]);

  const handleCancel = () => {
    setIsShowRemindPopup(false);
  };

  return (
    <Modal show={isShowRemindPopup} onClose={handleCancel} size={"xl"}>
      <Modal.Body className="scrollbar-primary relative max-h-[620px] overflow-auto rounded-xl px-6 py-3">
        <div className="flex flex-col">
          <div className="flex ml-auto">
            <span
              onClick={handleCancel}
              className="inline-block rounded cursor-pointer hover:bg-gray-200"
            >
              <IoMdClose className="w-6 h-6" />
            </span>
          </div>
          <div className="flex flex-col p-2 mx-auto">
            <p className="text-xl font-extrabold text-center text-brand-600 md:text-2xl">
              {t("detail_job_popup_title")}
            </p>
            <span className="w-auto h-auto mt-2 text-lg font-normal text-center text-neutral-900">
              {t("detail_job_popup_sub_title")}
            </span>
            <div className="flex flex-col items-center mt-6">
              <Image
                src="https://c.topdevvn.com/uploads/2024/07/01/main_2x.webp"
                alt="Main Remind Popup"
                width={236}
                height={137}
              />
              <span className="max-w-lg mt-6 text-base text-center text-neutral-600">
                {t("detail_job_popup_sub_main_content")}
              </span>
            </div>
            <div className="items-start w-auto h-auto mt-6">
              <span className="text-base font-bold text-brand-600">
                {t("detail_job_popup_benefit_title")}
              </span>
              <div className="mt-2">
                <div className="flex flex-row items-center">
                  <Image
                    src="https://c.topdevvn.com/uploads/2024/07/01/laptop_2x.webp"
                    alt="Laptop Remind Popup"
                    width={32}
                    height={27}
                  />
                  <span className="ml-6 text-base text-neutral-900">
                    {t("detail_job_popup_benefit_first")}
                  </span>
                </div>
                <div className="flex flex-row items-center mt-2">
                  <Image
                    src="https://c.topdevvn.com/uploads/2024/07/01/goal_2x.webp"
                    alt="Laptop Remind Popup"
                    width={32}
                    height={27}
                  />
                  <span className="ml-6 text-base text-neutral-900">
                    {t("detail_job_popup_benefit_second")}
                  </span>
                </div>
                <div className="flex flex-row items-center mt-2">
                  <Image
                    src="https://c.topdevvn.com/uploads/2024/07/01/salary_2x.webp"
                    alt="Laptop Remind Popup"
                    width={32}
                    height={27}
                  />
                  <span className="ml-6 text-base text-neutral-900">
                    {t("detail_job_popup_benefit_third")}
                  </span>
                </div>
                <button
                  id="signup-modal-btn"
                  onClick={() =>
                    device === 'mobile' ? forceLogin() : openLoginPopup([
                      {
                        name: "referring_name",
                        value: "popup",
                      },
                      {
                        name: "referring_from",
                        value: "remind_sign_up",
                      },
                    ])
                  }
                  className="flex w-64 py-4 mx-auto mt-6 rounded bg-brand-600 px-9"
                >
                  <span className="mx-auto text-base font-semibold text-white">
                    {t("detail_job_popup_remind_login_button")}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default PopupRemindSignIn;
