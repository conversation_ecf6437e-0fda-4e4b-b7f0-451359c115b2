"use client";
import dynamic from "next/dynamic";
import { useAppSelector } from "@/store";
import { JobType } from "@/types/job";
import { useTranslations } from "next-intl";
import { FC, useState } from "react";
import { sendPublishJob } from "@/services/jobAPI";
import { classNames } from "@/utils";
import { useRouter } from "@/navigation";
import ToastNotification from "@/components/Swal/ToastNotification";

const PopupReviewFeedback = dynamic(
  () => import("@/components/DetailJob/PopupReviewFeedback"),
  { ssr: false },
);

type CardFeedbackProps = {
  gridCols?: number;
  gridLargeCol?: number;
  job: JobType;
  left?: string;
};

const CardFeedback: FC<CardFeedbackProps> = ({
  gridCols = 2,
  gridLargeCol = 1,
  job,
}) => {
  const router = useRouter();

  const t = useTranslations();

  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);

  const [isShowFeedback, setIsShowFeedback] = useState<boolean>(false);

  const [loading, setLoading] = useState<boolean>(false);

  const submitPublish = async () => {
    setLoading(true);

    try {
      await sendPublishJob(job.id);

      ToastNotification({
        icon: "success",
        description: t("search_page_success"),
      });

      router.push({
        pathname: "/jobs",
      });

    } catch (error: any) {
      if (error?.response?.data?.message) {
        ToastNotification({
          icon: "error",
          description: error?.response?.data?.message,
        });
      }
    }
    setLoading(false);
  };

  if (!isLoggedIn) return null;

  return (
    <section className="flex flex-col justify-start w-full gap-2 p-4 py-6 border border-transparent rounded-lg bg-gradient-push-notification-OTW">
      {loading && (
        <div
          className={classNames(
            "load fixed left-0 top-0 z-50 h-full w-full items-center justify-center rounded bg-[rgba(0,0,0,.35)]",
          )}
        >
          <div className="flex items-center justify-center h-full">
            <div className="w-4 h-4 bg-white rounded-full animate-bounce"></div>
            <div className="w-4 h-4 mx-2 bg-white rounded-full animate-bounce"></div>
            <div className="w-4 h-4 bg-white rounded-full animate-bounce"></div>
          </div>
        </div>
      )}
      <h2 className="text-[24px] font-[600] text-[#292929]">
        {t("detail_job_preview_feedback")}
      </h2>

      <div className="border-t border-transparent rounded-lg">
        <div className="flex flex-col gap-1 bg-right-bottom bg-no-repeat bg-qna-for-interviews">
          <p className="mb-0 text-[16px] text-[#5d5d5d]">
            {t("recommend_job_detail_feedback_great")}
          </p>
          <p className="text-[16px] text-[#5d5d5d]">
            {t("recommend_job_detail_feedback_send")}
          </p>
        </div>
        <div
          className={`mt-2 grid items-center gap-2 grid-cols-${gridCols} md:min-h-36 min-h-auto`}
        >
          <button
            onClick={submitPublish}
            className={`relative h-[36px] w-full border-primary bg-primary text-[14px] md:col-span-${gridLargeCol} font-normal text-white hover:border-primary-400 hover:bg-primary-400 disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 `}
          >
            {t("publish_now")}
          </button>
          <button
            onClick={() => setIsShowFeedback(!isShowFeedback)}
            className={`border-1 h-[36px] w-full border border-primary text-[16px] font-normal text-[#5d5d5d] hover:border-primary-400 hover:bg-primary-100`}
          >
            {t("send_feedback")}
          </button>
        </div>
      </div>
      <PopupReviewFeedback
        job={job}
        isShowModalFeedback={isShowFeedback}
        setIsShowModalFeedback={setIsShowFeedback}
      />
    </section>
  );
};

export default CardFeedback;
