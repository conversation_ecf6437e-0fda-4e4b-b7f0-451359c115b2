"use client";

import { CardAboutCompany, CardSliderCompany } from "@/components/DetailJob";
import Round1 from "@/components/Icons/Round1";
import RoundN from "@/components/Icons/RoundN";
import { Link } from "@/navigation";
import { useAppSelector } from "@/store";
import { BenefitOriginalType, JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { HiMiniArrowRight } from "react-icons/hi2";
import CardFeedback from "../CardFeedback";
import CardContentTabNewBar from "./CardContentTabNewBar";

const SkeletonWrapper = () => {
  return (
    <div className="min-h-screen animate-pulse space-y-6 bg-gray-100">
      <div className="rounded-md h-[1000px] bg-gray-200"></div>
    </div>
  );
};

const CardNewContent = ({
  isMobile,
  jobs,
  locale,
  isPreview,
  isOpen,
}: {
  isMobile: boolean;
  jobs: JobType[];
  locale: Lang;
  isPreview?: boolean;
  isOpen?: boolean;
}) => {
  const t = useTranslations();
  const detailJobIndex = useAppSelector(
    (state) => state.job.detailJob.jobIndex,
  );
  const [isTailwindReady, setIsTailwindReady] = useState(false);
  const [job, setJob] = useState<JobType & { content_html?: any }>(
    jobs[detailJobIndex],
  );
  const contentRef = React.useRef<HTMLDivElement>(null);

  // Three-level fallback logic: benefits -> benefits_original -> company.benefits
  const isValidJobBenefits = job?.benefits && job.benefits.length > 0;
  const isValidBenefitsOriginal =
    job?.benefits_original && job.benefits_original?.[0]?.value;
  const isValidCompanyBenefits =
    job?.company?.benefits && job.company.benefits.length > 0;

  const getBenefits = () => {
    if (isValidBenefitsOriginal) {
      return job.benefits_original;
    }

    if (isValidJobBenefits) {
      return job.benefits;
    }

    if (isValidCompanyBenefits) {
      return job?.company?.benefits?.map((benefit) => ({
        value: benefit.value || benefit.description,
      }));
    }

    return [];
  };
  const benefits = getBenefits();
  const shouldUseBenefitsOriginal = isValidBenefitsOriginal;
  const recruitmentProcess =
    job?.recruiment_process && job?.recruiment_process.length > 0
      ? job?.recruiment_process
      : job?.company.recruitment_process;

  useEffect(() => {
    setJob(jobs[detailJobIndex]);
  }, [detailJobIndex, jobs]);
  useEffect(() => {
    const applyTailwindConfig = () => {
      if ((window as any).tailwind) {
        (window as any).tailwind.config = { prefix: "td-" };
        setIsTailwindReady(true);
      }
    };

    if ((window as any).tailwind) {
      applyTailwindConfig();
    } else {
      const observer = new MutationObserver(() => {
        if ((window as any).tailwind) {
          applyTailwindConfig();
          observer.disconnect();
        }
      });
      observer.observe(document.head, { childList: true, subtree: true });
    }
  }, []);

  useEffect(() => {
    if (contentRef.current && isTailwindReady) {
      contentRef.current.innerHTML = job.content_html || "";
      const scripts = contentRef.current.getElementsByTagName("script");

      for (let i = 0; i < scripts.length; i++) {
        const oldScript = scripts[i];
        const newScript = document.createElement("script");

        if (oldScript.src) {
          newScript.src = oldScript.src;
          newScript.async = true;
        } else {
          newScript.textContent = oldScript.textContent;
        }

        oldScript.replaceWith(newScript);
      }
    }
  }, [isTailwindReady]);

  if (
    !isTailwindReady &&
    job.content_html &&
    (isPreview ? job.is_content_image : job.is_content_image_enabled)
  ) {
    return <SkeletonWrapper />; // Prevent unstyled content
  }
  return (
    <section id="cardContentDetailJob">
      {/* Tab content detail job */}
      {isMobile && <CardContentTabNewBar />}
      {/* End Tab content detail job */}

      {/* Job Description */}
      <div
        id="JobDescription"
        className={`rounded bg-white p-4 md:py-[25px] ${
          job.content_html && job.is_content_image ? "pt-0" : "md:px-[25px]"
        }`}
      >
        {(isPreview && job.content_html && job.is_content_image) ||
        (job.content_html &&
          job.is_content_image &&
          job.is_content_image_enabled &&
          isOpen) ? (
          <div
            className="mb-4 max-w-full border-b border-gray-200 pb-2 text-sm last:mb-0 last:border-0 last:pb-0 lg:text-base"
            ref={contentRef}
          />
        ) : (
          <>
            {!!job.content && (
              <div
                className="prose mb-4 max-w-full border-gray-200 text-sm text-[#5D5D5D] last:mb-0 last:border-0 last:pb-0 lg:text-base"
                dangerouslySetInnerHTML={{ __html: job.content }}
              />
            )}

            {/* Responsibilities */}
            {job.responsibilities ? (
              <div className="prose max-w-full border-gray-200 pb-4 last:mb-0 last:border-0 last:pb-0">
                <h2 className="mb-4 rounded bg-[#F6F6F6] px-3 py-2 text-base font-semibold text-[#292929] md:font-bold">
                  {t("detail_job_page_responsibilities")}
                </h2>

                {Array.isArray(job.responsibilities) &&
                job.responsibilities.length > 0 ? (
                  <ul>
                    {job.responsibilities.map((item, index) => (
                      <li key={index}>{item?.description}</li>
                    ))}
                  </ul>
                ) : (
                  <div
                    className="prose max-w-full text-sm lg:text-base"
                    dangerouslySetInnerHTML={{
                      __html: job.responsibilities_original as string,
                    }}
                  />
                )}
              </div>
            ) : null}

            {/* Requirements */}
            {job.requirements ? (
              <div className="prose max-w-full border-gray-200 pb-4 last:mb-0 last:border-0 last:pb-0">
                <h2 className="mb-4 rounded bg-[#F6F6F6] px-3 py-2 text-base font-semibold text-[#292929] md:font-bold">
                  {t("detail_job_page_requirements")}
                </h2>
                {Array.isArray(job.requirements) &&
                job.requirements.length > 0 ? (
                  <ul>
                    {job.requirements.map((item, index) => (
                      <li key={index}>{item?.description}</li>
                    ))}
                  </ul>
                ) : (
                  <div
                    className="max-w-full text-sm lg:text-base"
                    dangerouslySetInnerHTML={{
                      __html: job.requirements_original as string,
                    }}
                  />
                )}
              </div>
            ) : null}
            {!!job.education_arr && job.education_arr?.length > 0 && (
              <div className="mb-4">
                <div>
                  <div className="flex flex-col gap-2 bg-[#f6f6f6] p-2 md:grid md:grid-cols-2 md:gap-3 md:p-3">
                    <div className="col-span-2">
                      <h2 className="text-base font-bold text-[#292929]">
                        {t("detail_job_page_education")}
                      </h2>
                    </div>

                    <div className="flex items-start gap-3 bg-[#fff] p-4">
                      <span className="flex h-[32px] w-[32px] items-center justify-center rounded-[2px] bg-[#f6f6f6]">
                        <Round1 />
                      </span>
                      <div>
                        <h3 className="text-sm font-bold text-[#292929] md:text-base">
                          Degree
                        </h3>
                        <p className="text-sm font-normal text-[#5d5d5d] md:text-base">
                          {job.education_arr.join(", ")} in{" "}
                          {job.education_major_arr.join(", ")}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-3 bg-[#fff] p-4">
                      <span className="flex h-[32px] w-[32px] items-center justify-center rounded-[2px] bg-[#f6f6f6]">
                        <Round1 />
                      </span>
                      <div>
                        <h3 className="text-sm font-bold text-[#292929] md:text-base">
                          Certification
                        </h3>
                        <p className="text-sm font-normal text-[#5d5d5d] md:text-base">
                          {job.education_certificate}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/* End Education */}

            {/* Benefits Section - Three-level fallback: benefits -> benefits_original -> company.benefits */}
            {(shouldUseBenefitsOriginal ||
              (benefits && benefits.length > 0)) && (
              <div>
                <h2 className="mb-4 rounded bg-[#F6F6F6] px-3 py-2 text-base font-semibold text-[#292929] md:font-bold">
                  {t("detail_job_page_benefits_of_this_jobs")}
                </h2>
                <div className="prose max-w-full text-sm lg:text-base">
                  {shouldUseBenefitsOriginal ? (
                    // Second priority: Use benefits_original array with icon and value
                    <>
                      {job?.benefits_original?.map(
                        (benefit: BenefitOriginalType, index: number) => (
                          <div
                            key={index}
                            dangerouslySetInnerHTML={{
                              __html: benefit.value,
                            }}
                          />
                        ),
                      )}
                    </>
                  ) : benefits && benefits.length > 0 && isValidJobBenefits ? (
                    // First/Third priority: Use benefits array (job.benefits or mapped company.benefits)
                    <ul className={classNames("list-none")}>
                      {benefits.map((benefit: any, index: number) => (
                        <li key={index} className="flex items-center">
                          {benefit.icon && (
                            <Image
                              src={benefit.icon}
                              alt=""
                              className="my-0 inline h-4 w-4"
                              width={16}
                              height={16}
                            />
                          )}
                          <span className="my-0.5 ml-2 inline-block">
                            {benefit.value ||
                              benefit.description ||
                              benefit?.name}
                          </span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <>
                      {benefits?.map((benefit: any, index: number) => (
                        <div
                          key={index}
                          dangerouslySetInnerHTML={{
                            __html: benefit.value,
                          }}
                        />
                      ))}
                    </>
                  )}
                </div>
              </div>
            )}

            {/* recruitment Process */}
            {!!recruitmentProcess && recruitmentProcess?.length > 0 && (
              <div>
                <h2 className="mb-4 rounded bg-[#F6F6F6] px-3 py-2 font-semibold  text-[#292929] md:font-bold">
                  {t("detail_job_page_recruitment_progress")}
                </h2>
                <div>
                  <div
                    className={`grid grid-cols-${
                      isMobile
                        ? 1
                        : recruitmentProcess.length < 3
                        ? recruitmentProcess.length
                        : 3
                    } flex-wrap gap-3 bg-[#f6f6f6] p-3`}
                  >
                    {recruitmentProcess?.map((process: any, index: number) => {
                      return (
                        <div
                          key={index}
                          className="flex items-start gap-3 bg-[#fff] p-4"
                        >
                          <span className="flex h-[32px] w-[32px] items-center justify-center rounded-[2px] bg-[#f6f6f6]">
                            {index === 0 ? <Round1 /> : <RoundN />}
                          </span>
                          <div>
                            <h3 className="text-sm font-bold text-[#292929] md:text-base">
                              {t("detail_job_page_round") + " " + (index + 1)}
                            </h3>
                            <p className="text-sm font-normal text-[#5d5d5d] md:text-base">
                              {process.description || process?.name}
                            </p>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            )}
            {/* End recruitment Process */}
          </>
        )}
      </div>
      {/* End Job Description */}

      {/* About Company */}
      <div
        id="AboutCompany"
        className={classNames(
          isMobile ? "hidden" : "mt-8",
          "rounded bg-white p-2 md:px-6 md:py-4",
        )}
      >
        <CardAboutCompany locale={locale as Lang} company={job?.company} />

        {/* Slide Company */}
        {Number(job?.company?.image_galleries?.length) > 0 && (
          <div id="GalleriesCompany" className="mt-4">
            <div className="overflow-hidden">
              <div className="lg:w-[808px]">
                <CardSliderCompany
                  isClient={true}
                  galleriesCompany={job?.company?.image_galleries}
                />
              </div>
            </div>
          </div>
        )}
        {/* End Slide Company */}

        <div className="mt-4 text-center">
          <Link
            className="text-primary-300 hover:text-primary-400"
            title={t(
              isMobile
                ? "detail_job_page_view_company_page"
                : "detail_job_page_view_compnay",
            )}
            href={{
              pathname: "/companies/[slug]",
              params: {
                slug: job?.company?.slug + "-" + job?.company?.id,
              },
              query: { src: "topdev_detailjob", medium: "viewcompany" },
            }}
          >
            <span className="text-sm font-semibold underline transition-all md:text-base">
              {t(
                isMobile
                  ? "detail_job_page_view_company_page"
                  : "detail_job_page_view_compnay",
              )}
            </span>
            {isMobile && (
              <HiMiniArrowRight className="ml-2 inline-block h-5 w-5 md:hidden" />
            )}
          </Link>
        </div>
      </div>
      {/* End About Company */}

      {/* Show Card Feedback */}
      {/* TODO: remove after Feb 5 */}
      {isPreview && (
        <div className="mt-4">
          <CardFeedback gridCols={4} gridLargeCol={3} job={job} left="left-5" />
        </div>
      )}

      {/* End Card Feedback */}
    </section>
  );
};

export default CardNewContent;
