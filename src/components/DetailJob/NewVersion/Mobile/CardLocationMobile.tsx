import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { HiOutlineMapPin } from "react-icons/hi2";
import { BsDot } from "react-icons/bs";

type CardLocationMobileProps = {
  address: string;
  fullAddress: string;
  count: number;
  isVisible: boolean;
  length: number;
  setIsVisible: Function;
};

const CardLocationMobile: React.FC<CardLocationMobileProps> = (props) => {
  const { address, fullAddress, count, isVisible, length, setIsVisible } =
    props;

  const [isOpen, setIsOpen] = useState(false);

  const t = useTranslations();

  return (
    <div>
      <div
        className={`flex px-4 text-[12px] lg:text-base ${
          count !== 0 && !isVisible ? "hidden" : ""
        }`}
      >
        <HiOutlineMapPin className="w-5 h-5 mr-2" />
        <div className="flex items-center w-11/12">
          <span className={`${isOpen ? 'font-bold' : ''}`} onClick={() => setIsOpen(!isOpen)}>{address}</span>
          {!isVisible && length > 0 ? (
            <span
              onClick={() => setIsVisible(!isVisible)}
              className="flex items-center cursor-pointer"
            >
              <BsDot className="text-[14px] text-gray-300" /> +{length} {t("other_local")}
            </span>
          ) : (
            ""
          )}
        </div>
      </div>
      <div
        className={`${
          isOpen ? "" : "hidden"
        } px-5 pl-[2.8rem] text-[12px] transition-all`}
      >
        {fullAddress}
      </div>
    </div>
  );
};

export default CardLocationMobile;
