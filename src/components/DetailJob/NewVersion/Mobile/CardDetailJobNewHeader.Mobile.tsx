"use client";
import ApplyJobButton from "@/components/Button/ApplyJobButton";
import ExpireButton from "@/components/Button/ExpireButton";
import ContractType from "@/components/Icons/ContractType";
import CubeTech from "@/components/Icons/CubeTech";
import Experience from "@/components/Icons/Experience";
import JobLevel from "@/components/Icons/JobLevel";
import JobTypeIcon from "@/components/Icons/JobType";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import useIsMobileBrowser from "@/hooks/useIsMobileBrowser";
import {
  forceLogin,
  getAccessToken,
  setJobId,
  tapBackButton,
  tapShareButton,
} from "@/services/connector";
import { useAppSelector } from "@/store";
import { JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { classNames, openLoginPopup } from "@/utils";
import { JOB_DISPLAY_STATUS_CLOSED } from "@/utils/enums";
import useTaxonomy from "@/utils/taxonomies";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import {
  HiMiniArrowLeft,
  HiOutlineBanknotes,
  HiOutlineClock,
  HiShare,
} from "react-icons/hi2";
import { VscLoading } from "react-icons/vsc";
import CardLocationMobile from "./CardLocationMobile";
import { getPromotionInfo } from "@/services/promotion";
import { Promotion } from "@/types/applyButton";
import ShareJobButton from "@/components/Button/ShareJobButton";
import ShareButtonMobile from "@/components/Button/ShareJobButton.mobile";

const PopupSuggestScrollSideway = dynamic(
  () => import("@/components/DetailJob/Mobile/PopupSuggestScrollSideway"),
);

//Component Follow Button
const FollowJobButton = dynamic(
  () => import("@/components/Button/FollowJobButton"),
);
//End Component Follow Button

//Interface Card Detail Job Header Mobile Props
interface CardDetailJobNewHeaderMobileProps {
  locale: Lang;
  jobData: JobType;
  jobs: Array<JobType>;
  srcPage?: string;
  mediumPage?: string;
  viewScreen?: string;
}

const CardDetailJobNewHeaderMobile = ({
  locale,
  jobData,
  srcPage = "detailjob",
  mediumPage = "general_info",
  viewScreen,
}: CardDetailJobNewHeaderMobileProps) => {
  const t = useTranslations();

  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const isShowApplyJob = useAppSelector(
    (state) => state?.setting?.isShowApplyJob,
  );
  const [isClient, setIsClient] = useState(false);
  const EXP_EXCEPT_IDS = [1640, 1651, 4851];
  const isMobileBrowser = useIsMobileBrowser();
  const [isVisible, setIsVisible] = useState(false);
  const [promotion, setPromotion] = useState<Promotion>();

  const [token, setToken] = useState<string>();

  const taxonomy = useTaxonomy();

  const shortAddresses = useMemo(() => {
    if (!jobData?.addresses?.sort_addresses) return [];

    return jobData?.addresses.sort_addresses.split("-");
  }, [jobData?.addresses.sort_addresses]);

  const handleScreenMobile = useCallback(() => {
    // Handle View Mobile
    document.body.classList.remove("pb-[66px]");

    // Add null check for header element
    const headerMobile = document?.body?.querySelector("#headerMobile");
    if (headerMobile) {
      (headerMobile as HTMLDivElement).style.display = "none";
    }

    // Handle connect to native app
    setJobId(jobData?.id);
  }, [jobData?.id]);

  const setTokenOnMobileApp = (token: string) => {
    setTimeout(() => {
      localStorage.setItem("accessToken", token);
      setToken(token);
    }, 500);
  };

  //Effect client
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (viewScreen === "app") {
      handleScreenMobile();

      // Init access token
      getAccessToken();

      // Handle set access token
      (window as any).setAccessToken = async function (token: string) {
        // Set token
        if (token) {
          console.log(token, "token **************");

          setTokenOnMobileApp(token);

          setTimeout(() => {
            // Reload screen
            window.location.reload();
          }, 1500);
        }
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [viewScreen, jobData?.id]);

  //isJobClosed is checkjob closed
  const isJobClosed = jobData?.status_display === JOB_DISPLAY_STATUS_CLOSED;

  const convertSlugTaxonomy = (id: number, slug: string): string => {
    return "/" + t("slug_it_jobs") + "/" + slug + "-kt" + id;
  };

  const isIncludeExceptId = (id: number) => {
    return EXP_EXCEPT_IDS.includes(id);
  };

  const ExperienceInfo = () => {
    if (!jobData?.experiences_ids || !jobData?.experiences_ids.length)
      return null;
    const fromExperienceId = jobData.experiences_ids[0];
    const dataTaxonomy = taxonomy(fromExperienceId, "experiences");

    if (!dataTaxonomy) {
      return null;
    }
    const name = locale === "en" ? dataTaxonomy?.text_en : dataTaxonomy?.text;
    return (
      <Link
        className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
        key={fromExperienceId}
        href={{
          pathname: convertSlugTaxonomy(
            fromExperienceId,
            dataTaxonomy?.slug as string,
          ),
          query: { src: "topdev_" + srcPage, medium: mediumPage },
        }}
        title={name}
      >
        {isIncludeExceptId(fromExperienceId) ? null : t("detail_job_from")}{" "}
        {name}
      </Link>
    );
  };

  useEffect(() => {
    const fetchPromotion = async () => {
      try {
        const request = await getPromotionInfo();
        if (request?.data) {
          if (request?.data) {
            setPromotion(request?.data?.data);
          }
        }
      } catch (err) {
        console.log(err);
      }
    };
    fetchPromotion();
  }, []);

  const handleForceLogin = () => {
    forceLogin();
    openLoginPopup([
      {
        name: "referring_name",
        value: "salary_job",
      },
    ]);
  };

  return (
    <>
      <div className="detailJobHeaderMobile sticky left-0 top-0 z-20 flex w-full justify-between gap-2 bg-white px-5 py-4 shadow-sm lg:px-2 lg:py-4">
        <button
          type="button"
          className="inline-block"
          onClick={() => tapBackButton()}
        >
          <HiMiniArrowLeft className="h-6 w-6" />
        </button>
        <h1 className="line-clamp-1 max-w-sm text-lg font-bold text-black">
          {jobData?.title}
        </h1>
        {!isJobClosed && (
          <ShareButtonMobile
            shareUrl={location?.pathname}
            jobId={jobData?.id}
          />
        )}
      </div>

      <div className="pt-5 md:py-5">
        <div className="h-full min-h-[500px] w-full overflow-hidden rounded bg-white py-3 md:px-5 md:py-4">
          <div className="headerInfoMobile flex flex-col gap-1 pb-2">
            <div className="px-4">
              <div className="shadow-none inline-block rounded">
                <Image
                  width="100"
                  height="70"
                  alt={jobData?.title}
                  src={String(jobData?.company?.image_logo)}
                  className="h-[60px] object-contain"
                  loading="lazy"
                />
              </div>
            </div>
            <p className="px-4 text-[10px] font-bold leading-none text-[#DD3F24]">
              {jobData?.company?.display_name}
            </p>
            <h3 className="w-full px-4 pb-1 font-bold text-black">
              {jobData?.title}
            </h3>
            <div className="mb-2 border-b border-[#f6f6f6] pb-2">
              {!!shortAddresses &&
                shortAddresses?.map((address: any, index: number) => {
                  return (
                    <CardLocationMobile
                      key={index}
                      isVisible={isVisible}
                      count={index}
                      address={address}
                      fullAddress={jobData?.addresses?.full_addresses[index]}
                      setIsVisible={setIsVisible}
                      length={shortAddresses.length - 1}
                    />
                  );
                })}
            </div>

            <div className="flex flex-col gap-4">
              <div className="flex items-center px-4 text-sm text-primary lg:text-base">
                <HiOutlineBanknotes className="mr-2 h-5 w-5" />
                {isClient ? (
                  isLoggedIn ? (
                    <p>
                      {jobData?.salary?.value ?? t("detail_job_negotiable")}
                    </p>
                  ) : (
                    <button
                      type="button"
                      onClick={() => handleForceLogin()}
                      className="text-sm font-semibold underline"
                    >
                      {t("detail_job_page_signin_to_view_salary")}
                    </button>
                  )
                ) : (
                  <VscLoading className="w-5t h-5 animate-spin text-primary-300" />
                )}
              </div>
              <div className="px-4">
                <p className="flex flex-row pb-1 text-sm lg:text-base">
                  <HiOutlineClock className="mr-2 h-5 w-5" />
                  <span className="flex items-center text-sm lg:text-base">
                    {t("detail_job_posted_date")} {jobData?.refreshed?.since}
                  </span>
                </p>
              </div>
              <div
                className={classNames(
                  "flex items-start border-b border-[#f6f6f6] px-4 pb-3",
                )}
              >
                <div>
                  <CubeTech className="mr-[5px]" />
                </div>
                <div>
                  <div className="flex flex-wrap gap-y-2">
                    {jobData?.skills_ids.map((id: number, index: number) => {
                      return (
                        <div key={index}>
                          <SkillTagTaxonomy
                            skillId={id}
                            srcPage={"detailjob"}
                            screenView="mobile"
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 pt-4">
              {/* experiences */}
              {!!jobData?.experiences_ids &&
                jobData?.experiences_ids.length > 0 && (
                  <div className="item-card-info mb-2 px-4 md:mb-4">
                    <div className="flex flex-col items-start gap-2">
                      <span className="flex h-[28px] w-[28px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
                        <Experience size={16} />
                      </span>
                      <div>
                        <h3 className="mb-0 text-[10px] font-normal leading-none text-[#888]">
                          {t("detail_job_minimum_year_of_experience")}
                        </h3>
                        <div>
                          <ExperienceInfo />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              {/* End experiences */}

              {/* job levels */}
              {!!jobData?.job_levels_ids &&
                jobData?.job_levels_ids.length > 0 && (
                  <div className="item-card-info mb-2 pr-5 md:mb-4 md:pl-0 md:pr-0 ">
                    <div className="flex flex-col items-start gap-2">
                      <span className="flex h-[28px] w-[28px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
                        <JobLevel size={16} />
                      </span>
                      <div>
                        <h3 className="mb-0 text-[10px] font-normal leading-none text-[#888]">
                          {t("detail_job_page_level")}
                        </h3>
                        <div>
                          {jobData?.job_levels_ids.map(
                            (id: number, index: number) => {
                              const dataTaxonomy = taxonomy(id, "job_levels");
                              const name =
                                locale === "en"
                                  ? dataTaxonomy?.text_en
                                  : dataTaxonomy?.text;
                              return (
                                <Fragment key={index + "-" + id}>
                                  <Link
                                    className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
                                    href={{
                                      pathname: convertSlugTaxonomy(
                                        id,
                                        dataTaxonomy?.slug as string,
                                      ),
                                      query: {
                                        src: "topdev_" + srcPage,
                                        medium: mediumPage,
                                      },
                                    }}
                                    title={name}
                                  >
                                    {name}
                                  </Link>
                                  {jobData?.job_levels_ids?.length - 1 > index
                                    ? ", "
                                    : ""}
                                </Fragment>
                              );
                            },
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              {/* End job levels */}
              <div className="col-span-2 mb-[10px] mt-1 h-[1px] bg-[#f6f6f6]"></div>

              {/* job types */}
              {!!jobData?.job_types_ids &&
                jobData?.job_types_ids.length > 0 && (
                  <div className="item-card-info mb-2 px-4 md:mb-4">
                    <div className="flex flex-col items-start gap-2">
                      <span className="flex h-[28px] w-[28px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
                        <JobTypeIcon size={16} />
                      </span>
                      <div>
                        <h3 className="mb-0 text-[10px] font-normal leading-none text-[#888]">
                          {t("detail_job_page_job_type")}
                        </h3>
                        <div>
                          {!!jobData?.job_types_ids &&
                            jobData?.job_types_ids.map(
                              (id: number, index: number) => {
                                const dataTaxonomy = taxonomy(id, "job_types");
                                const name =
                                  locale === "en"
                                    ? dataTaxonomy?.text_en
                                    : dataTaxonomy?.text;
                                return (
                                  <Fragment key={index + "-" + id}>
                                    <Link
                                      className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
                                      href={{
                                        pathname: convertSlugTaxonomy(
                                          id,
                                          dataTaxonomy?.slug as string,
                                        ),
                                        query: {
                                          src: "topdev_" + srcPage,
                                          medium: mediumPage,
                                        },
                                      }}
                                      title={name}
                                    >
                                      {name}
                                    </Link>
                                    {!!jobData.job_types_ids &&
                                    jobData?.job_types_ids.length - 1 > index
                                      ? ", "
                                      : ""}
                                  </Fragment>
                                );
                              },
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              {/* End job types */}

              {/* contract types */}
              <div className="item-card-info mb-2 pr-5 md:mb-4 md:pl-0 md:pr-0">
                <div className="flex flex-col items-start gap-2">
                  <span className="flex h-[28px] w-[28px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
                    <ContractType size={16} />
                  </span>
                  <div>
                    <h3 className="mb-0 text-[10px] font-normal leading-none text-[#888]">
                      {t("detail_job_page_contract_type")}
                    </h3>
                    <div>
                      {!!jobData?.contract_types_ids &&
                        jobData?.contract_types_ids.map(
                          (id: number, index: number) => {
                            const dataTaxonomy = taxonomy(id, "contract_types");
                            const name =
                              locale === "en"
                                ? dataTaxonomy?.text_en
                                : dataTaxonomy?.text;
                            return (
                              <Fragment key={index + "-" + id}>
                                <Link
                                  className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
                                  key={index + "-" + id}
                                  href={{
                                    pathname: convertSlugTaxonomy(
                                      id,
                                      dataTaxonomy?.slug as string,
                                    ),
                                    query: {
                                      src: "topdev_" + srcPage,
                                      medium: mediumPage,
                                    },
                                  }}
                                  title={name}
                                >
                                  {name}
                                </Link>
                                {!!jobData?.contract_types_ids &&
                                (jobData?.contract_types_ids
                                  ?.length as number) -
                                  1 >
                                  index
                                  ? ", "
                                  : ""}
                              </Fragment>
                            );
                          },
                        )}
                    </div>
                  </div>
                </div>
              </div>
              {/* End contract types */}
            </div>
          </div>
        </div>
      </div>

      {!isShowApplyJob && (
        <div className="bottomBarMobileJobDetail fixed bottom-0 left-0 z-[100] w-full bg-white shadow-[4px_0px_4px_0px_rgba(95,47,38,0.15)]">
          {jobData && <ExpireButton job={jobData} size="small" />}

          {isMobileBrowser && (
            <div className="flex gap-3 px-5 py-2">
              {!isJobClosed && (
                <div className="flex items-center rounded border border-primary bg-transparent px-[11px] py-[8px] text-primary hover:bg-primary-100 dark:border-white dark:text-white">
                  {jobData && (
                    <FollowJobButton
                      jobId={jobData?.id}
                      jobTitle={jobData.title}
                      screenView="mobile"
                    />
                  )}
                </div>
              )}
              {jobData && (
                <ApplyJobButton
                  statusDisplay={jobData?.status_display}
                  isApplied={jobData?.is_applied}
                  jobId={jobData?.id}
                  detailUrl={jobData?.detail_url}
                  companyDisplayName={jobData?.company.display_name}
                  jobTypesStr={jobData?.job_types_str}
                  job={jobData}
                  locale={locale}
                  voucherApply={promotion}
                />
              )}
            </div>
          )}
        </div>
      )}
    </>
  );
};
export default CardDetailJobNewHeaderMobile;
