"use client";
import FollowJob<PERSON>ewButton from "@/components/Button/FollowJobNewButton";
import FollowLocationButton from "@/components/Button/FollowLocationButton";
import ShareJobButton from "@/components/Button/ShareJobButton";
import ToastNotification from "@/components/Swal/ToastNotification";
import { URL_JOB_SEEKER } from "@/contansts/auth";
import useShareFacebookCongratModal from "@/hooks/useShareFacebookCongratModal";
import { Link } from "@/navigation";
import { tracking } from "@/services/activity";
import { getPromotionInfo } from "@/services/promotion";
import { useAppSelector } from "@/store";
import { Promotion } from "@/types/applyButton";
import { CompanyType } from "@/types/company";
import { JobType } from "@/types/job";
import { classNames, gtag } from "@/utils";
import { JOB_DISPLAY_STATUS_CLOSED } from "@/utils/enums";
import { getCurrentLocale } from "@/utils/locale";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { BsDot } from "react-icons/bs";
import { HiMapPin } from "react-icons/hi2";

const Button = dynamic(() => import("@/components/Button/Button"));

const ApplyJobButton = dynamic(
  () => import("@/components/Button/ApplyJobButton"),
);

const CardDetailJobNewHeader = ({
  jobTitle,
  jobAddress,
  jobId,
  company,
  statusDisplay,
  job,
}: {
  jobTitle: string;
  jobAddress: string[];
  jobId: number;
  company: CompanyType;
  jobSalary: string | null;
  statusDisplay: string | null;
  jobPosted?: string | null;
  job: JobType | any;
}) => {
  const t = useTranslations();
  const pathName = usePathname();
  const [isHidden, setIsHidden] = useState(false);
  const [promotion, setPromotion] = useState<Promotion>();
  const isJobClosed = statusDisplay === JOB_DISPLAY_STATUS_CLOSED;
  const locale = getCurrentLocale();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);

  const appliedJobs = useAppSelector((state) => state.user.applied_jobs);
  const isApplied = appliedJobs?.includes(job?.id);
  const user = useAppSelector((state) => state?.user?.user);
  const router = useRouter();
  const shortAddresses = useMemo(() => {
    if (!job?.addresses?.sort_addresses) return [];

    return job?.addresses.sort_addresses.split("-");
  }, [job?.addresses.sort_addresses]);

  //Effect client
  const handleScroll = () => {
    setIsHidden(false);

    if (window.scrollY >= 95) {
      setIsHidden(true);
    }
  };
  const handleOpenLogin = () => {
    const url = `${URL_JOB_SEEKER}?redirect_uri=${window.location.href}`;
    router.push(url);
  };
  //Start handleCreateCVToApply
  const handleCreateCVToApply = (jobId: number) => {
    const BUILDER_URL = process.env.NEXT_PUBLIC_CV_BUILDER_URL;
    if (user.roles && user.roles[0] === "employer") {
      ToastNotification({
        icon: "warning",
        title: t("detail_job_page_not_right_access"),
      });
      return;
    }

    if (!isLoggedIn) {
      handleOpenLogin();
      return;
    }
    const openCVBuilderAndPopupCreate = (path: any) => {
      window.open(
        BUILDER_URL +
          "?referring_name=cv_job&openPopupCreate=1&referer=" +
          encodeURIComponent(path),
        "__blank",
      );
    };

    let path = "/" + t("slug_it_jobs") + "/" + job.slug;

    openCVBuilderAndPopupCreate(path);

    // Tracking apply-now
    tracking({
      job_ids: jobId,
      collection: "apply-with-topdev-cv",
    });

    // Push GTM on click Apply Now
    gtag({
      event: "eec.jobApply",
      ecommerce: {
        add: {
          actionField: { list: "ApplyWithTopDevCV" },
          products: [
            {
              name: job.detail_url,
              id: job.id,
              brand: job?.company?.display_name,
              category: "Job",
              variant: job.job_types_str,
            },
          ],
        },
      },
    });
  };
  //End handleCreateCVToApply

  useEffect(() => {
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);
  useEffect(() => {
    const fetchPromotion = async () => {
      try {
        const request = await getPromotionInfo();
        if (request?.data) {
          setPromotion(request?.data?.data);
        }
      } catch (err) {
        console.log(err);
      }
    };
    fetchPromotion();
  }, []);

  const { show, handleCloseModal, handleOpenModal } =
    useShareFacebookCongratModal();

  return (
    <section
      id="detailJobHeader"
      className={classNames(
        isHidden ? "shadow-sm" : "",
        "sticky top-[68px] z-10 flex items-start gap-6 rounded bg-white p-4 lg:pl-[25px]",
      )}
    >
      {/* Logo detail job header */}
      <div
        className={classNames(
          isHidden ? "hidden" : "flex",
          "mt-[1rem] w-[10%] flex-initial items-start justify-center bg-white",
        )}
      >
        <Link
          className="flex min-h-[112px] items-start justify-center"
          href={{
            pathname: "/companies/[slug]",
            params: { slug: company.slug + "-" + company.id },
            query: { src: "topdev_detailjob", medium: "logo_company" },
          }}
          title={jobTitle}
        >
          <Image
            src={
              company?.image_logo
                ? company.image_logo
                : "/v4/assets/images/td-logo.png"
            }
            alt="TopDev"
            className="h-full max-h-[112px] w-[160px] bg-white object-contain"
            width="160"
            height="112"
            loading="lazy"
          />
        </Link>
      </div>
      {/* End Logo detail job header */}

      {/* Info detail job header */}
      <div
        className={classNames(
          isHidden ? "w-11/12" : "w-3/4",
          "flex flex-initial flex-col overflow-hidden",
        )}
      >
        <p
          className={classNames(
            isHidden ? "" : "my-1",
            "line-clamp-1 text-base font-bold text-[#F05C43]",
          )}
        >
          {company.display_name}
        </p>
        <h1 className={classNames("text-2xl font-bold text-black")}>
          {jobTitle}
        </h1>
        <div
          className={classNames(
            "my-2 mt-4 max-w-[650px] truncate text-base text-gray-500",
          )}
        >
          {!!shortAddresses && (
            <div className="flex items-center last:mb-0">
              <div className="mr-2 h-6 w-6">
                <HiMapPin className="text-[20px] text-gray-300" />
              </div>

              {shortAddresses.map((address: string, index: number) => {
                return (
                  <span key={index} className="flex items-center truncate">
                    {index > 0 ? (
                      <BsDot className="text-[20px] text-gray-300" />
                    ) : (
                      ""
                    )}{" "}
                    <FollowLocationButton fullAddress={jobAddress[index]}>
                      <span className="hover:text-[#DD3F24]">{address}</span>
                    </FollowLocationButton>
                  </span>
                );
              })}
            </div>
          )}
        </div>
        {/* <div
          className={classNames(
            isHidden ? "hidden" : "",
            "mb-2 max-w-[540px] text-base text-gray-500",
          )}
        >
          {!!jobPosted && (
            <div className="flex last:mb-0">
              <HiMiniClock className="flex items-center w-6 h-6 mr-2 text-gray-300" />
              <div className="flex items-center w-11/12 text-base text-gray-500">
                {t("detail_job_posted_date")} {jobPosted}
              </div>
            </div>
          )}
        </div>
        <div
          className={classNames(
            isHidden ? "hidden" : "flex",
            "max-w-[540px] items-center",
          )}
        >
          <HiMiniBanknotes className="w-6 h-6 mr-2 text-gray-300" />
          {isLoggedIn ? (
            <p className="text-primary">
              {jobSalary ?? t("detail_job_negotiable")}
            </p>
          ) : (
            <button
              type="button"
              onClick={() =>
                openLoginPopup([
                  { name: "referring_name", value: "salary_job" },
                ])
              }
              className="text-base font-semibold underline text-primary-300"
            >
              {t("detail_job_page_signin_to_view_salary")}
            </button>
          )}
        </div> */}
      </div>
      {/* End Info detail job header */}

      {/* Handle action */}
      <div className={classNames("flex flex-col gap-2")}>
        {
          <ApplyJobButton
            statusDisplay={job.status_display}
            isApplied={job.is_applied}
            jobId={job.id}
            detailUrl={job.detail_url}
            companyDisplayName={job?.company?.display_name}
            jobTypesStr={job.job_types_str}
            job={job}
            locale={locale}
            voucherApply={promotion}
          />
        }
        <div className="flex flex-row gap-2">
          {!isApplied && (
            <Button
              onClick={() => handleCreateCVToApply(job.id)}
              id="createTopdevCV"
              isBlock
              accent="outline"
              className="h-[48px] whitespace-nowrap md:w-[211px]"
            >
              {t("detail_job_page_create_cv_to_apply")}
            </Button>
          )}
          {!isJobClosed && (
            <FollowJobNewButton jobId={jobId} jobTitle={jobTitle} />
          )}

          {!isJobClosed && <ShareJobButton shareUrl={pathName} jobId={jobId} />}
        </div>
      </div>
      {/* End handle action */}
    </section>
  );
  //End Tempalte
};
export default CardDetailJobNewHeader;
