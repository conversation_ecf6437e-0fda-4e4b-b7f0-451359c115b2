import React from "react";

export type CardItemTextProps = {
  name: string;
  description: string;
  id: number | string;
  index: number;
};

const CardItemText = (props: CardItemTextProps) => {
  const { name, description, index } = props;
  return (
    <div
      className={`lg:flex lg:items-center gap-3 lg:py-2 text-sm lg:text-base ${
        index === 0 ? "pt-0" : ""
      } mx-[13px] lg:border-b lg:border-solid lg:border-[#F6F6F6] last:border-b-0 last:pb-0`}
    >
      <span className="lg:block lg:min-w-[210px] font-[500] text-[#3D3D3D]">
        {name}<span className="inline lg:hidden">:</span>
      </span>
      &nbsp;
      <span className="text-[#5D5D5D]">{description}</span>
    </div>
  );
};

export default CardItemText;
