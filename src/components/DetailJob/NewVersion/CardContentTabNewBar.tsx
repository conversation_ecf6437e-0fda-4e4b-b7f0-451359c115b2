"use client";

import TabNewVersion from "@/components/Tab/TabNewVersion";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { isMobile } from "react-device-detect";

export default function CardContentTabNewBar() {
  const t = useTranslations();
  const [isActive, setIsActive] = useState<string>("JobDescription");

  const handleTab = (type: string) => {
    setIsActive(type);
    const element = document.getElementById(type);
    if (!element) return;

    if (!isMobile) {
      const scrollY = (element?.offsetTop as number) - 160 || 0;
      window.scrollTo({
        top: scrollY,
        behavior: "smooth",
      });
    } else {
      if (type === "AboutCompany")
        document.getElementById("JobDescription")?.classList.add("hidden");
      else if (type === "JobDescription")
        document.getElementById("AboutCompany")?.classList.add("hidden");
      element.classList.remove("hidden");
    }
  };

  return (
    <div
      className={
        "sticky top-14 z-10 flex w-full overflow-hidden rounded-t md:top-[143px] py-2 bg-[#F5F5F5]"
      }
    >
      <div
        id="tabJobDescription"
        className="w-1/2 text-sm cursor-pointer md:text-base pl-2"
        onClick={() => handleTab("JobDescription")}
      >
        <TabNewVersion isActive={isActive === "JobDescription"}>
          {t("detail_job_page_job_description")}
        </TabNewVersion>
      </div>
      <div
        id="tabAboutCompany"
        className="w-1/2 text-sm cursor-pointer md:text-base pr-2"
        onClick={() => handleTab("AboutCompany")}
      >
        <TabNewVersion isActive={isActive === "AboutCompany"}>
          {t("detail_job_page_about_company")}
        </TabNewVersion>
      </div>
    </div>
  );
}
