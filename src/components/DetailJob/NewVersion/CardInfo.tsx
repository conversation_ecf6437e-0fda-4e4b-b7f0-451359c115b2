"use client";

import { JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { classNames, openLoginPopup } from "@/utils";
import useTaxonomy from "@/utils/taxonomies";
import { useTranslations } from "next-intl";
import { Fragment, useEffect, useState } from "react";
import { isMobile } from "react-device-detect";
import SkillTagTaxonomy from "@/components/Tag/SkillTagTaxonomy";
import Link from "next/link";
import { HiMiniBanknotes } from "react-icons/hi2";
import { PiClockCountdown } from "react-icons/pi";
import { useAppSelector } from "@/store";
import CubeTech from "@/components/Icons/CubeTech";
import Experience from "@/components/Icons/Experience";
import JobTypeIcon from "@/components/Icons/JobType";
import JobLevel from "@/components/Icons/JobLevel";
import ContractType from "@/components/Icons/ContractType";

interface CardInfoProps {
  locale: Lang;
  job?: JobType | any;
  srcPage?: string;
  mediumPage?: string;
}

const CardInfo = ({
  locale,
  job,
  srcPage = "detailjob",
  mediumPage = "general_info",
}: CardInfoProps) => {
  const t = useTranslations();
  const taxonomy = useTaxonomy();
  const [isClient, setIsClient] = useState(false);
  const EXP_EXCEPT_IDS = [1640, 1651, 4851];

  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);

  const convertSlugTaxonomy = (id: number, slug: string): string => {
    return "/" + t("slug_it_jobs") + "/" + slug + "-kt" + id;
  };

  const recruimentProcess =
    job?.recruiment_process && job?.recruiment_process.length > 0
      ? job?.recruiment_process
      : job?.company.recruitment_process;

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isIncludeExceptId = (id: number) => {
    return EXP_EXCEPT_IDS.includes(id);
  };

  const ExperienceInfo = () => {
    if (!job?.experiences_ids || !job?.experiences_ids.length) return null;
    const fromExperienceId = job.experiences_ids[0];
    const dataTaxonomy = taxonomy(fromExperienceId, "experiences");

    if (!dataTaxonomy) {
      return null;
    }
    const name = locale === "en" ? dataTaxonomy?.text_en : dataTaxonomy?.text;
    return (
      <Link
        className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
        key={fromExperienceId}
        href={{
          pathname: convertSlugTaxonomy(
            fromExperienceId,
            dataTaxonomy?.slug as string,
          ),
          query: { src: "topdev_" + srcPage, medium: mediumPage },
        }}
        title={name}
      >
        {isIncludeExceptId(fromExperienceId) ? null : t("detail_job_from")}{" "}
        {name}
      </Link>
    );
  };

  return (
    <>
      <div className="mb-4 flex flex-col gap-0 border-b border-[#F6F6F6] pb-[1rem]">
        <div className={classNames("mb-2 flex items-center")}>
          <HiMiniBanknotes className="mr-2 h-6 w-6 text-[#DD3F24]" />
          {isLoggedIn ? (
            <p className="text-primary">
              {job?.salary?.value ?? t("detail_job_negotiable")}
            </p>
          ) : (
            <button
              type="button"
              onClick={() =>
                openLoginPopup([
                  { name: "referring_name", value: "salary_job" },
                ])
              }
              className="text-base font-semibold underline text-primary-300"
            >
              {t("detail_job_page_signin_to_view_salary")}
            </button>
          )}
        </div>
        <div className={classNames("mb-2 text-base text-gray-500")}>
          {!!job?.refreshed?.since && (
            <div className="flex last:mb-0">
              <PiClockCountdown className="mr-2 flex h-6 w-6 -rotate-90 items-center text-[#888]" />
              <div className="flex w-11/12 items-center text-[14px] font-[500] text-[#5D5D5D]">
                {t("detail_job_posted_date")} {job?.refreshed?.since}
                <span className="text-[#292929]">
                  &nbsp;
                  {!job?.expires
                    ? null
                    : t("expire_and") +
                      " " +
                      t("job_expire_in") +
                      " " +
                      job?.expires?.since}
                </span>
              </div>
            </div>
          )}
        </div>
        <div className={classNames("flex items-center")}>
          <CubeTech className="mr-2" />
          <div
            className={classNames(
              isMobile ? "max-h-[200px] overflow-y-auto" : "",
            )}
          >
            <div className="flex flex-wrap gap-y-2">
              {job?.skills_ids.map((id: number, index: number) => {
                return (
                  <div key={index}>
                    <SkillTagTaxonomy skillId={id} srcPage={"detailjob"} />
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col md:flex-row">
        {/* experiences */}
        {!!job?.experiences_ids && job?.experiences_ids.length > 0 && (
          <div className="w-2/4 mb-2 item-card-info md:mb-4 md:w-full">
            <div className="flex items-center gap-2">
              <span className="flex h-[36px] w-[36px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
                <Experience />
              </span>
              <div>
                <h3 className="mb-0 text-[12px] font-normal text-[#888]">
                  {t("detail_job_minimum_year_of_experience")}
                </h3>
                <div>
                  <ExperienceInfo />
                </div>
              </div>
            </div>
          </div>
        )}
        {/* End experiences */}

        {/* job levels */}
        {!!job?.job_levels_ids && job?.job_levels_ids.length > 0 && (
          <div className="w-2/4 mb-2 item-card-info md:mb-4 md:w-full md:pl-0">
            <div className="flex items-center gap-2">
              <span className="flex h-[36px] w-[36px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
                <JobTypeIcon />
              </span>
              <div>
                <h3 className="mb-0 text-[12px] font-normal text-[#888]">
                  {t("detail_job_page_level")}
                </h3>
                <div>
                  {job?.job_levels_ids.map((id: number, index: number) => {
                    const dataTaxonomy = taxonomy(id, "job_levels");
                    const name =
                      locale === "en"
                        ? dataTaxonomy?.text_en
                        : dataTaxonomy?.text;
                    return (
                      <Fragment key={index + "-" + id}>
                        <Link
                          className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
                          href={{
                            pathname: convertSlugTaxonomy(
                              id,
                              dataTaxonomy?.slug as string,
                            ),
                            query: {
                              src: "topdev_" + srcPage,
                              medium: mediumPage,
                            },
                          }}
                          title={name}
                        >
                          {name}
                        </Link>
                        {job?.job_levels_ids?.length - 1 > index ? ", " : ""}
                      </Fragment>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}
        {/* End job levels */}

        {/* job types */}
        {!!job?.job_types_ids && job?.job_types_ids.length > 0 && (
          <div className="w-2/4 mb-2 item-card-info md:mb-4 md:w-full">
            <div className="flex items-center gap-2">
              <span className="flex h-[36px] w-[36px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
                <ContractType />
              </span>
              <div>
                <h3 className="mb-0 text-[12px] font-normal text-[#888]">
                  {t("detail_job_page_job_type")}
                </h3>
                <div>
                  {!!job?.job_types_ids &&
                    job?.job_types_ids.map((id: number, index: number) => {
                      const dataTaxonomy = taxonomy(id, "job_types");
                      const name =
                        locale === "en"
                          ? dataTaxonomy?.text_en
                          : dataTaxonomy?.text;
                      return (
                        <Fragment key={index + "-" + id}>
                          <Link
                            className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
                            href={{
                              pathname: convertSlugTaxonomy(
                                id,
                                dataTaxonomy?.slug as string,
                              ),
                              query: {
                                src: "topdev_" + srcPage,
                                medium: mediumPage,
                              },
                            }}
                            title={name}
                          >
                            {name}
                          </Link>
                          {!!job.job_types_ids &&
                          job?.job_types_ids.length - 1 > index
                            ? ", "
                            : ""}
                        </Fragment>
                      );
                    })}
                </div>
              </div>
            </div>
          </div>
        )}
        {/* End job types */}

        {/* contract types */}
        <div className="w-2/4 mb-2 item-card-info md:mb-4 md:w-full md:pl-0">
          <div className="flex items-center gap-2">
            <span className="flex h-[36px] w-[36px] items-center justify-center rounded-[4px] bg-[#f6f6f6]">
              <JobTypeIcon />
            </span>
            <div>
              <h3 className="mb-0 text-[12px] font-normal text-[#888]">
                {t("detail_job_page_contract_type")}
              </h3>
              <div>
                {!!job?.contract_types_ids &&
                  job?.contract_types_ids.map((id: number, index: number) => {
                    const dataTaxonomy = taxonomy(id, "contract_types");
                    const name =
                      locale === "en"
                        ? dataTaxonomy?.text_en
                        : dataTaxonomy?.text;
                    return (
                      <Fragment key={index + "-" + id}>
                        <Link
                          className="text-sm font-[600] leading-none text-[#292929] hover:text-primary-300 hover:underline md:text-base"
                          key={index + "-" + id}
                          href={{
                            pathname: convertSlugTaxonomy(
                              id,
                              dataTaxonomy?.slug as string,
                            ),
                            query: {
                              src: "topdev_" + srcPage,
                              medium: mediumPage,
                            },
                          }}
                          title={name}
                        >
                          {name}
                        </Link>
                        {!!job?.contract_types_ids &&
                        (job?.contract_types_ids?.length as number) - 1 > index
                          ? ", "
                          : ""}
                      </Fragment>
                    );
                  })}
              </div>
            </div>
          </div>
        </div>
        {/* End contract types */}
      </div>
    </>
  );
};

//End Template
export default CardInfo;
