"use client";

import { ImageGalleryType } from "@/types/company";
import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
const CardSliderCompany = ({
  isClient,
  galleriesCompany,
}: {
  isClient: boolean;
  galleriesCompany: Array<ImageGalleryType>;
}) => {
  const LoadingTemplate = () => {
    return (
      <div className="flex w-full gap-[22px]">
        <div className="flex w-1/3 animate-pulse gap-3">
          <div className="h-full w-2/5 bg-slate-200"></div>
          <div className="flex-1 space-y-6 py-1">
            <div className="h-2 rounded bg-slate-200"></div>
            <div className="h-2 rounded bg-slate-200"></div>
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2 h-2 rounded bg-slate-200"></div>
                <div className="col-span-1 h-2 rounded bg-slate-200"></div>
              </div>
              <div className="h-2 rounded bg-slate-200"></div>
              <div className="h-2 rounded bg-slate-200"></div>
            </div>
          </div>
        </div>
        <div className="flex w-1/3 animate-pulse gap-3">
          <div className="h-full w-2/5 bg-slate-200"></div>
          <div className="flex-1 space-y-6 py-1">
            <div className="h-2 rounded bg-slate-200"></div>
            <div className="h-2 rounded bg-slate-200"></div>
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2 h-2 rounded bg-slate-200"></div>
                <div className="col-span-1 h-2 rounded bg-slate-200"></div>
              </div>
              <div className="h-2 rounded bg-slate-200"></div>
              <div className="h-2 rounded bg-slate-200"></div>
            </div>
          </div>
        </div>
        <div className="flex w-1/3 animate-pulse gap-3">
          <div className="h-full w-2/5 bg-slate-200"></div>
          <div className="flex-1 space-y-6 py-1">
            <div className="h-2 rounded bg-slate-200"></div>
            <div className="h-2 rounded bg-slate-200"></div>
            <div className="space-y-3">
              <div className="grid grid-cols-3 gap-4">
                <div className="col-span-2 h-2 rounded bg-slate-200"></div>
                <div className="col-span-1 h-2 rounded bg-slate-200"></div>
              </div>
              <div className="h-2 rounded bg-slate-200"></div>
              <div className="h-2 rounded bg-slate-200"></div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  if (!isClient) return <LoadingTemplate />;

  return (
    <Swiper
      autoplay={{
        delay: 2500,
        disableOnInteraction: false,
      }}
      slidesPerView={1.5}
      slidesPerGroupSkip={0}
      spaceBetween={16}
      className="mySwiper w-full"
      breakpoints={{
        992: {
          slidesPerView: 3,
          spaceBetween: 22,
          slidesPerGroupSkip: 1,
        },
      }}
    >
      {galleriesCompany?.map((value: ImageGalleryType, index: number) => {
        return (
          <SwiperSlide key={index} className="!h-[128px]">
            <Image
              src={value.url}
              height={128}
              width={254}
              alt={value.name}
              className="h-[128px] w-full max-w-full object-cover lg:w-[256px]"
              loading="lazy"
            />
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
};

export default CardSliderCompany;
