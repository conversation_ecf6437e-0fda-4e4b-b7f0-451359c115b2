"use client";

import Link from "@/components/Link/Link";
import { useTranslations } from "next-intl";
import {
  HiBuildingOffice2,
  HiMiniUserGroup,
  HiMiniFlag,
} from "react-icons/hi2";
import { CompanyType } from "@/types/company";
import DefaultFormatHTML from "./DefaultFormatHTML";
import useTaxonomy from "@/utils/taxonomies";
import { Lang } from "@/types/page";

const CardAboutCompany = ({
  company,
  locale,
}: {
  company: CompanyType;
  locale: Lang;
}) => {
  const t = useTranslations();
  const pathName =
    process.env.NEXT_PUBLIC_BASE_URL +
    "/" +
    t("slug_it_companies") +
    "/" +
    company?.slug +
    "-" +
    company?.id;
  const taxonomy = useTaxonomy();

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="mb-2">
          <h2 className="font-bold text-black md:text-lg">
            {t("detail_job_page_company", { company: company?.display_name })}
          </h2>
          <Link
            className="inline-block break-all text-[#1047B2] hover:underline"
            href={{
              pathname: pathName ?? "",
              query: { src: "topdev_detailjob", medium: "viewcompany" },
            }}
            target="_blank"
          >
            {pathName}
          </Link>
        </div>
        <p className="px-2 py-1 mb-2 text-sm font-semibold border rounded-lg border-neutral-400 text-neutral-600 md:text-base">
          {company?.num_job_openings} {t("company_job_opening")}
        </p>
      </div>
      <div className="border-b-2 border-neutral-100"></div>
      <div className="flex flex-col mt-2">
        <div className="grid w-full grid-cols-12 gap-4 px-4 py-2">
          <div className="flex col-span-5 py-2">
            <span className="flex-shrink-0">
              <HiBuildingOffice2 width={20} height={20} className="m-auto" />
            </span>
            <div className="flex flex-col ml-2">
              <h3 className="text-sm font-bold text-black md:text-base">
                {t("detail_job_page_industry")}
              </h3>
              <p className="text-base line-clamp-2 text-neutral-600 md:text-base">
                {company?.industries_ids.length > 0
                  ? company?.industries_ids?.map(
                      (id: number, index: number) => {
                        const industry = taxonomy(id, "industries");
                        const industryName =
                          locale === "en" ? industry?.text_en : industry?.text;
                        return `${industryName}${
                          company.industries_ids.length - 1 > index ? ", " : ""
                        }`;
                      },
                    )
                  : null}
              </p>
            </div>
          </div>
          <div className="flex col-span-4 py-2">
            <span className="flex-shrink-0">
              <HiMiniUserGroup width={20} height={20} className="m-auto" />
            </span>
            <div className="flex flex-col ml-2">
              <h3 className="text-sm font-bold text-black md:text-base">
                {t("detail_job_page_company_size")}
              </h3>
              <p className="text-base line-clamp-2 text-neutral-600 md:text-base">
                {!!company?.num_employees &&
                taxonomy(company?.num_employees, "num_employees") &&
                locale == "en"
                  ? taxonomy(company?.num_employees, "num_employees")?.text_en
                  : taxonomy(company?.num_employees, "num_employees")
                      ?.text}{" "}
                {t("company_num_employees_text")}
              </p>
            </div>
          </div>
          <div className="flex w-auto col-span-3 py-2">
            <span className="flex-shrink-0">
              <HiMiniFlag width={20} height={20} className="m-auto" />
            </span>
            <div className="flex flex-col ml-2">
              <h3 className="text-sm font-bold text-black md:text-base">
                {t("detail_job_page_nationality")}
              </h3>
              <p className="text-base line-clamp-2 text-neutral-600 md:text-base">
                {company?.nationalities_arr.length > 0
                  ? company?.nationalities_arr
                      ?.map((value: any) => value?.national)
                      .join(", ")
                  : null}
              </p>
            </div>
          </div>
        </div>
      </div>
      {!!company?.description && (
        <div className="mt-2 overflow-hidden">
          <div className="relative flex" id="contentDescription">
            <span className="md:max-h-auto max-w-auto prose max-h-[90px] max-w-full overflow-hidden text-sm md:line-clamp-3 md:max-h-[102px] lg:text-base">
              <DefaultFormatHTML html={company?.description} />
            </span>{" "}
            <span className="absolute bottom-0 right-0 ml-1 bg-white">
              <span>...</span>
              <Link
                href={{
                  pathname: pathName ?? "",
                  query: { src: "topdev_detailjob", medium: "viewcompany" },
                }}
                target="_blank"
                title={t("detail_job_page_read_more")}
                className="text-sm font-semibold underline transition-all text-primary-300 hover:text-primary-400 lg:text-base"
              >
                {t("detail_job_page_read_more")}
              </Link>
            </span>
          </div>
        </div>
      )}
    </>
  );
};

export default CardAboutCompany;
