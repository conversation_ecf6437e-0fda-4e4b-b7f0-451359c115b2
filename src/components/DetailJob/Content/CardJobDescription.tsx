"use client";

import { useAppSelector } from "@/store";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { BenefitType, JobType, TextEditorResponse } from "../../../types/job";
import DefaultFormatHTML from "./DefaultFormatHTML";

type ContentProps = {
  content?: string;
  responsibilities?: string | TextEditorResponse;
  requirements?: string | TextEditorResponse;
  benefits?: BenefitType[];
};

const CardJobDescription = ({ job }: { job: JobType }) => {
  const t = useTranslations();

  //State content job
  const [contentNew, setContentNew] = useState<ContentProps>({
    content: "",
    responsibilities: "",
    requirements: "",
    benefits: [],
  });
  //End state content job

  // Effect update content job from job store
  useEffect(() => {
    if (!!job) {
      const isEmptyBenefitsJob = !job?.benefits
        .map((item) => item.value)
        .filter((element) => element !== null).length;
      const benefits =
        job?.benefits && !isEmptyBenefitsJob
          ? job?.benefits
          : job?.company?.benefits;

      setContentNew({
        content: job?.content,
        responsibilities: job?.responsibilities,
        requirements: job?.requirements,
        benefits: benefits,
      });
    }
  }, [job]);
  //End effect update content job from job store

  const checkJobDescription =
    !!job?.content ||
    !!job?.responsibilities ||
    !!job?.requirements ||
    !!job?.benefits;

  if (!checkJobDescription) return <></>;

  return (
    <>
      {!!contentNew?.content && (
        <div className="prose mb-4 max-w-full border-b border-gray-200 pb-2 text-sm last:mb-0 last:border-0 last:pb-0 lg:text-base">
          <DefaultFormatHTML html={contentNew?.content} />
        </div>
      )}

      {!!contentNew?.responsibilities && (
        <div className="mb-4 border-b border-gray-200 pb-4 last:mb-0 last:border-0 last:pb-0">
          <h2 className="mb-2 text-base font-bold text-black">
            {t("detail_job_page_responsibilities")}
          </h2>
          <div className="prose max-w-full text-sm text-black lg:text-base">
            <DefaultFormatHTML
              html={
                typeof contentNew?.responsibilities === "string"
                  ? contentNew.responsibilities
                  : ""
              }
            />
          </div>
        </div>
      )}

      {!!contentNew?.requirements && (
        <div className="mb-4 border-b border-gray-200 pb-4 last:mb-0 last:border-0 last:pb-0">
          <h2 className="mb-2 text-base font-bold text-black">
            {t("detail_job_page_requirements")}
          </h2>
          <div className="prose max-w-full text-sm text-black lg:text-base">
            <DefaultFormatHTML
              html={
                typeof contentNew?.requirements === "string"
                  ? contentNew.requirements
                  : ""
              }
            />
          </div>
        </div>
      )}

      {!!contentNew?.benefits && contentNew?.benefits.length > 0 && (
        <div>
          <h2 className="mb-2 text-base font-bold text-black">
            {t("detail_job_page_benefits_of_this_jobs")}
          </h2>
          <div className="prose max-w-full text-sm text-black lg:text-base">
            <div className="pl-0">
              {contentNew?.benefits.length > 1 ? (
                <ul>
                  {contentNew?.benefits.map((benefit, index) => (
                    <li
                      key={index}
                      dangerouslySetInnerHTML={{ __html: benefit.value }}
                    />
                  ))}
                </ul>
              ) : (
                <div className="pl-0">
                  {contentNew?.benefits.map((benefit, index) => (
                    <div
                      key={index}
                      className="prose max-w-full text-sm text-black lg:text-base"
                      dangerouslySetInnerHTML={{ __html: benefit.value }}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CardJobDescription;
