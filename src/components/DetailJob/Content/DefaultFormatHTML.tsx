"use client";
import React, { FC, useEffect, useState } from "react";

interface Props {
  html: string;
}
const DefaultFormatHTML: FC<Props> = ({ html }) => {
  const [defaultText, setDefaultText] = useState("");

  useEffect(() => {
    try {
      const wrapperElement = document.createElement("div");
      wrapperElement.innerHTML = html;
      const defaultFormat = wrapperElement.querySelectorAll("*") as any;
      [...defaultFormat].forEach((item) => {
        item.style.fontSize = "inherit";
      });
      setDefaultText(wrapperElement.outerHTML)
    } catch (error) {
      setDefaultText(html);
    }
  }, [html]);

  return (
    <div className="" dangerouslySetInnerHTML={{ __html: defaultText }}></div>
  );
};

export default DefaultFormatHTML;
