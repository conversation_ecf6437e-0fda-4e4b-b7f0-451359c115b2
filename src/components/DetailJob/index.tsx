import CardDetailJobHeader from "@/components/DetailJob/CardDetailJobHeader";
import CardRightApply from "@/components/DetailJob/CardRightApply";
import CardRightInfo from "@/components/DetailJob/CardRightInfo";
import CardJobDescription from "@/components/DetailJob/Content/CardJobDescription";
import CardDetailJobHeaderMobile from "@/components/DetailJob/Mobile/CardDetailJobHeader.Mobile";
import CardDetailJobHeaderNew from "@/components/DetailJob/NewVersion/CardDetailJobNewHeader";
import CardInfo from "@/components/DetailJob/NewVersion/CardInfo";
import CardDetailJobNewHeaderMobile from "@/components/DetailJob/NewVersion/Mobile/CardDetailJobNewHeader.Mobile";
import dynamic from "next/dynamic";

const InitTracking = dynamic(
  () => import("@/components/DetailJob/InitTracking"),
);

const CardMoreJob = dynamic(() => import("@/components/DetailJob/CardMoreJob"));
const ApplyJob = dynamic(() => import("@/components/DialogModal/ApplyJob"));

const ApplyAllJobs = dynamic(
  () => import("@/components/DialogModal/ApplyAllJobs"),
);

const NotificationSuccess = dynamic(
  () => import("@/components/DialogModal/NotificationSuccess"),
);

const CardAboutCompany = dynamic(
  () => import("@/components/DetailJob/Content/CardAboutCompany"),
);
const CardSliderCompany = dynamic(
  () => import("@/components/DetailJob/Content/CardSliderCompany"),
);

export {
  NotificationSuccess,
  ApplyJob,
  ApplyAllJobs,
  InitTracking,
  CardRightInfo,
  CardRightApply,
  CardDetailJobHeader,
  CardDetailJobHeaderMobile,
  CardMoreJob,
  CardJobDescription,
  CardAboutCompany,
  CardSliderCompany,

  // New Version Detail
  CardDetailJobHeaderNew,
  CardInfo,
  CardDetailJobNewHeaderMobile
};
