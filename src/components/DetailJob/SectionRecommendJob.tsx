"use client";
import React, { useEffect, useState } from "react";
import { JobRecommendData, JobType } from "@/types/job";
import { getNewestCandidate, getRecommendJobsByJobId } from "@/services/jobAPI";
import { getCurrentLocaleForParams } from "@/utils/locale";
import CardRecommendJob from "../Card/Job/CardRecommendJob";
import { useAppDispatch, useAppSelector } from "@/store";
import { useTranslations } from "next-intl";
import { openLoginPopup } from "@/utils";
import CardPopoverRecommendJob from "../Card/Job/CardPopoverRecommendJob";
import { setNewestCandidate } from "@/store/slices/userSlice";
import Image from "next/image";
import { IoMdClose } from "react-icons/io";
import { Link } from "@/navigation";
import Button from "../Button/Button";
import { NewestCandidate } from "@/types/user";

const SectionRecommendJob = ({ job }: { job: JobType | any }) => {
  const t = useTranslations();
  let timer: NodeJS.Timeout;
  const [isLoginPopupOpen, setIsLoginPopupOpen] = useState(false);
  const [jobState, setJobState] = useState<JobType>(job);
  const [recommendJobsByJobId, setRecommendJobByJobId] =
    useState<Array<JobType>>();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const [jobDataPopover, setJobDataPopover] =
    useState<JobRecommendData | null>();
  const [popoverVisible, setPopoverVisible] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState<{
    top: number;
    left: number;
  } | null>(null);
  const [isMouseOverPopover, setIsMouseOverPopover] = useState(false);
  const dispatch = useAppDispatch();
  const newestCandidate = useAppSelector(
    (state) => state?.user?.newest_candidate,
  );
  const [isEasyApply, setIsEasyApply] = useState<boolean>(true);
  const [isShowBanner, setIsShowBanner] = useState<boolean>(false);
  const eventData = useAppSelector((state) => state?.setting?.eventData);

  useEffect(() => {
    const fetchNewestCandidate = async () => {
      const newest_candidate: NewestCandidate = await getNewestCandidate().then(
        (response) => {
          setIsShowBanner(true);
          return response.data.data.newest_candidate;
        },
      );
      if (newest_candidate && newest_candidate.id) {
        setIsEasyApply(true);
        dispatch(setNewestCandidate(newest_candidate));
      }
    };
    if (!!isLoggedIn) {
      fetchNewestCandidate();
    }
  }, [isLoggedIn, dispatch, eventData]);

  useEffect(() => {
    const fecthData = async () => {
      const jobRecommendData = await getRecommendJobsByJobId(
        jobState?.id,
        getCurrentLocaleForParams(),
      );
      if (jobRecommendData) {
        setRecommendJobByJobId(
          jobRecommendData.data.slice(0, isLoggedIn ? 9 : 6),
        );
      }
    };
    if (jobState) {
      fecthData();
    }
  }, [jobState, isLoggedIn, isLoginPopupOpen]);

  if (!recommendJobsByJobId || recommendJobsByJobId.length === 0) return <></>;

  const handleMouseEnter = (data: any, event: React.MouseEvent) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      setJobDataPopover(data);
      setPopoverPosition({
        top: event.clientY + window.scrollY + 10,
        left: event.clientX + window.scrollX + 10,
      });
      setPopoverVisible(true);
    }, 100);
  };

  const handleMouseEnterPopover = () => {
    setIsMouseOverPopover(true);
    clearTimeout(timer);
  };

  const handleMouseLeavePopover = () => {
    setIsMouseOverPopover(false);
  };

  const handleMouseLeave = () => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      if (!isMouseOverPopover) {
        setPopoverVisible(false);
      }
    }, 200);
  };

  return (
    <section className="flex flex-col w-full gap-4 md:w-full">
      <div className={`${job?.content_html ? 'mx-auto max-w-[80rem]' : 'container'} flex flex-col flex-wrap items-start gap-6 px-0 mt-4 md:flex-nowrap md:px-4`}>
        <div className="flex flex-col w-full gap-1 p-4 bg-white">
          <h3 className="text-2xl font-semibold text-neutral-950">
            {t("recommend_title")}
          </h3>
          <p>
            {t("detail_job_recommended_sub_title")}
            {!isLoggedIn && (
              <Button
                size="sm"
                type="button"
                id="btn-login-header"
                accent="link"
                onClick={() => openLoginPopup()}
              >
                {t("detail_job_recommended_job_login_now")}
              </Button>
            )}
          </p>
        </div>

        {newestCandidate && isShowBanner && (
          <div className="flex flex-row items-center w-full gap-2 p-4 bg-cyan-light text-cyan-dark">
            <div className="flex flex-row items-start w-full gap-2">
              <Image
                src="https://c.topdevvn.com/uploads/2024/06/26/icon_robot_chat.webp"
                alt="icon robot chat"
                className="h-auto max-w-full"
                loading="lazy"
                width="18"
                height="18"
              />
              <p>
                <span>{t("detail_job_easy_apply_with_latest_cv_pdf")}</span>
                <span className="font-bold">
                  <Link
                    target="_blank"
                    href={{
                      pathname: "/detail-jobs/[slug]",
                      params: {
                        slug:
                          newestCandidate?.slug + "-" + newestCandidate?.job_id,
                      },
                    }}
                  >
                    {newestCandidate.position}
                  </Link>
                </span>
                <span> ({newestCandidate.created_at.short})</span>
              </p>
            </div>

            <span
              onClick={() => setIsShowBanner(false)}
              className="inline-block w-5 h-5 text-center cursor-pointer"
            >
              <IoMdClose className="inline-block w-5 h-5" />
            </span>
          </div>
        )}

        <div className="relative">
          <div className="grid grid-cols-3 gap-6">
            {recommendJobsByJobId?.map((job) => {
              const benefits = job.benefits
                .filter((benefit) => benefit.value !== null)
                .map((benefit) => benefit.value);
              return (
                <CardRecommendJob
                  srcPage={"detailjob"}
                  mediumPage={"recommenedjob"}
                  statusDisplay={job?.status_display}
                  isApplied={job?.is_applied}
                  key={job.id}
                  jobId={job.id}
                  logo={job?.company?.image_logo}
                  title={job.title}
                  company={job?.company?.display_name}
                  jobTypesStr={job?.job_types_str}
                  experiences_str={job?.experiences_str}
                  salary={
                    isLoggedIn
                      ? job?.salary.value ?? `${t("detail_job_negotiable")}`
                      : `${t("detail_job_page_signin_to_view_salary")}`
                  }
                  location={job.addresses.sort_addresses ?? ""}
                  skills={job.skills_ids}
                  benefits={benefits}
                  responsibilities={job.responsibilities}
                  detailUrl={job?.detail_url}
                  jobPosted={job?.refreshed?.since}
                  onHover={(data, event) => handleMouseEnter(data, event)}
                  onMouseLeave={handleMouseLeave}
                  job={job}
                />
              );
            })}
          </div>
          {!isLoggedIn && recommendJobsByJobId.length > 3 && (
            <>
              <div
                className={`absolute bottom-0 z-10 w-full bg-gradient-radial ${
                  recommendJobsByJobId.length > 6 ? "h-[70%]" : "h-[50%]"
                }`}
              ></div>
              <button
                role="navigation"
                onClick={() => {
                  openLoginPopup([
                    { name: "referring_name", value: "rec_job" },
                  ]);
                  setIsLoginPopupOpen(true);
                }}
                className={`relative z-20 mx-auto flex h-auto text-base font-semibold text-brand-600 underline ${
                  recommendJobsByJobId.length > 6 ? "bottom-96" : "bottom-16"
                }`}
              >
                {t("recommend_login_view_more_job")}
              </button>
            </>
          )}
        </div>
        <CardPopoverRecommendJob
          isVisible={popoverVisible}
          slug={jobDataPopover?.job?.slug}
          logo={jobDataPopover?.logo ?? ""}
          jobId={jobDataPopover?.jobId ?? 0}
          title={jobDataPopover?.title ?? ""}
          company={jobDataPopover?.company ?? ""}
          salary={
            isLoggedIn
              ? jobDataPopover?.salary ?? `${t("detail_job_negotiable")}`
              : `${t("detail_job_page_signin_to_view_salary")}`
          }
          location={jobDataPopover?.location ?? ""}
          skills={jobDataPopover?.skills ?? []}
          detailUrl={jobDataPopover?.detailUrl ?? ""}
          benefits={jobDataPopover?.benefits ?? []}
          jobTypesStr={jobDataPopover?.jobTypesStr ?? ""}
          responsibilities={jobDataPopover?.responsibilities ?? ""}
          experiences_str={jobDataPopover?.experiences_str ?? ""}
          position={popoverPosition}
          onMouseEnter={handleMouseEnterPopover}
          onMouseLeave={handleMouseLeavePopover}
          srcPage={"detailjob"}
          mediumPage={"recommenedjob"}
        />
      </div>
    </section>
  );
};

export default SectionRecommendJob;
