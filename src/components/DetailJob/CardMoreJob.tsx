"use client";

import { <PERSON> } from "@/navigation";
import dynamic from "next/dynamic";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { isMobile } from "react-device-detect";
import { JobType } from "@/types/job";
import { useAppDispatch, useAppSelector } from "@/store";
import { getJobsByCompanyApi, getNewestCandidate, getRecommendJobsByJobId } from "@/services/jobAPI";
import { getCurrentLocaleForParams } from "@/utils/locale";
import { NewestCandidate } from "@/types/user";
import { setNewestCandidate } from "@/store/slices/userSlice";
import { MAX_JOBS_RECOMMEND_MOBILE } from "@/utils/enums";

//Card Job Recommend
const CardJobRecommend = dynamic(
  () => import("@/components/Card/Job/CardRecommend"),
);
//End Card Job Recommend

//Card Job Recommend Mobile
const CardJobRecommendMobile = dynamic(
  () => import("@/components/Card/Job/mobile/CardRecommend.mobile"),
);
//End Card Job Recommend Mobile

const CardMoreJob = ({ job }: { job: JobType }) => {
  const t = useTranslations();
  const currentJob = useAppSelector((state) => state?.job?.currentJob);
  const [jobState, setJobState] = useState<JobType>(job);
  const [jobsByCompany, setJobsByCompany] = useState<Array<JobType>>();
  const [isClient, setIsClient] = useState(false);
  const dispatch = useAppDispatch();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const eventData = useAppSelector((state) => state?.setting?.eventData);
  // set newst Candidates in mobile
  useEffect(() => {
    const fetchNewestCandidate = async () => {
      const newest_candidate: NewestCandidate = await getNewestCandidate().then(
        (response) => {
          return response.data.data.newest_candidate;
        },
      );
      if (newest_candidate && newest_candidate.id) {
        dispatch(setNewestCandidate(newest_candidate));
      }
    };
    if (!!isLoggedIn) {
      fetchNewestCandidate();
    }
  }, [isLoggedIn, dispatch, eventData]);

  //Effect client
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    const fecthData = async () => {
      const { data } = await getRecommendJobsByJobId(
        jobState?.id,
        getCurrentLocaleForParams(),
        'mobile'
      ) as any;
      
      if (data) setJobsByCompany(data?.slice(0, MAX_JOBS_RECOMMEND_MOBILE));
    };
    if (jobState) {
      fecthData();
    }
  }, [jobState]);

  useEffect(() => {
    const fecthData = async () => {
      const { data }  = await getRecommendJobsByJobId(
        currentJob?.id,
        getCurrentLocaleForParams(),
        'mobile'
      ) as any;
      
      if (data) setJobsByCompany(data?.slice(0, MAX_JOBS_RECOMMEND_MOBILE));
    };
    if (!!currentJob && currentJob.id > 0) {
      fecthData();
      setJobState(currentJob);
    }
  }, [currentJob]);

  if (!isClient || (!!jobsByCompany && jobsByCompany.length === 0))
    return <></>;

  //Start Tempalte
  return (
    <section className="p-2 pb-6 bg-white md:p-4">
      <h2 className="text-base font-bold text-black">
        {t("more_job_here")}
      </h2>
      {!isMobile ? (
        <>
          {!!jobsByCompany &&
            jobsByCompany.length > 0 &&
            jobsByCompany.map((job: JobType, index: number) => {
              return (
                <div key={index} className="mt-2">
                  <CardJobRecommend job={job} srcPage={"detailjob"} mediumPage={"morejobs"} />
                </div>
              );
            })}
        </>
      ) : (
        <>
          {!!jobsByCompany &&
            jobsByCompany.length > 0 &&
            jobsByCompany.map((job: JobType, index: number) => {
              return (
                <div key={index} className="mt-2">
                  <CardJobRecommendMobile job={job} />
                </div>
              );
            })}
        </>
      )}
    </section>
  );
};

export default CardMoreJob;
