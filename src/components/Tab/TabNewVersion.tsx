import React, { FC, ReactNode } from "react";
import { classNames } from "@/utils";

interface Props {
  leadingIcon?: ReactNode;
  children: ReactNode;
  quantity?: number;
  isQuantity?: boolean;
  isActive: boolean;
  onSelect?: () => void;
}

const TabNewVersion: FC<Props> = (props) => {
  const {
    children,
    isQuantity = false,
    quantity = 0,
    leadingIcon,
    isActive = false,
    onSelect,
  } = props;
  return (
    <div
      onClick={() => onSelect && onSelect()}
      className={classNames(
        "select-none flex items-center justify-center gap-3 py-3 text-sm transition-all after:absolute after:bottom-0 after:left-0 after:w-full after:transition-all after:content-[''] lg:flex-none lg:px-6 lg:py-[1.125rem] lg:text-base",
        isActive
          ? "font-bold text-primary after:h-1 bg-white rounded"
          : "cursor-pointer text-gray-600 after:h-px",
      )}
    >
      {leadingIcon && <span>{leadingIcon}</span>}
      {children}
      {isQuantity && (
        <span className="inline-block aspect-square w-[1.875rem] rounded-full bg-primary-200 text-primary">
          {quantity}
        </span>
      )}
    </div>
  );
};

export default TabNewVersion;
