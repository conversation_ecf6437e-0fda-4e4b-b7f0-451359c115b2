"use client";
import React, {
  Di<PERSON><PERSON>,
  FC,
  ReactNode,
  createContext,
  useContext,
  useReducer,
} from "react";

interface TabStateType {
  type: string | null;
}
type TabActionType = { type: "UPDATE_TAB"; payload: string };
interface TabContextType {
  state: TabStateType;
  dispatch: Dispatch<TabActionType>;
}

const initialState: TabStateType = {
  type: "",
};

const TabReducer = (
  state: TabStateType = initialState,
  action: TabActionType,
): TabStateType => {
  switch (action.type) {
    case "UPDATE_TAB": {
      return { ...state, type: action.payload };
    }
    default:
      return state;
  }
};

const TabContext = createContext<TabContextType | undefined>({
  state: initialState,
  dispatch: () => {},
});

const TabGroupProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(TabReducer, initialState);
  return (
    <TabContext.Provider value={{ state, dispatch }}>
      {children}
    </TabContext.Provider>
  );
};

export const useTabContext = () => {
  const tabContext = useContext(TabContext);
  if (typeof tabContext === "undefined") {
    throw Error("Tab context is not available!");
  }
  return tabContext;
};

export default TabGroupProvider;
