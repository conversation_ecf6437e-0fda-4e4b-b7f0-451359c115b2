"use client";
import React, { FC, ReactNode, useEffect } from "react";
import { useTabContext } from ".";
import { classNames } from "@/utils";

interface Props {
  children: ReactNode;
  type: string;
}

const TabItem: FC<Props> = ({ children, type }) => {
  const { state, dispatch } = useTabContext();
  const handleSwitchTab = () => {
    dispatch({ type: "UPDATE_TAB", payload: type });
  };

  return (
    <div
      onClick={() => handleSwitchTab()}
      className={classNames(
        "relative flex flex-1 select-none items-center justify-center gap-3 bg-white py-2 text-sm transition-all lg:flex-none lg:px-9 lg:py-[1.125rem] lg:text-base",
        "after:absolute after:bottom-0 after:left-0 after:w-full after:transition-all after:content-['']",
        state.type === type
          ? "font-bold text-primary after:h-1 after:bg-primary"
          : "cursor-pointer text-gray-600 after:h-px after:bg-gray-600 hover:text-primary hover:after:bg-primary",
      )}
    >
      {children}
    </div>
  );
};

export default TabItem;
