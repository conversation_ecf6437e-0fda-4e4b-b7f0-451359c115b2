import React, { FC, ReactNode } from "react";
import { classNames } from "@/utils";

interface Props {
  leadingIcon?: ReactNode;
  children: ReactNode;
  quantity?: number;
  isQuantity?: boolean;
  isActive: boolean;
  onSelect?: () => void;
}

const Tab: FC<Props> = (props) => {
  const {
    children,
    isQuantity = false,
    quantity = 0,
    leadingIcon,
    isActive = false,
    onSelect,
  } = props;
  return (
    <div
      onClick={() => onSelect && onSelect()}
      className={classNames(
        "relative flex flex-1 select-none items-center justify-center gap-3 bg-white py-4 text-sm transition-all after:absolute after:bottom-0 after:left-0 after:w-full after:transition-all after:content-[''] lg:flex-none lg:px-6 lg:py-[1.125rem] lg:text-base",
        isActive
          ? "font-bold text-primary after:h-1 after:bg-primary"
          : "cursor-pointer text-gray-600 after:h-px after:bg-gray-600 hover:text-primary hover:after:bg-primary",
      )}
    >
      {leadingIcon && <span>{leadingIcon}</span>}
      {children}
      {isQuantity && (
        <span className="inline-block aspect-square w-[1.875rem] rounded-full bg-primary-200 text-primary">
          {quantity}
        </span>
      )}
    </div>
  );
};

export default Tab;
