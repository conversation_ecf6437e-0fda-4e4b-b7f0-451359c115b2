"use client";
import { FC, useState } from "react";
import Tab from "./Tab";
import { TabType } from "@/types/search";
import TabItem from "./TabGroup/TabItem";
import { useTranslations } from "next-intl";

interface TabGroupType {
  hasJob: boolean;
  hasCompany: boolean;
}

const TabResultSearch: FC<TabGroupType> = ({ hasJob, hasCompany }) => {
  const t = useTranslations();
  return (
    <div className="flex items-center justify-start">
      <TabItem type="">{t("search_page_tab_all")}</TabItem>
      {hasJob && (
        <TabItem type={TabType.TAB_JOB}>{t("search_page_tab_job")}</TabItem>
      )}
      {hasCompany && (
        <TabItem type={TabType.TAB_COMPANY}>
          {t("search_page_tab_company")}
        </TabItem>
      )}
    </div>
  );
};

export default TabResultSearch;
