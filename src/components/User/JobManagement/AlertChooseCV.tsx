"use client";
import { useTranslations } from "next-intl";
import { AlertUserProfileProps } from "@/types/userProfile";

const AlertChooseCV = ({
  mainCV,
  status,
  type,
  handleCloseAlert,
  handleChooseMainCv,
}: AlertUserProfileProps) => {
  const t = useTranslations();

  return (
    <>
      {status && type === "info" && (
        <div
          id="alert-border-info"
          className="flex items-center justify-between border-b border-blue-dark bg-[#edfbff] p-4 px-4 py-2 text-black"
          role="alert"
        >
          <div className="flex items-center">
            <svg
              className="h-6 w-6 flex-shrink-0 text-[#1047B2]"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
            </svg>
            <div className="ms-3 text-sm font-medium">
              {t("user_profile_you_have_new_cv_available")}
            </div>
          </div>
          <div className="flex items-center">
            <button
              type="button"
              className="rounded-sm bg-gray-200 px-4 py-[5px] text-sm text-gray-600"
              data-dismiss-target="#alert-border-info"
              aria-label="Close"
              onClick={() => {
                !!handleCloseAlert && handleCloseAlert(true, "success");
                !!handleChooseMainCv && handleChooseMainCv(mainCV);
              }}
            >
              Yes
            </button>
            <button
              type="button"
              className="py-[5px]text-gray-600 bg-transparent px-4 text-sm"
              data-dismiss-target="#alert-border-info"
              aria-label="Close"
              onClick={() =>
                !!handleCloseAlert && handleCloseAlert(false, "info")
              }
            >
              No
            </button>
          </div>
        </div>
      )}

      {status && type === "success" && (
        <div
          id="alert-border-success"
          className="flex items-center justify-between border-b border-[#0F5E1C] bg-[#d9fedf] p-4 px-4 py-2 text-black"
          role="alert"
        >
          <div className="flex items-center">
            <svg
              className="h-6 w-6 flex-shrink-0 text-[#0F5E1C]"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
            </svg>
            <div className="ms-3 text-sm font-medium">
              {t.rich(
                "user_profile_You_have_filled_your_profile_and_set_main_cv",
                {
                  a: (chunks) => (
                    <a className="underline" href="/users/profile">
                      <b>{chunks}</b>
                    </a>
                  ),
                },
              )}
            </div>
          </div>
          <button
            type="button"
            className="bg-transparent"
            data-dismiss-target="#alert-border-success"
            aria-label="Close"
            onClick={() =>
              !!handleCloseAlert && handleCloseAlert(false, "success")
            }
          >
            <svg
              className="h-3 w-3"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 14 14"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
              />
            </svg>
          </button>
        </div>
      )}
    </>
  );
};

export default AlertChooseCV;
