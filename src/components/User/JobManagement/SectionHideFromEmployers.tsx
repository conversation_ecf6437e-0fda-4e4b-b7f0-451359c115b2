import { useTranslations } from "next-intl";
import Link from "next/link";
import React from "react";

const SectionHideFromEmployers = () => {
  const t = useTranslations();
  return (
    <div className="mb-6 flex flex-wrap items-center justify-between gap-4 rounded bg-white p-4">
      <h5 className="text-base font-bold lg:text-xl">
        {t("user_profile_hide_profile_from_employers")}
      </h5>
      <Link
        href="#hide"
        className="inline-flex h-8 items-center justify-center rounded-sm bg-gray-200 px-4 text-sm font-semibold text-gray-600 transition-all hover:bg-gray-300"
      >
        {t("user_profile_do_it_now")}
      </Link>
    </div>
  );
};

export default SectionHideFromEmployers;
