"use client";
import Loading from "@/components/Common/Loading";
import { Link } from "@/navigation";
import { getEmployerViewedCV } from "@/services/userAPI";
import { CompanyViewedType } from "@/types/company";
import { BRAND_LOGO } from "@/utils/image";
import { Pagination } from "flowbite-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { FC, useEffect, useState } from "react";
import IconNotFound from "@/assets/images/job-management/not-found.webp";

interface Props {
  onOpenToWork: () => void;
}
const CompaniesViewedCV: FC<Props> = ({ onOpenToWork }) => {
  const [companies, setCompanies] = useState<Partial<CompanyViewedType>[]>([]);
  const [meta, setMeta] = useState({ lastPage: 0 });
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const t = useTranslations();

  useEffect(() => {
    getCompanyViewedCV();
  }, []);
  const getCompanyViewedCV = async (page?: number) => {
    if (meta.lastPage && currentPage >= meta.lastPage)
      setCurrentPage((pre) => pre);

    getEmployerViewedCV(page)
      .then((response) => {
        const data = response.data;
        setCompanies(data.data);
        setMeta({
          lastPage: data.meta.last_page,
        });
        setCurrentPage(data.meta.current_page);
      })
      .catch((error) => console.error(error))
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleChangePage = async (page: number) => {
    await getCompanyViewedCV(page);
    setCurrentPage(page);
  };

  if (isLoading) {
    return (
      <div className="mt-6 rounded bg-white py-6">
        <Loading />
      </div>
    );
  }

  if (companies.length === 0) {
    return (
      <div className="mt-6 rounded bg-white">
        <div className="border-b border-solid border-gray-200 p-4">
          <h4 className="text-2xl font-semibold">
            {t("user_profile_company_viewed_cv")}
          </h4>
        </div>
        <div className="p-4 text-center">
          <Image
            src={IconNotFound}
            className="mx-auto h-auto max-w-[9rem] object-contain"
            alt="Not found employers"
          />
          <div className="p-4 text-gray-500">
            <h5 className="mt-4 text-xl font-bold">
              {t("user_profile_no_employers_viewed_your_cv")}
            </h5>
            <p className="mt-2">
              {t.rich("user_profile_no_employers_viewed_your_cv_content", {
                user_profile: (chunk) => (
                  <a
                    title={chunk?.toString()}
                    href="/users/profile"
                    className="font-weight cursor-pointer font-bold text-primary underline"
                  >
                    {chunk}
                  </a>
                ),
                make_cv: (chunk) => (
                  <a
                    title={chunk?.toString()}
                    href="/tao-cv-online"
                    className="font-weight cursor-pointer font-bold text-primary underline"
                  >
                    {chunk}
                  </a>
                ),
                open_to_work: (chunk) => (
                  <span
                    title={chunk?.toString()}
                    onClick={() => onOpenToWork()}
                    className="cursor-pointer font-semibold text-primary underline"
                  >
                    {chunk}
                  </span>
                ),
              })}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-6 rounded bg-white">
      <div className="border-b border-solid border-gray-200 p-4">
        <h4 className="text-2xl font-semibold">
          {t("user_profile_company_viewed_cv")}
        </h4>
      </div>
      <div className="p-4">
        {companies.map((item) => {
          return (
            <div
              className="mb-4 flex items-center gap-2 rounded-xl bg-gray-100 p-4 last:mb-0"
              key={item.id}
            >
              <Image
                src={item.image_logo ?? "/v4/assets/images/td-logo.png"}
                width={BRAND_LOGO.small.width}
                height={BRAND_LOGO.small.height}
                className="h-[66px] w-[88px] rounded bg-white object-contain p-2"
                alt={item.display_name || ""}
                loading="lazy"
              />
              <div className="flex-1">
                <Link
                  className="text-lg font-bold transition-all hover:text-primary"
                  href={{
                    pathname: "/companies/[slug]",
                    params: {
                      slug: item.slug + "-" + item.id,
                    },
                  }}
                >
                  {item.display_name}
                </Link>
                <p className="text-xs text-gray-400">{item.unlocked_at}</p>
              </div>
            </div>
          );
        })}
      </div>
      <div className="flex items-center justify-end p-4 pt-0">
        <Pagination
          totalPages={meta.lastPage}
          currentPage={currentPage}
          showIcons
          previousLabel=""
          nextLabel=""
          onPageChange={(page) => handleChangePage(page)}
        />
      </div>
    </div>
  );
};

export default CompaniesViewedCV;
