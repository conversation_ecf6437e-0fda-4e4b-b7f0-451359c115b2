"use client";

import "@/assets/styles/pages/profile.scss";
import { HiXMark, HiOutlineXMark } from "react-icons/hi2";
import Image from "next/image";
import dynamic from "next/dynamic";
import { Modal } from "flowbite-react";
import { ToggleSwitch } from "flowbite-react";
import { useTranslations } from "next-intl";
import { useState, useEffect, ChangeEvent, useRef } from "react";
import NoAvatar from "@/assets/images/no-avatar.svg";
import NavUsers from "@/components/User/Common/NavUsers";
import useUserProfile from "@/hooks/useUserProfile";
import { useAppSelector } from "@/store";
import { classNames } from "@/utils";
import { emailRegExp } from "@/utils/enums";
import {
  getBlockedCompaniesUser,
  blockedCompaniesUser,
  toggleOpenToWork,
  getUserProfile,
} from "@/services/userAPI";
import { searchKeywordCompany } from "@/services/companyAPI";
import ListResumes from "@/components/User/JobManagement/ListResumes";
import { TheBasicInformationFormModal } from "@/components/User/Profile/index";
import { PROFILE_CONTEXT_TYPE, PROFILE_STATUS } from "@/contansts/userProfiles";
import CompaniesViewedCV from "@/components/User/JobManagement/CompaniesViewedCV";
import ToastNotification from "@/components/Swal/ToastNotification";
import SectionHideFromEmployers from "@/components/User/JobManagement/SectionHideFromEmployers";
import Profile from "@/types/profile";
import BagePercent from "@/components/User/Common/BagePercent";
import NoteStatusUser from "@/components/User/Common/NoteStatusUser";

interface Company {
  display_name: string;
  id: number;
}

interface DataGetBlockedCompanies {
  error: boolean;
  message: string;
  data: {
    companies: Company[];
    emails: string[];
  };
}

interface DataSearchCompanies {
  error: boolean;
  message: string;
  data: Company[];
}

const Button = dynamic(() => import("@/components/Button/Button"));

const LIMIT_CHOOSED_VALUES = 15;

const TheJobManagementPage = () => {
  const [userProfile, setUserProfile] = useUserProfile();
  const listSelectCompany = useRef(null);
  const t = useTranslations();
  const OPEN_TO_WORK = 7390;
  const NOT_OPEN_TO_WORK = 7391;
  const user = useAppSelector((state) => state?.user?.user);
  const statusWork = user?.status_works?.at(0) === OPEN_TO_WORK;
  const [errorsEmail, setErrorsEmail] = useState<{
    status: boolean;
    title: string;
  }>({
    status: false,
    title: "",
  });

  const [errorsCompnay, setErrorsCompnay] = useState<{
    status: boolean;
    title: string;
  }>({
    status: false,
    title: "",
  });

  const [changing, setChanging] = useState<boolean>(false);
  const [showOpenToWork, setShowOpenToWork] = useState(false);
  const [companyName, setCompanyName] = useState<string>("");
  const [domainEmail, setDomainEmail] = useState<string>("");
  const [listDomainEmail, setListDomainEmail] = useState<Array<string>>([]);
  const [listCompanyValue, setListCompanyValue] = useState<Array<Company>>([]);
  const [optionCompany, setOptionCompany] = useState<Array<Company>>([]);
  const [isShowToast, setIsShowToast] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [isUpdateSuccess, setIsUpdateSuccess] = useState<boolean>(false);

  //Set open to work
  useEffect(() => {
    setLoading(false);
  }, [statusWork]);

  useEffect(() => {
    getUserProfile().then((response) => {
      setUserProfile(response.data as Profile);
    });

    const handleAsyncChangeOTW = () =>
      setTimeout(() => setLoading(false), 1200);

    handleAsyncChangeOTW();

    return () => {
      clearTimeout(handleAsyncChangeOTW());
    };
  }, []);

  // Close droplist search company
  useEffect(() => {
    getBlockedCompaniesUser()
      .then(({ data }: { data: DataGetBlockedCompanies }) => {
        if (data?.error === false) {
          if (data?.data?.companies?.length > 0) {
            setListCompanyValue([...data?.data?.companies]);
          }

          if (data?.data?.emails?.length > 0) {
            setListDomainEmail([...data?.data?.emails]);
          }
        }
      })
      .catch((error) => {
        console.error(error);
      })
      .finally(() => setChanging(true));

    const handleCloseListsSelect = (event: any) => {
      if (
        listSelectCompany?.current &&
        !(listSelectCompany?.current as any).contains(event?.target)
      ) {
        setOptionCompany([]);
        setCompanyName("");
      }

      return;
    };
    document.addEventListener("click", handleCloseListsSelect);

    return () => {
      document.removeEventListener("click", handleCloseListsSelect);
    };
  }, []);

  //Change input and search company list
  const handleChangeCompanyList = (value: ChangeEvent<HTMLInputElement>) => {
    const companyNameTarget = value?.target?.value;

    setCompanyName(companyNameTarget);
    if (!companyNameTarget) {
      setOptionCompany([]);
      return;
    }

    searchKeywordCompany(companyNameTarget)
      .then(({ data }: { data: DataSearchCompanies }) => {
        if (data?.error === false) {
          const listIdsValue = listCompanyValue.map(
            (company: Company) => company.id,
          );

          const listNotIsset = data?.data.filter(
            (company: Company) => !listIdsValue.includes(company.id),
          );
          setOptionCompany([...listNotIsset]);
        }
      })
      .catch((error) => console.error(error));
  };

  //Choose company
  const handleChooseCompanyList = (value: Company) => {
    if (!value) {
      setListCompanyValue([]);
      return;
    }

    setListCompanyValue([...listCompanyValue, value]);
    setChanging(true);
    setOptionCompany([]);
    setCompanyName("");
  };

  //Remove company in company list
  const handleRemoveCompany = (companyId: number) => {
    const listCompaniesNew = listCompanyValue.filter(
      (company: Company) => company.id !== companyId,
    );
    setListCompanyValue([...listCompaniesNew]);
    setChanging(true);
  };

  //Change input email address
  const handleChangeCompanyEmail = (value: ChangeEvent<HTMLInputElement>) => {
    setDomainEmail(value?.target?.value);
    const regex = new RegExp(emailRegExp);
    if (!!value?.target?.value && !regex.test(value?.target?.value)) {
      setErrorsEmail({
        status: true,
        title: t("user_profile_validate_domain_email_invalid"),
      });
      return;
    }

    setErrorsEmail({
      status: false,
      title: "",
    });
  };

  //press button Enter input email address
  const handleKeyDownEmail = (event: any) => {
    if (errorsEmail.status === true) return;

    if (event.key === "Enter") {
      setDomainEmail("");
      setListDomainEmail([...listDomainEmail, event.target.value]);
      setChanging(true);
    }
  };

  //Remove email in list
  const handleRemoveEmail = (email: string) => {
    const listEmailsNew = listDomainEmail.filter(
      (value: string) => value !== email,
    );

    setListDomainEmail([...listEmailsNew]);
    setChanging(true);
  };

  //Set email and company into API
  useEffect(() => {
    if (!changing) return;

    setErrorsCompnay({
      status: false,
      title: "",
    });

    setErrorsEmail({
      status: false,
      title: "",
    });

    const listIdCompanies = listCompanyValue.map((company) => company.id);
    blockedCompaniesUser(listIdCompanies ?? [], listDomainEmail ?? []).catch(
      (error) => {
        if (error?.response?.data?.errors?.emails?.length > 0) {
          setErrorsEmail({
            status: true,
            title: error?.response?.data?.errors?.emails?.[0],
          });
        }
      },
    );

    setChanging(false);

    if (listCompanyValue.length >= LIMIT_CHOOSED_VALUES) {
      setErrorsCompnay({
        status: true,
        title: t("user_profile_maximum_company_names"),
      });
      setOptionCompany([]);
      setCompanyName("");
    }

    if (listDomainEmail.length >= LIMIT_CHOOSED_VALUES) {
      setErrorsEmail({
        status: true,
        title: t("user_profile_maximum_company_emails_allowed"),
      });

      setDomainEmail("");
    }
  }, [listCompanyValue, listDomainEmail, changing]);

  const handleChangeOTW = (checked: boolean) => {
    if (!!userProfile && userProfile.status === "new_star" && !statusWork) {
      setIsShowToast(true);
      return;
    }

    if (checked) {
      ToastNotification({
        icon: "success",
        timer: 7000,
        description: `<div>
        <p>${t.markup("user_profile_notification_open_to_work_successfully", {
          strong: (chunk) => `<strong>${chunk}</strong>`,
        })}</p>
        <p class="mt-2">${t.markup(
          "user_profile_notification_hide_status_from_employer",
          {
            link: (chunk) =>
              `<a href="#hide" class="text-blue-600">${chunk}</a>`,
            strong: (chunk) => `<strong>${chunk}</strong>`,
          },
        )}</p>
        </div>`,
      });
    }
    toggleOpenToWork({
      status_works: checked ? OPEN_TO_WORK : NOT_OPEN_TO_WORK,
    });
    setShowOpenToWork(checked);
  };

  const handleButtonClick = () => {
    handleChangeOTW(!showOpenToWork);
  };

  useEffect(() => {
    if (statusWork && !isUpdateSuccess) {
      setShowOpenToWork(statusWork);
      return;
    }

    if (!isUpdateSuccess) return;

    toggleOpenToWork({
      status_works: !!isUpdateSuccess ? OPEN_TO_WORK : NOT_OPEN_TO_WORK,
    })
      .then((response) => {
        if (!!response && response.data.error == false) setShowOpenToWork(true);
      })
      .catch((err) => {
        console.error(err);
      });
  }, [isUpdateSuccess, statusWork]);

  const completePercent = userProfile?.complete_percent ?? 0;

  const TempalateToast = () => {
    return (
      <Modal
        show={isShowToast}
        onClose={() => setIsShowToast(false)}
        size={"xl"}
      >
        <Modal.Body>
          <div className="flex items-start justify-between border-none rounded-t">
            <button
              onClick={() => setIsShowToast(false)}
              aria-label="Close"
              className="ml-auto inline-flex items-center justify-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-200 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
              type="button"
            >
              <HiOutlineXMark className="w-5 h-5" />
            </button>
          </div>
          <div className="inline-flex flex-col items-center justify-start w-full gap-6 pb-6 bg-white rounded shadow">
            <div className="flex flex-col items-center self-stretch justify-center gap-6 bg-white shrink grow basis-0">
              <Image
                width="190"
                height="146"
                loading="lazy"
                src="/v4/assets/images/user_profile/update-profile.svg"
                alt="image toast"
                className="flex h-[146px] w-[190px]"
              />
              <div className="flex flex-wrap justify-center gap-2 text-center">
                <h3 className="text-lg font-bold">
                  {t("user_profile_open_to_work_you_need_enter_infomation")}
                </h3>
                <div className="self-stretch text-base font-normal leading-tight tracking-tight text-center text-zinc-600">
                  {t.rich("user_profile_open_to_work_update_notice", {
                    b: (chunks) => <b>{chunks}</b>,
                  })}
                </div>
              </div>
            </div>
          </div>
          <div className="flex w-full flex-shrink-0 flex-grow-0 items-center justify-center gap-4 overflow-hidden border-b-0 border-l-0 border-r-0 border-[#dbdbdb] bg-white">
            <Button
              accent={"primary"}
              type={"button"}
              onClick={() => {
                setIsShowToast(false);
                setShowModal(true);
              }}
            >
              {t("user_profile_open_to_work_notice_btn_update_now")}
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    );
  };

  return (
    <div className={"scroll-smooth bg-gray-light pb-48"}>
      <NavUsers />
      <TempalateToast />
      {userProfile && (
        <TheBasicInformationFormModal
          openModal={showModal}
          onClose={() => setShowModal(false)}
          stateTypeProp={PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO}
          setIsUpdateSuccess={setIsUpdateSuccess}
        />
      )}
      <div className="md:container">
        <div className="grid items-start grid-cols-7 px-0 gap-7">
          <div className="flex flex-col col-span-7 gap-4 p-4 mx-5 bg-white rounded shadow-md md:mx-0 md:col-span-2">
            <div className="flex gap-4">
              <div className={"h-16 w-16 rounded-full"}>
                <Image
                  src={userProfile?.avatar_url ?? NoAvatar}
                  width={64}
                  height={64}
                  alt={"Avatar"}
                  className={"h-16 w-16 rounded-full"}
                />
              </div>
              <div className="">
                <span className="text-sm leading-5 text-gray-400">
                  {t("user_profile_welcome")},
                </span>
                <h3 className="font-semibold capitalize text-black mb-5">
                  {userProfile.display_name}
                </h3>
                {completePercent && <BagePercent complete_percent={completePercent} />}
              </div>
            </div>
            <div
              className={
                "flex flex-col gap-2 overflow-hidden rounded bg-gray-light p-4"
              }
            >
              <div
                className={"card-title flex items-center justify-between pb-1"}
              >
                <h2 className={"font-semibold"}>
                  {t("user_profile_open_to_work_toggle")}
                </h2>
                <div>
                  <ToggleSwitch
                    checked={showOpenToWork}
                    onChange={handleChangeOTW}
                    label={""}
                    disabled={loading}
                  />
                </div>
              </div>
              <NoteStatusUser complete_percent={completePercent}/>
            </div>
            {completePercent <= 10 && (
            <div className="item-update-profile">
              <button
                onClick={() => {
                  setIsShowToast(false);
                  setShowModal(true);
                }}
                className="button-update-profile"
              >
                {t("user_profile_update_profile")}
              </button>
            </div>
          )}
          </div>
          <div className="flex flex-col col-span-7 md:col-span-5">
            <SectionHideFromEmployers />
            <ListResumes />
            <div className="p-4 mt-4 bg-white rounded scroll-mt-28" id="hide">
              <h3 className="text-2xl font-semibold text-black capitalize">
                {t("user_profile_title_hidden_employer")}
              </h3>
              <div className="mt-4">
                <div className="p-4 bg-gray-100">
                  <label
                    htmlFor="compnay_name"
                    className="block mb-1 font-bold text-gray-500"
                  >
                    {t("user_profile_enter_a_company_name")}
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      id="compnay_name"
                      className={classNames(
                        listCompanyValue.length === LIMIT_CHOOSED_VALUES
                          ? "bg-gray-200 opacity-60"
                          : "bg-white",
                        "block w-full rounded-[4px] border border-gray-300 px-5 py-[15px] text-sm placeholder:text-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",
                      )}
                      placeholder="Applancer JSC."
                      onChange={handleChangeCompanyList}
                      value={companyName}
                      disabled={
                        listCompanyValue.length === LIMIT_CHOOSED_VALUES
                      }
                    />

                    {errorsCompnay.status && (
                      <div className={"mt-2 text-sm text-primary-300"}>
                        {errorsCompnay?.title}
                      </div>
                    )}

                    {!!optionCompany && optionCompany.length > 0 && (
                      <ul
                        ref={listSelectCompany}
                        className="absolute left-0 top-[60px] z-10 w-full cursor-pointer select-none rounded-sm bg-white py-1 shadow-sm"
                      >
                        {optionCompany.map(
                          (company: Company, index: number) => {
                            return (
                              <li
                                onClick={() => handleChooseCompanyList(company)}
                                key={index}
                                className="px-3 py-2 hover:bg-blue-200"
                              >
                                {company.display_name}
                              </li>
                            );
                          },
                        )}
                      </ul>
                    )}
                  </div>
                  {!!listCompanyValue && listCompanyValue.length > 0 && (
                    <ul className="flex flex-wrap gap-2 mt-2">
                      {listCompanyValue.map((company: Company) => {
                        return (
                          <li
                            key={company.id}
                            className="flex items-center px-2 py-1 text-sm text-gray-600 bg-gray-200 rounded"
                          >
                            {company.display_name}
                            <HiXMark
                              className="w-5 h-5 ml-1 cursor-pointer hover:text-primary"
                              onClick={() => handleRemoveCompany(company.id)}
                            />
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>

                <div className="p-4">
                  <label
                    htmlFor="compnay_email"
                    className="block mb-1 font-bold text-gray-500"
                  >
                    {t("user_profile_or_enter_compnay_email")}
                  </label>
                  <input
                    onChange={handleChangeCompanyEmail}
                    onKeyDown={handleKeyDownEmail}
                    type="text"
                    id="compnay_email"
                    className={classNames(
                      listDomainEmail.length === LIMIT_CHOOSED_VALUES
                        ? "bg-gray-200 opacity-60"
                        : "bg-white",
                      "block w-full rounded-[4px] border border-gray-300 px-5 py-[15px] text-sm placeholder:text-gray-300 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500",
                    )}
                    value={domainEmail}
                    placeholder="topdev.vn"
                    disabled={listDomainEmail.length === LIMIT_CHOOSED_VALUES}
                  />
                  {errorsEmail.status && (
                    <div className={"mt-2 text-sm text-primary-300"}>
                      {errorsEmail?.title}
                    </div>
                  )}

                  {!!listDomainEmail && listDomainEmail.length > 0 && (
                    <ul className="flex flex-wrap gap-2 mt-2">
                      {listDomainEmail.map((email: string, index: number) => {
                        return (
                          <li
                            key={`${email} - ${index}`}
                            className="flex items-center px-2 py-1 text-sm text-gray-600 bg-gray-200 rounded"
                          >
                            {email}
                            <HiXMark
                              className="w-5 h-5 ml-1 cursor-pointer hover:text-primary"
                              onClick={() => handleRemoveEmail(email)}
                            />
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>
              </div>
            </div>
            <CompaniesViewedCV onOpenToWork={() => handleChangeOTW(true)} />
          </div>
        </div>
      </div>
    </div>
  );
};
export default TheJobManagementPage;
