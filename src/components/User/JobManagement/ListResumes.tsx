"use clinet";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>UpTray } from "react-icons/hi2";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { useState, useEffect, ChangeEvent } from "react";
import dayjs from "dayjs";
import Image from "next/image";
import { useTranslations } from "next-intl";
import EmptyFile from "@/assets/images/icons/empty-file.svg";
import {
  getMainCVUserProfile,
  setMainCVUserProfile,
  getListCVUserProfile,
  getUserProfile,
  downloadUserProfileCv,
} from "@/services/userAPI";
import {
  DataGetCVUserProfileProps,
  DataListCVUserProfileProps,
  MainCVUserProfileProps,
  ResumeUserProfile,
  TOPDEV_CV_FILE_TYPE,
} from "@/types/userProfile";
import { classNames } from "@/utils";
import AlertChooseCV from "./AlertChooseCV";
import useUserProfile from "@/hooks/useUserProfile";
import RenderBagePercent from "@/components/User/Common/RenderPercent";
import { useAppSelector } from "@/store";
import RecentJobApplication from "@/components/User/Common/RecentJobApplication";
import { getNewestCandidate } from "@/services/jobAPI";
import { UserResumes } from "@/types/resume";
import { FileCvVaidateProps } from "@/components/DialogModal/ApplyJob";
import { useFormik } from "formik";
import { uploadFileApi } from "@/services/resumeAPI";
import BagdeNewUploadCV from "../Common/BagdeNewUploadCV";

type FormDataProps = {
  resume: UserResumes;
};

const ListResumes = () => {
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();
  const [isDownloading, setIsDownloading] = useState(false);
  const [isClient, setIsClient] = useState<Boolean>(false);
  const [loading, setLoading] = useState<Boolean>(false);
  const [listCV, setListCv] = useState<Array<ResumeUserProfile>>([]);
  const [recentJobApplicationId, setRecentJobApplicationId] =
    useState<number>();
  const eventData = useAppSelector((state) => state?.setting?.eventData);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const [showAlert, setShowAlert] = useState<{
    status: boolean;
    type: "info" | "success";
  }>({ status: false, type: "info" });

  const defineMainCv: MainCVUserProfileProps = {
    cv_id: null,
    cv_type: "topdev_cv",
    updated_at: null,
  };

  const [mainCV, setMainCV] = useState<MainCVUserProfileProps>(defineMainCv);
  const [mainCVNew, setMainCVNew] =
    useState<MainCVUserProfileProps>(defineMainCv);

  // Save Error file upload
  const [resumes, setResumes] = useState<Array<UserResumes>>([]);
  const [fileList, setFileList] = useState<File[]>([]);
  const [fileCvVaidate, setFileCvVaidate] = useState<FileCvVaidateProps | any>(
    [],
  );
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [newFileUpload, setNewFileUpload] = useState<boolean>(false);
  const [newFileUploadId, setNewFileUploadId] = useState<number>(); //Set main CV profile
  useEffect(() => {
    setIsClient(true);
    setLoading(true);

    getMainCVUserProfile()
      .then(({ data }: { data: DataGetCVUserProfileProps }) => {
        if (data?.error == false && data?.data) {
          setMainCV(data?.data);
        }
      })
      .catch((error) => console.error(error))
      .finally(() => setLoading(false));

    getListCVUserProfile()
      .then(({ data }: { data: DataListCVUserProfileProps }) => {
        if (data?.error == false && data?.data) {
          setListCv(data?.data);
          setUploadSuccess(false); // set the state to false after render data
        }
      })
      .catch((error) => console.error(error));
  }, [setListCv, fileList, uploadSuccess]);

  //Change main CV profile
  const handleChooseMainCv = (
    cv_id: number,
    cv_type: "topdev_cv" | "cv_builder" | "upload_cv",
  ) => {
    if (!cv_id || !cv_type) return;

    setLoading(true);
    setMainCVUserProfile(cv_id, cv_type)
      .then(({ data }: { data: DataGetCVUserProfileProps }) => {
        if (data?.error == false && data?.data) {
          setMainCV({ cv_id, cv_type });
        }
      })
      .catch((error) => console.error(error))
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    if (listCV.length === 0 || !mainCV) return;
    //Lấy main cv mới nhất
    const formatDateUnix = (date?: Date) => dayjs(date).unix();

    const getCVLatest = listCV.reduce(
      (accumulator: ResumeUserProfile, current: ResumeUserProfile) => {
        return formatDateUnix(accumulator.updated_at) >
          formatDateUnix(current.updated_at)
          ? accumulator
          : current;
      },
    );
    if (getCVLatest) {
      setNewFileUploadId(getCVLatest.id);
    } else {
    }
    //Chạy khi người dùng mới và Chưa có Main CV
    const checkNewUserNotMainCV =
      listCV.length === 1 && !listCV[0].selected && mainCV.cv_id === null;

    if (checkNewUserNotMainCV) {
      handleChooseMainCv(listCV[0]?.id as number, listCV[0]?.type);
      setShowAlert({ status: false, type: "info" });
      return;
    }

    //Chạy khi người dùng cũ và Chưa có Main CV
    const checkCvSelected = listCV.find(
      (resume: ResumeUserProfile) => !!resume.selected,
    );

    const checkOldUserNotMainCV =
      listCV.length > 1 && mainCV.cv_id === null && !checkCvSelected;
    if (checkOldUserNotMainCV) {
      handleChooseMainCv(getCVLatest.id as number, getCVLatest.type);
      setShowAlert({ status: false, type: "info" });
      return;
    }

    //Chạy khi người dùng cũ và đã có main cv, hiện thông báo để chọn cv mới nhất cho main cv
    if (
      !!getCVLatest &&
      getCVLatest.id === mainCV.cv_id &&
      formatDateUnix(getCVLatest.updated_at) <
        formatDateUnix(mainCV.updated_at as Date)
    ) {
      if (showAlert.status && showAlert.type === "success") {
        setShowAlert((pre) => pre);
        return;
      }
      setShowAlert({ status: false, type: "info" });
      return;
    }

    setShowAlert({ status: true, type: "info" });
    setMainCVNew({ cv_id: getCVLatest.id ?? 0, cv_type: getCVLatest.type });
  }, [mainCV, listCV]);

  // get percent complete of user profile
  const completePercent = userProfile?.complete_percent;
  // get ID of list cv
  const matchingCV = listCV.filter((cv) => cv.id === recentJobApplicationId);
  const matchingCVIds = matchingCV.map((cv) => cv.id);
  useEffect(() => {
    const fetchNewestCandidate = async () => {
      try {
        const response = await getNewestCandidate();
        const data = response.data.data.newest_candidate;
        if (
          data.collection_name === "files_topdev_cv" &&
          data.user_profile_id
        ) {
          setRecentJobApplicationId(data.user_profile_id);
        } else {
          setRecentJobApplicationId(data.media_id);
        }
      } catch (error) {
        console.error("Error fetching newest candidate:", error);
      }
    };
    if (isLoggedIn) {
      fetchNewestCandidate();
    }
  }, [isLoggedIn, eventData]);

  //handle submit
  const { setFieldValue, setSubmitting, handleSubmit } =
    useFormik<FormDataProps>({
      initialValues: {
        resume: {},
      },
      onSubmit: (values: FormDataProps) => {
        const formData = new FormData();
        formData.append("upload_from", "UploadFromCvBuilder");
        for (const file of fileList) {
          formData.append("files", file);
        }
        uploadFileApi(formData)
          .then(() => {
            // Set the state to true after successful upload
            setUploadSuccess(true);
            setNewFileUpload(true);
          })
          .catch(() => {
            setUploadSuccess(false);
            setNewFileUpload(false);
          });
      },
    });
  const removeUploadPrevious = (resumes: Array<UserResumes>) => {
    const newListResumes = resumes?.filter(
      (resume: UserResumes) => resume.type !== "upload",
    );
    setResumes(newListResumes);
    setFieldValue("resume", newListResumes?.[0]);
    setSubmitting(false);
  };
  /**
   * Upload file and check file
   * @param event
   * @returns
   */
  const handleChangeFile = (event: ChangeEvent<HTMLInputElement>) => {
    const file = (event.target as any).files[0];
    if (!file) return;
    const { type, size } = file;

    /**
     * Check file type
     */
    const fileType =
      type === "application/pdf" ||
      type ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
      type === "application/msword";

    if (!fileType) {
      setFieldValue("resume", []);
      setFileCvVaidate({
        ...fileCvVaidate,
        uploadError: true,
        message: t("detail_job_page_apply_validate_type_file_popup"),
        icon: "triangle",
      });
      removeUploadPrevious(resumes);
      return;
    }

    /**
     * 5MB file
     * Check file size
     */
    const fileSize = size / 1024 / 1024 < 5;
    if (!fileSize) {
      setFieldValue("resume", []);
      setFileCvVaidate({
        ...fileCvVaidate,
        uploadError: true,
        message: t("detail_job_page_apply_validate_type_file_popup"),
        icon: "triangle",
      });
      removeUploadPrevious(resumes);
      return;
    }

    if (fileSize && fileType) {
      setFileList((prevFileList) => [...prevFileList, file]);
      handleSubmit();
    }
    setFileCvVaidate({ ...fileCvVaidate, uploadError: false });
    // Hiden Alert Error
    const newListResumes = resumes?.filter(
      (resume: UserResumes) => resume.type !== "upload",
    );
    newListResumes.unshift({
      id: 0,
      name: file.name,
      type: "upload",
      created_at: dayjs().format("HH:mm DD/MM/YYYY"),
    });
    if (newListResumes.length === 0) return;
    setResumes(newListResumes);
    setFieldValue("resume", newListResumes?.[0]);
    setSubmitting(false);
  };

  // Cleanup effect to set newFileUpload to false when the component unmounts
  useEffect(() => {
    return () => {
      setNewFileUpload(false);
    };
  }, [eventData]);

  return (
    <>
      <div className="p-4 bg-white rounded">
        <h4 className="text-xl font-semibold capitalize text-neutral-950 md:text-2xl">
          {t("user_profile_your_main_cv")}
        </h4>
        <p className="text-sm font-normal text-neutral-500 md:text-base">
          {t("user_profile_the_cv_cound_be_seen")}
        </p>

        <div className="gap-4 mt-4 md:p-4">
          <span className="block pb-2 font-bold border-b text-neutral-900">
            {t("user_profile_current_resumes")}
          </span>
          {listCV.length > 0 ? (
            <div className="pt-4">
              <div
                className={classNames(
                  listCV.length > 4
                    ? "scroll-bar-resume-profile-user max-h-[332px] overflow-y-scroll pr-5"
                    : "",
                  "flex flex-col gap-2",
                )}
              >
                {listCV.map((resume: ResumeUserProfile, index: number) => {
                  if (resume.type === TOPDEV_CV_FILE_TYPE.TOPDEV_CV) {
                    return (
                      <div
                        className={classNames(
                          mainCV.cv_id === resume.id
                            ? "active !opacity-100"
                            : "",
                          loading ? "!cursor-default  opacity-60" : "",
                          "flex-column item-resumes relative flex cursor-pointer select-none items-center",
                        )}
                        onClick={() =>
                          !loading &&
                          handleChooseMainCv(resume.id as number, resume.type)
                        }
                        key={index}
                      >
                        <span
                          className={classNames(
                            loading ? "bg-gray-100 opacity-60" : "",
                            "dots-resume mx-2 inline-block h-4 w-4 rounded-full border border-gray-300",
                          )}
                        ></span>
                        <div className="justify-between p-4 bg-gray-100 info-resumes rounded-xl">
                          <div className="flex flex-wrap items-center font-semibold name-resume">
                            <HiUserCircle className="w-5 h-5 mr-2" />
                            <span className="mr-1">{resume.name}</span>
                            {!!resume && (
                              <RenderBagePercent
                                complete_percent={completePercent ?? 0}
                              />
                            )}
                            <span className="mt-2 md:ml-2 md:mt-0">
                              {Array.isArray(matchingCVIds) &&
                                matchingCVIds.includes(resume.id) && (
                                  <RecentJobApplication />
                                )}
                            </span>
                          </div>

                          <span className="text-xs text-gray-400">
                            {t("user_profile_last_update_at")}{" "}
                            {isClient &&
                              dayjs(resume.updated_at).format(
                                "HH:mm DD/MM/YYYY",
                              )}
                          </span>
                          <div
                            className="flex justify-start cursor-pointer hover:text-primary md:hidden"
                            onClick={async () => {
                              if (isDownloading) return;

                              setIsDownloading(true);
                              getUserProfile()
                                .then((response) => {
                                  downloadUserProfileCv(
                                    response.data,
                                    true,
                                  ).finally(() => setIsDownloading(false));
                                })
                                .catch(() => setIsDownloading(false));
                            }}
                          >
                            {isDownloading ? (
                              <AiOutlineLoading3Quarters className="text-base animate-spin" />
                            ) : (
                              <span className="text-sm font-semibold underline text-brand-600">
                                {t("job_management_view_my_topdev_cv")}
                              </span>
                            )}
                          </div>
                        </div>
                        <div
                          className="absolute z-10 justify-start hidden -translate-y-1/2 cursor-pointer hover:text-primary md:right-3 md:top-1/2 md:flex md:items-center md:justify-center"
                          onClick={async () => {
                            if (isDownloading) return;

                            setIsDownloading(true);
                            getUserProfile()
                              .then((response) => {
                                downloadUserProfileCv(
                                  response.data,
                                  true,
                                ).finally(() => setIsDownloading(false));
                              })
                              .catch(() => setIsDownloading(false));
                          }}
                        >
                          {isDownloading ? (
                            <AiOutlineLoading3Quarters className="text-base animate-spin" />
                          ) : (
                            <span className="text-sm font-semibold underline text-brand-600">
                              {t("job_management_view_my_topdev_cv")}
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  } else {
                    return (
                      <div
                        className={classNames(
                          mainCV.cv_id === resume.id
                            ? "active !cursor-default !opacity-100"
                            : "",
                          loading ? "!cursor-default  opacity-60" : "",
                          "flex-column item-resumes  relative flex cursor-pointer select-none items-center",
                        )}
                        onClick={() =>
                          !loading &&
                          handleChooseMainCv(resume.id as number, resume.type)
                        }
                        key={index}
                      >
                        <span
                          className={classNames(
                            loading ? "bg-gray-100 opacity-60" : "",
                            "dots-resume mx-2 inline-block h-4 w-4 rounded-full border border-gray-300",
                          )}
                        ></span>
                        <div className="p-4 bg-gray-100 info-resumes rounded-xl pr-9">
                          <div className="flex flex-wrap items-center font-semibold name-resume">
                            {resume.name}
                            <div className="md:ml-2">
                              {matchingCVIds.includes(resume.id) && (
                                <RecentJobApplication />
                              )}
                            </div>
                            {!!resume &&
                              resume.id === newFileUploadId &&
                              newFileUpload === true && (
                                <div className="md:ml-2">
                                  <BagdeNewUploadCV />
                                </div>
                              )}
                          </div>
                          <span className="text-xs text-gray-400">
                            {t("user_profile_last_update_at")}{" "}
                            {isClient &&
                              dayjs(resume.updated_at).format(
                                "HH:mm DD/MM/YYYY",
                              )}
                          </span>
                        </div>
                        <a
                          href={resume.url}
                          className="absolute z-10 flex items-center justify-center w-8 h-8 -translate-y-1/2 right-3 top-1/2 hover:text-primary"
                          target="_blank"
                        >
                          <HiOutlineEye className="text-base" />
                        </a>
                      </div>
                    );
                  }
                })}
              </div>
              <div className="mx-7 mt-4 flex h-12 w-80 flex-col items-center rounded bg-neutral-200 px-[29px] md:mx-8">
                <form className="flex flex-row items-center w-auto py-3 mx-auto">
                  <label
                    className="flex gap-3 text-base font-semibold rounded cursor-pointer w-80 text-neutral-900"
                    htmlFor="upload_new_cv"
                  >
                    <HiArrowUpTray size={20} />
                    <span>{t("job_management_view_upload_cv")}</span>
                  </label>
                  <input
                    onChange={handleChangeFile}
                    type="file"
                    id="upload_new_cv"
                    className="hidden px-4"
                  />
                </form>
              </div>
              <div className="px-6">
                {fileCvVaidate.uploadError &&
                  fileCvVaidate.icon != "circle" && (
                    <div className="flex w-full mt-2 text-sm text-primary">
                      <span className="mr-2 text-sm font-normal">(*)</span>

                      <span className="w-full max-w-xs text-sm font-bold md:w-auto md:max-w-full">
                        {fileCvVaidate.message}
                      </span>
                    </div>
                  )}
              </div>
            </div>
          ) : (
            <div className="flex flex-row items-center bg-gray-100">
              <Image
                src={EmptyFile}
                width={58}
                height={48}
                alt={"Avatar"}
                className={"h-[48px] w-[58px] rounded-full"}
              />
              <h3 className="text-lg font-bold text-black">
                {t("user_profile_content_cv_empty")}
              </h3>
            </div>
          )}
        </div>
      </div>

      <AlertChooseCV
        mainCV={mainCVNew}
        status={showAlert?.status ?? false}
        type={showAlert?.type ?? "info"}
        handleCloseAlert={(status, type) => setShowAlert({ status, type })}
        handleChooseMainCv={(mainCV) =>
          handleChooseMainCv(mainCV.cv_id as number, mainCV.cv_type)
        }
      />
    </>
  );
};

export default ListResumes;
