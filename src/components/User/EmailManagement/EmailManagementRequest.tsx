"use client";
import React, { FC, useEffect, useState, useCallback } from "react";
import { IEmail } from "@/types/email";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { updateEmailCategory } from "@/services/emaiManagementAPI";
import { useFormik } from "formik";
import { LocaleType } from "@/types/page";
import ToastNotification from "@/components/Swal/ToastNotification";

type EmailManagementRequestProps = {
  locale: LocaleType;
  emailCategories?: IEmail;
};

type UpdateSubscribeEmailForm = {
  ids: number;
};

const EmailManagementRequest: FC<EmailManagementRequestProps> = ({
  locale,
  emailCategories,
}) => {
  const t = useTranslations();
  const [checkedCategories, setCheckedCategories] = useState<{
    [key: string]: boolean;
  }>({});

  useEffect(() => {
    if (emailCategories) {
      const initialCheckedState = emailCategories.data.reduce(
        (acc, category) => {
          acc[category.id] = category.active;
          return acc;
        },
        {} as { [key: string]: boolean },
      );
      setCheckedCategories(initialCheckedState);
    }
  }, [emailCategories]);

  const handleCheckboxChange = useCallback((categoryId: number) => {
    setCheckedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId],
    }));
  }, []);

  const form = useFormik<UpdateSubscribeEmailForm[]>({
    initialValues: [],
    onSubmit: async () => {
      const uncheckedIds = Object.keys(checkedCategories)
        .filter((key) => !checkedCategories[key])
        .map((id) => parseInt(id));
      try {
        await updateEmailCategory(uncheckedIds, locale);
        ToastNotification({
          icon: "success",
          title: t("user_profile_email_subscribe_success_title"),
          description: t("user_profile_email_subscribe_success_description", {
            section: t("user_profile_email_subscribe_success_title"),
          }),
          timer: 2000,
        });
      } catch (error) {
        console.error("Error updating email categories: ", error);
      }
    },
  });

  return (
    <section className="container -mt-5 p-4 md:mt-0 md:p-0">
      <div className="w-full rounded bg-white p-4">
        <h4 className="text-xl font-semibold text-neutral-950 md:text-2xl">
          {t("user_profile_email_title")}
        </h4>
        <p className="mt-2 text-sm font-normal text-neutral-500 md:text-base">
          {t("user_profile_email_sub_title")}
        </p>
      </div>
      <div className="mt-4 w-full">
        <div className=" rounded-t bg-white p-4 pb-2">
          <h4 className="text-lg font-bold text-neutral-950">
            {t("user_profile_email_preferred_title")}
          </h4>
          <span className="text-sm font-normal text-neutral-500 md:text-base">
            {t("user_profile_email_preferred_sub_title")}
          </span>
        </div>
        <hr className="mx-4 w-auto" />

        <div
          className={classNames(
            "mt-0 flex flex-col gap-2 text-base transition-all",
          )}
        >
          <form onSubmit={form.handleSubmit}>
            <div className="rounded-b bg-white px-4 pb-6 pt-4">
              {emailCategories?.data.map((category, index) => (
                <div
                  key={category.id}
                  className={classNames(
                    `${index === 0 ? "mt-0" : "mt-6"}`,
                    "flex flex-row-reverse gap-2 px-2",
                  )}
                >
                  <label
                    htmlFor={`category-${category.id}`}
                    className={`w-full flex-nowrap overflow-x-auto text-base text-neutral-900 ${
                      checkedCategories[category.id]
                        ? "font-semibold"
                        : "font-normal"
                    }`}
                  >
                    {t(`user_profile_email_subscribe_${category.id}`)}
                  </label>
                  <input
                    id={`category-${category.id}`}
                    type="checkbox"
                    onChange={() => handleCheckboxChange(category.id)}
                    checked={checkedCategories[category.id] || false}
                    value={category.id}
                    className="h-5 w-5 rounded border border-neutral-300 checked:bg-primary checked:text-primary checked:accent-primary focus:ring-0"
                  />
                </div>
              ))}
            </div>
            <div className="btn-subscribe mt-4 px-4 md:px-0">
              <button
                type="submit"
                className="w-full rounded bg-brand-600 px-8 py-4 font-bold text-white md:w-auto"
              >
                {t("user_profile_email_subscribe")}
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  );
};

export default EmailManagementRequest;
