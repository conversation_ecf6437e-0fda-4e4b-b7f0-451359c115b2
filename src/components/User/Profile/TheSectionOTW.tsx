import React from "react";
import { useTranslations } from "next-intl";
import { HiChevronRight } from "react-icons/hi2";
import Link from "next/link";
import "@/assets/styles/pages/profile.scss";

const TheSectionOTW = () => {
  const t = useTranslations();

  return (
    <div className="grid mt-6 mb-0">
      <div className="flex flex-col col-span-7 gap-4 p-4 bg-center rounded shadow-md bg-otw-bg bg-full md:col-span-2">
        <span className="text-xs font-semibold uppercase text-neutral-900">
          {t("user_profile_otw_banner_title")}
        </span>
        <div className="flex flex-col gap-1 p-3 overflow-hidden bg-white rounded">
          <div className="flex items-center justify-between card-title"></div>
          <div className="text-sm text-gray-400 card-body">
            <div className="text-sm text-gray-500">
              {/* Check if either booster or standard profile status is true */}

              <div className="flex-wrap items-center text-base font-normal text-left">
                {t.rich("user_profile_section_otw_description", {
                  b: (chunk) => (
                    <b className="font-bold text-gray-600">{chunk}</b>
                  ),
                })}
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center">
          <Link
            target="_blank"
            href="job-management?src=topdev.vn&medium=bannerotw"
            className="text-sm font-semibold underline text-brand-600"
          >
            {t("user_profile_otw_now")}
          </Link>
          <span className="ml-2 text-sm font-semibold text-brand-600">
            <HiChevronRight
              className=""
              width={20}
              height={20}
              fontWeight={700}
            />
          </span>
        </div>
      </div>
    </div>
  );
};

export default TheSectionOTW;
