"use client";
import React, { FC, useEffect, useState } from "react";
import { GrDrag } from "react-icons/gr";
import { arrayMoveImmutable } from "array-move";
import { Modal, TextInput } from "flowbite-react";
import { HiOutlinePlus, HiPencil, HiTrash } from "react-icons/hi2";
import { useFormik } from "formik";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile } from "@/services/userAPI";
import TextEditor from "@/components/TextEditor";
import ToastNotification from "@/components/Swal/ToastNotification";
import { Button } from "@/components/Button";
import { useTranslations } from "next-intl";
import { CertificateSchema } from "@/schemas/UserProfileSchema";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { formatIfValid } from "@/utils/date";
import MonthPicker from "@/components/MonthPicker/MonthPicker";
import { ReactSortable, SortableEvent } from "react-sortablejs";
import { isMobile } from "react-device-detect";
interface Props {
  openModal: boolean;
  onClose?: () => void;
}

interface CertificateFormValue {
  name?: string;
  date_completed?: string;
  description?: string;
  is_editting?: boolean;
  is_new?: boolean;
  is_dirty?: boolean;
  id: number;
}

const TheCertificateFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();
  const [stateSortable, setStateSortable] = useState<CertificateFormValue[]>(
    [],
  );
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();
  const form = useFormik<CertificateFormValue[]>({
    initialValues: [],
    validationSchema: CertificateSchema(t),
    onSubmit: async (values) => {
      if (!userProfile) return;
      setIsSaving(true);
      const certificates = values.map((certificate) => {
        return {
          name: certificate.name,
          date_completed: certificate.date_completed,
          description: certificate.description,
          is_dirty: certificate.is_dirty ?? false,
        };
      });

      patchUserProfile({
        certificates: certificates,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_certificate_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);
          const editingIndex = form.values.findIndex(
            (value: CertificateFormValue) => value.is_editting == true,
          );
          form.setFieldValue(editingIndex + ".is_editting", false);
        })
        .finally(() => setIsSaving(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.certificates) return;

    form.resetForm({ values: [...userProfile.certificates] });
  }, [userProfile]);

  useEffect(() => {
    if (openModal) {
      if (!userProfile || !userProfile.certificates) return;
      form.resetForm({ values: [...userProfile.certificates] });
    }
  }, [openModal]);

  const handleAddNewCertificateBtnClick = () => {
    form.setValues([
      ...form.values,
      {
        id: 0,
        name: "",
        date_completed: "",
        description: "",
        is_editting: true,
        is_new: true,
        is_dirty: true,
      },
    ]);
  };

  const handleDeleteCertificateBtnClick = async (index: number) => {
    const certificates = [...form.values];
    if (certificates[index].is_new) {
      certificates.splice(index, 1);
      form.setValues(certificates);
      return;
    }
    setIsDeleting({ [index]: true });
    certificates.splice(index, 1);

    patchUserProfile({
      certificates: certificates,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_certificate_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleCancelCertificateBtnClick = async (index: number) => {
    const isNew = form.values[index].is_new ?? false;
    if (isNew) {
      const values = [...form.values];
      values.splice(index, 1);
      form.setValues(values);
    } else {
      form.setFieldValue(index + ".is_editting", false);
      form.setFieldValue(index + ".is_dirty", false);
    }

    form.setTouched({ [index]: { name: false } } as any);
  };

  const hasEditing = () =>
    form.values.filter(
      (value: CertificateFormValue) => value.is_editting == true,
    ).length > 0;

  useEffect(() => {
    setStateSortable([...form.values]);
  }, [form.values]);

  const handleChangeArray = (event: SortableEvent) => {
    setIsSaving(true);

    const newCertificates = arrayMoveImmutable(
      stateSortable,
      event?.newIndex as number,
      event?.oldIndex as number,
    );
    if (!!newCertificates && newCertificates.length === 0) return;

    patchUserProfile({
      certificates: newCertificates,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_certificate_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsSaving(false));
  };

  return (
    <Modal
      show={openModal}
      onClose={() => (onClose ? onClose() : "")}
      size={"3xl"}
    >
      <Modal.Header className={"bg-gray-light"}>
        <p className={"font-bold text-black"}>
          {t("user_profile_certificate_title")}
        </p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        <form
          className="space-y-6"
          onSubmit={form.handleSubmit}
          id={"certificate-form"}
        >
          <ReactSortable
            list={stateSortable.map((value) => ({ ...value }))}
            setList={setStateSortable}
            animation={100}
            onEnd={(evt) => handleChangeArray(evt)}
            disabled={
              isSaving || stateSortable.length < 2 || hasEditing() || isMobile
            }
            className="flex flex-col flex-wrap gap-4"
          >
            {stateSortable.map((certificate, index) => (
              <div key={index} className={"bg-gray-light p-4"}>
                {certificate.is_editting ? (
                  <div className={"grid grid-cols-2 gap-4"}>
                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_certificate_modal_field_name")}{" "}
                        <span className={"font-normal text-primary"}>*</span>
                      </label>
                      <TextInput
                        value={form.values[index]?.name ?? ""}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".name",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_certificate_modal_field_placeholder_name",
                        )}
                      />
                      {form.errors[index]?.name && form.touched[index]?.name ? (
                        <div className={"text-sm text-primary-300"}>
                          {form.errors[index]?.name}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"form-group col-span-2 md:col-span-1"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_certificate_modal_field_timeline")}
                      </label>
                      <MonthPicker
                        selected={String(certificate.date_completed)}
                        onChange={(value) => {
                          form.setFieldValue(index + ".date_completed", value);
                        }}
                        placeholder="MM-YYYY"
                        format="MM-YYYY"
                      />
                    </div>

                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_certificate_modal_field_description")}
                      </label>
                      <TextEditor
                        onChange={(value) =>
                          form.setFieldValue(index + ".description", value)
                        }
                        value={form.values[index]?.description ?? ""}
                        placeholder={t(
                          "user_profile_certificate_modal_field_placeholder_description",
                        )}
                      />
                    </div>
                    <div className={"form-group col-span-2 flex justify-end"}>
                      <Button
                        accent={"ghost"}
                        type={"button"}
                        onClick={() => handleCancelCertificateBtnClick(index)}
                      >
                        {t("user_profile_certificate_modal_btn_delete")}
                      </Button>
                      <Button
                        type={"button"}
                        form={"certificate-form"}
                        accent={"primary"}
                        trailingIcon={
                          isSaving ? (
                            <VscLoading className="h-6 w-6 animate-spin text-white" />
                          ) : (
                            ""
                          )
                        }
                        disabled={isSaving}
                        onClick={async () => {
                          const errors = await form.validateForm();
                          form.setTouched({ [index]: { name: true } } as any);
                          if (!Object.values(errors).length) {
                            form.submitForm();
                          }
                        }}
                      >
                        {t("user_profile_certificate_modal_btn_save_all")}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className={"flex"}>
                      {!isMobile && (
                        <div className="pr-3 pt-1">
                          <GrDrag />
                        </div>
                      )}

                      <div className={"grow"}>
                        <p className={"font-bold md:text-lg"}>
                          {certificate.name}
                        </p>
                        <p className="text-sm md:text-base">
                          {formatIfValid(certificate.date_completed ?? "")}
                        </p>
                      </div>
                      <div className={"flex-none"}>
                        <button
                          className={"h-5 w-5"}
                          type={"button"}
                          disabled={hasEditing()}
                          onClick={() => {
                            form.values.forEach(
                              (language: CertificateFormValue, index: number) =>
                                form.setFieldValue(
                                  index + ".is_editting",
                                  false,
                                ),
                            );
                            form.setFieldValue(index + ".is_editting", true);
                            form.setFieldValue(index + ".is_dirty", true);
                          }}
                        >
                          <HiPencil
                            className={hasEditing() ? "text-gray-200" : ""}
                          />
                        </button>

                        <button
                          className={"ml-4 h-5 w-5"}
                          type={"button"}
                          disabled={
                            (isDeleting && isDeleting[index] ? true : false) ||
                            hasEditing()
                          }
                          onClick={() => handleDeleteCertificateBtnClick(index)}
                        >
                          {isDeleting && isDeleting[index] ? (
                            <VscLoading className="h-4 w-4 animate-spin text-black" />
                          ) : (
                            <HiTrash
                              className={hasEditing() ? "text-gray-200" : ""}
                            />
                          )}
                        </button>
                      </div>
                    </div>
                    <div
                      className={"prose pl-5 text-gray-400"}
                      dangerouslySetInnerHTML={{
                        __html: certificate.description ?? "",
                      }}
                    ></div>
                  </>
                )}
              </div>
            ))}
          </ReactSortable>

          {!form.values ||
          !form.values.length ||
          !form.values.filter(
            (value: CertificateFormValue) => value.is_editting == true,
          ).length ? (
            <div className={"flex justify-center"}>
              <Button
                type={"button"}
                onClick={() => handleAddNewCertificateBtnClick()}
                leadingIcon={<HiOutlinePlus />}
                accent={isMobile ? "primary" : "outline"}
                isBlock={isMobile}
              >
                {t("user_profile_certificate_modal_btn_add")}
              </Button>
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default TheCertificateFormModal;
