import React, { useEffect, useState } from "react";
import { useProfilePopupContext } from "@/components/User/Profile/TheProfilePage";
import {
  IS_SHOW_CONVERT_CV_MODAL,
  PROFILE_CONTEXT_TYPE,
} from "@/contansts/userProfiles";
import { HiArrowDownTray, HiDocumentArrowUp } from "react-icons/hi2";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { VscLoading } from "react-icons/vsc";
import { downloadUserProfileCv } from "@/services/userAPI";
import useUserProfile from "@/hooks/useUserProfile";

const TheTopdevCVChoose = () => {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const profilePopupContext = useProfilePopupContext();
  const [userProfile] = useUserProfile();
  //State
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (
      searchParams.has(IS_SHOW_CONVERT_CV_MODAL) &&
      Number(searchParams.get(IS_SHOW_CONVERT_CV_MODAL)) === 1
    ) {
      profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV);
    }
  }, [searchParams]);

  return (
    <div className="flex grid-cols-3 flex-col items-center bg-white p-4 md:flex-row md:px-6 md:py-4">
      <div className="col-span-2 w-auto md:max-w-[539px]">
        <h4 className="text-2xl font-semibold text-neutral-950">
          {t("user_profile_topdev_cv_title")}
        </h4>
        <span className="mt-1 text-base font-normal text-neutral-400 md:mt-2">
          {t("user_profile_topdev_cv_description")}
        </span>
      </div>
      <div className="col-span-1 mt-4 flex items-center justify-center md:mx-auto md:mt-0">
        <div className="flex items-center  gap-2 md:gap-4">
          <button
            onClick={() =>
              profilePopupContext.dispatch(
                PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV,
              )
            }
            className="my-auto flex h-12 w-auto items-center gap-3 rounded-lg border border-brand-600 bg-brand-600 px-6 py-4 text-base font-bold text-white"
          >
            <HiDocumentArrowUp width={24} height={24} size={24} />
            {t("user_profile_basic_info_btn_import")}
          </button>
          <button
            type="button"
            disabled={isSaving}
            onClick={async () => {
              setIsSaving(true);
              downloadUserProfileCv(userProfile).finally(() =>
                setIsSaving(false),
              );
            }}
            className="my-auto flex h-12 items-center rounded-lg border border-neutral-200 bg-neutral-200 px-6 py-4 text-base font-bold text-neutral-900"
          >
            {isSaving ? (
              <VscLoading className="h-6 w-6 animate-spin text-white" />
            ) : (
              <div className="flex gap-3">
                <HiArrowDownTray
                  width={24}
                  height={24}
                  className="my-auto"
                  size={24}
                />
                {t("user_profile_basic_info_btn_save_as")}
              </div>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TheTopdevCVChoose;
