"use client";

import React, { FC, useState } from "react";
import { HiPencil } from "react-icons/hi2";
import { TheExtraInformationFormModal } from "@/components/User/Profile/index";

const TheExtraInformationSection: FC = () => {
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);

  return (
    <>
      <section
        id={"extra-information"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex items-center p-4"}>
          <div className={"grow"}>
            <h2 className={"text-2xl font-bold"}>Add more section</h2>
            <div className={"text-gray-400"}>
              Add more sections helps your CV more impressie
            </div>
          </div>
        </div>

        <div className={"card-body flex flex-col divide-y px-6"}>
          chuaw lafm
        </div>
      </section>

      <TheExtraInformationFormModal
        openModal={isFormModalOpen}
        onClose={() => setIsFormModalOpen(false)}
      />
    </>
  );
};

export default TheExtraInformationSection;
