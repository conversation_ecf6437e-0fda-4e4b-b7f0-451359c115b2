import { useTranslations } from "next-intl";
import { useState } from "react";

export const ReadMoreMobile = ({ children }: { children: string }) => {
  const [isReadMore, setIsReadMore] = useState(true);
  const t = useTranslations();
  const TEXT_LIMIT = 155;
  const text = children;
  const lengthText = !!text ? text.length : 0;
  if (!text && lengthText == 0) return <></>;

  const toggleReadMore = () => {
    setIsReadMore((pre) => !pre);
  };

  return (
    <div className="relative block md:hidden">
      <div
        className={`${
          isReadMore ? "line-clamp-2" : ""
        } prose text-sm leading-6 md:text-base`}
        dangerouslySetInnerHTML={{
          __html: text,
        }}
      ></div>
      {isReadMore && lengthText >= TEXT_LIMIT && (
        <span
          onClick={toggleReadMore}
          className="absolute bottom-1 right-0 z-10 cursor-pointer bg-white pl-2 text-xs font-semibold text-primary underline"
        >
          {isReadMore ? "..." + t("user_profile_read_more_experiences") : ""}
        </span>
      )}
    </div>
  );
};
