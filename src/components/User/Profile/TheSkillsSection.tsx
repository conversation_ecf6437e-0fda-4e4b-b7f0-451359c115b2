"use client";

import React, { FC, useEffect, useState } from "react";
import { HiPencil } from "react-icons/hi2";
import { TheSkillsFormModal } from "@/components/User/Profile/index";
import Image from "next/image";
import SummaryIllustrator from "@/assets/images/illustrators/skill.svg";
import { classNames } from "@/utils";
import useUserProfile from "@/hooks/useUserProfile";
import { useTranslations } from "next-intl";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";
import { isMobile } from "react-device-detect";

const TheSkillsSection: FC = () => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();
  const profilePopupContext = useProfilePopupContext();

  useEffect(() => {
    setIsFormModalOpen(
      profilePopupContext.state.type == PROFILE_CONTEXT_TYPE.POPUP_SKILLS,
    );
  }, [profilePopupContext]);

  const isSkillsExist =
    userProfile?.skills &&
    ((userProfile.skills.technical_skills &&
      userProfile.skills.technical_skills.length > 0) ||
      (userProfile.skills.soft_skills &&
        userProfile.skills.soft_skills.length > 0));

  return (
    <>
      <section
        id={"skills"}
        className={"divide-y divide-gray-200 rounded bg-white"}
        onClick={() => isMobile && setIsFormModalOpen(true)}
      >
        <div className={"card-title flex p-4 md:p-6"}>
          <div
            className={classNames(
              isSkillsExist
                ? "grow"
                : "mr-2 w-[calc(100%_-_85px)] md:mr-0 md:w-auto md:grow",
            )}
          >
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_skill_title")}
            </h2>
            <div
              className={classNames(
                isSkillsExist ? "hidden md:block" : "",
                "text-sm text-gray-500 md:text-base",
              )}
            >
              {t("user_profile_skill_description")}
            </div>
          </div>
          <div
            className={classNames(
              isSkillsExist
                ? "hidden"
                : "flex w-[73px] items-center md:w-auto md:flex-none",
            )}
          >
            <Image
              src={SummaryIllustrator}
              alt={"Summary"}
              height={130}
              className={"h-[65px] w-[74px] md:mx-10 md:h-[130px] md:w-[147px]"}
            />
          </div>
          <div className={classNames(
              userProfile?.summary ? "md:items-start items-center" : "",
              "flex flex-none md:block",
            )}>
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>
          </div>
        </div>
        <div
          className={classNames(
            isSkillsExist ? "flex" : "hidden",
            "card-body flex-col gap-4 md:p-6 py-4 px-6",
          )}
        >
          <div>
            <div className={"font-bold md:text-base text-sm"}>
              {t("user_profile_skill_technical_skill_section")}
            </div>
            <div className={"mt-2 flex flex-wrap gap-2 bg-gray-light p-2"}>
              {userProfile &&
              userProfile.skills &&
              userProfile.skills.technical_skills
                ? userProfile.skills.technical_skills.map((item) => (
                    <a
                      key={item.skill_id}
                      href="#"
                      className={
                        "rounded border border-gray-200 bg-white px-2 py-1 text-sm"
                      }
                    >
                      {item.skill_name}
                    </a>
                  ))
                : ""}
            </div>
          </div>

          {userProfile &&
          userProfile.skills &&
          userProfile.skills.soft_skills &&
          userProfile.skills.soft_skills.length ? (
            <div>
              <div className={"font-bold md:text-base text-sm"}>
                {t("user_profile_skill_soft_skill_section")}
              </div>
              <div className={"mt-2 flex flex-wrap gap-2 bg-gray-light p-2"}>
                {userProfile.skills.soft_skills.map((item, index) => (
                  <a
                    key={index}
                    href="#"
                    className={
                      "rounded border border-gray-200 bg-white px-2 py-1 text-sm"
                    }
                  >
                    {item}
                  </a>
                ))}
              </div>
            </div>
          ) : (
            ""
          )}
        </div>
      </section>

      {userProfile ? (
        <TheSkillsFormModal
          openModal={isFormModalOpen}
          onClose={() => {
            setIsFormModalOpen(false);
            profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED);
          }}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheSkillsSection;
