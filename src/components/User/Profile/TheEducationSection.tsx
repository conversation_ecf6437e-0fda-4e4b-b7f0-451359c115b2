"use client";

import React, { FC, useEffect, useState } from "react";
import { HiPencil } from "react-icons/hi2";
import { TheEducationFormModal } from "@/components/User/Profile/index";
import Image from "next/image";
import SummaryIllustrator from "@/assets/images/illustrators/education.svg";
import useUserProfile from "@/hooks/useUserProfile";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { formatIfValid } from "@/utils/date";
import { getIfNotEmpty } from "@/utils/string";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";
import { ReadMoreMobile } from "@/components/User/Profile/ReadMoreMobile";

const TheEducationSection: FC = () => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();
  const profilePopupContext = useProfilePopupContext();

  useEffect(() => {
    setIsFormModalOpen(
      profilePopupContext.state.type == PROFILE_CONTEXT_TYPE.POPUP_EDUCATIONS,
    );
  }, [profilePopupContext]);

  const isEducationsExits =
    userProfile?.educations && Array.isArray(userProfile.educations) && userProfile.educations.length > 0;

  return (
    <>
      <section
        id={"education"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex p-4 md:p-6"}>
          <div
            className={classNames(
              isEducationsExits
                ? "grow"
                : "mr-2 w-[calc(100%_-_85px)] md:mr-0 md:w-auto md:grow",
            )}
          >
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_education_title")}
            </h2>
            <div
              className={classNames(
                isEducationsExits ? "hidden md:block" : "",
                "text-sm text-gray-400 md:text-base",
              )}
            >
              {t("user_profile_education_description")}
            </div>
          </div>
          <div
            className={classNames(
              isEducationsExits
                ? "hidden"
                : "flex w-[73px] items-center md:w-auto md:flex-none",
            )}
          >
            <Image
              src={SummaryIllustrator}
              alt={"Summary"}
              height={130}
              className={"h-[65px] w-[74px] md:mx-10 md:h-[130px] md:w-[147px]"}
            />
          </div>
          <div
            className={classNames(
              isEducationsExits ? "items-center md:items-start" : "",
              "flex flex-none md:block",
            )}
          >
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>
          </div>
        </div>
        <div
          className={classNames(
            "card-body flex-col divide-y md:px-6 px-4",
            isEducationsExits ? "flex" : "hidden",
          )}
        >
          {userProfile && userProfile.educations && isEducationsExits
            ? userProfile.educations.map((item, index) => (
                <div key={index} className={"flex gap-6 md:py-6 py-4"}>
                  <div className={"hidden w-44 flex-none md:block"}>
                    {formatIfValid(item.from)}
                    {getIfNotEmpty(" - ", [item.from, item.to])}
                    {item.is_studying_here
                      ? t("user_profile_basic_info_date_present_text")
                      : formatIfValid(item.to)}
                  </div>
                  <div className="md:w-auto w-full">
                    <div className={"font-bold"}>{item.school_name}</div>
                    <div className={"block md:hidden text-sm"}>
                      {formatIfValid(item.from)}
                      {getIfNotEmpty(" - ", [item.from, item.to])}
                      {item.is_studying_here
                        ? t("user_profile_basic_info_date_present_text")
                        : formatIfValid(item.to)}
                    </div>
                    <div className="my-1">{item.degree}</div>
                    <div
                      className="prose leading-6 hidden md:block"
                      dangerouslySetInnerHTML={{ __html: item.description }}
                    ></div>
                    <ReadMoreMobile>{item.description}</ReadMoreMobile>
                  </div>
                </div>
              ))
            : ""}
        </div>
      </section>

      {userProfile ? (
        <TheEducationFormModal
          openModal={isFormModalOpen}
          onClose={() => {
            setIsFormModalOpen(false);
            profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED);
          }}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheEducationSection;
