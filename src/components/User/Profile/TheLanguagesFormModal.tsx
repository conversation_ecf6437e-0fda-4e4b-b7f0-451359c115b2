"use client";
import React, { FC, useEffect, useState } from "react";
import { Modal } from "flowbite-react";
import { HiOutlinePlus, HiPencil, HiTrash } from "react-icons/hi2";
import { useFormik } from "formik";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile } from "@/services/userAPI";
import Select from "react-select";
import ToastNotification from "@/components/Swal/ToastNotification";
import { Button } from "@/components/Button";
import { useTranslations } from "next-intl";
import { LanguageSchema } from "@/schemas/UserProfileSchema";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { isMobile } from "react-device-detect";
interface Props {
  openModal: boolean;
  onClose?: () => void;
}

interface LanguageFormValue {
  language?: string;
  fluent?: string;
  is_editting?: boolean;
  is_new?: boolean;
  is_dirty?: boolean;
}

const TheLanagugesFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();

  const LANG_OPTIONS_LEVEL = [
    {
      value: "Beginner",
      label: t("user_profile_language_modal_field_level_option_beginer"),
    },
    {
      value: "Intermediate",
      label: t("user_profile_language_modal_field_level_option_intermediate"),
    },
    {
      value: "Proficient",
      label: t("user_profile_language_modal_field_level_option_proficient"),
    },
    {
      value: "Fluent",
      label: t("user_profile_language_modal_field_level_option_fluent"),
    },
    {
      value: "Native",
      label: t("user_profile_language_modal_field_level_option_native"),
    },
  ];

  const LANG_OPTIONS = [
    {
      value: "English",
      label: t("user_profile_language_modal_field_language_option_english"),
    },
    {
      value: "Japan",
      label: t("user_profile_language_modal_field_language_option_japan"),
    },
    {
      value: "Chinese",
      label: t("user_profile_language_modal_field_language_option_chinese"),
    },
    {
      value: "Korean",
      label: t("user_profile_language_modal_field_language_option_korea"),
    },
    {
      value: "French",
      label: t("user_profile_language_modal_field_language_option_french"),
    },
    {
      value: "German",
      label: t("user_profile_language_modal_field_language_option_german"),
    },
  ];

  const form = useFormik<LanguageFormValue[]>({
    initialValues: [],
    validationSchema: LanguageSchema(t),
    onSubmit: async (values) => {
      if (!userProfile || !userProfile.languages) return;
      setIsSaving(true);
      const languages = values.map((language) => {
        return {
          language: language.language,
          fluent: language.fluent,
          is_dirty: language.is_dirty ?? false,
        };
      });

      patchUserProfile({
        languages: languages,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_language_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);
          const editingIndex = form.values.findIndex(
            (value: LanguageFormValue) => value.is_editting == true,
          );
          form.setFieldValue(editingIndex + ".is_editting", false);
        })
        .finally(() => setIsSaving(false));
    },
  });
  useEffect(() => {
    if (!userProfile || !userProfile.languages) return;
    form.resetForm({ values: [...userProfile.languages] });
  }, [userProfile]);

  useEffect(() => {
    if (!userProfile || !userProfile.languages) return;
    if (openModal) {
      form.resetForm({ values: [...userProfile.languages] });
    }
  }, [openModal]);

  const handleAddNewLanguageBtnClick = () => {
    form.setValues([
      ...form.values,
      { is_editting: true, is_new: true, is_dirty: true },
    ]);
  };

  const handleDeleteLanguageBtnClick = async (index: number) => {
    setIsDeleting({ [index]: true });
    const languages = [...form.values];
    languages.splice(index, 1);

    patchUserProfile({
      languages: languages,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_language_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleCancelReferenceBtnClick = async (index: number) => {
    const isNew = form.values[index].is_new ?? false;
    if (isNew) {
      const values = [...form.values];
      values.splice(index, 1);
      form.setValues(values);
    } else {
      form.setFieldValue(index + ".is_editting", false);
      form.setFieldValue(index + ".is_dirty", false);
    }

    form.setTouched({ [index]: { language: false } } as any);
  };

  const hasEditing = () =>
    form.values.filter((value: LanguageFormValue) => value.is_editting == true)
      .length > 0;

  const converValueLanguage = (value: string) =>
    LANG_OPTIONS.filter((lang) => lang.value == value)?.[0]?.label;
  const converValueFluent = (value: string) =>
    LANG_OPTIONS_LEVEL.filter((lang) => lang.value == value)?.[0]?.label;

  return (
    <Modal
      show={openModal}
      onClose={() => (onClose ? onClose() : "")}
      size={"3xl"}
    >
      <Modal.Header className={"bg-gray-light"}>
        <p className={"font-bold text-black"}>
          {t("user_profile_language_title")}
        </p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        <form
          className="space-y-4"
          onSubmit={form.handleSubmit}
          id={"the-language-form"}
        >
          {form.values.map((language, index) => (
            <div key={index} className={"rounded bg-gray-light !md:mb-0 !mb-3 p-4"}>
              {language.is_editting ? (
                <div className={"grid grid-cols-2 gap-3 md:gap-5"}>
                  <div className={"form-group col-span-2 md:col-span-1"}>
                    <label
                      htmlFor=""
                      className={"text-sm font-bold text-gray-500"}
                    >
                      {t("user_profile_language_modal_field_language")}{" "}
                      <span className={"font-normal text-primary"}>*</span>
                    </label>
                    <Select
                      value={{
                        value: language.language,
                        label: converValueLanguage(language.language as string),
                      }}
                      classNamePrefix="select"
                      options={LANG_OPTIONS}
                      onChange={(value) => {
                        form.setFieldValue(index + ".language", value?.value);
                      }}
                      maxMenuHeight={80}
                      placeholder={t(
                        "user_profile_language_modal_field_placeholder_language",
                      )}
                    />
                    {form.errors[index]?.language &&
                    form.touched[index]?.language ? (
                      <div className={"text-sm text-primary-300"}>
                        {form.errors[index]?.language}
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                  <div className={"form-group col-span-2 md:col-span-1"}>
                    <label
                      htmlFor=""
                      className={"text-sm font-bold text-gray-500"}
                    >
                      {t("user_profile_language_modal_field_level")}{" "}
                    </label>
                    <Select
                      value={{
                        value: language.fluent,
                        label: converValueFluent(language.fluent as string),
                      }}
                      classNamePrefix="select"
                      options={LANG_OPTIONS_LEVEL}
                      onChange={(value) => {
                        form.setFieldValue(index + ".fluent", value?.value);
                      }}
                      maxMenuHeight={80}
                      placeholder={t(
                        "user_profile_language_modal_field_placeholder_fluent",
                      )}
                    />
                  </div>
                  <div className={"col-span-2 flex justify-end"}>
                    <Button
                      accent={"ghost"}
                      type={"button"}
                      onClick={() => handleCancelReferenceBtnClick(index)}
                    >
                      {t("user_profile_language_modal_btn_remove")}
                    </Button>
                    <Button
                      type={"button"}
                      form={"the-language-form"}
                      accent={"primary"}
                      onClick={async () => {
                        const errors = await form.validateForm();
                        form.setTouched({ [index]: { language: true } } as any);
                        if (!Object.values(errors).length) {
                          form.submitForm();
                        }
                      }}
                      disabled={isSaving}
                      trailingIcon={
                        isSaving ? (
                          <VscLoading className="h-6 w-6 animate-spin text-white" />
                        ) : (
                          ""
                        )
                      }
                    >
                      {t("user_profile_language_modal_btn_save")}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className={"flex items-center"}>
                  <div className={"grow"}>
                    <p className={"font-bold"}>
                      {converValueLanguage(language.language as string)}
                    </p>
                    <p className={""}>
                      {converValueFluent(language.fluent as string)}
                    </p>
                  </div>
                  <div className={"flex-none"}>
                    <button
                      className={"h-5 w-5"}
                      type={"button"}
                      disabled={hasEditing()}
                      onClick={() => {
                        form.values.forEach(
                          (language: LanguageFormValue, index: number) =>
                            form.setFieldValue(index + ".is_editting", false),
                        );
                        form.setFieldValue(index + ".is_editting", true);
                        form.setFieldValue(index + ".is_dirty", true);
                      }}
                    >
                      <HiPencil
                        className={hasEditing() ? "text-gray-200" : ""}
                      />
                    </button>

                    <button
                      className={"ml-4 h-5 w-5"}
                      type={"button"}
                      onClick={() => handleDeleteLanguageBtnClick(index)}
                      disabled={
                        (isDeleting && isDeleting[index] ? true : false) ||
                        hasEditing()
                      }
                    >
                      {isDeleting && isDeleting[index] ? (
                        <VscLoading className="h-4 w-4 animate-spin text-black" />
                      ) : (
                        <HiTrash
                          className={hasEditing() ? "text-gray-200" : ""}
                        />
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
          {!form.values ||
          !form.values.length ||
          !form.values.filter(
            (value: LanguageFormValue) => value.is_editting == true,
          ).length ? (
            <div className={"flex justify-center"}>
              <Button
                type={"button"}
                onClick={() => handleAddNewLanguageBtnClick()}
                leadingIcon={<HiOutlinePlus />}
                accent={isMobile ? "primary" : "outline"}
                isBlock={isMobile}
              >
                {t("user_profile_language_modal_btn_add")}
              </Button>
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default TheLanagugesFormModal;
