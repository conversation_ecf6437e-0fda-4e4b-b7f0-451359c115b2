"use client";
import React, { FC, useEffect, useState } from "react";
import { Modal, TextInput } from "flowbite-react";
import { HiOutlinePlus, HiPencil, HiTrash } from "react-icons/hi2";
import { ReactSortable, SortableEvent } from "react-sortablejs";
import { arrayMoveImmutable } from "array-move";
import { isMobile } from "react-device-detect";
import TextEditor from "@/components/TextEditor";
import { GrDrag } from "react-icons/gr";
import { useFormik } from "formik";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile } from "@/services/userAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { Button } from "@/components/Button";
import { ProjectSchema } from "@/schemas/UserProfileSchema";
import { useTranslations } from "next-intl";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";

interface Props {
  openModal: boolean;
  onClose: () => void;
}

interface ProjectFormValue {
  id: number;
  project_name?: string;
  project_time?: string;
  position?: string;
  description?: string;
  is_new?: boolean;
  is_editting?: boolean;
  is_dirty?: boolean;
}

const TheProjectFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const profilePopupContext = useProfilePopupContext();
  const [stateSortable, setStateSortable] = useState<ProjectFormValue[]>([]);
  const [userProfile, setUserProfile] = useUserProfile();
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();
  const form = useFormik<ProjectFormValue[]>({
    initialValues: [],
    validationSchema: ProjectSchema(t),
    onSubmit: async (values) => {
      if (!userProfile || !userProfile.projects) return;
      setIsSaving(true);
      const projects = values.map((project) => {
        return {
          id: 0,
          project_name: project.project_name,
          project_time: project.project_time,
          position: project.position,
          description: project.description,
          is_dirty: project.is_dirty ?? false,
        };
      });

      patchUserProfile({
        projects: projects,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_project_title"),
            }),
            timer: 2000,
          });

          profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_PROJECTS);
          setUserProfile(response.data.data);
          const editingIndex = form.values.findIndex(
            (value: ProjectFormValue) => value.is_editting == true,
          );
          form.setFieldValue(editingIndex + ".is_editting", false);
        })
        .finally(() => setIsSaving(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.projects) return;
    form.resetForm({ values: [...userProfile.projects] });
  }, [userProfile]);

  useEffect(() => {
    if (!userProfile || !userProfile.projects) return;
    if (openModal) {
      form.resetForm({ values: [...userProfile.projects] });
    }
  }, [openModal]);

  const handleBtnDeleteClick = async (index: number) => {
    setIsDeleting({ [index]: true });
    const projects = [...form.values];
    projects.splice(index, 1);

    patchUserProfile({ projects })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_project_title"),
          }),
          timer: 2000,
        });

        profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_PROJECTS);
        setUserProfile(response.data.data);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleBtnAddProjectClick = () => {
    form.setValues([
      ...form.values,
      {
        id: 0,
        project_name: "",
        project_time: "",
        position: "",
        description: "",
        is_new: true,
        is_editting: true,
        is_dirty: true,
      },
    ]);
  };

  const handleCancelReferenceBtnClick = async (index: number) => {
    const isNew = form.values[index]?.is_new ?? false;
    if (isNew) {
      const values = [...form.values];
      values.splice(index, 1);
      form.setValues(values);
    } else {
      form.setFieldValue(index + ".is_editting", false);
      form.setFieldValue(index + ".is_dirty", false);
    }
    form.setTouched({ [index]: { project_name: false } } as any);
  };

  const hasEditing = () =>
    form.values.filter((value: ProjectFormValue) => value.is_editting == true)
      .length > 0;

  useEffect(() => {
    setStateSortable(form.values);
  }, [form.values]);

  const handleChangeArray = (event: SortableEvent) => {
    setIsSaving(true);

    const newProjects = arrayMoveImmutable(
      stateSortable,
      event?.newIndex as number,
      event?.oldIndex as number,
    );
    if (!!newProjects && newProjects.length === 0) return;

    patchUserProfile({
      projects: newProjects,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_project_title"),
          }),
          timer: 2000,
        });

        profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_PROJECTS);
        setUserProfile(response.data.data);
      })
      .finally(() => setIsSaving(false));
  };

  return (
    <Modal show={openModal} onClose={() => onClose()} size={"3xl"} className="custom-modal-profile">
      <Modal.Header className={"bg-gray-light"}>
        <p className={"text-black"}>{t("user_profile_project_title")}</p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        <form
          className="space-y-4"
          onSubmit={form.handleSubmit}
          id={"project-form"}
        >
          <ReactSortable
            list={stateSortable.map((value) => ({ ...value }))}
            setList={setStateSortable}
            animation={100}
            onEnd={(evt) => handleChangeArray(evt)}
            disabled={
              isSaving || stateSortable.length < 2 || hasEditing() || isMobile
            }
            className="flex flex-col flex-wrap gap-4"
          >
            {stateSortable.map((project, index) => (
              <div key={index} className={"rounded bg-gray-light px-4 py-2"}>
                {project.is_editting ? (
                  <div className={"grid grid-cols-2 gap-6"}>
                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_project_modal_field_name")}{" "}
                        <span className={"font-normal text-primary"}>*</span>
                      </label>
                      <TextInput
                        value={form.values[index]?.project_name ?? ""}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".project_name",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_project_modal_field_placeholder_name",
                        )}
                      />
                      {form.errors[index]?.project_name &&
                      form.touched[index]?.project_name ? (
                        <div className={"text-sm text-primary-300"}>
                          {form.errors[index]?.project_name}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"form-group"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_project_modal_field_position")}
                      </label>
                      <TextInput
                        value={form.values[index]?.position ?? ""}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".position",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_project_modal_field_placeholder_position",
                        )}
                        height={250}
                      />
                      {form.errors[index]?.position &&
                      form.touched[index]?.project_name ? (
                        <div className={"text-sm text-primary-300"}>
                          {form.errors[index]?.position}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"form-group"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_project_modal_field_timeline")}{" "}
                      </label>
                      <TextInput
                        value={form.values[index]?.project_time ?? ""}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".project_time",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_project_modal_field_placeholder_timeline",
                        )}
                      />
                      {form.errors[index]?.project_time &&
                      form.touched[index]?.project_name ? (
                        <div className={"text-sm text-primary-300"}>
                          {form.errors[index]?.project_time}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_project_modal_field_description")}
                      </label>
                      <TextEditor
                        onChange={(newValue) =>
                          form.setFieldValue(index + ".description", newValue)
                        }
                        value={form.values[index]?.description ?? ""}
                        placeholder={t(
                          "user_profile_project_modal_field_placeholder_description",
                        )}
                        height={250}
                      />
                      {form.errors[index]?.description &&
                      form.touched[index]?.project_name ? (
                        <div className={"text-sm text-primary-300"}>
                          {form.errors[index]?.description}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"col-span-2 flex justify-end"}>
                      <Button
                        accent={"ghost"}
                        onClick={() => handleCancelReferenceBtnClick(index)}
                      >
                        {t("user_profile_project_modal_btn_delete")}
                      </Button>

                      <Button
                        accent={"primary"}
                        type={"submit"}
                        form={"project-form"}
                        disabled={isSaving}
                        trailingIcon={
                          isSaving ? (
                            <VscLoading className="h-6 w-6 animate-spin text-white" />
                          ) : (
                            ""
                          )
                        }
                      >
                        {t("user_profile_project_modal_btn_save")}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className={"space-y-4"}>
                    <div key={index} className={"flex items-center gap-2"}>
                      {!isMobile && (
                        <div>
                          <GrDrag />
                        </div>
                      )}

                      <div
                        className={
                          "flex grow bg-gray-100 md:px-4 py-4"
                        }
                      >
                        <div className={"grow"}>
                          <p className={"font-bold"}>{project.project_name}</p>
                          <p className={"text-sm text-primary md:text-base"}>
                            {project.position}
                          </p>
                          <p className={"text-sm text-gray-500 md:text-base"}>
                            {project.project_time}
                          </p>
                        </div>
                        <div className={"flex items-center"}>
                          <button
                            className={
                              "flex h-[40px] w-[40px] items-center justify-center md:block"
                            }
                            type={"button"}
                            disabled={hasEditing()}
                            onClick={() => {
                              form.setFieldValue(index + ".is_editting", true);
                              form.setFieldValue(index + ".is_dirty", true);
                            }}
                          >
                            <HiPencil
                              className={hasEditing() ? "text-gray-200" : ""}
                            />
                          </button>
                          <button
                            type={"button"}
                            className={
                              "flex h-[40px] w-[40px] items-center justify-center md:block"
                            }
                            disabled={hasEditing()}
                            onClick={() => handleBtnDeleteClick(index)}
                          >
                            {isDeleting && isDeleting[index] ? (
                              <VscLoading className="h-4 w-4 animate-spin text-black" />
                            ) : (
                              <HiTrash
                                className={hasEditing() ? "text-gray-200" : ""}
                              />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </ReactSortable>

          {!hasEditing() ? (
            <div className={"flex justify-center"}>
              <Button
                type={"button"}
                onClick={() => handleBtnAddProjectClick()}
                leadingIcon={<HiOutlinePlus />}
                accent={isMobile ? "primary" : "outline"}
                isBlock={isMobile}
              >
                {t("user_profile_project_modal_btn_add_new")}
              </Button>
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default TheProjectFormModal;
