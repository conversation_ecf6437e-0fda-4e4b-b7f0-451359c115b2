"use client";

import React, { FC, useState } from "react";
import { HiPencil, HiTrash } from "react-icons/hi2";
import useUserProfile from "@/hooks/useUserProfile";
import TheHobbyFormModal from "@/components/User/Profile/TheHobbyFormModal";
import { useTranslations } from "next-intl";
import { isMobile } from "react-device-detect";

const TheHobbySection: FC<{
  onDelete: () => void;
}> = ({ onDelete }) => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();

  return (
    <>
      <section
        id={"extra-information"}
        className={"divide-y divide-gray-200 rounded bg-white"}
        onClick={() => isMobile && setIsFormModalOpen(true)}
      >
        <div className={"card-title flex items-center p-4"}>
          <div className={"grow"}>
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_hobby_title")}
            </h2>
            <div className={"text-gray-400"}></div>
          </div>
          <div className={"flex-none"}>
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>

            <button
              className={"ml-4 h-5 w-5"}
              type={"button"}
              onClick={() => onDelete()}
            >
              <HiTrash />
            </button>
          </div>
        </div>

        {Number(userProfile.interests?.length) > 0 && (
          <div className={"card-body flex flex-col divide-y p-4 md:p-6"}>
            <div
              className="prose"
              dangerouslySetInnerHTML={{
                __html:
                  userProfile.interests && userProfile.interests[0]
                    ? userProfile.interests[0]
                    : "",
              }}
            ></div>
          </div>
        )}
      </section>

      <TheHobbyFormModal
        openModal={isFormModalOpen}
        onClose={() => setIsFormModalOpen(false)}
      />
    </>
  );
};

export default TheHobbySection;
