"use client";
import { FC, useEffect, useState } from "react";
import { Modal } from "flowbite-react";
import TextEditor from "@/components/TextEditor";
import { useFormik } from "formik";
import { patchUserProfile } from "@/services/userAPI";
import useUserProfile from "@/hooks/useUserProfile";
import { SummarySchema } from "@/schemas/UserProfileSchema";
import ToastNotification from "@/components/Swal/ToastNotification";
import { Button } from "@/components/Button";
import { useTranslations } from "next-intl";
import { VscLoading } from "react-icons/vsc";
import { isMobile } from "react-device-detect";

interface Props {
  openModal: boolean;
  onClose: () => void;
}

interface SummaryFormValue {
  summary: string | null;
}

const TheSummaryFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();
  const [isUpdating, setIsUpdating] = useState(false);
  const form = useFormik<SummaryFormValue>({
    initialValues: {
      summary: userProfile?.summary,
    },
    validationSchema: SummarySchema(t),
    onSubmit: async (values) => {
      setIsUpdating(true);
      patchUserProfile({
        summary: values.summary,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_summary_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);

          onClose && onClose();
        })
        .finally(() => setIsUpdating(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.summary) return;
    form.resetForm({ values: { summary: userProfile.summary } });
  }, [userProfile]);

  useEffect(() => {
    if (!userProfile || !userProfile.summary) return;
    if (openModal) {
      form.resetForm({ values: { summary: userProfile.summary } });
    }
  }, [openModal]);

  return (
    <Modal show={openModal} onClose={() => onClose()} size={"3xl"}>
      <Modal.Header className={"bg-gray-light"}>
        <p className={"text-black text-center m-auto md:text-left"}>{t("user_profile_summary_title")}</p>
        <p className={"hidden text-base font-normal text-gray-400 md:block"}>
          {t("user_profile_summary_modal_description")}
        </p>
      </Modal.Header>
      <Modal.Body>
        <form id={"summary-form"} onSubmit={form.handleSubmit}>
          <TextEditor
            value={form.values.summary as string}
            onChange={(newValue) => form.setFieldValue("summary", newValue)}
            height={250}
          />
          {form.errors.summary && form.touched.summary ? (
            <div className={"mt-2 text-sm text-primary-300"}>
              {form.errors.summary}
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
      <Modal.Footer className={"justify-end bg-gray-light"}>
        <Button
          accent={"primary"}
          type={"submit"}
          form={"summary-form"}
          disabled={isUpdating}
          trailingIcon={
            isUpdating ? (
              <VscLoading className="h-6 w-6 animate-spin text-white" />
            ) : (
              ""
            )
          }
          isBlock={isMobile}
        >
          {t("user_profile_summary_modal_btn_save")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default TheSummaryFormModal;
