"use client";
import React, { FC, useEffect, useState } from "react";
import { Modal, TextInput } from "flowbite-react";
import { HiOutlinePlus, HiPencil, HiTrash } from "react-icons/hi2";
import { useFormik } from "formik";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile } from "@/services/userAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { GrDrag } from "react-icons/gr";
import { Button } from "@/components/Button";
import { useTranslations } from "next-intl";
import { ReferenceSchema } from "@/schemas/UserProfileSchema";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { getIfNotEmpty } from "@/utils/string";
import { arrayMoveImmutable } from "array-move";
import { ReactSortable, SortableEvent } from "react-sortablejs";
import { isMobile } from "react-device-detect";
interface Props {
  openModal: boolean;
  onClose?: () => void;
}

interface ReferenceFormValue {
  refer_name?: string;
  refer_email?: string;
  refer_phone?: string;
  refer_profession?: string;
  is_editting?: boolean;
  is_new?: boolean;
  is_dirty?: boolean;
  id: number;
}

const TheReferenceFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const [stateSortable, setStateSortable] = useState<ReferenceFormValue[]>([]);
  const [userProfile, setUserProfile] = useUserProfile();
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();
  const form = useFormik<ReferenceFormValue[]>({
    initialValues: [],
    validationSchema: ReferenceSchema(t),
    onSubmit: async (values) => {
      if (!userProfile) return;
      setIsSaving(true);
      const references = values.map((reference) => {
        return {
          refer_name: reference.refer_name,
          refer_email: reference.refer_email,
          refer_phone: reference.refer_phone,
          refer_profession: reference.refer_profession,
          is_dirty: reference.is_dirty ?? false,
          id: 0,
        };
      });

      patchUserProfile({
        references: references,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_reference_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);
          const editingIndex = form.values.findIndex(
            (value: ReferenceFormValue) => value.is_editting == true,
          );
          form.setFieldValue(editingIndex + ".is_editting", false);
        })
        .finally(() => setIsSaving(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.references) return;

    form.resetForm({ values: [...userProfile.references] });
  }, [userProfile]);

  const handleAddNewReferenceBtnClick = () => {
    form.setValues([
      ...form.values,
      {
        id: 0,
        refer_name: "",
        refer_email: "",
        refer_phone: "",
        refer_profession: "",
        is_editting: true,
        is_new: true,
        is_dirty: true,
      },
    ]);
  };

  const handleDeleteReferenceBtnClick = async (index: number) => {
    setIsDeleting({ [index]: true });
    const references = [...form.values];
    references.splice(index, 1);
    patchUserProfile({
      references: references,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_reference_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleCancelReferenceBtnClick = async (index: number) => {
    const isNew = form.values[index].is_new ?? false;
    if (isNew) {
      const values = [...form.values];
      values.splice(index, 1);
      form.setValues(values);
    } else {
      form.setFieldValue(index + ".is_editting", false);
    }
    form.setTouched({ [index]: { refer_name: false } } as any);
  };

  const hasEditing = () =>
    form.values.filter((value: ReferenceFormValue) => value.is_editting == true)
      .length > 0;

  useEffect(() => {
    setStateSortable([...form.values]);
  }, [form.values]);

  const handleChangeArray = (event: SortableEvent) => {
    setIsSaving(true);

    const newReferences = arrayMoveImmutable(
      stateSortable,
      event?.newIndex as number,
      event?.oldIndex as number,
    );
    if (!!newReferences && newReferences.length === 0) return;

    patchUserProfile({
      references: newReferences,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_reference_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsSaving(false));
  };

  return (
    <Modal
      show={openModal}
      onClose={() => (onClose ? onClose() : "")}
      size={"3xl"}
      className="custom-modal-profile"
    >
      <Modal.Header className={"bg-gray-light"}>
        <p className={"font-bold text-black"}>
          {t("user_profile_reference_title")}
        </p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        <form
          className="space-y-6"
          onSubmit={form.handleSubmit}
          id={"certificate-form"}
        >
          <ReactSortable
            list={stateSortable.map((value) => ({ ...value }))}
            setList={setStateSortable}
            animation={100}
            onEnd={(evt) => handleChangeArray(evt)}
            disabled={
              isSaving || stateSortable.length < 2 || hasEditing() || isMobile
            }
            className="flex flex-col flex-wrap gap-4"
          >
            {stateSortable.map((reference, index) => (
              <div key={index} className={"bg-gray-light p-4"}>
                {reference.is_editting ? (
                  <div className={"grid grid-cols-2 gap-4"}>
                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_reference_modal_field_name")}{" "}
                        <span className={"font-normal text-primary"}>*</span>
                      </label>
                      <TextInput
                        value={reference.refer_name}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".refer_name",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_reference_modal_field_placeholder_name",
                        )}
                      />
                      {form.errors[index]?.refer_name &&
                      form.touched[index]?.refer_name ? (
                        <div className={"text-sm text-primary-300"}>
                          {form.errors[index]?.refer_name}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_reference_modal_field_position")}{" "}
                      </label>
                      <TextInput
                        value={reference.refer_profession}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".refer_profession",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_reference_modal_field_placeholder_position",
                        )}
                      />
                    </div>

                    <div className={"form-group col-span-2 md:col-span-1"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_reference_modal_field_email")}
                      </label>
                      <TextInput
                        value={reference.refer_email}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".refer_email",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_reference_modal_field_placeholder_email",
                        )}
                      />
                    </div>

                    <div className={"form-group col-span-2 md:col-span-1"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_reference_modal_field_phone")}
                      </label>
                      <TextInput
                        value={reference.refer_phone}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".refer_phone",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_reference_modal_field_placeholder_phone",
                        )}
                      />
                    </div>

                    <div className={"form-group col-span-2 flex justify-end"}>
                      <Button
                        accent={"ghost"}
                        type={"button"}
                        onClick={() => handleCancelReferenceBtnClick(index)}
                      >
                        {t("user_profile_reference_modal_btn_remove")}
                      </Button>
                      <Button
                        type={"button"}
                        form={"certificate-form"}
                        accent={"primary"}
                        trailingIcon={
                          isSaving ? (
                            <VscLoading className="h-6 w-6 animate-spin text-white" />
                          ) : (
                            ""
                          )
                        }
                        disabled={isSaving}
                        onClick={async () => {
                          const errors = await form.validateForm();
                          form.setTouched({
                            [index]: { refer_name: true },
                          } as any);
                          if (!Object.values(errors).length) {
                            form.submitForm();
                          }
                        }}
                      >
                        {t("user_profile_reference_modal_btn_save")}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className={"flex items-center"}>
                      {!isMobile && (
                        <div className="pr-3">
                          <GrDrag />
                        </div>
                      )}

                      <div className={"grow"}>
                        <p className={"font-bold"}>{reference.refer_name}</p>
                        <p className={"text-sm text-gray-400 md:text-base"}>
                          {reference.refer_profession}
                        </p>
                        <p className={"text-sm text-gray-400 md:text-base"}>
                          {reference.refer_email}
                          {getIfNotEmpty(" - ", [
                            reference.refer_email,
                            reference.refer_phone,
                          ])}
                          {reference.refer_phone}
                        </p>
                      </div>
                      <div className={"flex-none"}>
                        <button
                          className={
                            "h-5 w-5 items-center justify-center md:block"
                          }
                          type={"button"}
                          disabled={hasEditing()}
                          onClick={() => {
                            form.values.forEach(
                              (language: ReferenceFormValue, index: number) =>
                                form.setFieldValue(
                                  index + ".is_editting",
                                  false,
                                ),
                            );
                            form.setFieldValue(index + ".is_editting", true);
                            form.setFieldValue(index + ".is_dirty", true);
                          }}
                        >
                          <HiPencil
                            className={hasEditing() ? "text-gray-200" : ""}
                          />
                        </button>

                        <button
                          className={
                            "ml-4 h-5 w-5 items-center justify-center md:block"
                          }
                          type={"button"}
                          disabled={
                            (isDeleting && isDeleting[index] ? true : false) ||
                            hasEditing()
                          }
                          onClick={() => handleDeleteReferenceBtnClick(index)}
                        >
                          {isDeleting && isDeleting[index] ? (
                            <VscLoading className="h-4 w-4 animate-spin text-black" />
                          ) : (
                            <HiTrash
                              className={hasEditing() ? "text-gray-200" : ""}
                            />
                          )}
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </ReactSortable>

          {!form.values || !form.values.length || !hasEditing() ? (
            <div className={"flex justify-center"}>
              <Button
                type={"button"}
                onClick={() => handleAddNewReferenceBtnClick()}
                leadingIcon={<HiOutlinePlus />}
                accent={isMobile ? "primary" : "outline"}
                isBlock={isMobile}
              >
                {t("user_profile_reference_btn_add")}
              </Button>
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default TheReferenceFormModal;
