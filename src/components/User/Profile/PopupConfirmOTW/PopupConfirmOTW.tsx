import { MissingSection, ResumeUserProfile } from "@/types/userProfile";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import React, { FC } from "react";
import { sectionPercentages } from "../precent-config/BadgeRemindMissing";
import { classNames } from "@/utils";

type PopupConfirmOTWProps = {
  selectedCv: ResumeUserProfile;
  missingSections: MissingSection[];
  missingType: string;
  isShowPopupIsNotTopdevCV: boolean;
  isClosePopup: () => void;
};

const PopupConfirmOTW: FC<PopupConfirmOTWProps> = ({
  selectedCv,
  missingSections,
  missingType,
  isShowPopupIsNotTopdevCV,
  isClosePopup,
}) => {
  const t = useTranslations();

  if (!isShowPopupIsNotTopdevCV) return null;

  const renderSectionBadgeInPopup = (section: MissingSection["section"]) => {
    const percentage = sectionPercentages[section];
    if (!percentage) return null;

    return (
      <span
        className={classNames(
          `my-auto cursor-pointer rounded-lg bg-green-500 px-2 text-xs font-bold text-white`,
        )}
      >
        {percentage}
      </span>
    );
  };
  return (
    <div
      className="fixed inset-0 z-10 flex items-center justify-center bg-[#292929] bg-opacity-75"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <div className="relative w-full max-w-md p-4 sm:my-8  sm:w-full sm:max-w-[476px]">
        <div className="relative transform overflow-hidden rounded-lg bg-white text-left transition-all sm:w-[476px]">
          <div className="relative bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <button
              type="button"
              className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
              onClick={isClosePopup}
            >
              <svg
                className="h-6 w-6"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          {/* Body of Popup  */}
          <div className="mx-auto flex flex-col px-6">
            <div className="mx-auto">
              <Image
                src="https://c.topdevvn.com/uploads/2024/08/07/imge_in_popup_not_Topdev_CV.webp"
                width={111}
                height={135}
                className="h-auto w-auto"
                alt="Image in popup"
              />
            </div>
            <div className="content-popup flex flex-col text-center">
              <span className="mt-6 flex-nowrap text-2xl font-semibold text-neutral-950">
                {t("user_profile_popup_conrfirm_otw_title")}
              </span>
              <span className="text-2xl font-semibold text-neutral-950">
                {selectedCv.name && selectedCv.name.length > 35
                  ? `${selectedCv.name.substring(
                      0,
                      28,
                    )}...${selectedCv.name.substring(
                      selectedCv.name.length - 4,
                    )}`
                  : selectedCv.name}
              </span>
              <span className="mt-2 tracking-normal text-neutral-600 sm:text-base sm:tracking-[0.00875rem]">
                {t.rich("user_profile_popup_conrfirm_otw_recommend", {
                  b: (chunk) => (
                    <b className="font-bold text-neutral-600">{chunk}</b>
                  ),
                })}
              </span>
              {missingType === "standardMissing" &&
                selectedCv.type === "topdev_cv" &&
                missingSections.length > 0 && (
                  <div className="mt-4 w-full rounded bg-neutral-light p-2">
                    <span className="text-base font-normal text-neutral-600">
                      {t("user_profile_popup_conrfirm_otw_remind")}
                    </span>
                    {missingSections.length > 0 && (
                      <div className="infomation-missing-type--standardMissing mt-4 flex h-auto w-auto flex-row flex-wrap justify-center gap-2 align-middle">
                        {missingSections.map((item, index) => (
                          <div
                            className="item-cemter flex gap-2 rounded bg-white py-1 pl-2 pr-1"
                            key={index}
                          >
                            <span className="cursor-pointer bg-green-50 text-sm font-bold text-neutral-900 hover:underline">
                              {t(item.title)}
                            </span>
                            {renderSectionBadgeInPopup(item.section)}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
            </div>
          </div>
          {/* Footer of Popup */}
          <div className="flex w-full flex-col-reverse gap-4 p-6 sm:flex sm:w-auto sm:flex-row sm:items-center sm:justify-between">
            <Link
              href="job-management?src=topdev.vn&medium=bannerotw"
              type="button"
              className="inline-flex w-full justify-center whitespace-nowrap rounded border border-brand-600 bg-white px-4 py-4 text-base font-bold text-brand-600 sm:w-full"
              onClick={isClosePopup}
            >
              {t("user_profile_popup_conrfirm_otw_button_setup_cv")}
            </Link>
            <button
              type="button"
              className="inline-flex w-full items-center justify-center whitespace-nowrap rounded border border-brand-600 bg-brand-600 px-7 py-4 text-base font-bold text-white sm:w-full"
              onClick={isClosePopup}
            >
              {t("user_profile_popup_conrfirm_otw_button_update_cv")}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PopupConfirmOTW;
