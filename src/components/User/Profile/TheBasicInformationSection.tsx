"use client";
import React, { FC, useEffect } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { AiFillGithub, AiFillLinkedin } from "react-icons/ai";
import { HiPencil, HiStar } from "react-icons/hi2";
import { TheBasicInformationFormModal } from "@/components/User/Profile/index";
import NoAvatar from "@/assets/images/no-avatar.svg";
import useUserProfile from "@/hooks/useUserProfile";
import { useAppSelector } from "@/store";
import TheConvertCVModal from "@/components/User/Profile/TheConvertCVModal";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { useProfilePopupContext } from "@/components/User/Profile/TheProfilePage";
import {
  IS_SHOW_BASIC_INFO_MODAL,
  PROFILE_CONTEXT_TYPE,
} from "@/contansts/userProfiles";
import { IS_SHOW_CONVERT_CV_MODAL } from "@/contansts/userProfiles";
import { isDesktop } from "react-device-detect";

type TheBasicInformationSectionProps = {
  completion: number;
  missingType: string;
};
const TheBasicInformationSection: FC<TheBasicInformationSectionProps | any> = ({
  completion,
  missingType,
}) => {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const [userProfile] = useUserProfile();
  const user = useAppSelector((state) => state.user.user);
  const profilePopupContext = useProfilePopupContext();
  useEffect(() => {
    if (
      searchParams.has(IS_SHOW_CONVERT_CV_MODAL) &&
      Number(searchParams.get(IS_SHOW_CONVERT_CV_MODAL)) === 1
    ) {
      profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV);
    }
    if (
      searchParams.has(IS_SHOW_BASIC_INFO_MODAL) &&
      Number(searchParams.get(IS_SHOW_BASIC_INFO_MODAL)) === 1
    ) {
      profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO);
    }
  }, [searchParams]);

  return (
    <div className="p-0 m-0">
      <div className="flex-row items-center justify-between hidden sm:flex">
        <span className="mb-3 text-sm font-bold uppercase text-neutral-400">
          {t("user_profile_section_basic_info_title")}
        </span>
        <span className="items-center py-1 pl-3 pr-2 mb-3 text-xs font-normal border border-blue-500 rounded-full bg-blue-50">
          {isDesktop && completion >= 50
            ? t.rich("user_profile_section_basic_info_title_complete", {
                b: (chunk) => (
                  <b className="font-bold text-blue-600">{chunk}</b>
                ),
              })
            : t.rich("user_profile_section_basic_info_title_not_complete", {
                b: (chunk) => (
                  <b className="font-bold text-blue-600">{chunk}</b>
                ),
              })}
        </span>
      </div>
      <section
        id={"basic-information"}
        className={
          "relative hidden gap-8 rounded bg-white py-6 pl-6 pr-4 md:flex"
        }
      >
        <div className={"flex-none"}>
          <div className={"h-40 w-40 rounded-full bg-gray-500"}>
            <Image
              src={userProfile?.avatar_url ?? NoAvatar}
              width={160}
              height={160}
              alt={"Avatar"}
              className={"h-40 w-40 rounded-full"}
              priority={!!userProfile?.avatar_url}
              />
          </div>
        </div>
        <div className={"flex-auto overflow-hidden"}>
          <div className={"flex flex-col"}>
            <div className={"flex items-center gap-1.5"}>
              <h4 className={"overflow-hidden text-2xl font-bold text-black"}>
                {userProfile?.display_name ?? user.full_name}
              </h4>
              {completion < 50 && (
                <span className="text-brand-700 ml-2 rounded-full bg-brand-100 px-3 py-[2px] text-sm font-bold">
                  {completion}%
                </span>
              )}
              {completion >= 50 && completion < 100 && (
                <span className="ml-2 rounded-full bg-blue-50 px-3 py-[2px] text-sm font-bold text-blue-500">
                  {completion}%
                </span>
              )}
              {completion === 100 && missingType !== "luckyStarMissing" && (
                <span className="ml-2 rounded-full bg-green-50 px-3 py-[2px] text-sm font-bold text-green-500">
                  {completion}%
                </span>
              )}
              {completion === 100 && missingType === "luckyStarMissing" && (
                <div className="ml-2 flex items-center gap-1 rounded-full bg-gradient-to-r from-[#D34127] to-[#FF7B42] px-3 py-[2px] text-center text-sm font-bold text-white">
                  <HiStar
                    className="inline-block text-xl"
                    width={20}
                    height={20}
                  />
                  <span className="flex text-sm">Lucky Star</span>
                </div>
              )}
            </div>

            <div className={"flex"}>
              <div className={"flex items-center"}>
                {userProfile?.position ? (
                  <span className="overflow-hidden text-xl font-bold text-neutral-600">
                    {userProfile?.position}
                  </span>
                ) : (
                  <span className={"text-xl font-bold text-neutral-300"}>
                    {t("user_profile_basic_info_default_position")}
                  </span>
                )}
                <div
                  className={"w-[30px] items-center justify-center text-center"}
                >
                  -
                </div>
              </div>
              <div className={"flex-none text-xl text-gray-500"}>
                {userProfile?.years_of_exp ? (
                  userProfile.years_of_exp.toString() +
                  " " +
                  (userProfile.years_of_exp > 1
                    ? t("user_profile_basic_info_default_YOEs")
                    : t("user_profile_basic_info_default_YOE"))
                ) : (
                  <span className={"text-gray-300"}>
                    {t("user_profile_basic_info_default_exp")}
                  </span>
                )}
              </div>
            </div>

            <div className={"mt-4 text-lg text-gray-400"}>
              <div className={"flex w-96 truncate"}>
                {userProfile &&
                (userProfile.address || userProfile.province_name) ? (
                  (userProfile.address ? userProfile.address + ", " : "") +
                  userProfile.province_name
                ) : (
                  <span className={"text-gray-300"}>
                    {t("user_profile_basic_info_default_address")}
                  </span>
                )}
              </div>

              <div className={"mt-1 flex"}>
                <div className={"flex"}>
                  <a className={"underline"}>
                    {userProfile?.email ?? user.email}
                  </a>
                </div>
                <div className={"w-[30px] text-center"}>-</div>
                <div>
                  {userProfile?.phone ?? (
                    <span className={"whitespace-nowrap text-gray-300"}>
                      {t("user_profile_basic_info_default_phone")}
                    </span>
                  )}
                </div>
                <div className={"w-[30px] text-center"}>-</div>
                <div>
                  {userProfile?.birthday ? (
                    dayjs(userProfile?.birthday).format("DD-MM-YYYY")
                  ) : (
                    <span className={"whitespace-nowrap text-gray-300"}>
                      {t("user_profile_basic_info_default_birthday")}
                    </span>
                  )}
                </div>
              </div>

              <div className={"mt-1 flex  gap-4"}>
                {!userProfile ||
                (!userProfile?.linkedin_link && !userProfile?.github_link) ? (
                  <span className={"text-gray-300"}>
                    {t("user_profile_basic_info_default_socialite_link")}
                  </span>
                ) : (
                  ""
                )}
                {userProfile?.linkedin_link ? (
                  <div
                    className={"flex w-6/12 items-center gap-1 overflow-hidden"}
                  >
                    <AiFillLinkedin className={"flex-none"} />
                    <a className={"truncate underline"}>
                      {userProfile?.linkedin_link}
                    </a>
                  </div>
                ) : (
                  ""
                )}
                {userProfile?.github_link ? (
                  <div
                    className={"flex w-6/12 items-center gap-1 overflow-hidden"}
                  >
                    <AiFillGithub className={"flex-none"} />
                    <a className={"truncate underline"}>
                      {userProfile?.github_link}
                    </a>
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          </div>
        </div>
        <div className={"absolute right-4 flex flex-none"}>
          <button
            className={"flex h-auto w-auto items-center"}
            type={"button"}
            onClick={() => {
              profilePopupContext.dispatch(
                PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
              );
            }}
          >
            <HiPencil />
            <span className="px-4 py-2 text-sm font-semibold text-neutral-900">
              {t("user_profile_basic_info_btn_edit_info")}
            </span>
          </button>
        </div>

        <TheConvertCVModal
          openModal={[
            PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV,
            PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_CONVERT_CV,
            PROFILE_CONTEXT_TYPE.TOGGLE_SELECT_CONVERT_CV,
          ].includes(profilePopupContext.state.type ?? "")}
          onClose={() => {
            profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED);
          }}
        />

        {userProfile ? (
          <TheBasicInformationFormModal
            openModal={[
              PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO,
              PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
            ].includes(profilePopupContext.state.type ?? "")}
            onClose={() =>
              profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED)
            }
          />
        ) : (
          ""
        )}
      </section>
    </div>
  );
};

export default TheBasicInformationSection;
