import useUserProfile from "@/hooks/useUserProfile";
import dynamic from "next/dynamic";
import { useAppSelector } from "@/store";
import Image from "next/image";
import React, { FC, useEffect, useState } from "react";
import NoAvatar from "@/assets/images/no-avatar.svg";
import { classNames } from "@/utils";
import calculateCompletionPercentage from "@/components/User/Profile/precent-config/count-infomation-precent";

import { MissingSection } from "@/types/userProfile";
import {
  PROFILE_CONTEXT_TYPE,
  STATUS_MISSING_SECTIONS,
} from "@/contansts/userProfiles";
const TheSectionOTW = dynamic(
  () => import("@/components/User/Profile/TheSectionOTW"),
);
import { useTranslations } from "next-intl";
import { HiPencil } from "react-icons/hi2";
import { AiFillGithub, AiFillLinkedin } from "react-icons/ai";
import { useProfilePopupContext } from "@/components/User/Profile/TheProfilePage";
const TheInformationRemindMissing = dynamic(
  () => import("@/components/User/Profile/TheInformationRemindMissing"),
);
const TheTipsBannerBooster = dynamic(
  () => import("@/components/User/Profile/TheTipsBannerBooster"),
);
type TheInformationProcessBarProps = {
  completion: number;
  missingSections: MissingSection[];
  missingType: string;
};

const TheInformationProcessBarMobile: FC<TheInformationProcessBarProps> = ({
  completion,
  missingSections,
  missingType,
}) => {
  const profilePopupContext = useProfilePopupContext();
  const t = useTranslations();
  const user = useAppSelector((state) => state.user.user);
  const [userProfile] = useUserProfile();
  const [isBannerTipBoosterVisible, setIsBannerTipBoosterVisible] =
    useState<boolean>(true);

  return (
    <>
      <div className="flex flex-row gap-2">
        <span className="ml-2 text-sm font-bold uppercase text-neutral-400">
          {t("user_profile_section_basic_info_title")}
        </span>
        <span className="items-center px-2 text-xs font-bold text-blue-600 border border-blue-500 rounded-full bg-blue-50">
          50%
        </span>
      </div>
      <div className="w-full h-auto p-4 mt-2 bg-white rounded infomation-card">
        <div className="flex gap-2 infomation-card-data">
          <div className="infomation-card__process-bar">
            <div className="relative size-40 w-28">
              <svg
                className="size-full rotate-[135deg]"
                viewBox="0 0 36 36"
                xmlns="http://www.w3.org/2000/svg"
              >
                <defs>
                  <linearGradient
                    id="gradient1"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="100%"
                  >
                    <stop
                      offset="0%"
                      style={{ stopColor: "#ff7b42", stopOpacity: 1 }}
                    />
                    <stop
                      offset="100%"
                      style={{ stopColor: "#d34127", stopOpacity: 1 }}
                    />
                  </linearGradient>
                </defs>
                <circle
                  cx="18"
                  cy="18"
                  r="16"
                  fill="none"
                  className="text-gray-200 dark:text-neutral-700"
                  strokeWidth="1.5"
                  strokeDasharray="75 100"
                  strokeLinecap="round"
                ></circle>

                <circle
                  cx="18"
                  cy="18"
                  r="16"
                  fill="none"
                  stroke="url(#gradient1)"
                  className={classNames(
                    completion < 50
                      ? "stroke-current text-brand-600"
                      : completion >= 50 && completion < 100
                      ? "stroke-current text-blue-600"
                      : completion === 100 && missingType !== "luckyStarMissing"
                      ? "stroke-current text-green-500"
                      : "",
                  )}
                  strokeWidth="1.5"
                  strokeDasharray={`${Math.min(completion, 75)} 100`}
                  strokeLinecap="round"
                ></circle>
              </svg>

              <div className="absolute -translate-x-1/2 -translate-y-1/2 start-1/2 top-1/2 sm:relative sm:start-0 sm:top-0 sm:translate-x-0 sm:translate-y-0">
                <div className="flex items-center justify-center">
                  <Image
                    src={userProfile?.avatar_url ?? NoAvatar}
                    width={88}
                    height={88}
                    alt={"Avatar"}
                    className={
                      "h-[88px] min-h-[88px] w-[88px] min-w-[88px] rounded-full"
                    }
                    priority={
                      !!userProfile?.avatar_url
                        ? !!userProfile?.avatar_url
                        : false
                    }
                  />
                  <span
                    className={classNames(
                      completion < 50
                        ? "text-brand-600"
                        : completion >= 50 && completion < 100
                        ? "text-blue-600"
                        : completion === 100
                        ? "text-green-500"
                        : "",
                      "absolute start-1/2 top-24 flex w-full -translate-x-1/2 -translate-y-1/3 items-center justify-center text-center text-base font-bold",
                    )}
                  >
                    {completion}%
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex mt-5 infomation-card__content">
            <div className="flex flex-col">
              <span className="text-lg font-bold text-neutral-950">
                {userProfile?.display_name ?? user.full_name}
              </span>
              <div>
                {userProfile?.position ? (
                  <>
                    <span className="text-sm font-bold text-neutral-900">
                      {userProfile.position}
                    </span>
                  </>
                ) : (
                  <span className="text-sm font-bold text-neutral-300">
                    {t("user_profile_basic_info_default_position")}
                  </span>
                )}
                {userProfile?.years_of_exp ? (
                  <>
                    <span className="ml-2 text-sm font-normal text-neutral-600">
                      -
                    </span>
                    <span className="ml-2 text-sm font-normal text-neutral-600">
                      {userProfile.years_of_exp} YOE
                    </span>
                  </>
                ) : (
                  <span className="flex flex-col text-sm font-normal text-neutral-300">
                    {t("user_profile_basic_info_default_exp")}
                  </span>
                )}
              </div>
              <span className="text-sm text-neutral-500">
                {userProfile?.email ?? ""}
              </span>
            </div>
          </div>
          <div
            className="flex justify-end ml-auto infomation-card__content_second"
            onClick={() => {
              profilePopupContext.dispatch(
                PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
              );
            }}
          >
            <HiPencil />
          </div>
        </div>
        <div className="flex flex-col mt-2 information-card__contact">
          <div className="flex mt-2">
            {userProfile?.phone ? (
              <span className="text-sm font-normal text-neutral-500">
                {userProfile.phone}
              </span>
            ) : (
              <span className="text-sm font-normal text-neutral-300">
                {t("user_profile_basic_info_default_phone")}
              </span>
            )}
            <span className="mx-2">-</span>
            {userProfile?.birthday ? (
              <span className="ml-2 text-sm">{userProfile.birthday}</span>
            ) : (
              <span className="text-sm text-neutral-300 md:text-base">
                {t("user_profile_basic_info_default_birthday")}
              </span>
            )}
          </div>
          <div className="infomation-card--local">
            {userProfile?.address ? (
              <span className="text-sm font-normal text-neutral-500">
                {userProfile.address.length > 25
                  ? `${userProfile.address.slice(0, 25)}...`
                  : userProfile.address}
              </span>
            ) : (
              <span className="text-sm font-normal text-neutral-300">
                {t("user_profile_basic_info_default_address")}
              </span>
            )}
            {userProfile?.address && userProfile?.province_name ? (
              <span>,</span>
            ) : userProfile?.address || userProfile?.province_name ? (
              <span className="mx-2">-</span>
            ) : null}
            {userProfile?.province_name ? (
              <span className="ml-1 text-sm font-normal text-neutral-500">
                {userProfile.province_name}
              </span>
            ) : (
              <span className="text-sm font-normal text-neutral-300">
                {t("user_profile_basic_info_default_city")}
              </span>
            )}
          </div>
          <div className="infomation-card--social">
            <div className="flex flex-col">
              {!userProfile?.linkedin_link && !userProfile?.github_link ? (
                <span className="text-sm font-normal text-neutral-300">
                  {t("user_profile_basic_info_default_socialite_link")}
                </span>
              ) : (
                <>
                  {userProfile?.linkedin_link && (
                    <div className="flex flex-row items-center gap-1 py-[2px] font-normal text-neutral-500">
                      <AiFillLinkedin className={"flex-none"} size={24} />
                      <a href={userProfile.linkedin_link} className="text-sm">
                        {userProfile?.linkedin_link}
                      </a>
                    </div>
                  )}
                  {userProfile?.github_link && (
                    <div className="flex flex-row items-center gap-1 py-[2px] font-normal text-neutral-500">
                      <AiFillGithub className={"flex-none"} size={24} />
                      <a href={userProfile.github_link} className="text-sm">
                        {userProfile?.github_link}
                      </a>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="p-4 mt-4 bg-white">
        <TheInformationRemindMissing
          // precentUnit={percentages}
          userProfile={userProfile}
          missingSections={missingSections}
          missingType={missingType}
        />
        {missingType === "boosterMissing" && (
          <TheTipsBannerBooster
            isBannerTipBoosterVisible={isBannerTipBoosterVisible}
            onCloseTips={() => setIsBannerTipBoosterVisible(false)}
          />
        )}
      </div>

      <TheSectionOTW />
    </>
  );
};

export default TheInformationProcessBarMobile;
