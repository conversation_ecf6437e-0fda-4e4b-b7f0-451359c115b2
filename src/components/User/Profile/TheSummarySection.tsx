"use client";
import React, { FC, useEffect, useState } from "react";
import { HiPencil } from "react-icons/hi2";
import { TheSummaryFormModal } from "@/components/User/Profile/index";
import useUserProfile from "@/hooks/useUserProfile";
import SummaryIllustrator from "@/assets/images/illustrators/summary.svg";
import Image from "next/image";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { useProfilePopupContext } from "@/components/User/Profile/TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";
import { isMobile } from "react-device-detect";

type TheSummarySectionProps = {
  completion: number;
};
const TheSummarySection: FC<TheSummarySectionProps> = ({ completion }) => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();
  const profilePopupContext = useProfilePopupContext();

  useEffect(() => {
    setIsFormModalOpen(
      profilePopupContext.state.type == PROFILE_CONTEXT_TYPE.POPUP_SUMMARY,
    );
  }, [profilePopupContext]);

  return (
    <div className="m-0 p-0">
      <div className="ml-4 flex flex-row items-center gap-2 sm:ml-0 sm:justify-between sm:gap-0">
        <span className="mb-3 text-sm font-bold uppercase text-neutral-400">
          {t("user_profile_section_experience_title")}
        </span>
        <span className="mb-3 hidden items-center rounded-full border border-green-400 bg-green-50 py-1 pl-3 pr-2 text-xs font-normal sm:block">
          {completion === 100
            ? t.rich("user_profile_section_experience_title_complete", {
                b: (chunk) => (
                  <b className="font-bold text-green-700">{chunk}</b>
                ),
              })
            : t.rich("user_profile_section_experience_title_not_complete", {
                b: (chunk) => (
                  <b className="font-bold text-green-700">{chunk}</b>
                ),
              })}
        </span>
        <span className="mb-3 block items-center rounded-full border border-green-400 bg-green-50 px-2 text-xs font-bold text-green-700 sm:hidden">
          50%
        </span>
      </div>
      <section
        id={"summary"}
        className={"divide-y divide-gray-200 rounded bg-white"}
        onClick={() => isMobile && setIsFormModalOpen(true)}
      >
        <div className={"card-title flex p-4 md:p-6"}>
          <div
            className={classNames(
              userProfile?.summary
                ? "grow"
                : "mr-2 w-[calc(100%_-_85px)] md:mr-0 md:w-auto md:grow",
            )}
          >
            <h2 className={"text-xl font-bold text-black md:text-2xl"}>
              {t("user_profile_summary_title")}
            </h2>
            <div
              className={classNames(
                userProfile?.summary ? "hidden md:block" : "",
                "text-sm text-gray-400 md:text-base",
              )}
            >
              {t("user_profile_summary_description")}
            </div>
          </div>
          <div
            className={classNames(
              userProfile?.summary
                ? "hidden"
                : "flex w-[73px] items-center md:w-auto md:flex-none",
            )}
          >
            <Image
              src={SummaryIllustrator}
              alt={"Summary"}
              height={130}
              className={"h-[65px] w-[74px] md:mx-10 md:h-[130px] md:w-[147px]"}
            />
          </div>
          <div
            className={classNames(
              userProfile?.summary ? "items-center md:items-start" : "",
              "flex flex-none md:block",
            )}
          >
            <button
              className={"flex h-5 w-5 items-center justify-center"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>
          </div>
        </div>
        <div
          className={classNames(
            "card-body px-6 py-4 md:p-6",
            userProfile?.summary ? "flex" : "hidden",
          )}
        >
          <div
            className={
              "prose w-full max-w-full rounded bg-gray-light p-4 text-sm leading-6 text-gray-400 md:text-base"
            }
            dangerouslySetInnerHTML={{ __html: userProfile?.summary as string }}
          ></div>
        </div>
      </section>

      {userProfile ? (
        <TheSummaryFormModal
          openModal={isFormModalOpen}
          onClose={() => {
            setIsFormModalOpen(false);
            profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED);
          }}
        />
      ) : (
        ""
      )}
    </div>
  );
};

export default TheSummarySection;
