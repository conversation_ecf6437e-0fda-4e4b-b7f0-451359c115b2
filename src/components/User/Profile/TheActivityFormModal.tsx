"use client";
import React, { FC, useEffect, useState } from "react";
import { Checkbox, Modal, TextInput } from "flowbite-react";
import { HiOutlinePlus, HiPencil, HiTrash } from "react-icons/hi2";
import { useFormik } from "formik";
import { arrayMoveImmutable } from "array-move";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile } from "@/services/userAPI";
import TextEditor from "@/components/TextEditor";
import ToastNotification from "@/components/Swal/ToastNotification";
import { ReactSortable, SortableEvent } from "react-sortablejs";
import { Button } from "@/components/Button";
import { GrDrag } from "react-icons/gr";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { ActivitySchema } from "@/schemas/UserProfileSchema";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { formatIfValid } from "@/utils/date";
import { getIfNotEmpty } from "@/utils/string";
import MonthPicker from "@/components/MonthPicker/MonthPicker";
import { isMobile } from "react-device-detect";

interface Props {
  openModal: boolean;
  onClose?: () => void;
}

interface ActivityFormValue {
  id: number;
  activity?: string;
  is_working_here?: boolean;
  from?: string;
  to?: string;
  achievement?: string;
  is_editting?: boolean;
  is_new?: boolean;
  is_dirty?: boolean;
}

const TheActivityFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();
  const [stateSortable, setStateSortable] = useState<ActivityFormValue[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();
  const form = useFormik<ActivityFormValue[]>({
    initialValues: [],
    validationSchema: ActivitySchema(t),
    onSubmit: async (values) => {
      if (!userProfile) return;
      setIsSaving(true);
      const activities = values.map((activity) => {
        return {
          id: 0,
          activity: activity.activity,
          is_working_here: activity.is_working_here ?? false,
          from: activity.from ?? null,
          to: activity.to ?? null,
          achievement: activity.achievement,
          is_dirty: activity.is_dirty ?? false,
        };
      });

      patchUserProfile({
        activities: activities,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_activity_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);

          const editingIndex = form.values.findIndex(
            (value: ActivityFormValue) => value.is_editting == true,
          );
          form.setFieldValue(editingIndex + ".is_editting", false);
        })
        .finally(() => setIsSaving(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.activities) return;
    form.resetForm({ values: [...userProfile.activities] });
  }, [userProfile]);

  useEffect(() => {
    if (openModal) {
      if (!userProfile || !userProfile.activities) return;
      form.resetForm({ values: [...userProfile.activities] });
    }
  }, [openModal]);

  const handleAddNewAcitivityBtnClick = () => {
    form.setValues([
      ...form.values,
      {
        id: 0,
        activity: "",
        is_working_here: false,
        from: "",
        to: "",
        achievement: "",
        is_editting: true,
        is_new: true,
        is_dirty: true,
      },
    ]);
  };

  const handleDeleteActivityBtnClick = (index: number) => {
    setIsDeleting({ [index]: true });
    const activities = [...form.values];
    activities.splice(index, 1);
    patchUserProfile({
      activities: activities,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_activity_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleCancelReferenceBtnClick = async (index: number) => {
    const isNew = form.values[index].is_new ?? false;
    if (isNew) {
      const values = [...form.values];
      values.splice(index, 1);
      form.setValues(values);
    } else {
      form.setFieldValue(index + ".is_editting", false);
      form.setFieldValue(index + ".is_dirty", false);
    }

    form.setTouched({ [index]: { activity: false } } as any);
  };

  const hasEditing = () =>
    form.values.filter((value: ActivityFormValue) => value.is_editting == true)
      .length > 0;

  const handleChangeArray = (event: SortableEvent) => {
    setIsSaving(true);

    const newActivities = arrayMoveImmutable(
      stateSortable,
      event?.newIndex as number,
      event?.oldIndex as number,
    );
    if (!!newActivities && newActivities.length === 0) return;

    patchUserProfile({
      activities: newActivities,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_activity_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsSaving(false));
  };

  useEffect(() => {
    setStateSortable(form.values);
  }, [form.values]);

  return (
    <Modal
      show={openModal}
      onClose={() => (onClose ? onClose() : "")}
      size={"3xl"}
      className="custom-modal-profile"
    >
      <Modal.Header className={"bg-gray-light"}>
        <p className={"font-bold text-black"}>
          {t("user_profile_activity_title")}
        </p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        <form
          className="space-y-6"
          onSubmit={form.handleSubmit}
          id={"certificate-form"}
        >
          <ReactSortable
            list={stateSortable.map((value) => ({ ...value }))}
            setList={setStateSortable}
            animation={100}
            onEnd={(evt) => handleChangeArray(evt)}
            disabled={
              isSaving || stateSortable.length < 2 || hasEditing() || isMobile
            }
            className="flex flex-col flex-wrap gap-4"
          >
            {stateSortable.map((activity, index) => (
              <div key={index} className={"bg-gray-light p-4"}>
                {activity.is_editting ? (
                  <div className={"grid grid-cols-2 gap-4"}>
                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_activity_modal_field_name")}{" "}
                        <span className={"font-normal text-primary"}>*</span>
                      </label>
                      <TextInput
                        value={form.values[index]?.activity ?? ""}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".activity",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_activity_modal_field_placeholder_name",
                        )}
                      />
                      {form.errors[index]?.activity &&
                      form.touched[index]?.activity ? (
                        <div className={"text-sm text-primary-300"}>
                          {form.errors[index]?.activity}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"form-group col-span-2"}>
                      <label className={"text-sm font-bold text-gray-500"}>
                        <Checkbox
                          className={"mr-2"}
                          checked={form.values[index]?.is_working_here ?? false}
                          onChange={(event) => {
                            form.setFieldValue(
                              index + ".is_working_here",
                              event.target.checked,
                            );
                            form.setFieldValue(
                              index + ".to",
                              dayjs().format("YYYY-MM-DD"),
                            );
                          }}
                        />
                        {t("user_profile_activity_modal_field_working_here")}
                      </label>
                    </div>

                    <div className={"form-group col-span-2 md:col-span-1"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_activity_modal_field_start_date")}
                      </label>
                      <MonthPicker
                        selected={String(form.values[index]?.from)}
                        onChange={(value) => {
                          form.setFieldValue(index + ".from", value);
                        }}
                        placeholder="MM-YYYY"
                        format="MM-YYYY"
                      />
                    </div>

                    <div className={"form-group col-span-2 md:col-span-1"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_activity_modal_field_end_date")}
                      </label>
                      <MonthPicker
                        startDate={form.values[index]?.from}
                        endDate="9999-12-01"
                        disabled={form.values[index]?.is_working_here}
                        selected={String(form.values[index]?.to)}
                        onChange={(value) => {
                          form.setFieldValue(index + ".to", value);
                        }}
                        placeholder="MM-YYYY"
                        format="MM-YYYY"
                      />
                    </div>

                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_activity_modal_field_description")}
                      </label>
                      <TextEditor
                        value={form.values[index]?.achievement ?? ""}
                        onChange={(value) =>
                          form.setFieldValue(index + ".achievement", value)
                        }
                        placeholder={t(
                          "user_profile_activity_modal_field_placeholder_description",
                        )}
                        height={250}
                      />
                    </div>

                    <div className={"form-group col-span-2 flex justify-end"}>
                      <Button
                        accent={"ghost"}
                        type={"button"}
                        onClick={() => handleCancelReferenceBtnClick(index)}
                      >
                        {t("user_profile_activity_modal_btn_delete")}
                      </Button>
                      <Button
                        type={"button"}
                        form={"certificate-form"}
                        accent={"primary"}
                        disabled={isSaving}
                        trailingIcon={
                          isSaving ? (
                            <VscLoading className="h-6 w-6 animate-spin text-white" />
                          ) : (
                            ""
                          )
                        }
                        onClick={async () => {
                          const errors = await form.validateForm();
                          form.setTouched({
                            [index]: { activity: true },
                          } as any);
                          if (!Object.values(errors).length) {
                            form.submitForm();
                          }
                        }}
                      >
                        {t("user_profile_activity_modal_btn_save_all")}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className={"flex items-center gap-2"}>
                      {!isMobile && (
                        <div>
                          <GrDrag />
                        </div>
                      )}

                      <div className={"flex grow px-2 md:p-4"}>
                        <div className={"grow"}>
                          <p className={"font-bold"}>{activity.activity}</p>
                          <p className="text-sm md:text-base">
                            {formatIfValid(activity.from ?? "")}
                            {getIfNotEmpty(" - ", [activity.from, activity.to])}
                            {activity.is_working_here
                              ? t("user_profile_basic_info_date_present_text")
                              : formatIfValid(activity.to ?? "")}
                          </p>
                          <div
                            className="prose w-full text-sm md:text-base"
                            dangerouslySetInnerHTML={{
                              __html: activity.achievement ?? "",
                            }}
                          ></div>
                        </div>
                        <div className={"flex-none"}>
                          <button
                            className={"h-5 w-5"}
                            type={"button"}
                            disabled={hasEditing()}
                            onClick={() => {
                              form.values.forEach(
                                (language: ActivityFormValue, index: number) =>
                                  form.setFieldValue(
                                    index + ".is_editting",
                                    false,
                                  ),
                              );
                              form.setFieldValue(index + ".is_editting", true);
                              form.setFieldValue(index + ".is_dirty", true);
                            }}
                          >
                            <HiPencil
                              className={hasEditing() ? "text-gray-200" : ""}
                            />
                          </button>

                          <button
                            className={"ml-4 h-5 w-5"}
                            type={"button"}
                            disabled={
                              (isDeleting && isDeleting[index]
                                ? true
                                : false) || hasEditing()
                            }
                            onClick={() => handleDeleteActivityBtnClick(index)}
                          >
                            {isDeleting && isDeleting[index] ? (
                              <VscLoading className="h-4 w-4 animate-spin text-black" />
                            ) : (
                              <HiTrash
                                className={hasEditing() ? "text-gray-200" : ""}
                              />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </ReactSortable>
          {!form.values || !form.values.length || !hasEditing() ? (
            <div className={"flex justify-center"}>
              <Button
                type={"button"}
                onClick={() => handleAddNewAcitivityBtnClick()}
                accent={isMobile ? "primary" : "outline"}
                leadingIcon={<HiOutlinePlus />}
                isBlock={isMobile}
              >
                {t("user_profile_activity_modal_btn_add")}
              </Button>
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default TheActivityFormModal;
