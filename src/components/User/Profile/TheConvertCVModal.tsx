"use client";

import React, { FC, useEffect, useState } from "react";
import { usePathname, useSearchParams } from "next/navigation";
import { Modal } from "flowbite-react";
import {
  downloadUserProfileCv,
  getUserProfile,
  getUserProfileParsingProcess,
  parseUserProfileUploadedCv,
  uploadUserProfileCV,
} from "@/services/userAPI";
import GirlQuestion from "@/assets/images/illustrators/girl-question.svg";
import FailSendMail from "@/assets/images/illustrators/fail-send-mail.svg";
import Image from "next/image";
import useUserProfile from "@/hooks/useUserProfile";
import { Button } from "@/components/Button";
import { Player } from "@lottiefiles/react-lottie-player";
import loadingConvertAnimation from "@/assets/animations/loading-convert.json";
import Convert1Illustrator from "@/assets/images/illustrators/convert-1.svg";
import Convert2Illustrator from "@/assets/images/illustrators/convert-2.svg";
import Convert3Illustrator from "@/assets/images/illustrators/convert-3.svg";
import Convert4Illustrator from "@/assets/images/illustrators/convert-4.svg";
import { HiCloudArrowUp } from "react-icons/hi2";
import FileIcon from "@/assets/images/icons/file.svg";
import SuccessTickIcon from "@/assets/images/icons/success-tick.svg";
import { useTranslations } from "next-intl";
import { VscLoading } from "react-icons/vsc";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE, PROFILE_STATUS } from "@/contansts/userProfiles";
import { useAppDispatch, useAppSelector } from "@/store";
import ListResumesApplyJob from "@/components/DialogModal/ApplyJob/ListResumesApplyJob";
import { UserResumes } from "@/types/resume";
import { FaCloudUploadAlt } from "react-icons/fa";
import { setResumesUser } from "@/store/slices/userSlice";
import dayjs from "dayjs";
import ToastNotification from "@/components/Swal/ToastNotification";
import Profile from "@/types/profile";
import { isMobile } from "react-device-detect";
import { gtag } from "@/utils";
import Link from "next/link";

interface Props {
  openModal: boolean;
  onClose: () => void;
}

const STEP_1_INTRODUCTION = 1;
const STEP_2_UPLOAD_FILE = 2;
const STEP_2_SELECT_FILE = 21;
const STEP_3_UPLOAD_SUCCESS = 3;
const STEP_4_CONVERTING_CV = 4;
const STEP_5_CONVERT_SUCCESS = 5;
const STEP_6_CONVERT_FAIL = 6; // TODO: Trigger khi lỗi fail do TopDev parse fail.
const STEP_7_CONVERT_FAIL = 7; // TODO: Trigger khi lỗi fail do openai, network 502 hoặc, timeout.

const TheConvertCVModal: FC<Props> = ({ openModal = false, onClose }) => {
  const pathname = usePathname();
  const t = useTranslations();
  const [step, setStep] = useState(1);
  const [fileName, setFileName] = useState<string>("");
  const [fileMediaId, setFileMediaId] = useState<number | null>(null);
  const [isUploadSuccess, setIsUploadSuccess] = useState<boolean>(false);
  const [userProfile, setUserProfile] = useUserProfile();
  const [isWorking, setIsWorking] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isSelectFile, setIsSelectFile] = useState<boolean>(false);
  const profilePopupContext = useProfilePopupContext();
  const [fileCvValidate, setFileCvValidate] = useState<boolean>(false);
  const userResume = useAppSelector((state) => state.user.resumes);
  const [fileCV, setFileCV] = useState<any>(null);
  const [selectedResume, setSelectedResume] = useState<UserResumes>({
    id: -1,
  });
  const [newResumesList, setNewResumesList] = useState<UserResumes[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [paramTracking, setParamTracking] = useState<{
    utm_source: string | null;
    utm_medium: string | null;
    utm_campaign: string | null;
    device_type: "mobile" | "PC";
  }>();
  const layoutType = 'convert_cv'
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();

  useEffect(() => {
    const utm_source = searchParams.has("utm_source")
      ? searchParams.get("utm_source")
      : "";
    const utm_medium = searchParams.has("utm_medium")
      ? searchParams.get("utm_medium")
      : "";
    const utm_campaign = searchParams.has("utm_campaign")
      ? searchParams.get("utm_campaign")
      : "";
    const device_type = isMobile ? "mobile" : "PC";

    setParamTracking({ utm_source, utm_medium, utm_campaign, device_type });
  }, [isMobile, searchParams]);

  useEffect(() => {
    dispatch(setResumesUser(newResumesList));
  }, [newResumesList]);

  useEffect(() => {
    setSelectedResume(userResume?.[0]);

    if (step === STEP_1_INTRODUCTION) {
      setIsUploading(false);
      setIsWorking(false);
      setIsUploadSuccess(false);
      setIsSelectFile(false);
    }

    if (!!userResume && userResume.length > 0 && step === STEP_2_SELECT_FILE) {
      setFileMediaId(Number(userResume?.[0]?.id));
      setIsSelectFile(true);
    }
  }, [userResume, step]);

  useEffect(() => {
    if (openModal) {
      setStep(
        profilePopupContext.state.type ==
          PROFILE_CONTEXT_TYPE.TOGGLE_SELECT_CONVERT_CV
          ? STEP_2_SELECT_FILE
          : profilePopupContext.state.type ==
            PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_CONVERT_CV
          ? STEP_2_UPLOAD_FILE
          : STEP_1_INTRODUCTION,
      );
      setFileName("");
      setFileMediaId(null);
      setIsUploadSuccess(false);
    }
  }, [openModal]);

  useEffect(() => {
    if (profilePopupContext?.state?.type === PROFILE_CONTEXT_TYPE.POPUP_CLOSED)
      setFileCvValidate(false);
  }, [profilePopupContext?.state]);

  const handleUploadFile = async (file: File) => {
    setFileName(file.name);
    setIsUploadSuccess(false);
    if (step == STEP_2_SELECT_FILE) {
      setFileCV(file);
      setIsUploading(true);
      uploadUserProfileCV(file)
        .then((response) => {
          const mediaId = response.data.media_id;
          setFileName("");
          const foundResume = {
            id: mediaId,
            name: file.name.substring(0, file.name.lastIndexOf(".")),
            type: "upload",
            created_at: dayjs().format("HH:mm DD/MM/YYYY"),
          };
          const newResumesList: UserResumes[] = [foundResume, ...userResume];
          setNewResumesList(newResumesList);
          setSelectedResume(foundResume);
          setFileCvValidate(false);
          setIsUploadSuccess(true);
          setIsUploading(false);
          setFileMediaId(mediaId);
          setStep(STEP_3_UPLOAD_SUCCESS);
        })
        .catch(() => {
          setStep(STEP_2_SELECT_FILE);
          setFileCvValidate(true);
          setIsUploadSuccess(false);
          setIsUploading(false);
        });
    } else {
      uploadUserProfileCV(file)
        .then((response) => {
          const mediaId = response.data.media_id;
          const foundResume = {
            id: mediaId,
            name: file.name.substring(0, file.name.lastIndexOf(".")),
            type: "upload",
            created_at: dayjs().format("HH:mm DD/MM/YYYY"),
          };
          const newResumesList: UserResumes[] = [foundResume, ...userResume];

          setNewResumesList(newResumesList);
          setFileMediaId(response.data.media_id);
          setIsUploadSuccess(true);
          setFileCvValidate(false);
          setStep(STEP_3_UPLOAD_SUCCESS);
        })
        .catch((error) => {
          setStep(STEP_2_UPLOAD_FILE);
          setFileCvValidate(true);
          setIsUploadSuccess(false);
        });
    }
  };

  const handleChangeResume = (resumeId: number) => {
    setFileMediaId(resumeId);
  };

  const autoToggleOpenToWork = () => {
    getUserProfile().then((response) => {
      const userProfile = response.data as Profile;
      setUserProfile(userProfile);
      if (
        [PROFILE_STATUS.standard, PROFILE_STATUS.booster].includes(
          userProfile.status ?? "",
        )
      ) {
        profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_TO_WORK);
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_activate_open_to_work_success"),
          timer: 3000,
          timerProgressBar: true,
        });
      } else {
        // onClose();
        setStep(STEP_5_CONVERT_SUCCESS);
      }
    });
  };

  const isCreateCVOnline = () =>
    pathname === "/tao-cv-online" || pathname === "/create-cv-online";

  return (
    <Modal show={openModal} onClose={() => onClose()} size={"2xl"}>
      {step == STEP_1_INTRODUCTION ? (
        <Modal.Header>
          <div className="flex w-full max-w-[560px] flex-shrink-0 flex-grow-0 items-center justify-start overflow-hidden border-l-0 border-r-0 border-t-0 border-[#dbdbdb] bg-white md:p-4">
            <div className="relative flex flex-grow-0 flex-col items-start justify-center md:flex-shrink-0">
              <p className="flex-grow-0 text-left font-semibold text-[#292929] md:flex-shrink-0 md:text-2xl">
                {t("user_profile_auto_fill_step_1_title")}
              </p>
              <p className="flex-grow-0 text-left text-sm text-[#757575] md:flex-shrink-0 md:text-base">
                {t("user_profile_auto_fill_step_1_description")}
              </p>
            </div>
          </div>
        </Modal.Header>
      ) : (
        ""
      )}

      {[STEP_2_UPLOAD_FILE, STEP_3_UPLOAD_SUCCESS, STEP_2_SELECT_FILE].includes(
        step,
      ) ? (
        <Modal.Header>
          <div className="inline-flex min-h-[52px] flex-col justify-center">
            <div className="text-left font-semibold text-zinc-800 md:text-2xl md:leading-[30px]">
              {t("user_profile_auto_fill_step_2_title")}
            </div>
            <div className="text-left text-sm font-normal leading-snug tracking-tight text-neutral-500 md:text-base">
              {t("user_profile_auto_fill_step_2_description")}
            </div>
          </div>
        </Modal.Header>
      ) : (
        ""
      )}

      <Modal.Body className="p-3 md:p-6">
        {step == STEP_1_INTRODUCTION ? (
          <div className="m-auto flex w-full max-w-[560px] flex-grow-0 flex-col items-center justify-start gap-6 overflow-hidden bg-white md:flex-shrink-0 md:px-6 md:pb-4 md:pt-6">
            <div className="relative flex flex-grow-0 items-start justify-center gap-6 self-stretch md:flex-shrink-0">
              <div className="relative flex flex-grow-0 flex-col flex-wrap items-start gap-2 md:flex-shrink-0">
                <div className="relative flex flex-grow-0 items-center justify-center gap-2 bg-[#dd3f24] p-2 md:flex-shrink-0">
                  <p className="flex-grow-0 whitespace-nowrap text-left text-base font-bold text-white md:flex-shrink-0">
                    {t("user_profile_auto_fill_step_1_btn_step_1")}
                  </p>
                </div>
                <p className="w-full max-w-[251px] flex-grow-0 text-left text-base text-[#424242] md:flex-shrink-0">
                  {t("user_profile_auto_fill_step_1_step_1")}
                </p>
              </div>
              <div className="relative h-[100px] w-[120px] flex-shrink-0 flex-grow-0 md:h-[130px] md:w-[180px]">
                <Image
                  src={Convert1Illustrator}
                  alt={"Convert CV"}
                  height={130}
                  width={180}
                />
              </div>
            </div>
            <div className="relative flex flex-grow-0 items-center justify-center gap-6 self-stretch md:flex-shrink-0">
              <div className="relative h-[100px] w-[120px] flex-shrink-0 flex-grow-0 md:h-[130px] md:w-[197px]">
                <Image
                  src={Convert2Illustrator}
                  alt={"Convert CV"}
                  height={130}
                  width={197}
                />
              </div>
              <div className="relative flex flex-grow-0 flex-col items-start justify-center gap-2 md:flex-shrink-0">
                <div className="relative flex flex-shrink-0 flex-grow-0 items-center justify-center gap-2 bg-[#dd3f24] p-2">
                  <p className="flex-shrink-0 flex-grow-0 text-left text-base font-bold text-white">
                    {t("user_profile_auto_fill_step_1_btn_step_2")}
                  </p>
                </div>
                <p className="max-w-[231px] flex-grow-0 text-left text-base text-[#424242] md:flex-shrink-0">
                  <span className="max-w-[231px] flex-grow-0 text-left text-base text-[#424242] md:flex-shrink-0">
                    {t.rich("user_profile_auto_fill_step_1_step_2", {
                      b: (chunks) => <b>{chunks}</b>,
                    })}
                  </span>
                </p>
              </div>
            </div>
            <div className="relative flex flex-grow-0 items-center justify-center gap-6 self-stretch md:flex-shrink-0">
              <div className="relative flex w-80 flex-grow-0 flex-col items-start justify-center gap-2 md:flex-shrink-0">
                <div className="relative flex flex-grow-0 items-center justify-center gap-2 bg-[#dd3f24] p-2 md:flex-shrink-0">
                  <p className="flex-grow-0 text-left text-base font-bold text-white md:flex-shrink-0">
                    {t("user_profile_auto_fill_step_1_btn_step_3")}
                  </p>
                </div>
                <p className=" w-full max-w-[320px] flex-grow-0 self-stretch text-left text-base text-[#424242] md:flex-shrink-0">
                  {t.rich("user_profile_auto_fill_step_1_step_3", {
                    b: (chunks) => <b>{chunks}</b>,
                  })}
                </p>
              </div>
              <div className="h-[130px] w-[92px] flex-shrink-0 flex-grow-0">
                <Image
                  src={Convert3Illustrator}
                  alt={"Convert CV"}
                  height={130}
                  width={92}
                />
              </div>
            </div>
          </div>
        ) : (
          ""
        )}

        {step == STEP_2_UPLOAD_FILE ? (
          <div>
            {/* SELECT file */}
            <div
              className={
                `${!userResume || userResume.length < 1 && "hidden"} ` +
                "flex flex-grow flex-col items-center justify-center gap-2 self-stretch overflow-hidden bg-white md:p-2 mb-6 md:mb-4"
              }
            >
              {userResume && userResume.length && (
                <>
                  <label className="w-full flex-none justify-center rounded font-bold">
                    {t("user_profile_uploaded_cv_title")}
                  </label>
                  <div className="list-resumes-cv w-full rounded bg-gray-100 p-1">
                    <ListResumesApplyJob
                      resumes={userResume}
                      fileCV={fileCV}
                      transition={t}
                      valueCheck={selectedResume}
                      isLoggedIn
                      onChangeResume={(resumeId: number) =>
                        handleChangeResume(resumeId)
                      }
                      layoutType={layoutType}
                    />
                  </div>
                </>
              )}
            </div>

            {/* Upload */}
            <div className="flex flex-grow flex-col items-center justify-center gap-6 self-stretch overflow-hidden bg-white md:p-2">
              <label className="flex flex-grow flex-col items-center justify-start gap-2 self-stretch cursor-pointer">
                <div
                  onDragOver={(event) => {
                    event.preventDefault();
                  }}
                  onDrop={(event) => {
                    event.preventDefault();

                    if (event.dataTransfer.files.length <= 0) {
                      return;
                    }

                    const file = event.dataTransfer.files[0];
                    handleUploadFile(file);
                  }}
                  className="relative flex flex-grow flex-col items-center justify-center gap-3 self-stretch rounded border border-dashed border-[#ffbcb0] bg-[#feeeeb] p-3 md:p-6"
                >
                  <input
                    type={"file"}
                    className={"hidden"}
                    onChange={(event) => {
                      if (event.target.files) {
                        handleUploadFile(event.target.files[0]);
                      }
                    }}
                  />
                  <div className="relative h-12 w-12 flex-shrink-0 flex-grow-0 text-5xl text-primary">
                    <HiCloudArrowUp />
                  </div>
                  <div className="relative flex flex-grow-0 flex-col items-center justify-center md:flex-shrink-0">
                    <div className="relative flex flex-grow-0 items-center justify-start gap-2 md:flex-shrink-0">
                      <p className="flex-shrink-0 flex-grow-0 text-center text-sm font-bold text-[#292929] md:text-base">
                        {t("user_profile_auto_fill_step_2_upload_title_1")}
                      </p>
                      <p className="flex-shrink-0 flex-grow-0 text-center text-sm text-[#757575] md:text-base">
                        {t("user_profile_auto_fill_step_2_upload_title_2")}
                      </p>
                      <div className="relative flex h-6 flex-grow-0 items-center justify-center gap-2 rounded md:flex-shrink-0">
                        <p className="flex-shrink-0 flex-grow-0 text-left text-sm font-semibold text-[#dd3f24] md:text-base">
                          {t("user_profile_auto_fill_step_2_upload_title_3")}
                        </p>
                      </div>
                    </div>
                    <p className="flex-shrink-0 flex-grow-0 text-center text-xs text-[#757575]">
                      {t("user_profile_auto_fill_step_2_upload_tip_1")}
                    </p>
                    <span className={"text-xs text-[#757575]"}>
                      {t("user_profile_auto_fill_step_2_upload_tip_2")}
                    </span>
                  </div>
                </div>
              </label>
              {fileCvValidate && (
                <div className="mt-3 flex w-full items-center rounded bg-brand-100 p-5 font-semibold leading-8 text-primary">
                  <span className="mr-2 border-r border-[#ff9785] pr-2">
                    <Image
                      src="/v4/assets/images/apply_job/icon-alert-circle.png"
                      alt="icon circle"
                      width="20"
                      height="21"
                    />
                  </span>
                  <span className="text-sm md:text-base">
                    {t("detail_job_page_apply_only_type_file_popup")}
                  </span>
                </div>
              )}
            </div>
          </div>

        ) : (
          ""
        )}

        {/* Uploaded success */}
        {step == STEP_3_UPLOAD_SUCCESS ? (
          <div className="flex flex-grow flex-col items-center justify-start md:gap-6 gap-3 self-stretch overflow-hidden bg-white md:p-6 p-0">
            <div className="flex flex-shrink-0 flex-grow-0 flex-col items-start justify-start gap-2 self-stretch">
              <div className="flex flex-shrink-0 flex-grow-0 flex-col items-start justify-center gap-2 self-stretch">
                <div className="relative flex flex-shrink-0 flex-grow-0 items-center justify-start gap-2 self-stretch rounded bg-neutral-100 p-3">
                  <Image src={FileIcon} alt={"Files"} width={21} height={24} />
                  <p className="flex-grow-0 text-left text-base font-bold text-[#424242] sm:flex-shrink-0">
                    <span className="line-clamp-1">{fileName}</span>
                  </p>
                </div>
              </div>
            </div>
            <div className="flex flex-shrink-0 flex-grow-0 flex-col items-start justify-center self-stretch rounded bg-[#d9ffdf]">
              {isUploadSuccess ? (
                <div className="relative flex flex-shrink-0 flex-grow-0 items-center justify-start self-stretch px-4 py-2">
                  <Image
                    src={SuccessTickIcon}
                    alt={"Files"}
                    width={20}
                    height={20}
                  />
                  <div className="relative flex flex-grow flex-col items-start justify-start px-2">
                    <p className="w-[187px] flex-shrink-0 flex-grow-0 self-stretch text-left text-sm font-semibold text-[#292929]">
                      {t("user_profile_auto_fill_step_3_uploaded_success_text")}
                    </p>
                  </div>
                  <div className="flex h-6 w-[253px] flex-shrink-0 flex-grow-0 items-center justify-end gap-2 self-stretch"></div>
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
        ) : (
          ""
        )}

        {/* Waiting convert */}
        {step == STEP_4_CONVERTING_CV ? (
          <div className="shadow inline-flex md:h-[450px] h-[350px] w-full flex-col items-center justify-start rounded bg-white md:pb-6">
            <div className="flex shrink grow basis-0 flex-col items-center justify-center gap-6 self-stretch bg-white md:p-6 p-0">
              <Player
                autoplay
                loop
                src={loadingConvertAnimation}
                style={{ height: "162px", width: "174px" }}
              ></Player>

              <div className="flex h-14 flex-col items-center justify-start gap-2 self-stretch">
                <div className="text-center text-xl font-bold leading-7 tracking-tight text-neutral-700">
                  {t("user_profile_auto_fill_step_4_converting_title")}
                </div>
                <div className="h-5 self-stretch text-center text-sm font-normal leading-tight tracking-tight text-zinc-600">
                  {t("user_profile_auto_fill_step_4_converting_tip")}
                </div>
              </div>
            </div>
            <div className="md:w-auto w-full flex md:h-[90px] flex-col items-center justify-start gap-4">
              <div className="inline-flex items-center justify-center gap-3 self-stretch rounded bg-zinc-300 px-8 py-4">
                <div className="text-base font-bold leading-snug tracking-tight text-neutral-100">
                  {t("user_profile_auto_fill_step_5_btn_update_info")}
                </div>
              </div>
            </div>
          </div>
        ) : (
          ""
        )}

        {/* Convert success */}
        {step == STEP_5_CONVERT_SUCCESS ? (
          <div className="shadow inline-flex flex-col items-center justify-start rounded bg-white pb-6">
            <div className="flex min-h-[336px] flex-col items-center justify-center gap-6 self-stretch bg-white p-6">
              <Image
                width={174}
                height={162}
                alt={"Image"}
                className="h-[162px] w-[174px]"
                src={Convert4Illustrator}
              />
              <div className="flex flex-col items-center justify-start gap-2 self-stretch md:h-14">
                <div className="text-center text-xl font-bold leading-7 tracking-tight text-neutral-700">
                  {t("user_profile_auto_fill_step_5_title")}
                </div>
                <div className="self-stretch text-center text-sm font-normal leading-tight tracking-tight text-zinc-600 md:h-5">
                  {isMobile
                    ? t("user_profile_auto_fill_step_5_tip_mobile")
                    : t("user_profile_auto_fill_step_5_tip")}
                </div>
              </div>
            </div>
            <div className="inline-flex w-full max-w-[410px] flex-wrap items-end justify-center gap-4 sm:flex-nowrap">
              {!isCreateCVOnline() && (
                <>
                  <Button
                    id={"btn-convert-cv-complete-modal-convert-cv"}
                    type={"button"}
                    disabled={isUpdating || isDownloading}
                    onClick={() => {
                      setIsUpdating(true);
                      getUserProfile()
                        .then((response) => {
                          setUserProfile(response.data);
                          onClose();
                        })
                        .finally(() => setIsUpdating(false));
                    }}
                    accent={"primary"}
                    trailingIcon={
                      isUpdating ? (
                        <VscLoading className="h-6 w-6 animate-spin text-white" />
                      ) : (
                        ""
                      )
                    }
                    isBlock={isMobile}
                  >
                    {t("user_profile_auto_fill_step_5_btn_update_info")}
                  </Button>

                  <Button
                    type={"button"}
                    accent={"outline"}
                    disabled={isDownloading || isUpdating}
                    onClick={async () => {
                      setIsDownloading(true);
                      getUserProfile()
                        .then((response) => {
                          setUserProfile(response.data);
                          downloadUserProfileCv(response.data).finally(() =>
                            setIsDownloading(false),
                          );
                        })
                        .catch(() => setIsDownloading(false));
                    }}
                    trailingIcon={
                      isDownloading ? (
                        <VscLoading className="text-red h-6 w-6 animate-spin" />
                      ) : (
                        ""
                      )
                    }
                    isBlock={isMobile}
                  >
                    {t("user_profile_auto_fill_step_5_btn_download_cv")}
                  </Button>
                </>
              )}

              {isCreateCVOnline() && (
                <>
                  <Link href="/users/profile"
                    className="flex w-full items-center justify-center rounded border border-primary-300 px-4 py-[6px] text-sm text-primary-300 transition-all bg-primary-300 text-white md:px-2 md:py-4 md:text-base md:font-bold">
                    {t("user_profile_auto_fill_step_5_btn_update_info")}
                  </Link>
                  <Button
                    type={"button"}
                    accent={"outline"}
                    disabled={isDownloading || isUpdating}
                    onClick={async () => {
                      setIsDownloading(true);
                      getUserProfile()
                        .then((response) => {
                          setUserProfile(response.data);
                          downloadUserProfileCv(response.data).finally(() =>
                            setIsDownloading(false),
                          );
                        })
                        .catch(() => setIsDownloading(false));
                    }}
                    trailingIcon={
                      isDownloading ? (
                        <VscLoading className="text-red h-6 w-6 animate-spin" />
                      ) : (
                        ""
                      )
                    }
                    isBlock={true}
                  >
                    {t("user_profile_auto_fill_step_5_btn_download_cv")}
                  </Button>
                </>
              )}
            </div>
          </div>
        ) : (
          ""
        )}

        {step == STEP_6_CONVERT_FAIL && (
          <div className="shadow inline-flex h-[450px] w-full flex-col items-center justify-start rounded bg-white pb-6">
            <div className="flex min-h-[336px] flex-col items-center justify-center gap-6 self-stretch bg-white p-6">
              <div className="relative h-[200px] w-[227.48px]">
                <Image src={GirlQuestion} alt={"Image"} />
              </div>
              <div className="flex h-14 flex-col items-center justify-start gap-2 self-stretch">
                <div className="text-center text-xl font-bold leading-7 tracking-tight text-neutral-700">
                  {t("user_profile_auto_fill_step_6_title")}
                </div>
                <div className="h-5 self-stretch text-center text-sm font-normal leading-tight tracking-tight text-zinc-600">
                  {t("user_profile_auto_fill_step_6_tip")}
                </div>
              </div>
            </div>
            <Button
              type={"button"}
              onClick={() => setStep(STEP_1_INTRODUCTION)}
              accent={"primary"}
            >
              {t("user_profile_auto_fill_step_6_btn_retry")}
            </Button>
          </div>
        )}

        {step == STEP_7_CONVERT_FAIL && (
          <div className="shadow inline-flex w-full flex-col items-center justify-start rounded bg-white pb-6">
            <div className="flex flex-col items-center justify-center gap-6 self-stretch bg-white p-6">
              <div className="relative h-[128.659px] w-[165.055px] md:h-[200px] md:w-[275.23px]">
                <Image src={FailSendMail} alt={"Image"} />
              </div>
              <div className="flex flex-col items-center justify-start gap-2 self-stretch">
                <div className="text-center text-lg font-bold leading-7 tracking-tight text-neutral-700 md:text-xl">
                  {t("user_profile_auto_fill_step_7_title")}
                </div>
                <div className="text-center text-xs font-normal leading-tight tracking-tight text-zinc-600 md:text-sm">
                  {t("user_profile_auto_fill_step_7_tip")}
                </div>
              </div>
            </div>
            <div className="m-auto w-full max-w-[410px]">
              <Button
                type={"button"}
                onClick={() => setStep(STEP_1_INTRODUCTION)}
                accent={"primary"}
                isBlock
              >
                {t("user_profile_auto_fill_step_7_btn_retry")}
              </Button>
            </div>
          </div>
        )}
      </Modal.Body>

      {step == STEP_1_INTRODUCTION ? (
        <Modal.Footer>
          <div className="flex w-full flex-shrink-0 flex-grow-0 items-center justify-end gap-4 overflow-hidden border-b-0 border-l-0 border-r-0 border-[#dbdbdb] bg-white">
            <Button accent={"ghost"} type={"button"} onClick={() => onClose()}>
              {t("user_profile_auto_fill_step_1_btn_exit")}
            </Button>
            <Button
              accent={"primary"}
              type={"button"}
              onClick={() => setStep(STEP_2_UPLOAD_FILE)}
              id={"btn-upload-now-modal-convert-cv"}
            >
              {t("user_profile_auto_fill_step_1_btn_upload_now")}
            </Button>
          </div>
        </Modal.Footer>
      ) : (
        ""
      )}

      {[STEP_2_UPLOAD_FILE, STEP_3_UPLOAD_SUCCESS, STEP_2_SELECT_FILE].includes(
        step,
      ) ? (
        <Modal.Footer>
          <div className="inline-flex w-full items-center justify-end gap-4 bg-white">
            <Button
              type={"button"}
              accent={"primary"}
              onClick={async () => {
                if (fileMediaId === null) return;
                setIsWorking(true);
                setStep(STEP_4_CONVERTING_CV);

                parseUserProfileUploadedCv(fileMediaId, paramTracking)
                  .then((response) => {
                    const timee = setInterval(() => {
                      getUserProfileParsingProcess(fileMediaId)
                        .then((response) => {
                          
                          const { data } = response;
                          if (data.status == "failed") {
                            setStep(STEP_6_CONVERT_FAIL);
                            clearInterval(timee);
                          }

                          if (data.status == "retry") {
                            setStep(STEP_7_CONVERT_FAIL);
                            clearInterval(timee);
                          }

                          if (data.status == "success") {
                            gtag({
                              event: "convert_cv_complete",
                            });

                            clearInterval(timee);
                            if (
                              [
                                PROFILE_CONTEXT_TYPE.TOGGLE_SELECT_CONVERT_CV,
                                PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_CONVERT_CV,
                              ].includes(profilePopupContext.state.type ?? "")
                            ) {
                              autoToggleOpenToWork();
                            } else {
                              setStep(STEP_5_CONVERT_SUCCESS);
                            }
                          }
                        })
                        .catch((err) => {
                          console.log(err);
                          clearInterval(timee);
                          setFileMediaId(null);
                          setFileName("");
                          setIsUploadSuccess(false);
                          setStep(STEP_6_CONVERT_FAIL);
                        })
                        .finally(() => setIsWorking(false));
                    }, 10000);
                  })
                  .finally(() => setIsWorking(false));
                setStep(STEP_4_CONVERTING_CV);
              }}
              disabled={
                (!isUploadSuccess && !fileMediaId && isSelectFile) || isWorking || isUploading
              }
              trailingIcon={
                isWorking ? (
                  <VscLoading className="h-6 w-6 animate-spin text-white" />
                ) : (
                  ""
                )
              }
            >
              {t("user_profile_auto_fill_step_3_btn_convert_now")}
            </Button>
          </div>
        </Modal.Footer>
      ) : (
        ""
      )}
    </Modal>
  );
};

export default TheConvertCVModal;
