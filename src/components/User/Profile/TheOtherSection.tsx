import React, { FC, useState } from "react";
import { HiPencil, HiTrash } from "react-icons/hi2";
import { useTranslations } from "next-intl";
import useUserProfile from "@/hooks/useUserProfile";
import TheOtherFormModal from "./TheOtherFormModal";
import { ReadMoreMobile } from "@/components/User/Profile/ReadMoreMobile";

const TheOtherSection: FC<{
  onDelete: () => void;
}> = ({ onDelete }) => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();

  return (
    <>
      <section
        id={"extra-information"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex items-center p-4"}>
          <div className={"grow"}>
            <h2 className={"md:text-2xl text-xl font-bold"}>
              {t("user_profile_other_title")}
            </h2>
            <div className={"text-gray-400"}></div>
          </div>
          <div className={"flex-none"}>
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>
            <button
              className={"ml-4 h-5 w-5"}
              type={"button"}
              onClick={() => onDelete()}
            >
              <HiTrash />
            </button>
          </div>
        </div>
        <div className={"card-body flex flex-col divide-y px-6"}>
          {userProfile && userProfile.additionals
            ? userProfile.additionals.map((additional, index) => (
                <div key={index} className={"flex pt-4 pb-2"}>
                  <div className={"grow"}>
                    <p className={"font-bold"}>{additional.additional}</p>
                    <div
                      className={"prose hidden md:block text-gray-400"}
                      dangerouslySetInnerHTML={{
                        __html: additional.description,
                      }}
                    ></div>
                    <ReadMoreMobile>{additional.description}</ReadMoreMobile>
                  </div>
                </div>
              ))
            : ""}
        </div>
      </section>
      {userProfile ? (
        <TheOtherFormModal
          openModal={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheOtherSection;
