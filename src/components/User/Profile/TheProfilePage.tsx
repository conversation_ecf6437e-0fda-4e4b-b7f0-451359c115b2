"use client";

import React, {
  FC,
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useReducer,
  useState,
} from "react";
import dynamic from "next/dynamic";
const TheBasicInformationSection = dynamic(
  () => import("@/components/User/Profile/TheBasicInformationSection"),
);
const TheSummarySection = dynamic(
  () => import("@/components/User/Profile/TheSummarySection"),
);
import TheSkillsSection from "@/components/User/Profile/TheSkillsSection";
import TheWorkExperienceSection from "@/components/User/Profile/TheWorkExperienceSection";
import TheEducationSection from "@/components/User/Profile/TheEducationSection";
import TheProjectSection from "@/components/User/Profile/TheProjectSection";
import useUserProfile from "@/hooks/useUserProfile";
import Profile from "@/types/profile";
import { getUserProfile, patchUserProfile } from "@/services/userAPI";
const TheAddMoreSection = dynamic(
  () => import("@/components/User/Profile/TheAddMoreSection"),
);
import TheLanguagesSection from "@/components/User/Profile/TheLanguagesSection";
import TheCertificateSection from "@/components/User/Profile/TheCertificateSection";
import TheHobbySection from "@/components/User/Profile/TheHobbySection";
import TheReferenceSection from "@/components/User/Profile/TheReferenceSection";
import TheActivitySection from "@/components/User/Profile/TheActivitySection";
import TheOtherSection from "@/components/User/Profile/TheOtherSection";
import ToastNotification from "@/components/Swal/ToastNotification";
import { useTranslations } from "next-intl";
import {
  MissingSection,
  PopupContextType,
  PopupStateType,
} from "@/types/userProfile";
import {
  INITIAL_POPUP_STATE,
  STATUS_MISSING_SECTIONS,
} from "@/contansts/userProfiles";
import { fetchResumesApi } from "@/services/resumeAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import { setResumesUser } from "@/store/slices/userSlice";
import { UserResumes } from "@/types/resume";
import NavUsers from "@/components/User/Common/NavUsers";

const TheSectionOTW = dynamic(
  () => import("@/components/User/Profile/TheSectionOTW"),
);
import TheTopdevCVChoose from "@/components/User/Profile/TheTopdevCVChoose";
import { useSearchParams } from "next/navigation";
import AlertJobPrevious from "@/components/Alert/AlertJobPrevious";
import calculateCompletionPercentage from "@/components/User/Profile/precent-config/count-infomation-precent";
import TheInformationProcessBar from "./TheInformationProcessBar";
import TheInformationProcessBarMobile from "./TheInformationProcessBarMobile";

const ProfilePopupContext = createContext<PopupContextType | undefined>({
  state: INITIAL_POPUP_STATE,
  dispatch: () => {},
});

const ProfilePopupReducer = (
  state: PopupStateType = INITIAL_POPUP_STATE,
  action: string,
): PopupStateType => {
  return { ...state, type: action };
};

const ProfilePopupProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(
    ProfilePopupReducer,
    INITIAL_POPUP_STATE,
  );

  useEffect(() => {
    const hanldCloseModal = (event: any) => {
      if (event?.key === "Escape") {
        dispatch(INITIAL_POPUP_STATE.type as string);
      }
    };
    document.body.addEventListener("keydown", (event) =>
      hanldCloseModal(event),
    );

    return () => {
      document.body.removeEventListener("keydown", hanldCloseModal);
    };
  }, []);

  return (
    <ProfilePopupContext.Provider value={{ state, dispatch }}>
      {children}
    </ProfilePopupContext.Provider>
  );
};

export const useProfilePopupContext = () => {
  const profilePopupContext = useContext(ProfilePopupContext);
  if (typeof profilePopupContext === "undefined") {
    throw Error("Tab context is not available!");
  }
  return profilePopupContext;
};

const TheProfilePage: FC = () => {
  const [isClient, setIsClient] = useState(false);
  const [userProfile, setUserProfile] = useUserProfile();
  const [showLanguage, setShowLanguage] = useState(false);
  const [showCertificate, setShowCertificate] = useState(false);
  const [showHobby, setShowHobby] = useState(false);
  const searchParams = useSearchParams();
  const [showReference, setShowReference] = useState(false);
  const [showActivity, setShowActivity] = useState(false);
  const [showAdditionals, setShowAdditionals] = useState(false);
  const dispatch = useAppDispatch();
  const t = useTranslations();

  //Params Job
  const params: string = searchParams.toString();
  const paramJob = new URLSearchParams(params);
  const jobTitle = paramJob.get("job_title");
  const jobUrl = paramJob.get("job_url");
  const jobCompany = paramJob.get("job_company");
  const jobId = paramJob.get("job_id");
  const [isShow, setIsShow] = useState(true);
  const handleHideAlert = () => {
    setIsShow(false);
  };
  //Update User Profile
  const user = useAppSelector((state) => state.user.user);
  const [missingSections, setMissingSections] = useState<MissingSection[]>([]);
  const [missingType, setMissingType] = useState<string>("");
  // Handle status missing
  useEffect(() => {
    let status: MissingSection[] = [];
    if (
      !userProfile ||
      !userProfile.status ||
      userProfile.status == "new_star"
    ) {
      status = STATUS_MISSING_SECTIONS.new_star.filter(
        (item) => !userProfile.completed_sections?.includes(item.section),
      );
      setMissingType("newStarMissing");
    } else if (userProfile.status == "standard") {
      status = STATUS_MISSING_SECTIONS.standard.filter(
        (item) => !userProfile.completed_sections?.includes(item.section),
      );
      setMissingType("standardMissing");
    } else if (userProfile.status == "booster") {
      setMissingType("boosterMissing");
    }
    setMissingSections(status); // Update the state with the missing sections
  }, [userProfile]);

  useEffect(() => {
    // TODO: login if not logged in
    if (false) {
      return;
    }

    getUserProfile().then((response) => {
      setUserProfile(response.data as Profile);
    });
    fetchResumesApi({
      ready_to_apply: true,
      page_size: 10,
    }).then((data) => {
      if (data?.status === 200) {
        const dataFilterNotDeleted = data?.data?.data?.filter(
          (resume: UserResumes) => resume?.features?.apply,
        ) as Array<UserResumes>;
        dispatch(setResumesUser(dataFilterNotDeleted));
      }
    });

    setIsClient(true);
  }, []);

  const isShowAddButton =
    !(showLanguage || (userProfile?.languages?.length ?? 0) > 0) ||
    !(showCertificate || (userProfile?.certificates?.length ?? 0) > 0) ||
    !(showHobby || (userProfile?.interests?.length ?? 0) > 0) ||
    !(showReference || (userProfile?.references?.length ?? 0) > 0) ||
    !(showActivity || (userProfile?.activities?.length ?? 0) > 0) ||
    !(showAdditionals || (userProfile?.additionals?.length ?? 0) > 0);

  return (
    <>
      <div className={"relative bg-gray-light pb-7 md:pb-48"}>
        <NavUsers />
        <ProfilePopupProvider>
          <div className="sticky z-10 flex-col items-end col-span-12 gap-4 md:container md:relative md:top-0 md:col-span-3 md:flex">
            {jobTitle && jobUrl && jobCompany && jobId && isShow && (
              <AlertJobPrevious
                jobCompany={jobCompany}
                jobTitle={jobTitle}
                jobUrl={jobUrl}
                jobId={jobId}
                closeAlert={handleHideAlert}
                srcPage={"userprofile"}
                mediumPage={"floatingbanner"}
                isShowApplyJob={true}
                source={"ApplyNow"}
              />
            )}
          </div>
          <div className="block sm:hidden">
            <TheTopdevCVChoose />
          </div>
          <section
            className={
              "mt-6 grid grid-cols-12 gap-6 md:container md:mt-0 md:p-0"
            }
          >
            <aside
              className={"col-span-12 flex-col gap-4 md:col-span-3 md:flex"}
            >
              <div className="hidden sm:block">
                <TheInformationProcessBar
                  completion={userProfile.complete_percent ?? 0}
                  missingSections={missingSections}
                  missingType={missingType}
                />
              </div>
              <div className="block sm:hidden">
                <TheInformationProcessBarMobile
                  completion={userProfile.complete_percent ?? 0}
                  missingSections={missingSections}
                  missingType={missingType}
                />
              </div>

              {/* <TheOpenToWorkCard /> */}
              {/* temporarily hidden  */}
              {/* <TheQuickLinkCard /> */}
            </aside>
            <div className={"col-span-12 flex flex-col gap-6 md:col-span-9"}>
              {/* <TheBasicInformationSectionMobile /> */}
              {/*Feature with Topdev cv PC  */}
              <div className="hidden sm:block">
                <TheTopdevCVChoose />
              </div>

              <TheBasicInformationSection
                completion={userProfile.complete_percent ?? 0}
                missingSections={missingSections}
                missingType={missingType}
              />
              {/* <TheProfileStatusSection /> */}
              <TheSummarySection
                completion={userProfile.complete_percent ?? 0}
              />
              <TheSkillsSection />
              <TheWorkExperienceSection />
              <TheEducationSection />
              <TheProjectSection />

              {/* Additional section */}
              {(showLanguage ||
                (userProfile &&
                  userProfile.languages &&
                  Array.isArray(userProfile?.languages) &&
                  userProfile.languages.length > 0)) && (
                <TheLanguagesSection
                  onDelete={() => {
                    setShowLanguage(false);

                    patchUserProfile({
                      languages: [],
                    }).then((response) => {
                      ToastNotification({
                        icon: "success",
                        title: t("user_profile_save_success_title"),
                        description: t(
                          "user_profile_toast_save_success_message",
                          { section: t("user_profile_language_title") },
                        ),
                        timer: 2000,
                      });

                      setUserProfile(response.data.data);
                    });
                  }}
                />
              )}

              {(showCertificate ||
                (userProfile &&
                  userProfile.certificates &&
                  Array.isArray(userProfile?.certificates) &&
                  userProfile.certificates.length > 0)) && (
                <TheCertificateSection
                  onDelete={() => {
                    setShowCertificate(false);

                    patchUserProfile({
                      certificates: [],
                    }).then((response) => {
                      ToastNotification({
                        icon: "success",
                        title: t("user_profile_save_success_title"),
                        description: t(
                          "user_profile_toast_save_success_message",
                          { section: t("user_profile_certificate_title") },
                        ),
                        timer: 2000,
                      });

                      setUserProfile(response.data.data);
                    });
                  }}
                />
              )}

              {(showHobby ||
                (userProfile &&
                  userProfile.interests &&
                  userProfile.interests.length > 0)) && (
                <TheHobbySection
                  onDelete={() => {
                    setShowHobby(false);

                    patchUserProfile({
                      interests: [],
                    }).then((response) => {
                      ToastNotification({
                        icon: "success",
                        title: t("user_profile_save_success_title"),
                        description: t(
                          "user_profile_toast_save_success_message",
                          { section: t("user_profile_hobby_title") },
                        ),
                        timer: 2000,
                      });

                      setUserProfile(response.data.data);
                    });
                  }}
                />
              )}

              {(showReference ||
                (userProfile &&
                  userProfile.references &&
                  Array.isArray(userProfile?.references) &&
                  userProfile.references.length > 0)) && (
                <TheReferenceSection
                  onDelete={() => {
                    setShowReference(false);

                    patchUserProfile({
                      references: [],
                    }).then((response) => {
                      ToastNotification({
                        icon: "success",
                        title: t("user_profile_save_success_title"),
                        description: t(
                          "user_profile_toast_save_success_message",
                          { section: t("user_profile_reference_title") },
                        ),
                        timer: 2000,
                      });

                      setUserProfile(response.data.data);
                    });
                  }}
                />
              )}

              {(showActivity ||
                (userProfile &&
                  userProfile.activities &&
                  Array.isArray(userProfile?.activities) &&
                  userProfile.activities.length > 0)) && (
                <TheActivitySection
                  onDelete={() => {
                    setShowActivity(false);

                    patchUserProfile({
                      activities: [],
                    }).then((response) => {
                      ToastNotification({
                        icon: "success",
                        title: t("user_profile_save_success_title"),
                        description: t(
                          "user_profile_toast_save_success_message",
                          { section: t("user_profile_activity_title") },
                        ),
                        timer: 2000,
                      });

                      setUserProfile(response.data.data);
                    });
                  }}
                />
              )}

              {(showAdditionals ||
                (userProfile &&
                  userProfile.additionals &&
                  Array.isArray(userProfile?.additionals) &&
                  userProfile.additionals.length > 0)) && (
                <TheOtherSection
                  onDelete={() => {
                    setShowAdditionals(false);

                    patchUserProfile({
                      additionals: [],
                    }).then((response) => {
                      ToastNotification({
                        icon: "success",
                        title: t("user_profile_save_success_title"),
                        description: t(
                          "user_profile_toast_save_success_message",
                          { section: t("user_profile_other_title") },
                        ),
                        timer: 2000,
                      });

                      setUserProfile(response.data.data);
                    });
                  }}
                />
              )}

              {isShowAddButton && (
                <TheAddMoreSection
                  missingType={missingType}
                  setShowLanguage={setShowLanguage}
                  showLanguage={
                    showLanguage || (userProfile?.languages?.length ?? 0) > 0
                  }
                  setShowCertificate={setShowCertificate}
                  showCertificate={
                    showCertificate ||
                    (userProfile?.certificates?.length ?? 0) > 0
                  }
                  setShowHobby={setShowHobby}
                  showHobby={
                    showHobby || (userProfile?.interests?.length ?? 0) > 0
                  }
                  setShowReference={setShowReference}
                  showReference={
                    showReference || (userProfile?.references?.length ?? 0) > 0
                  }
                  setShowActivity={setShowActivity}
                  showActivity={
                    showActivity || (userProfile?.activities?.length ?? 0) > 0
                  }
                  setShowAdditionals={setShowAdditionals}
                  showAdditionals={
                    showAdditionals ||
                    (userProfile?.additionals?.length ?? 0) > 0
                  }
                />
              )}

              {/* End Additional section */}
            </div>
          </section>
        </ProfilePopupProvider>
      </div>
    </>
  );
};

export default TheProfilePage;
