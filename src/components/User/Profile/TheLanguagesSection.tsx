"use client";

import React, { FC, useState } from "react";
import { Hi<PERSON><PERSON>cil, HiTrash } from "react-icons/hi2";
import useUserProfile from "@/hooks/useUserProfile";
import TheLanagugesFormModal from "@/components/User/Profile/TheLanguagesFormModal";
import { useTranslations } from "next-intl";
import { isMobile } from "react-device-detect";

const TheLanguagesSection: FC<{
  onDelete: () => void;
}> = ({ onDelete }) => {
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();
  const t = useTranslations();

  const LANG_OPTIONS_LEVEL = [
    {
      value: "Beginner",
      label: t("user_profile_language_modal_field_level_option_beginer"),
    },
    {
      value: "Intermediate",
      label: t("user_profile_language_modal_field_level_option_intermediate"),
    },
    {
      value: "Proficient",
      label: t("user_profile_language_modal_field_level_option_proficient"),
    },
    {
      value: "Fluent",
      label: t("user_profile_language_modal_field_level_option_fluent"),
    },
    {
      value: "Native",
      label: t("user_profile_language_modal_field_level_option_native"),
    },
  ];

  const LANG_OPTIONS = [
    {
      value: "English",
      label: t("user_profile_language_modal_field_language_option_english"),
    },
    {
      value: "Japan",
      label: t("user_profile_language_modal_field_language_option_japan"),
    },
    {
      value: "Chinese",
      label: t("user_profile_language_modal_field_language_option_chinese"),
    },
    {
      value: "Korean",
      label: t("user_profile_language_modal_field_language_option_korea"),
    },
    {
      value: "French",
      label: t("user_profile_language_modal_field_language_option_french"),
    },
    {
      value: "German",
      label: t("user_profile_language_modal_field_language_option_german"),
    },
  ];

  const converValueLanguage = (value: string) =>
    LANG_OPTIONS.filter((lang) => lang.value == value)?.[0]?.label;
  const converValueFluent = (value: string) =>
    LANG_OPTIONS_LEVEL.filter((lang) => lang.value == value)?.[0]?.label;

  return (
    <>
      <section
        id={"extra-information"}
        className={"divide-y divide-gray-200 rounded bg-white"}
        onClick={() => isMobile && setIsFormModalOpen(true)}
      >
        <div className={"card-title flex items-center p-4"}>
          <div className={"grow"}>
            <h2 className={"md:text-2xl text-xl font-bold"}>
              {t("user_profile_language_title")}
            </h2>
            <div className={"text-gray-400"}></div>
          </div>
          <div className={"flex-none"}>
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>

            <button
              className={"ml-4 h-5 w-5"}
              type={"button"}
              onClick={() => onDelete()}
            >
              <HiTrash />
            </button>
          </div>
        </div>

        <div className={"card-body flex flex-col divide-y md:px-6 px-4"}>
          {userProfile && userProfile.languages
            ? userProfile.languages.map((language, index) => (
                <div key={index} className={"flex justify-between p-4"}>
                  <div className={"font-bold"}>
                    {converValueLanguage(language.language as string)}
                  </div>
                  <div>{converValueFluent(language.fluent as string)}</div>
                </div>
              ))
            : ""}
        </div>
      </section>

      {userProfile ? (
        <TheLanagugesFormModal
          openModal={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheLanguagesSection;
