import { MissingSection } from "@/types/userProfile";
import React, { FC } from "react";
import { useTranslations } from "next-intl";
import { useProfilePopupContext } from "@/components/User/Profile/TheProfilePage";
import { HiChevronRight } from "react-icons/hi2";
import Link from "next/link";
import Profile from "@/types/profile";
import { sectionPercentages } from "@/components/User/Profile/precent-config/BadgeRemindMissing";
import { classNames } from "@/utils";

type TheInformationRemindMissingProps = {
  missingSections: MissingSection[];
  missingType: string;
  userProfile: Profile;
};

const TheInformationRemindMissing: FC<TheInformationRemindMissingProps> = ({
  missingSections,
  missingType,
  userProfile,
}) => {
  const t = useTranslations();
  const profilePopupContext = useProfilePopupContext();
  const {
    email,
    phone,
    province_name: cityOrProvince,
    display_name: fullName,
    position,
    skills,
    years_of_exp: yearOfExperience,
  } = userProfile;
  const technicalSkills = skills?.technical_skills;

  const filteredSections = missingSections.filter((section) => {
    const sectionMap: { [key: string]: any } = {
      email,
      phone,
      cityOrProvince,
      fullName,
      position,
      technicalSkills,
      yearOfExperience,
    };
    return !sectionMap[section.section];
  });
  const renderSectionBadge = (section: MissingSection["section"]) => {
    const percentage = sectionPercentages[section];
    if (!percentage) return null;

    return (
      <span
        className={classNames(
          `my-auto cursor-pointer rounded-lg px-2 text-xs font-bold text-white ${
            missingType === "newStarMissing"
              ? "bg-blue-500 "
              : missingType === "standardMissing"
              ? "bg-green-500"
              : "bg-green-500"
          }`,
        )}
      >
        {percentage}
      </span>
    );
  };
  const renderMissingTypeContent = () => {
    switch (missingType) {
      case "newStarMissing":
        return (
          <>
            <div className="flex flex-col text-base font-normal sub-content text-neutral-600">
              <span className="sub-conntent-first">
                {t.rich("information_missing_basic_info", {
                  b: (chunk) => (
                    <b className={"font-bold text-neutral-600"}>{chunk}</b>
                  ),
                })}
              </span>
              <span className="sub-content-second">
                {t("information_missing_list")}
              </span>
            </div>
            {filteredSections.length > 0 &&
              filteredSections.map((item, index) => (
                <div
                  className="inline-flex py-1 pl-2 pr-1 mt-2 mr-2 rounded item-center bg-blue-50"
                  key={index}
                  onClick={() => {
                    profilePopupContext.dispatch(item.action);
                  }}
                >
                  <span className="mr-2 text-sm font-bold cursor-pointer bg-blue-50 text-neutral-900">
                    {t(item.title)}
                  </span>
                  {renderSectionBadge(item.section)}
                </div>
              ))}
          </>
        );
      case "standardMissing":
        return (
          <>
            <div className="flex flex-col text-base font-normal sub-content text-neutral-600">
              <span className="sub-conntent-first">
                {t.rich("information_missing_experience_info", {
                  b: (chunk) => (
                    <b className={"font-bold text-neutral-600"}>{chunk}</b>
                  ),
                })}
              </span>
              <span className="sub-conntent-first">
                {t("information_missing_list")}
              </span>
            </div>
            {missingSections.length > 0 && (
              <div className="flex flex-row flex-wrap w-auto h-auto gap-2 mt-4 infomation-missing-type--standardMissing">
                {missingSections.map((item, index) => (
                  <div
                    className="flex gap-2 py-1 pl-2 pr-1 rounded item-cemter bg-green-50"
                    key={index}
                    onClick={() => {
                      profilePopupContext.dispatch(item.action);
                    }}
                  >
                    <span className="text-sm font-bold cursor-pointer bg-green-50 text-neutral-900 hover:underline">
                      {t(item.title)}
                    </span>
                    {renderSectionBadge(item.section)}
                  </div>
                ))}
              </div>
            )}
          </>
        );
      case "boosterMissing":
        return (
          <>
            <div className="flex flex-col sub-content">
              <span className="text-base font-bold text-booster">
                {t("user_profile_lucky_title")}
              </span>
              <span className="font-normal">
                {t.rich("user_profile_lucky_description", {
                  b: (chunk) => (
                    <b className={"font-bold text-neutral-600"}>{chunk}</b>
                  ),
                })}
              </span>
            </div>
          </>
        );
      default:
        return null;
    }
  };

  return <div>{renderMissingTypeContent()}</div>;
};

export default TheInformationRemindMissing;
