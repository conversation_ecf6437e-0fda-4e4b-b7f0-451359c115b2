import useUserProfile from "@/hooks/useUserProfile";
import dynamic from "next/dynamic";
import { useAppSelector } from "@/store";
import Image from "next/image";
import React, { FC, useState } from "react";
import NoAvatar from "@/assets/images/no-avatar.svg";
import { classNames } from "@/utils";
import { MissingSection } from "@/types/userProfile";
const TheInformationRemindMissing = dynamic(
  () => import("@/components/User/Profile/TheInformationRemindMissing"),
);
const TheSectionOTW = dynamic(
  () => import("@/components/User/Profile/TheSectionOTW"),
);
const TheTipsBannerBooster = dynamic(
  () => import("@/components/User/Profile/TheTipsBannerBooster"),
);

type TheInformationProcessBarProps = {
  completion: number;
  missingSections: MissingSection[];
  missingType: string;
};
const TheInformationProcessBar: React.FC<TheInformationProcessBarProps> = ({
  completion,
  missingSections,
  missingType,
}) => {
  const user = useAppSelector((state) => state.user.user);
  const [userProfile] = useUserProfile();
  const [isBannerTipBoosterVisible, setIsBannerTipBoosterVisible] =
    useState<boolean>(true);

  return (
    <>
      <div className="w-full h-auto p-4 bg-white rounded infomation-card">
        <div className="flex gap-2 infomation-card-data">
          <div className="infomation-card__process-bar">
            <div className="relative size-40 w-28">
              <svg
                className="size-full rotate-[135deg]"
                viewBox="0 0 36 36"
                xmlns="http://www.w3.org/2000/svg"
              >
                <defs>
                  <linearGradient
                    id="gradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="100%"
                  >
                    <stop
                      offset="0%"
                      style={{ stopColor: "#ff7b42", stopOpacity: 1 }}
                    />
                    <stop
                      offset="100%"
                      style={{ stopColor: "#d34127", stopOpacity: 1 }}
                    />
                  </linearGradient>
                </defs>
                <circle
                  cx="18"
                  cy="18"
                  r="16"
                  fill="none"
                  className="text-gray-200 stroke-current dark:text-neutral-700"
                  strokeWidth="1.5"
                  strokeDasharray="75 100"
                  strokeLinecap="round"
                ></circle>

                <circle
                  cx="18"
                  cy="18"
                  r="16"
                  fill="none"
                  stroke="url(#gradient)"
                  className={classNames(
                    completion < 50
                      ? "stroke-current text-brand-600"
                      : completion >= 50 && completion < 100
                      ? "stroke-current text-blue-600"
                      : completion === 100 && missingType !== "luckyStarMissing"
                      ? "stroke-current text-green-500"
                      : "",
                  )}
                  strokeWidth="1.5"
                  strokeDasharray={`${Math.min(completion, 75)} 100`}
                  strokeLinecap="round"
                ></circle>
              </svg>

              <div className="absolute items-center text-center transform -translate-x-1/2 -translate-y-1/2 start-1/2 top-1/2">
                <div className={"md:h-[5.4375rem] md:w-[5.4375rem]"}>
                  <Image
                    src={userProfile?.avatar_url ?? NoAvatar}
                    width={88}
                    height={88}
                    alt={"Avatar"}
                    className={"w-[88px h-[88px] rounded-full"}
                    priority={
                      !!userProfile?.avatar_url
                        ? !!userProfile?.avatar_url
                        : false
                    }
                  />
                  <span
                    className={classNames(
                      completion < 50
                        ? "text-brand-600"
                        : completion >= 50 && completion < 100
                        ? "text-blue-600"
                        : completion === 100
                        ? "text-green-500"
                        : "",
                      "absolute start-1/2 top-24 flex w-full -translate-x-1/2 -translate-y-1/2 items-center justify-center text-center text-base font-bold",
                    )}
                  >
                    {completion}%
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center infomation-card__content">
            <div className="flex flex-col text-neutral-950">
              <span className="text-base font-bold">
                {userProfile?.display_name ?? user.full_name}
              </span>
              <span className="text-base font-normal text-neutral-600">
                {userProfile?.position ?? user.recent_position}
              </span>
            </div>
          </div>
        </div>
        <hr className="mt-4 mb-4 text-neutral-100" />
        <TheInformationRemindMissing
          userProfile={userProfile}
          missingSections={missingSections}
          missingType={missingType}
        />
      </div>
      <TheSectionOTW />

      {missingType === "boosterMissing" && (
        <TheTipsBannerBooster
          isBannerTipBoosterVisible={isBannerTipBoosterVisible}
          onCloseTips={() => setIsBannerTipBoosterVisible(false)}
        />
      )}
    </>
  );
};

export default TheInformationProcessBar;
