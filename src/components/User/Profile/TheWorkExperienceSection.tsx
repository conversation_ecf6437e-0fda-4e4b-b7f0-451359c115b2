"use client";
import React, { FC, useEffect, useState } from "react";
import { HiDocumentText, HiPencil } from "react-icons/hi2";
import { TheWorkExperienceFormModal } from "@/components/User/Profile/index";
import Image from "next/image";
import SummaryIllustrator from "@/assets/images/illustrators/work-experience.svg";
import useUserProfile from "@/hooks/useUserProfile";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { formatIfValid } from "@/utils/date";
import { getIfNotEmpty } from "@/utils/string";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";
import { ReadMoreMobile } from "@/components/User/Profile/ReadMoreMobile";

const TheWorkExperienceSection: FC = () => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();
  const profilePopupContext = useProfilePopupContext();

  useEffect(() => {
    setIsFormModalOpen(
      profilePopupContext.state.type == PROFILE_CONTEXT_TYPE.POPUP_EXPERIENCES,
    );
  }, [profilePopupContext]);

  const isExperiencesExist =
    userProfile?.experiences && Array.isArray(userProfile?.experiences) && userProfile.experiences.length > 0;

  return (
    <>
      <section
        id={"work-experiences"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex p-4 md:p-6"}>
          <div
            className={classNames(
              isExperiencesExist
                ? "grow"
                : "mr-2 w-[calc(100%_-_85px)] md:mr-0 md:w-auto md:grow",
            )}
          >
            <h2 className={"text-xl font-bold text-black md:text-2xl"}>
              {t("user_profile_work_experience_title")}
            </h2>
            <div
              className={classNames(
                isExperiencesExist ? "hidden md:block" : "",
                "text-sm text-gray-400 md:text-base",
              )}
            >
              {t("user_profile_work_experience_description")}
            </div>
          </div>
          <div
            className={classNames(
              isExperiencesExist
                ? "hidden"
                : "flex w-[73px] items-center md:w-auto md:flex-none",
            )}
          >
            <Image
              src={SummaryIllustrator}
              alt={"Summary"}
              height={130}
              className={"h-[65px] w-[74px] md:mx-10 md:h-[130px] md:w-[147px]"}
            />
          </div>
          <div
            className={classNames(
              isExperiencesExist ? "items-center md:items-start" : "",
              "flex flex-none md:block",
            )}
          >
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>
          </div>
        </div>
        <div
          className={classNames(
            "card-body flex-col divide-y px-4 md:px-6",
            isExperiencesExist ? "flex" : "hidden",
          )}
        >
          {userProfile && isExperiencesExist && userProfile?.experiences
            ? userProfile?.experiences.map((experience, index) => (
                <div key={index} className={"flex gap-6 py-4 md:py-6"}>
                  <div className={"hidden w-44 flex-none md:block"}>
                    {formatIfValid(experience.from)}{" "}
                    {getIfNotEmpty(" - ", [experience.from, experience.to])}
                    {experience.is_working_here
                      ? t("user_profile_basic_info_date_present_text")
                      : formatIfValid(experience.to)}
                  </div>
                  <div className={"flex w-full flex-col gap-2"}>
                    <h3 className={"md:text-lg"}>
                      <span className={"font-bold text-primary"}>
                        {experience.position}
                      </span>{" "}
                      {t("user_profile_work_experience_work_at") + " "}
                      <span className={"font-bold text-gray-500"}>
                        {experience.company}
                      </span>
                    </h3>
                    <div className={"block text-sm md:hidden"}>
                      {formatIfValid(experience.from)}{" "}
                      {getIfNotEmpty(" - ", [experience.from, experience.to])}
                      {experience.is_working_here
                        ? t("user_profile_basic_info_date_present_text")
                        : formatIfValid(experience.to)}
                    </div>
                    <div
                      className="prose hidden text-sm leading-6 md:block"
                      dangerouslySetInnerHTML={{
                        __html: experience.description,
                      }}
                    ></div>

                    <ReadMoreMobile>{experience.description}</ReadMoreMobile>

                    {!!experience && experience?.skills?.length > 0 ? (
                      <div className={"flex flex-col gap-2"}>
                        <div
                          className={
                            "text-sm font-bold text-gray-500 md:text-base"
                          }
                        >
                          {t("user_profile_work_experience_applied_skills")}
                        </div>
                        <div className={"flex flex-wrap gap-2"}>
                          {experience.skills
                            ? experience.skills.map((skill, index) => (
                                <span
                                  key={index}
                                  className={
                                    "rounded border px-2 py-1 text-sm md:text-base"
                                  }
                                >
                                  {skill.skill_name}
                                </span>
                              ))
                            : ""}
                        </div>
                      </div>
                    ) : (
                      ""
                    )}

                    {!!experience && experience?.projects?.length > 0 ? (
                      <div
                        className={
                          "flex flex-col gap-2 rounded bg-gray-light px-4 py-2"
                        }
                      >
                        <div
                          className={
                            "text-sm font-bold text-gray-500 md:text-base"
                          }
                        >
                          {t("user_profile_work_experience_project_title")}
                        </div>
                        <div className={"divide-y"}>
                          {(experience.projects ?? []).map((project, index) => (
                            <div
                              key={index}
                              className={
                                "flex items-center gap-2 py-2 text-sm md:text-base"
                              }
                            >
                              <div>
                                <HiDocumentText />
                              </div>
                              <div className={"line-clamp-1 grow font-bold"}>
                                {project.project_name}
                              </div>
                              <div className={"whitespace-nowrap text-sm"}>
                                {project.project_time}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              ))
            : ""}
        </div>
      </section>
      {userProfile ? (
        <TheWorkExperienceFormModal
          openModal={isFormModalOpen}
          onClose={() => {
            setIsFormModalOpen(false);
            profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED);
          }}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheWorkExperienceSection;


