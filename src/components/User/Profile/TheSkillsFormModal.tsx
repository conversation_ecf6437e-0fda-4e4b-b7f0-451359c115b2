"use client";
import React, { FC, useEffect, useState } from "react";
import { Modal, TextInput } from "flowbite-react";
import useUserProfile from "@/hooks/useUserProfile";
import { useFormik } from "formik";
import { ProfileSkill } from "@/types/profile";
import "simplebar-react/dist/simplebar.min.css";
import { useAppSelector } from "@/store";
import { patchUserProfile } from "@/services/userAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { Button } from "@/components/Button";
import Select, { MultiValue } from "react-select";
import CreatableSelect from "react-select/creatable";
import { useTranslations } from "next-intl";
import { SkillSchema } from "@/schemas/UserProfileSchema";
import { VscLoading } from "react-icons/vsc";
import { isMobile } from "react-device-detect";

interface Props {
  openModal: boolean;
  onClose: () => void;
}

interface SkillFormValue {
  technical_skills: ProfileSkill[];
  soft_skills: string[];
}

const TheSkillsFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();
  const skills = useAppSelector((state) => state.taxonomies.skills);
  const [isUpdating, setIsUpdating] = useState(false);
  const [inputValue, setInputValue] = React.useState("");

  const form = useFormik<SkillFormValue>({
    initialValues: {
      technical_skills: userProfile.skills?.technical_skills ?? [],
      soft_skills: userProfile.skills?.soft_skills ?? [],
    },
    validationSchema: SkillSchema(t),
    onSubmit: async (values) => {
      setIsUpdating(true);
      patchUserProfile({
        skills: {
          soft_skills: values.soft_skills,
          technical_skills: values.technical_skills,
        },
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_skill_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);
          onClose && onClose();
        })
        .finally(() => setIsUpdating(false));
    },
  });
  useEffect(() => {
    if (!userProfile || !userProfile.skills) return;
    form.resetForm({
      values: {
        technical_skills: userProfile.skills?.technical_skills ?? [],
        soft_skills: userProfile.skills?.soft_skills ?? [],
      },
    });
  }, [userProfile]);

  useEffect(() => {
    if (!userProfile || !userProfile.skills) return;
    if (openModal) {
      form.resetForm({
        values: {
          technical_skills: userProfile.skills?.technical_skills ?? [],
          soft_skills: userProfile.skills?.soft_skills ?? [],
        },
      });
    }
  }, [openModal]);

  return (
    <Modal show={openModal} onClose={() => onClose()} size={"3xl"}>
      <Modal.Header className={"bg-gray-light"}>
        <p className={"text-black"}>{t("user_profile_skill_title")}</p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        <form id={"skills-form"} onSubmit={form.handleSubmit}>
          <div className="space-y-6">
            <h2 className={"text-sm font-bold uppercase text-gray-400"}>
              {t("user_profile_skill_modal_field_technical_skill")}
              <span className={"font-normal text-primary"}>*</span>
            </h2>
            <div className={"relative space-y-2 rounded bg-gray-light p-2"}>
              <Select
                maxMenuHeight={140}
                isMulti={true}
                options={skills.map((item) => {
                  return {
                    label: item.text,
                    value: item.id,
                  };
                })}
                classNamePrefix="select"
                value={form.values.technical_skills.map((item) => {
                  return { value: item.skill_id, label: item.skill_name };
                })}
                onChange={(value) => {
                  form.setFieldValue(
                    "technical_skills",
                    value.map((item) => {
                      return {
                        skill_id: item.value,
                        skill_name: item.label,
                      };
                    }),
                  );
                }}
              />
              {form.errors.technical_skills && form.touched.technical_skills ? (
                <div className={"text-sm text-primary-300"}>
                  {form.errors.technical_skills.toString()}
                </div>
              ) : (
                ""
              )}
            </div>

            <h2 className={"text-sm font-bold uppercase text-gray-400"}>
              {t("user_profile_skill_modal_field_soft_skill")}
            </h2>
            <div className={"space-y-2 rounded bg-gray-light p-2"}>
              <TextInput
                value={form.values.soft_skills?.join(", ") ?? ""}
                id="soft_skills"
                onChange={(event) =>
                  form.setFieldValue(
                    "soft_skills",
                    event.target.value !== ""
                      ? event.target.value?.split(", ")
                      : [],
                  )
                }
                color={
                  form.errors.soft_skills && form.touched.soft_skills
                    ? "failure"
                    : "gray"
                }
                placeholder={t("user_profile_skill_modal_field_soft_skill")}
              />
            </div>
          </div>
        </form>
      </Modal.Body>
      <Modal.Footer className={"justify-end bg-gray-light"}>
        <Button
          accent={"primary"}
          type={"submit"}
          form={"skills-form"}
          disabled={isUpdating}
          isBlock={isMobile}
          trailingIcon={
            isUpdating ? (
              <VscLoading className="h-6 w-6 animate-spin text-white" />
            ) : (
              ""
            )
          }
        >
          {t("user_profile_skill_modal_btn_save")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default TheSkillsFormModal;
