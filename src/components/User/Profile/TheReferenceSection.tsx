"use client";

import React, { FC, useState } from "react";
import { HiPencil, HiTrash } from "react-icons/hi2";
import useUserProfile from "@/hooks/useUserProfile";
import TheReferenceFormModal from "@/components/User/Profile/TheReferenceFormModal";
import { useTranslations } from "next-intl";
import { getIfNotEmpty } from "@/utils/string";
import { isMobile } from "react-device-detect";

const TheReferenceSection: FC<{
  onDelete: () => void;
}> = ({ onDelete }) => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();

  return (
    <>
      <section
        id={"extra-information"}
        className={"divide-y divide-gray-200 rounded bg-white"}
        onClick={() => isMobile && setIsFormModalOpen(true)}
      >
        <div className={"card-title flex items-center p-4"}>
          <div className={"grow"}>
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_reference_title")}
            </h2>
            <div className={"text-gray-400"}></div>
          </div>
          <div className={"flex-none"}>
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>

            <button
              className={"ml-4 h-5 w-5"}
              type={"button"}
              onClick={() => onDelete()}
            >
              <HiTrash />
            </button>
          </div>
        </div>

        <div className={"card-body flex flex-col divide-y px-4 md:px-6"}>
          {userProfile && userProfile.references
            ? userProfile.references.map((reference, index) => (
                <div key={index} className={"md:p-4 p-2"}>
                  <div className={"hidden font-bold md:block"}>
                    {reference.refer_name}
                    {getIfNotEmpty(" - ", [
                      reference.refer_name,
                      reference.refer_profession,
                    ])}
                    <span className={"font-normal"}>
                      {reference.refer_profession}
                    </span>
                  </div>
                  <div className={"block font-bold md:hidden"}>
                    {reference.refer_name}
                  </div>
                  <div className={"block text-sm font-normal md:hidden"}>
                    {reference.refer_profession}
                  </div>
                  <div className={"block text-sm text-gray-400 font-normal md:hidden"}>
                    {reference.refer_email}
                  </div>
                  <div className={"block text-sm text-gray-400 font-normal md:hidden"}>
                    {reference.refer_phone}
                  </div>
                  <div className="hidden md:block">
                    {reference.refer_email}
                    {getIfNotEmpty(" - ", [
                      reference.refer_email,
                      reference.refer_phone,
                    ])}
                    {reference.refer_phone}
                  </div>
                </div>
              ))
            : ""}
        </div>
      </section>

      {userProfile ? (
        <TheReferenceFormModal
          openModal={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheReferenceSection;
