"use client";
import React, { FC, useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import bannerOTW from "@/assets/images/icons/bannerOTW.svg";
import { isMobile } from "react-device-detect";
import { classNames } from "@/utils";

const TheOpenToWorkCard: FC<any> = () => {
  // Replace by taxonomy status_works later
  const t = useTranslations();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <>
      <div
        onClick={() => (window.location.href = "/users/job-management")}
        className={
          "cursor-pointer overflow-hidden rounded-xl border border-primary-200 shadow-black md:text-center"
        }
      >
        <div
          className={"card-body flex gap-4 bg-white px-6 pb-4 pt-6 md:flex-col"}
        >
          <h3 className="hidden font-semibold md:block md:text-lg">
            {t("user_profile_title_card_open_to_work")}
          </h3>
          <Image
            className={"m-auto h-[87px] w-[94px] md:h-[119px] md:w-[122px]"}
            src={bannerOTW}
            alt="banner otw"
            width={isMobile && isClient ? 94 : 122}
            height={isMobile && isClient ? 87 : 119}
          />
          <div className={"flex flex-col gap-2"}>
            <h3 className="block font-semibold md:hidden md:text-lg">
              {t("user_profile_title_card_open_to_work")}
            </h3>
            <div className={"text-gray-400"}>
              {t("user_profile_open_to_work_description")}
            </div>
          </div>
        </div>
        <div className={"card-title flex justify-center bg-red-200 p-4"}>
          <h2 className={"md:text-lg font-bold text-primary-300"}>
            {t("user_profile_open_to_work_now_toggle")}
          </h2>
        </div>
      </div>
    </>
  );
};

export default TheOpenToWorkCard;
