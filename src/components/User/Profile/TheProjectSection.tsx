"use client";

import React, { FC, useEffect, useState } from "react";
import { HiPencil } from "react-icons/hi2";
import { TheProjectFormModal } from "@/components/User/Profile/index";
import useUserProfile from "@/hooks/useUserProfile";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";
import { ReadMoreMobile } from "@/components/User/Profile/ReadMoreMobile";

const TheProjectSection: FC = () => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();
  const profilePopupContext = useProfilePopupContext();

  useEffect(() => {
    setIsFormModalOpen(
      profilePopupContext.state.type == PROFILE_CONTEXT_TYPE.POPUP_PROJECTS,
    );
  }, [profilePopupContext]);

  const isProjectsExits =
    userProfile?.projects && Array.isArray(userProfile?.projects) && userProfile.projects.length > 0;

  return (
    <>
      <section
        id={"projects"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex p-4 md:p-6"}>
          <div className={"grow"}>
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_project_title")}
            </h2>
          </div>
          <div
            className={classNames(
              isProjectsExits ? "items-center md:items-start" : "",
              "flex flex-none md:block",
            )}
          >
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>
          </div>
        </div>
        <div
          className={classNames(
            "card-body flex-col divide-y md:px-6 px-4",
            isProjectsExits ? "flex" : "hidden",
          )}
        >
          {userProfile && userProfile.projects && isProjectsExits
            ? userProfile.projects.map((project, index) => (
                <div key={index} className={"flex gap-6 md:py-6 py-4"}>
                  <div className={"hidden w-44 flex-none md:block"}>
                    {project.project_time}
                  </div>
                  <div className={"flex flex-col gap-2 md:w-auto w-full"}>
                    <div>
                      <h3 className={"md:text-lg text-base font-bold"}>
                        {project.project_name}
                      </h3>
                      <div className={"block text-sm w-44 flex-none md:hidden"}>
                        {project.project_time}
                      </div>
                      <p className={"text-primary md:text-base text-sm"}>{project.position}</p>
                    </div>
                    <div
                      className="md;block prose hidden"
                      dangerouslySetInnerHTML={{ __html: project.description }}
                    ></div>

                    <ReadMoreMobile>{project.description}</ReadMoreMobile>
                  </div>
                </div>
              ))
            : ""}
        </div>
      </section>
      {userProfile ? (
        <TheProjectFormModal
          openModal={isFormModalOpen}
          onClose={() => {
            setIsFormModalOpen(false);
            profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED);
          }}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheProjectSection;
