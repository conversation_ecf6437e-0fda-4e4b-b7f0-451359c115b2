import React, { <PERSON> } from "react";
import { HiArrowSmallRight } from "react-icons/hi2";
import { useTranslations } from "next-intl";

const TheQuickLinkCard: FC = () => {
  const t = useTranslations();
  return (
    <div className={"divide-y"}>
      <div className={"flex flex-col gap-2 py-4"}>
        <div>{t("user_profile_cv_it_description")}</div>
        <div>
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/blog/category/cv-it?src=topdev.vn&medium=userprofile`}
            target="_blank"
            className={
              "inline-flex items-center text-sm font-bold text-primary underline"
            }
          >
            <span>{t("user_profile_cv_it_link")}</span>

            <span className={"ml-2 inline-block"}>
              <HiArrowSmallRight />
            </span>
          </a>
        </div>
      </div>
      <div className={"flex flex-col gap-2 py-4"}>
        <div>{t("user_profile_personality_test_description")}</div>
        <div>
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/trac-nghiem-tinh-cach?src=topdev.vn&medium=userprofile`}
            target="_blank"
            className={
              "inline-flex items-center text-sm font-bold text-primary underline"
            }
          >
            <span>{t("user_profile_personality_test_link")}</span>

            <span className={"ml-2 inline-block"}>
              <HiArrowSmallRight />
            </span>
          </a>
        </div>
      </div>
    </div>
  );
};

export default TheQuickLinkCard;
