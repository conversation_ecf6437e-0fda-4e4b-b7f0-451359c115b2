import React, { FC, useEffect, useState } from "react";
import dynamic from "next/dynamic";
import {
  HiArrowUpT<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  HiRocketLaunch,
  HiStar,
} from "react-icons/hi2";
import { HiBriefcase } from "react-icons/hi";
import useUserProfile from "@/hooks/useUserProfile";
import { useAppSelector } from "@/store";
import { useTranslations } from "next-intl";

const BagdeStatus = dynamic(
  () => import("@/components/User/Common/BagdeStatus"),
);
import { MissingSection } from "@/types/userProfile";
import {
  PROFILE_CONTEXT_TYPE,
  PROFILE_STATUS,
  STATUS_MISSING_SECTIONS,
} from "@/contansts/userProfiles";
import { useProfilePopupContext } from "@/components/User/Profile/TheProfilePage";

const TheProfileStatusSection: FC<any> = () => {
  const t = useTranslations();
  const [userProfile] = useUserProfile();
  const [bootStatusWidth, setBootStatusWidth] = useState("w-[30px]");
  const user = useAppSelector((state) => state.user.user);
  const [missingSections, setMissingSections] = useState<MissingSection[]>([]);
  const profilePopupContext = useProfilePopupContext();
  useEffect(() => {
    let status: MissingSection[] = [];
    if (
      !userProfile ||
      !userProfile.status ||
      userProfile.status == PROFILE_STATUS.new_star
    ) {
      setBootStatusWidth("w-[30px]");
      status = STATUS_MISSING_SECTIONS.new_star.filter(
        (item) => !userProfile.completed_sections?.includes(item.section),
      );
    } else if (userProfile.status == PROFILE_STATUS.standard) {
      setBootStatusWidth("w-1/2");
      status = STATUS_MISSING_SECTIONS.standard.filter(
        (item) => !userProfile.completed_sections?.includes(item.section),
      );
    } else if (userProfile.status == PROFILE_STATUS.booster) {
      setBootStatusWidth("w-full");
    }
    setMissingSections(status);
  }, [userProfile]);
  return (
    <section id={"profile-status"} className={"rounded bg-white"}>
      <div className={"card-title p-4 md:p-6"}>
        <div className={"gap-2 md:flex"}>
          <h2
            className={
              "flex overflow-hidden text-xl font-bold md:truncate md:text-2xl"
            }
          >
            {t("user_profile_status_title")}{" "}
            {userProfile?.display_name ?? user.full_name}
          </h2>
          <BagdeStatus status={userProfile.status} />
        </div>
        <div className={"text-sm text-gray-500 md:text-base"}>
          {userProfile && userProfile.status == PROFILE_STATUS.booster ? (
            t("user_profile_status_description")
          ) : userProfile && userProfile.status == PROFILE_STATUS.standard ? (
            t("user_profile_status_description_standard")
          ) : (
            <div className={"items-center md:inline-flex"}>
              {t("user_profile_status_description_new")}&nbsp;
              <div
                className={
                  "inline-flex items-center gap-1 rounded bg-green-light px-2 py-1.5 text-sm text-green-dark"
                }
              >
                <span>{t("user_profile_open_to_work_toggle")}</span>
              </div>
            </div>
          )}
        </div>
      </div>
      <div className={"card-body divide-y divide-gray-300 p-6"}>
        <div className={"pb-6"}>
          <div className={"progress-container relative mb-3"}>
            <div className={"new-star absolute top-[-28px] z-10 text-center"}>
              <div className={"whitespace-nowrap text-sm text-yellow-dark"}>
                {t("user_profile_status_new_star")}
              </div>
              <div
                className={
                  "bg-new-star-icon inline-flex h-[30px] w-[30px] items-center justify-center rounded-full border border-yellow-dark bg-white text-yellow-dark"
                }
              >
                <HiStar className={"text-white"} />
              </div>
            </div>

            <div
              className={
                "new-star absolute left-1/2 top-[-28px] z-10 -translate-x-1/2 text-center"
              }
            >
              <div className={"whitespace-nowrap text-sm text-green-dark"}>
                {t("user_profile_status_standard")}
              </div>
              <div
                className={
                  "bg-standard-icon inline-flex " +
                  (userProfile &&
                  !["standard", "booster"].includes(userProfile.status ?? "")
                    ? "inactive"
                    : "") +
                  " h-[30px] w-[30px] items-center justify-center rounded-full border border-green-dark bg-white text-green-dark"
                }
              >
                <HiBriefcase className={"text-white"} />
              </div>
            </div>

            <div
              className={
                "new-star absolute -right-[10px] top-[-32px] z-10 text-center"
              }
            >
              <div
                className={"whitespace-nowrap text-sm font-bold text-primary"}
              >
                {t("user_profile_status_booster")}
              </div>
              <div
                className={
                  "bg-booster-icon " +
                  (userProfile &&
                  !["booster"].includes(userProfile.status ?? "")
                    ? "inactive"
                    : "") +
                  " inline-flex h-[40px] w-[40px] items-center justify-center rounded-full border text-white"
                }
              >
                <HiRocketLaunch className={"text-white"} />
              </div>
            </div>

            <div
              className={
                "progress-bar h-[18px] w-full overflow-hidden rounded-[22px] border border-gray-300 bg-gray-100"
              }
            >
              <div
                className={"h-full " + bootStatusWidth + " bg-primary-200"}
              ></div>
            </div>
          </div>
          <div className={"progress-footer-container text-center"}>
            <div
              className={
                "inline-flex items-center gap-1 rounded bg-green-light px-2 py-1.5 text-sm text-green-dark"
              }
            >
              <HiLockOpen />
              <span>{t("user_profile_status_open_to_work")}</span>
            </div>
          </div>
        </div>
        <div className={"items-center pt-6 text-gray-600"}>
          <div className={"flex-col items-start justify-center gap-1"}>
            <div className="flex flex-wrap items-center justify-start gap-2">
              <div>
                {userProfile && userProfile.status == "booster"
                  ? t("user_profile_status_missing_description_booster")
                  : userProfile && userProfile.status == "standard"
                  ? t("user_profile_status_missing_description_standard")
                  : t("user_profile_status_missing_description_new")}
              </div>
              {userProfile && userProfile.status == "booster" ? (
                ""
              ) : (
                <>
                  {missingSections.length
                    ? missingSections.map((item, index) => (
                        <button
                          key={index}
                          type={"button"}
                          onClick={() => {
                            profilePopupContext.dispatch(item.action);
                          }}
                          className={
                            "flex items-center justify-start gap-1 rounded border border-gray-300 p-1 text-sm text-gray-600"
                          }
                        >
                          {t(item.title)}
                        </button>
                      ))
                    : ""}
                  <div>{t("user_profile_status_missing_quickly_text")}</div>
                </>
              )}
              <button
                type={"button"}
                className={
                  "ml-1 flex w-full items-center justify-center gap-1 rounded border border-primary px-4 py-2 text-sm font-bold text-primary md:w-auto"
                }
                onClick={() => {
                  profilePopupContext.dispatch(
                    PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV,
                  );
                }}
              >
                <HiArrowUpTray />
                {t("user_profile_status_btn_import_pdf")}
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TheProfileStatusSection;
