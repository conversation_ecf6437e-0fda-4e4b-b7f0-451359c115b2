"use client";

import React, { FC, useState, useEffect } from "react";
import { HiOutlinePlusCircle } from "react-icons/hi2";
import useUserProfile from "@/hooks/useUserProfile";
import { Button } from "@/components/Button";
import { useTranslations } from "next-intl";
import { isMobile } from "react-device-detect";

const TheAddMoreSection: FC<{
  missingType: string;
  setShowLanguage: (show: boolean) => void;
  showLanguage: boolean;
  setShowCertificate: (show: boolean) => void;
  showCertificate: boolean;
  setShowHobby: (show: boolean) => void;
  showHobby: boolean;
  setShowReference: (show: boolean) => void;
  showReference: boolean;
  setShowActivity: (show: boolean) => void;
  showActivity: boolean;
  setShowAdditionals: (show: boolean) => void;
  showAdditionals: boolean;
}> = ({
  missingType,
  setShowLanguage,
  showLanguage,
  setShowCertificate,
  showCertificate,
  setShowHobby,
  showHobby,
  setShowReference,
  showReference,
  setShowActivity,
  showActivity,
  setShowAdditionals,
  showAdditionals,
}) => {
  const t = useTranslations();
  const [isClient, setIsClient] = useState(false);
  const [userProfile, setUserProfile] = useUserProfile();

  const handleBtnAddClick = ({
    keyUserProfile,
    setShow,
  }: {
    keyUserProfile: string;
    setShow(show: boolean): void;
  }) => {
    const newUserProfile = { ...userProfile, [keyUserProfile]: [] };
    setUserProfile(newUserProfile);
    setShow(true);
  };

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="p-0 m-0">
      <div className="flex flex-row items-center gap-2 ml-4 sm:ml-0 sm:justify-between sm:gap-0">
        <span className="mb-3 text-sm font-bold uppercase text-neutral-400">
          {t("user_profile_section_info_more_title")}
        </span>
      </div>
      <section
        id={"info-more"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex p-4 md:p-6"}>
          <div className={"grow"}>
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_more_section_title")}
            </h2>
          </div>
          <div className={"flex-none"}></div>
        </div>
        <div className={"card-body p-4 md:p-6"}>
          <div className={"flex flex-wrap gap-2 md:gap-4"}>
            {!showLanguage && (
              <Button
                accent={"outline"}
                type={"button"}
                onClick={() =>
                  handleBtnAddClick({
                    keyUserProfile: "languages",
                    setShow: setShowLanguage,
                  })
                }
                leadingIcon={<HiOutlinePlusCircle />}
                isBlock={isMobile && isClient}
              >
                {t("user_profile_more_section_btn_add_language")}
              </Button>
            )}

            {!showHobby && (
              <Button
                accent={"outline"}
                type={"button"}
                onClick={() =>
                  handleBtnAddClick({
                    keyUserProfile: "interests",
                    setShow: setShowHobby,
                  })
                }
                leadingIcon={<HiOutlinePlusCircle />}
                isBlock={isMobile && isClient}
              >
                {t("user_profile_more_section_btn_add_hobby")}
              </Button>
            )}

            {!showReference && (
              <Button
                accent={"outline"}
                type={"button"}
                onClick={() =>
                  handleBtnAddClick({
                    keyUserProfile: "references",
                    setShow: setShowReference,
                  })
                }
                leadingIcon={<HiOutlinePlusCircle />}
                isBlock={isMobile && isClient}
              >
                {t("user_profile_more_section_btn_add_reference")}
              </Button>
            )}

            {!showActivity && (
              <Button
                accent={"outline"}
                type={"button"}
                onClick={() =>
                  handleBtnAddClick({
                    keyUserProfile: "activities",
                    setShow: setShowActivity,
                  })
                }
                leadingIcon={<HiOutlinePlusCircle />}
                isBlock={isMobile && isClient}
              >
                {t("user_profile_more_section_btn_add_activity")}
              </Button>
            )}

            {!showCertificate && (
              <Button
                accent={"outline"}
                type={"button"}
                onClick={() =>
                  handleBtnAddClick({
                    keyUserProfile: "certificates",
                    setShow: setShowCertificate,
                  })
                }
                leadingIcon={<HiOutlinePlusCircle />}
                isBlock={isMobile && isClient}
              >
                {t("user_profile_more_section_btn_add_certificate")}
              </Button>
            )}

            {!showAdditionals && (
              <Button
                accent={"outline"}
                type={"button"}
                onClick={() =>
                  handleBtnAddClick({
                    keyUserProfile: "additionals",
                    setShow: setShowAdditionals,
                  })
                }
                leadingIcon={<HiOutlinePlusCircle />}
                isBlock={isMobile && isClient}
              >
                {t("user_profile_more_section_btn_add_other")}
              </Button>
            )}
          </div>
        </div>
      </section>
    </div>
  );
};

export default TheAddMoreSection;
