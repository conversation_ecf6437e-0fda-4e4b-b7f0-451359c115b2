"use client";

import React, { FC, useState } from "react";
import { HiP<PERSON>cil, HiTrash } from "react-icons/hi2";
import useUserProfile from "@/hooks/useUserProfile";
import TheCertificateFormModal from "@/components/User/Profile/TheCertificateFormModal";
import { useTranslations } from "next-intl";
import { formatIfValid } from "@/utils/date";
import { ReadMoreMobile } from "@/components/User/Profile/ReadMoreMobile";

const TheCertificateSection: FC<{
  onDelete: () => void;
}> = ({ onDelete }) => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();

  return (
    <>
      <section
        id={"extra-information"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex items-center p-4"}>
          <div className={"grow"}>
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_certificate_title")}
            </h2>
            <div className={"text-gray-400"}></div>
          </div>
          <div className={"flex-none"}>
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>

            <button
              className={"ml-4 h-5 w-5"}
              type={"button"}
              onClick={() => onDelete()}
            >
              <HiTrash />
            </button>
          </div>
        </div>

        <div className={"card-body flex flex-col divide-y px-4 md:px-6"}>
          {userProfile && userProfile.certificates
            ? userProfile.certificates.map((certificate, index) => (
                <div key={index} className={"flex p-4"}>
                  <div className={"hidden w-[166px] flex-none md:block"}>
                    {formatIfValid(certificate.date_completed)}
                  </div>
                  <div className={"grow"}>
                    <p className={"font-bold"}>{certificate.name}</p>
                    <div className={"block text-sm md:hidden"}>
                      {formatIfValid(certificate.date_completed)}
                    </div>
                    <div
                      className={"prose hidden text-gray-400 md:block"}
                      dangerouslySetInnerHTML={{
                        __html: certificate.description,
                      }}
                    ></div>
                    <ReadMoreMobile>{certificate.description}</ReadMoreMobile>
                  </div>
                </div>
              ))
            : ""}
        </div>
      </section>

      {userProfile ? (
        <TheCertificateFormModal
          openModal={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheCertificateSection;
