type IProfileSkill = {
  skill_id: number;
  skill_name: string;
};

type SkillsType = {
  skills: {
    technical_skills: IProfileSkill[];
  } | null;
};

const calculateCompletionPercentage = (
  fullName?: string,
  email?: string,
  position?: string,
  years_of_exp?: number | null,
  phone?: string,
  city?: string,
  skills?: SkillsType,
  summary?: string,
  workExperience?: any[],
  education?: any[],
  projects?: any[],
) => {
  const percentages = {
    fullName: fullName ? 5 : 0,
    email: email ? 5 : 0,
    position: position ? 5 : 0,
    years_of_exp:
      years_of_exp !== null && years_of_exp !== undefined && years_of_exp >= 0
        ? 10
        : 0,
    phone: phone ? 10 : 0,
    city: city ? 5 : 0,
    technical_skills: skills?.skills?.technical_skills?.length ? 10 : 0,
    summary: summary ? 10 : 0,
    workExperience: workExperience?.length ? 20 : 0,
    education: education?.length ? 10 : 0,
    projects: projects?.length ? 10 : 0,
  };

  const completion = Object.values(percentages).reduce((a, b) => a + b, 0);

  return { completion, percentages };
};

export default calculateCompletionPercentage;
