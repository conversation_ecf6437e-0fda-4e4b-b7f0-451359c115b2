"use client";
import React, { FC, useEffect, useState } from "react";
import { arrayMoveImmutable } from "array-move";
import { Checkbox, Modal, TextInput } from "flowbite-react";
import useUserProfile from "@/hooks/useUserProfile";
import { HiOutlinePlus, HiPencil, HiTrash } from "react-icons/hi2";
import { GrDrag } from "react-icons/gr";
import { useFormik } from "formik";
import TextEditor from "@/components/TextEditor";
import { patchUserProfile } from "@/services/userAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { Button } from "@/components/Button";
import { EducationSchema } from "@/schemas/UserProfileSchema";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { formatIfValid } from "@/utils/date";
import { getIfNotEmpty } from "@/utils/string";
import { ReactSortable, SortableEvent } from "react-sortablejs";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";
import MonthPicker from "@/components/MonthPicker/MonthPicker";
import { isMobile } from "react-device-detect";

interface Props {
  openModal: boolean;
  onClose: () => void;
}

interface EducationFormValue {
  id: number;
  to?: string;
  from?: string;
  degree?: string;
  description?: string;
  school_name?: string;
  is_studying_here?: boolean;
  is_new?: boolean;
  is_editting?: boolean;
  is_dirty?: boolean;
}

const TheEducationFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const profilePopupContext = useProfilePopupContext();
  const t = useTranslations();
  const [stateSortable, setStateSortable] = useState<EducationFormValue[]>([]);
  const [userProfile, setUserProfile] = useUserProfile();
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();

  const form = useFormik<EducationFormValue[]>({
    initialValues: [],
    validationSchema: EducationSchema(t),
    onSubmit: async (values) => {
      if (!userProfile || !userProfile.educations) return;
      setIsSaving(true);
      const educations = values.map((education) => {
        return {
          id: 0,
          to: education.to,
          from: education.from,
          degree: education.degree,
          description: education.description,
          school_name: education.school_name,
          is_studying_here: education.is_studying_here,
          is_dirty: education.is_dirty ?? false,
        };
      });

      patchUserProfile({
        educations: educations,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_education_title"),
            }),
            timer: 2000,
          });

          profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_EDUCATIONS);
          setUserProfile(response.data.data);
          const editingIndex = form.values.findIndex(
            (value: EducationFormValue) => value.is_editting == true,
          );
          form.setFieldValue(editingIndex + ".is_editting", false);
        })
        .finally(() => setIsSaving(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.educations) return;
    form.resetForm({ values: [...userProfile.educations] });
  }, [userProfile]);

  useEffect(() => {
    if (!userProfile || !userProfile.educations) return;
    if (openModal) {
      form.resetForm({ values: [...userProfile.educations] });
    }
  }, [openModal]);

  const handleBtnDeleteClick = async (index: number) => {
    setIsDeleting({ [index]: true });
    const educations = [...form.values];
    educations.splice(index, 1);

    patchUserProfile({ educations: educations })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_education_title"),
          }),
          timer: 2000,
        });

        profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_EDUCATIONS);
        setUserProfile(response.data.data);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleBtnAddEducationClick = () => {
    form.setValues([
      ...form.values,
      {
        id: 0,
        to: "",
        from: "",
        degree: "",
        description: "",
        school_name: "",
        is_studying_here: false,
        is_editting: true,
        is_new: true,
        is_dirty: true,
      },
    ]);
  };

  const handleCancelReferenceBtnClick = async (index: number) => {
    const isNew = form.values[index]?.is_new ?? false;
    if (isNew) {
      const values = [...form.values];
      values.splice(index, 1);
      form.setValues(values);
    } else {
      form.setFieldValue(index + ".is_editting", false);
      form.setFieldValue(index + ".is_dirty", false);
    }

    form.setTouched({ [index]: { school_name: false } } as any);
  };

  const hasEditing = () =>
    form.values.filter((value: EducationFormValue) => value.is_editting == true)
      .length > 0;

  useEffect(() => {
    setStateSortable(form.values);
  }, [form.values]);

  const handleChangeArray = (event: SortableEvent) => {
    setIsSaving(true);

    const newEducations = arrayMoveImmutable(
      stateSortable,
      event?.newIndex as number,
      event?.oldIndex as number,
    );
    if (!!newEducations && newEducations.length === 0) return;

    patchUserProfile({
      educations: newEducations,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_education_title"),
          }),
          timer: 2000,
        });

        profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_EDUCATIONS);
        setUserProfile(response.data.data);
      })
      .finally(() => setIsSaving(false));
  };

  return (
    <Modal
      show={openModal}
      onClose={() => onClose()}
      size={"3xl"}
      className="custom-modal-profile"
    >
      <Modal.Header className={"bg-gray-light"}>
        <p className={"text-black"}>{t("user_profile_education_title")}</p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        {
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit}
            id={"education-form"}
          >
            <ReactSortable
              list={stateSortable.map((value) => ({ ...value }))}
              setList={setStateSortable}
              animation={100}
              onEnd={(evt) => handleChangeArray(evt)}
              disabled={isSaving || stateSortable.length < 2 || hasEditing() || isMobile}
              className="flex flex-col flex-wrap gap-4"
            >
              {stateSortable.map((education, index) => (
                <div
                  key={index}
                  className={"rounded bg-gray-light p-2 md:px-4 md:py-2"}
                >
                  {education.is_editting ? (
                    <div className={"gap- grid grid-cols-2 gap-6"}>
                      <div className={"form-group col-span-2"}>
                        <label
                          htmlFor={`${index}_school_name`}
                          className={"text-sm font-bold text-gray-500"}
                        >
                          {t("user_profile_education_modal_field_school_name")}{" "}
                          <span className={"font-normal text-primary"}>*</span>
                        </label>
                        <TextInput
                          value={form.values[index]?.school_name ?? ""}
                          onChange={(event) =>
                            form.setFieldValue(
                              index + ".school_name",
                              event.target.value,
                            )
                          }
                          id={`${index}_school_name`}
                        />
                        {form.errors[index]?.school_name &&
                        form.touched[index]?.school_name ? (
                          <div className={"text-sm text-primary-300"}>
                            {form.errors[index]?.school_name}
                          </div>
                        ) : (
                          ""
                        )}
                      </div>

                      <div className={"form-group col-span-2"}>
                        <label
                          htmlFor={`${index}_degree`}
                          className={"text-sm font-bold text-gray-500"}
                        >
                          {t("user_profile_education_modal_field_major")}{" "}
                          <span className={"font-normal text-primary"}>*</span>
                        </label>
                        <TextInput
                          value={form.values[index]?.degree ?? ""}
                          id={`${index}_degree`}
                          onChange={(event) =>
                            form.setFieldValue(
                              index + ".degree",
                              event.target.value,
                            )
                          }
                          placeholder={t(
                            "user_profile_education_modal_field_placeholder_degree",
                          )}
                        />
                        {form.errors[index]?.degree &&
                        form.touched[index]?.school_name ? (
                          <div className={"text-sm text-primary-300"}>
                            {form.errors[index]?.degree}
                          </div>
                        ) : (
                          ""
                        )}
                      </div>

                      <div className={"form-group col-span-2"}>
                        <label className={"text-sm font-bold text-gray-500"}>
                          <Checkbox
                            className={"mr-2"}
                            checked={
                              form.values[index]?.is_studying_here ?? false
                            }
                            onChange={(event) => {
                              form.setFieldValue(
                                index + ".is_studying_here",
                                event.target.checked,
                              );
                              form.setFieldValue(
                                index + ".to",
                                dayjs().format("YYYY-MM-DD"),
                              );
                            }}
                          />
                          {t("user_profile_education_modal_field_study_here")}
                        </label>
                      </div>

                      <div className={"form-group col-span-2 md:col-span-1"}>
                        <label
                          htmlFor=""
                          className={"text-sm font-bold text-gray-500"}
                        >
                          {t("user_profile_education_modal_field_start_date")}{" "}
                          <span className={"font-normal text-primary"}>*</span>
                        </label>
                        <MonthPicker
                          selected={String(education?.from)}
                          onChange={(value) => {
                            form.setFieldValue(index + ".from", value);
                          }}
                          placeholder="MM-YYYY"
                          format="MM-YYYY"
                          validation={Boolean(
                            form.errors[index]?.from &&
                              form.touched[index]?.school_name,
                          )}
                        />
                        {form.errors[index]?.from &&
                        form.touched[index]?.school_name ? (
                          <div className={"text-sm text-primary-300"}>
                            {form.errors[index]?.from}
                          </div>
                        ) : (
                          ""
                        )}
                      </div>

                      <div className={"form-group col-span-2 md:col-span-1"}>
                        <label
                          htmlFor={`${index}_endDate`}
                          className={"text-sm font-bold text-gray-500"}
                        >
                          {t("user_profile_education_modal_field_end_date")}{" "}
                          {!education.is_studying_here ? (
                            <span className={"font-normal text-primary"}>
                              *
                            </span>
                          ) : (
                            ""
                          )}
                        </label>
                        <MonthPicker
                          startDate={education.from}
                          endDate="9999-01-01"
                          disabled={education.is_studying_here}
                          selected={String(education?.to)}
                          onChange={(value) => {
                            form.setFieldValue(index + ".to", value);
                          }}
                          placeholder="MM-YYYY"
                          format="MM-YYYY"
                          validation={Boolean(
                            form.errors[index]?.to &&
                              form.touched[index]?.school_name,
                          )}
                        />
                        {form.errors[index]?.to &&
                        form.touched[index]?.school_name ? (
                          <div className={"text-sm text-primary-300"}>
                            {form.errors[index]?.to}
                          </div>
                        ) : (
                          ""
                        )}
                      </div>

                      <div className={"form-group col-span-2"}>
                        <label
                          htmlFor={`${index}_description`}
                          className={"text-sm font-bold text-gray-500"}
                        >
                          {t("user_profile_education_modal_field_description")}
                        </label>
                        <TextEditor
                          onChange={(newValue) =>
                            form.setFieldValue(index + ".description", newValue)
                          }
                          value={form.values[index]?.description ?? ""}
                          placeholder={t(
                            "user_profile_education_modal_field_placeholder_description",
                          )}
                          height={250}
                        />
                        {form.errors[index]?.description &&
                        form.touched[index]?.school_name ? (
                          <div className={"text-sm text-primary-300"}>
                            {form.errors[index]?.description}
                          </div>
                        ) : (
                          ""
                        )}
                      </div>
                      <div className={"col-span-2 flex justify-end"}>
                        <Button
                          accent={"ghost"}
                          onClick={() => {
                            handleCancelReferenceBtnClick(index);
                          }}
                        >
                          {t("user_profile_education_modal_btn_delete")}
                        </Button>

                        <Button
                          accent={"primary"}
                          type={"submit"}
                          form={"education-form"}
                          disabled={isSaving}
                          trailingIcon={
                            isSaving ? (
                              <VscLoading className="h-6 w-6 animate-spin text-white" />
                            ) : (
                              ""
                            )
                          }
                        >
                          {t("user_profile_education_modal_btn_save")}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className={"space-y-4"}>
                      <div className={"flex items-center gap-2"}>
                        {!isMobile && (
                          <div>
                            <GrDrag />
                          </div>
                        )}

                        <div
                          className={
                            "flex grow bg-gray-100 pb-4 pl-4 pt-4 md:pr-4"
                          }
                        >
                          <div className={"grow"}>
                            <p className={"font-bold"}>
                              {education.school_name}
                            </p>
                            <p className={"text-sm text-primary md:text-base"}>
                              {education.degree}
                            </p>
                            <p className={"text-sm text-gray-500 md:text-base"}>
                              {formatIfValid(education.from ?? "")}
                              {getIfNotEmpty(" - ", [
                                education.from,
                                education.to,
                              ])}
                              {education.is_studying_here
                                ? t("user_profile_basic_info_date_present_text")
                                : formatIfValid(education.to ?? "")}
                            </p>
                          </div>
                          <div className={"flex items-center"}>
                            <button
                              className={
                                "flex h-[40px] w-[40px] items-center justify-center md:block"
                              }
                              type={"button"}
                              disabled={hasEditing()}
                              onClick={() => {
                                form.setFieldValue(
                                  index + ".is_editting",
                                  true,
                                );
                                form.setFieldValue(index + ".is_dirty", true);
                              }}
                            >
                              <HiPencil
                                className={hasEditing() ? "text-gray-200" : ""}
                              />
                            </button>
                            <button
                              type={"button"}
                              className={
                                "flex h-[40px] w-[40px] items-center justify-center md:block"
                              }
                              disabled={hasEditing()}
                              onClick={() => handleBtnDeleteClick(index)}
                            >
                              {isDeleting && isDeleting[index] ? (
                                <VscLoading className="h-4 w-4 animate-spin text-black" />
                              ) : (
                                <HiTrash
                                  className={
                                    hasEditing() ? "text-gray-200" : ""
                                  }
                                />
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </ReactSortable>
            {!hasEditing() ? (
              <div className={"flex justify-center"}>
                <Button
                  type={"button"}
                  onClick={() => handleBtnAddEducationClick()}
                  leadingIcon={<HiOutlinePlus />}
                  accent={isMobile ? "primary" : "outline"}
                  isBlock={isMobile}
                >
                  {t("user_profile_education_modal_btn_add_new")}
                </Button>
              </div>
            ) : (
              ""
            )}
          </form>
        }
      </Modal.Body>
    </Modal>
  );
};

export default TheEducationFormModal;
