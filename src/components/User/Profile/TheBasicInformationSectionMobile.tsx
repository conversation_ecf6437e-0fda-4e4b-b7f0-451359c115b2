"use client";

import React, { FC, useState, useEffect } from "react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { AiFillGithub, AiFillLinkedin } from "react-icons/ai";
import {
  HiArrowDownTray,
  HiDocumentArrowUp,
  HiPencil,
  HiRocketLaunch,
  HiStar,
} from "react-icons/hi2";
import { TheBasicInformationFormModal } from "@/components/User/Profile/index";
import NoAvatar from "@/assets/images/no-avatar.svg";
import useUserProfile from "@/hooks/useUserProfile";
import { useAppSelector } from "@/store";
import { HiBriefcase } from "react-icons/hi";
import TheConvertCVModal from "@/components/User/Profile/TheConvertCVModal";
import { downloadUserProfileCv } from "@/services/userAPI";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { getIfNotEmpty } from "@/utils/string";
import { useProfilePopupContext } from "./TheProfilePage";
import {
  IS_SHOW_BASIC_INFO_MODAL,
  PROFILE_CONTEXT_TYPE,
} from "@/contansts/userProfiles";
import { IS_SHOW_CONVERT_CV_MODAL } from "@/contansts/userProfiles";
import { TheOpenToWorkCard } from "@/components/User/Profile/index";

const TheBasicInformationSectionMobile: FC<any> = () => {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const [userProfile] = useUserProfile();
  const user = useAppSelector((state) => state.user.user);
  const [isClient, setIsClient] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const profilePopupContext = useProfilePopupContext();

  useEffect(() => {
    if (
      searchParams.has(IS_SHOW_CONVERT_CV_MODAL) &&
      Number(searchParams.get(IS_SHOW_CONVERT_CV_MODAL)) === 1
    ) {
      profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV);
    }
    if (
      searchParams.has(IS_SHOW_BASIC_INFO_MODAL) &&
      Number(searchParams.get(IS_SHOW_BASIC_INFO_MODAL)) === 1
    ) {
      profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO);
    }
  }, [searchParams]);

  useEffect(() => {
    setIsClient(true);
  }, []);
  //Params Job
  const params: string = searchParams.toString();
  const paramJob = new URLSearchParams(params);

  return (
    <>
      <section
        id={"basic-information"}
        className={
          "relative z-10 flex flex-wrap justify-between rounded bg-white p-4 md:hidden"
        }
      >
        <div className={"flex-none"}>
          <div className={"h-25 w-25 rounded-full bg-gray-500"}>
            <Image
              src={userProfile?.avatar_url ?? NoAvatar}
              width={100}
              height={100}
              alt={"Avatar"}
              className={"h-25 w-25 rounded-full"}
              priority={!!userProfile?.avatar_url ?? false}
            />
          </div>
        </div>
        <div className={"w-[calc(100%_-_110px)] overflow-hidden pr-5"}>
          <div className={"flex flex-col"}>
            <div className={"flex items-center gap-1.5"}>
              <h1
                className={
                  "overflow-hidden text-sm font-bold text-black sm:text-lg"
                }
              >
                {userProfile?.display_name ?? user.full_name}
              </h1>

              {userProfile == null || userProfile?.status == "new_star" ? (
                <div
                  className={
                    "bg-new-star-icon inline-flex h-[32px] w-[32px] flex-none items-center justify-center rounded-full border border-yellow-dark bg-yellow-dark text-white"
                  }
                >
                  <HiStar />
                </div>
              ) : (
                ""
              )}

              {userProfile && userProfile.status == "standard" ? (
                <div
                  className={
                    "bg-standard-icon inline-flex h-[32px] w-[32px] flex-none items-center justify-center rounded-full border border-yellow-dark bg-yellow-dark text-white"
                  }
                >
                  <HiBriefcase />
                </div>
              ) : (
                ""
              )}

              {userProfile && userProfile.status == "booster" ? (
                <div
                  className={
                    "bg-booster-icon inline-flex h-[32px] w-[32px] flex-none items-center justify-center rounded-full border border-yellow-dark bg-yellow-dark text-white"
                  }
                >
                  <HiRocketLaunch />
                </div>
              ) : (
                ""
              )}
            </div>

            <div className={"flex flex-wrap sm:flex-nowrap"}>
              <div
                className={
                  "w-full overflow-hidden text-sm font-bold text-gray-600 sm:w-auto"
                }
              >
                {userProfile?.position ?? (
                  <span className={"text-gray-300"}>
                    {t("user_profile_basic_info_default_position")}
                  </span>
                )}
              </div>
              <div
                className={
                  "hidden w-[30px] flex-none text-center text-sm sm:block"
                }
              >
                {getIfNotEmpty("-", [
                  userProfile?.position,
                  userProfile?.years_of_exp,
                ])}
              </div>
              <div
                className={"w-full flex-none text-sm text-gray-500 sm:w-auto"}
              >
                {userProfile?.years_of_exp ? (
                  userProfile.years_of_exp.toString() +
                  " " +
                  (userProfile.years_of_exp > 1
                    ? t("user_profile_basic_info_default_YOEs")
                    : t("user_profile_basic_info_default_YOE"))
                ) : (
                  <span className={"text-gray-300"}>
                    {t("user_profile_basic_info_default_exp")}
                  </span>
                )}
              </div>
            </div>
            <div className={"flex text-sm"}>
              <a className={"underline"}>{userProfile?.email ?? user.email}</a>
            </div>
          </div>
        </div>
        <div className="w-full">
          <div className={"mt-4 text-sm text-gray-400"}>
            <div className={"mt-1 flex"}>
              <div>
                {userProfile?.phone ?? (
                  <span className={"whitespace-nowrap text-gray-300"}>
                    {t("user_profile_basic_info_default_phone")}
                  </span>
                )}
              </div>
              <div className={"w-[30px] text-center"}>
                {getIfNotEmpty("-", [
                  userProfile?.phone,
                  userProfile?.birthday,
                ])}
              </div>
              <div>
                {userProfile?.birthday ? (
                  dayjs(userProfile?.birthday).format("DD-MM-YYYY")
                ) : (
                  <span className={"whitespace-nowrap text-gray-300"}>
                    {t("user_profile_basic_info_default_birthday")}
                  </span>
                )}
              </div>
            </div>

            <div className={"line-clamp-1"}>
              {userProfile &&
              (userProfile.address || userProfile.province_name) ? (
                (userProfile.address ? userProfile.address + ", " : "") +
                userProfile.province_name
              ) : (
                <span className={"text-gray-300"}>
                  {t("user_profile_basic_info_default_address")}
                </span>
              )}
            </div>

            <div className={"mt-1"}>
              {!userProfile ||
              (!userProfile?.linkedin_link && !userProfile?.github_link) ? (
                <span className={"text-gray-300"}>
                  {t("user_profile_basic_info_default_socialite_link")}
                </span>
              ) : (
                ""
              )}
              {userProfile?.linkedin_link ? (
                <div
                  className={"flex w-full items-center gap-1 overflow-hidden"}
                >
                  <AiFillLinkedin className={"flex-none text-2xl"} />
                  <a className={"truncate underline"}>
                    {userProfile?.linkedin_link}
                  </a>
                </div>
              ) : (
                ""
              )}
              {userProfile?.github_link ? (
                <div
                  className={
                    "mt-1 flex w-full items-center gap-1 overflow-hidden"
                  }
                >
                  <AiFillGithub className={"flex-none text-2xl"} />
                  <a className={"truncate underline"}>
                    {userProfile?.github_link}
                  </a>
                </div>
              ) : (
                ""
              )}
            </div>
          </div>
          <div className={"mt-6 flex flex-wrap gap-3"}>
            <button
              onClick={() =>
                profilePopupContext.dispatch(
                  PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV,
                )
              }
              className="inline-flex h-10 w-full items-center justify-center gap-1 rounded border border-solid border-primary bg-primary px-4 font-semibold text-white transition-all hover:border-primary-400 hover:bg-primary-400 disabled:cursor-not-allowed disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 lg:gap-3 lg:px-6 lg:text-base"
            >
              <span className="flex h-5 w-5 items-center justify-center text-lg lg:text-2xl">
                <HiDocumentArrowUp />
              </span>
              {t("user_profile_basic_info_btn_auto_fill")}
            </button>
            <button
              onClick={async () => {
                setIsSaving(true);
                downloadUserProfileCv(userProfile).finally(() =>
                  setIsSaving(false),
                );
              }}
              disabled={isSaving}
              className="inline-flex h-10 w-full items-center justify-center gap-1 rounded border border-solid border-gray-200 bg-gray-200 px-4 font-semibold text-gray-600 transition-all hover:border-gray-300 hover:bg-gray-300 disabled:cursor-not-allowed dark:border-white dark:text-white lg:gap-3 lg:px-6 lg:text-base"
            >
              <span className="flex h-5 w-5 items-center justify-center text-lg lg:text-2xl">
                <HiArrowDownTray />
              </span>
              {t("user_profile_basic_info_btn_save_as")}
            </button>
          </div>
        </div>

        <button
          className={
            "absolute right-0 top-0 flex h-12 w-12 items-center justify-center"
          }
          type={"button"}
          onClick={() => {
            profilePopupContext.dispatch(
              PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
            );
          }}
        >
          <HiPencil />
        </button>
        <TheConvertCVModal
          openModal={[
            PROFILE_CONTEXT_TYPE.POPUP_CONVERT_CV,
            PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_CONVERT_CV,
            PROFILE_CONTEXT_TYPE.TOGGLE_SELECT_CONVERT_CV,
          ].includes(profilePopupContext.state.type ?? "")}
          onClose={() => {
            profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED);
          }}
        />

        {userProfile ? (
          <TheBasicInformationFormModal
            openModal={[
              PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO,
              PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
            ].includes(profilePopupContext.state.type ?? "")}
            onClose={() =>
              profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_CLOSED)
            }
          />
        ) : (
          ""
        )}
      </section>
      <section
        id={"the-open-to-work-card-mobile"}
        className="relaitve z-[20] block md:hidden"
      >
        <TheOpenToWorkCard />
      </section>
    </>
  );
};

export default TheBasicInformationSectionMobile;
