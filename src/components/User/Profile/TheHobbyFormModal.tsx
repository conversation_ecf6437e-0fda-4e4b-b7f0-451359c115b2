"use client";
import React, { FC, useEffect, useState } from "react";
import { Modal } from "flowbite-react";
import { useFormik } from "formik";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile } from "@/services/userAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import TextEditor from "@/components/TextEditor";
import { Button } from "@/components/Button";
import { HobbySchema } from "@/schemas/UserProfileSchema";
import { useTranslations } from "next-intl";
import { VscLoading } from "react-icons/vsc";
import { isMobile } from "react-device-detect";

interface Props {
  openModal: boolean;
  onClose?: () => void;
}

interface HobbyFormValue {
  interests: string[] | null;
}

const TheHobbyFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();
  const [isSaving, setIsSaving] = useState(false);
  const form = useFormik<HobbyFormValue>({
    initialValues: {
      interests: userProfile.interests,
    },
    validationSchema: HobbySchema(t),
    onSubmit: async (values) => {
      setIsSaving(true);
      patchUserProfile({
        interests: values.interests,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_hobby_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);
          onClose && onClose();
        })
        .finally(() => setIsSaving(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.interests) return;
    form.resetForm({ values: { interests: userProfile.interests } });
  }, [userProfile]);

  useEffect(() => {
    if (!userProfile || !userProfile.interests) return;
    if (openModal) {
      form.resetForm({ values: { interests: userProfile.interests } });
    }
  }, [openModal]);

  return (
    <Modal
      show={openModal}
      onClose={() => (onClose ? onClose() : "")}
      size={"3xl"}
    >
      <Modal.Header className={"bg-gray-light"}>
        <p className={"font-bold text-black"}>
          {t("user_profile_hobby_title")}
        </p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="md:p-6 p-4 flex-1 overflow-auto">
        <form id={"interest-form"} onSubmit={form.handleSubmit}>
          <TextEditor
            value={
              form.values.interests ? (form.values.interests[0] as string) : ""
            }
            onChange={(newValue) => form.setFieldValue("interests.0", newValue)}
            placeholder={t("user_profile_hobby_modal_field_placeholder")}
          />
          {form.errors.interests && form.touched.interests ? (
            <div className={"text-sm text-primary-300"}>
              {form.errors.interests}
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
      <Modal.Footer className={"justify-end"}>
        <Button
          type={"submit"}
          form={"interest-form"}
          accent={"primary"}
          disabled={isSaving}
          isBlock={isMobile}
          trailingIcon={
            isSaving ? (
              <VscLoading className="h-6 w-6 animate-spin text-white" />
            ) : (
              ""
            )
          }
        >
          {t("user_profile_hobby_btn_save")}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default TheHobbyFormModal;
