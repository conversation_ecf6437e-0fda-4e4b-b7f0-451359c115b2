"use client";

import React, { FC, useState } from "react";
import { HiPencil, HiTrash } from "react-icons/hi2";
import useUserProfile from "@/hooks/useUserProfile";
import TheActivityFormModal from "@/components/User/Profile/TheActivityFormModal";
import { useTranslations } from "next-intl";
import { formatIfValid } from "@/utils/date";
import { getIfNotEmpty } from "@/utils/string";
import { ReadMoreMobile } from "@/components/User/Profile/ReadMoreMobile";

const TheActivitySection: FC<{
  onDelete: () => void;
}> = ({ onDelete }) => {
  const t = useTranslations();
  const [isFormModalOpen, setIsFormModalOpen] = useState<boolean>(false);
  const [userProfile] = useUserProfile();

  return (
    <>
      <section
        id={"extra-information"}
        className={"divide-y divide-gray-200 rounded bg-white "}
      >
        <div className={"card-title flex items-center p-4"}>
          <div className={"grow"}>
            <h2 className={"text-xl font-bold md:text-2xl"}>
              {t("user_profile_activity_title")}
            </h2>
            <div className={"text-gray-400"}></div>
          </div>
          <div className={"flex-none"}>
            <button
              className={"h-5 w-5"}
              type={"button"}
              onClick={() => setIsFormModalOpen(true)}
            >
              <HiPencil />
            </button>

            <button
              className={"ml-4 h-5 w-5"}
              type={"button"}
              onClick={() => onDelete()}
            >
              <HiTrash />
            </button>
          </div>
        </div>

        <div className={"card-body flex flex-col divide-y px-4 md:px-6"}>
          {userProfile && userProfile.activities
            ? userProfile.activities.map((activity, index) => (
                <div key={index} className={"flex md:p-4 px-2 py-4"}>
                  <div className={"hidden w-[166px] flex-none md:block"}>
                    {formatIfValid(activity.from)}
                    {getIfNotEmpty(" - ", [activity.from, activity.to])}
                    {activity.is_working_here
                      ? t("user_profile_basic_info_date_present_text")
                      : formatIfValid(activity.to)}
                  </div>
                  <div className={"grow"}>
                    <p className={"font-bold"}>{activity.activity}</p>
                    <div className={"block text-sm md:hidden"}>
                      {formatIfValid(activity.from)}
                      {getIfNotEmpty(" - ", [activity.from, activity.to])}
                      {activity.is_working_here
                        ? t("user_profile_basic_info_date_present_text")
                        : formatIfValid(activity.to)}
                    </div>
                    <div
                      className={"prose hidden text-gray-400 md:block"}
                      dangerouslySetInnerHTML={{ __html: activity.achievement }}
                    ></div>
                    <ReadMoreMobile>{activity.achievement}</ReadMoreMobile>
                  </div>
                </div>
              ))
            : ""}
        </div>
      </section>

      {userProfile ? (
        <TheActivityFormModal
          openModal={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
        />
      ) : (
        ""
      )}
    </>
  );
};

export default TheActivitySection;
