"use client";

import React, { FC, useEffect, useState } from "react";
import { arrayMoveImmutable } from "array-move";
import { Checkbox, Modal, Textarea, TextInput } from "flowbite-react";
import useUserProfile from "@/hooks/useUserProfile";
import { GrDrag } from "react-icons/gr";
import {
  HiChevronLeft,
  HiDocumentText,
  HiOutlinePlusCircle,
  HiPencil,
  HiTrash,
} from "react-icons/hi2";
import TextEditor from "@/components/TextEditor";
import { ProfileSkill } from "@/types/profile";
import { useFormik } from "formik";
import { patchUserProfile } from "@/services/userAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { WorkExperienceSchema } from "@/schemas/UserProfileSchema";
import { Button } from "@/components/Button";
import Select from "react-select";
import { useAppSelector } from "@/store";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { getIfNotEmpty } from "@/utils/string";
import { formatIfValid } from "@/utils/date";
import MonthPicker from "@/components/MonthPicker/MonthPicker";
import { ReactSortable, SortableEvent } from "react-sortablejs";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE } from "@/contansts/userProfiles";
import { isMobile } from "react-device-detect";

interface Props {
  openModal: boolean;
  onClose: () => void;
}

interface ExperienceFormValue {
  id: number;
  to?: string;
  from?: string;
  company?: string;
  position?: string;
  description?: string;
  is_working_here?: boolean;
  skills?: ProfileSkill[];
  projects?: ProjectExperienceFormValue[];
  is_new?: boolean;
  is_dirty?: boolean;
}

interface ProjectExperienceFormValue {
  description?: string;
  project_name?: string;
  project_time?: string;
  is_editting?: boolean;
  is_new?: boolean;
  id: number;
}

const TheWorkExperienceFormModal: FC<Props> = ({
  openModal = false,
  onClose,
}) => {
  const t = useTranslations();
  const [stateSortable, setStateSortable] = useState<ExperienceFormValue[]>([]);
  const [userProfile, setUserProfile] = useUserProfile();
  const [edittingExperienceIndex, setEddingExperienceIndex] = useState(-1);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();
  const skills = useAppSelector((state) => state.taxonomies.skills);
  const profilePopupContext = useProfilePopupContext();

  const form = useFormik<ExperienceFormValue>({
    initialValues: {
      id: 0,
      is_dirty: false,
    },
    validationSchema: WorkExperienceSchema(t),
    onSubmit: async (values) => {
      if (!userProfile || !userProfile.experiences) return;
      setIsSaving(true);

      const experiences = [...userProfile.experiences];
      const experience = {
        id: values.id ?? 0,
        to: values.to ?? "",
        from: values.from ?? "",
        company: values.company ?? "",
        position: values.position ?? "",
        description: values.description ?? "",
        is_working_here: values.is_working_here ?? false,
        skills: values.skills ?? [],
        is_dirty: values.is_dirty ?? false,
        projects: [
          ...(values.projects?.map((item) => {
            return {
              description: item.description ?? "",
              project_name: item.project_name ?? "",
              project_time: item.project_time ?? "",
              id: 0,
            };
          }) ?? []),
        ],
      };

      if (edittingExperienceIndex == experiences.length) {
        experiences.push(experience);
      } else {
        experiences[edittingExperienceIndex] = experience;
      }

      patchUserProfile({
        experiences: experiences,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_work_experience_title"),
            }),
            timer: 2000,
          });

          profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_EXPERIENCES);
          setUserProfile(response.data.data);
          // Close editing form
          setEddingExperienceIndex(-1);
        })
        .finally(() => setIsSaving(false));
    },
  });

  //Set data for Sortable
  useEffect(() => {
    const experiences = Array.isArray(userProfile.experiences) ? userProfile.experiences : [];
    setStateSortable(experiences as ExperienceFormValue[]);
  }, [userProfile.experiences]);

  useEffect(() => {
    setEddingExperienceIndex(-1);
    form.resetForm({ values: { ...{ id: 0, is_dirty: false } } });
  }, [openModal]);

  const handleBtnEditClick = (index: number) => {
    if (!userProfile || !userProfile.experiences || index < 0) return;
    setEddingExperienceIndex(index);
    const experience = userProfile.experiences[index];
    const newExperience = { ...experience, ...{ is_dirty: true } };
    form.resetForm({ values: { ...newExperience } });
  };

  const handleBtnDeleteClick = async (index: number) => {
    if (edittingExperienceIndex > -1) {
      setEddingExperienceIndex(-1);
      if (!userProfile || !userProfile.experiences || index < 0) return;
      const experience = userProfile.experiences[index];
      const newExperience = { ...experience, ...{ is_dirty: false } };
      form.resetForm({ values: { ...newExperience } });
      return;
    }

    setIsDeleting({ [index]: true });
    const experiences = [...(userProfile.experiences ?? [])];
    experiences.splice(index, 1);

    patchUserProfile({
      experiences: experiences,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_work_experience_title"),
          }),
          timer: 2000,
        });

        profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_EXPERIENCES);
        setUserProfile(response.data.data);
        // Close editing form if delete it in dediting form
        edittingExperienceIndex > -1 && setEddingExperienceIndex(-1);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleBtnAddExperienceClick = () => {
    if (!userProfile || !userProfile.experiences) return;

    setEddingExperienceIndex(userProfile.experiences.length);
    form.resetForm({
      values: {
        id: 0,
        to: "",
        from: "",
        company: "",
        position: "",
        description: "",
        is_working_here: false,
        skills: [],
        projects: [],
        is_new: true,
        is_dirty: true,
      },
    });
  };

  const handleBtnAddProjectClick = () => {
    const projects = [...(form.values.projects ?? [])];
    form.setFieldValue("projects", [
      ...projects,
      {
        project_time: "",
        project_name: "",
        description: "",
        is_editting: true,
        is_new: true,
      },
    ]);
  };

  const handleBtnDeleteProjectClick = (index: number) => {
    const projects = [...(form.values.projects ?? [])];
    projects.splice(index, 1);
    form.setFieldValue("projects", projects);
  };

  const handleCancelProjectBtnClick = async (index: number) => {
    const isNew =
      form.values.projects && form.values.projects[index].is_new ? true : false;
    if (isNew) {
      const projects = [...(form.values.projects ?? [])];
      projects.splice(index, 1);
      form.setFieldValue("projects", projects);
    } else {
      const projects = [...(form.values.projects ?? [])];
      projects[index].is_editting = false;
      form.setFieldValue("projects", projects);
    }
    form.setTouched({ projects: false });
  };

  const handleChangeArray = (event: SortableEvent) => {
    setIsSaving(true);
    const newExperiences = arrayMoveImmutable(
      stateSortable,
      event?.newIndex as number,
      event?.oldIndex as number,
    );

    if (!!newExperiences && newExperiences.length === 0) return;

    patchUserProfile({
      experiences: newExperiences,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_work_experience_title"),
          }),
          timer: 2000,
        });

        profilePopupContext.dispatch(PROFILE_CONTEXT_TYPE.POPUP_EXPERIENCES);
        setUserProfile(response.data.data);
      })
      .finally(() => setIsSaving(false));
  };

  // @ts-ignore
  return (
    <Modal className="custom-modal-profile" show={openModal} onClose={() => onClose()} size={"3xl"}>
      <Modal.Header className={"bg-gray-light"}>
        {edittingExperienceIndex >= 0 ? (
          <div className={"flex items-center"}>
            <button onClick={() => setEddingExperienceIndex(-1)}>
              <HiChevronLeft />
            </button>
            <div className="text-sm md:text-xl">
              {form.values.position}{" "}
              {form.values.company && form.values.position
                ? t("user_profile_education_modal_at_title")
                : ""}{" "}
              {form.values.company}
            </div>
          </div>
        ) : (
          <>
            <p className={"text-black"}>
              {t("user_profile_work_experience_title")}
            </p>
            <p className={"text-base font-normal text-gray-400"}></p>
          </>
        )}
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        {edittingExperienceIndex >= 0 ? (
          <form id={"experience-form"} onSubmit={form.handleSubmit}>
            <div className={"grid grid-cols-2 gap-6"}>
              <div className={"form-group col-span-2"}>
                <label
                  htmlFor="position"
                  className={"text-sm font-bold text-gray-500"}
                >
                  {t("user_profile_work_experience_modal_field_position")}{" "}
                  <span className={"font-normal text-primary"}>*</span>
                </label>
                <TextInput
                  value={form.values.position ?? ""}
                  id="position"
                  onChange={(event) =>
                    form.setFieldValue("position", event.target.value)
                  }
                  color={
                    form.errors.position && form.touched.position
                      ? "failure"
                      : "gray"
                  }
                  placeholder={t(
                    "user_profile_work_experience_modal_field_placeholder_position",
                  )}
                />
                {form.errors.position && form.touched.position ? (
                  <div className={"text-sm text-primary-300"}>
                    {form.errors.position}
                  </div>
                ) : (
                  ""
                )}
              </div>

              <div className={"form-group col-span-2"}>
                <label
                  htmlFor="company"
                  className={"text-sm font-bold text-gray-500"}
                >
                  {t("user_profile_work_experience_modal_field_company")}{" "}
                  <span className={"font-normal text-primary"}>*</span>
                </label>
                <TextInput
                  value={form.values.company ?? ""}
                  onChange={(event) =>
                    form.setFieldValue("company", event.target.value)
                  }
                  id="company"
                  color={
                    form.errors.company && form.touched.company
                      ? "failure"
                      : "gray"
                  }
                  placeholder={t(
                    "user_profile_work_experience_modal_field_placeholder_company",
                  )}
                />
                {form.errors.company && form.touched.company ? (
                  <div className={"text-sm text-primary-300"}>
                    {form.errors.company}
                  </div>
                ) : (
                  ""
                )}
              </div>

              <div className={"form-group col-span-2"}>
                <label className={"text-sm font-bold text-gray-500"}>
                  <Checkbox
                    className={"mr-2"}
                    checked={form.values.is_working_here}
                    onChange={(event) => {
                      form.setFieldValue(
                        "is_working_here",
                        event.target.checked,
                      );
                      form.setFieldValue("to", dayjs().format("YYYY-MM-DD"));
                    }}
                  />{" "}
                  {t("user_profile_work_experience_modal_field_work_here")}
                </label>
              </div>
              <div className={"form-group col-span-2 md:col-span-1"}>
                <label
                  htmlFor="startDate"
                  className={"text-sm font-bold text-gray-500"}
                >
                  {t("user_profile_work_experience_modal_field_start_date")}{" "}
                  <span className={"font-normal text-primary"}>*</span>
                </label>

                <MonthPicker
                  selected={String(form.values.from)}
                  onChange={(value) => {
                    form.setFieldValue("from", value);
                  }}
                  placeholder="MM-YYYY"
                  format="MM-YYYY"
                />
                {form.errors.from && form.touched.from ? (
                  <div className={"text-sm text-primary-300"}>
                    {form.errors.from}
                  </div>
                ) : (
                  ""
                )}
              </div>

              <div className={"form-group col-span-2 md:col-span-1"}>
                <label
                  htmlFor="startDate"
                  className={"text-sm font-bold text-gray-500"}
                >
                  {t("user_profile_work_experience_modal_field_end_date")}{" "}
                  {!form.values.is_working_here ? (
                    <span className={"font-normal text-primary"}>*</span>
                  ) : (
                    ""
                  )}
                </label>
                <MonthPicker
                  startDate={form.values.from}
                  endDate="9999-01-01"
                  disabled={form.values.is_working_here}
                  selected={String(form.values.to)}
                  onChange={(value) => {
                    form.setFieldValue("to", value);
                  }}
                  placeholder="MM-YYYY"
                  format="MM-YYYY"
                />

                {form.errors.to && form.touched.to ? (
                  <div className={"text-sm text-primary-300"}>
                    {form.errors.to}
                  </div>
                ) : (
                  ""
                )}
              </div>

              <div className={"form-group col-span-2"}>
                <label htmlFor="" className={"text-sm font-bold text-gray-500"}>
                  {t("user_profile_work_experience_modal_field_description")}
                </label>
                <TextEditor
                  value={form.values.description ?? ""}
                  onChange={(value) => form.setFieldValue("description", value)}
                  placeholder={t(
                    "user_profile_work_experience_modal_field_placeholder_description",
                  )}
                  height={250}
                />
              </div>

              <div className={"form-group col-span-2"}>
                <label
                  htmlFor="skills"
                  className={"text-sm font-bold text-gray-500"}
                >
                  {t(
                    "user_profile_work_experience_modal_field_technical_skill",
                  )}
                </label>
                <Select
                  id="skills"
                  isMulti={true}
                  options={skills.map((item) => {
                    return {
                      label: item.text,
                      value: item.id,
                    };
                  })}
                  value={(form.values.skills ?? []).map((item) => {
                    return { label: item.skill_name, value: item.skill_id };
                  })}
                  classNamePrefix="select"
                  onChange={(value) => {
                    form.setFieldValue(
                      "skills",
                      value.map((item) => {
                        return {
                          skill_id: item.value,
                          skill_name: item.label,
                        };
                      }),
                    );
                  }}
                />
              </div>

              <div className={"form-group col-span-2"}>
                <label htmlFor="" className={"text-sm font-bold text-gray-500"}>
                  {t("user_profile_work_experience_modal_project_title")}
                </label>
                <p className={"text-gray-300"}>
                  {t("user_profile_work_experience_modal_project_description")}
                </p>
                <div className={"mt-4 space-y-5"}>
                  {form.values.projects
                    ? form.values.projects.map((project, index) => (
                        <div key={index} className={"flex items-center gap-2"}>
                          {/* <div>
                                <GrDrag />
                              </div> */}
                          <div className={"grow rounded p-2 pr-0"}>
                            {project?.is_editting ? (
                              <div className={"space-y-2"}>
                                <div className={"form-group"}>
                                  <label
                                    htmlFor={`projects_${index}_project_name`}
                                    className={
                                      "text-sm font-bold text-gray-500"
                                    }
                                  >
                                    {t(
                                      "user_profile_work_experience_modal_field_project",
                                    )}{" "}
                                    <span
                                      className={"font-normal text-primary"}
                                    >
                                      *
                                    </span>
                                  </label>
                                  <TextInput
                                    id={`projects_${index}_project_name`}
                                    value={project.project_name}
                                    onChange={(event) =>
                                      form.setFieldValue(
                                        "projects." + index + ".project_name",
                                        event.target.value,
                                      )
                                    }
                                    placeholder={t(
                                      "user_profile_work_experience_modal_field_placeholder_project_name",
                                    )}
                                  />

                                  {form.errors.projects &&
                                  form.errors.projects[index] &&
                                  /* @ts-ignore */
                                  form.errors.projects[index].project_name &&
                                  form.touched.projects ? (
                                    <div className={"text-sm text-primary-300"}>
                                      {
                                        /* @ts-ignore */
                                        form.errors.projects[index].project_name
                                      }
                                    </div>
                                  ) : (
                                    ""
                                  )}
                                </div>
                                <div className={"form-group"}>
                                  <label
                                    htmlFor={`projects_${index}_project_time`}
                                    className={
                                      "text-sm font-bold text-gray-500"
                                    }
                                  >
                                    {t(
                                      "user_profile_work_experience_modal_field_project_timeline",
                                    )}{" "}
                                    <span
                                      className={"font-normal text-primary"}
                                    >
                                      *
                                    </span>
                                  </label>
                                  <TextInput
                                    id={`projects_${index}_project_time`}
                                    value={project.project_time}
                                    onChange={(event) =>
                                      form.setFieldValue(
                                        "projects." + index + ".project_time",
                                        event.target.value,
                                      )
                                    }
                                    placeholder={t(
                                      "user_profile_work_experience_modal_field_placeholder_project_time",
                                    )}
                                  />
                                  {form.errors.projects &&
                                  form.errors.projects[index] &&
                                  /* @ts-ignore */
                                  form.errors.projects[index].project_time &&
                                  form.touched.projects ? (
                                    <div className={"text-sm text-primary-300"}>
                                      {
                                        /* @ts-ignore */
                                        form.errors.projects[index].project_time
                                      }
                                    </div>
                                  ) : (
                                    ""
                                  )}
                                </div>
                                <div className={"form-group"}>
                                  <label
                                    htmlFor={`projects_${index}_description`}
                                    className={
                                      "text-sm font-bold text-gray-500"
                                    }
                                  >
                                    {t(
                                      "user_profile_work_experience_modal_field_project_description",
                                    )}{" "}
                                  </label>
                                  <Textarea
                                    id={`projects_${index}_description`}
                                    value={project.description}
                                    onChange={(event) =>
                                      form.setFieldValue(
                                        "projects." + index + ".description",
                                        event.target.value,
                                      )
                                    }
                                    color={"gray"}
                                    placeholder={t(
                                      "user_profile_work_experience_modal_field_placeholder_project_description",
                                    )}
                                    className="h-32"
                                  />
                                </div>
                                <div className={"flex justify-end"}>
                                  <Button
                                    accent={"ghost"}
                                    size={"sm"}
                                    type={"button"}
                                    onClick={() =>
                                      handleCancelProjectBtnClick(index)
                                    }
                                  >
                                    {t(
                                      "user_profile_work_experience_modal_btn_delete_project",
                                    )}
                                  </Button>
                                  <Button
                                    type={"button"}
                                    size={"sm"}
                                    form={"certificate-form"}
                                    accent={"primary"}
                                    onClick={async () => {
                                      form.validateForm().then((error) => {
                                        form.setTouched({ projects: true });

                                        if (
                                          (error.projects ?? []).length == 0
                                        ) {
                                          form.setTouched({
                                            projects: false,
                                          });
                                          form.setFieldValue(
                                            "projects." + index + ".is_new",
                                            false,
                                          );
                                          form.setFieldValue(
                                            "projects." +
                                              index +
                                              ".is_editting",
                                            false,
                                          );
                                        }
                                      });
                                    }}
                                  >
                                    {t(
                                      "user_profile_work_experience_modal_btn_save_project",
                                    )}
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className={"flex"}>
                                <div className={"grow"}>
                                  <div
                                    className={
                                      "flex items-center gap-2 font-bold"
                                    }
                                  >
                                    <div>
                                      <HiDocumentText />
                                    </div>
                                    <div className={"line-clamp-1"}>
                                      {project.project_name}
                                    </div>
                                  </div>
                                  <p className={"text-gray-500"}>
                                    {project.project_time}
                                  </p>
                                </div>
                                <div className={"flex items-center"}>
                                  <button
                                    className={"h-[40px] w-[40px]"}
                                    type={"button"}
                                    onClick={() =>
                                      form.setFieldValue(
                                        "projects." + index + ".is_editting",
                                        true,
                                      )
                                    }
                                  >
                                    <HiPencil />
                                  </button>
                                  <button
                                    className={"h-[40px] w-[40px]"}
                                    onClick={() => {
                                      handleBtnDeleteProjectClick(index);
                                    }}
                                  >
                                    <HiTrash />
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ))
                    : ""}

                  <div className={"flex justify-center"}>
                    <div>
                      <Button
                        type={"button"}
                        onClick={() => handleBtnAddProjectClick()}
                        accent={"outline"}
                        leadingIcon={<HiOutlinePlusCircle />}
                        disabled={
                          (form.values.projects ?? []).findIndex(
                            (item) => item.is_editting,
                          ) > -1
                        }
                      >
                        {t(
                          "user_profile_work_experience_modal_btn_add_project",
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        ) : (
          <div className={"space-y-4"}>
            {!!stateSortable && stateSortable.length > 0 && (
              <ReactSortable
                list={stateSortable.map((value) => ({ ...value }))}
                setList={setStateSortable}
                animation={100}
                onEnd={(evt) => handleChangeArray(evt)}
                disabled={isSaving || stateSortable.length < 2 || isMobile}
                className="flex flex-col flex-wrap gap-4"
              >
                {stateSortable.map((item, index) => (
                  <div
                    key={index}
                    className={"flex select-none items-center gap-2"}
                  >
                    {!isMobile && (
                      <div>
                        <GrDrag />
                      </div>
                    )}

                    <div className={"grow bg-gray-100 p-4"}>
                      <div className={"flex"}>
                        <div className={"grow"}>
                          <p className={"font-bold"}>
                            <span className={"text-primary"}>
                              {item.position}
                            </span>{" "}
                            <span className={"text-normal"}>at</span>{" "}
                            {item.company}
                          </p>
                          <p className={"text-sm text-gray-500 md:text-base"}>
                            {formatIfValid(item.from as string)}
                            {getIfNotEmpty(" - ", [item.from, item.to])}
                            {item.is_working_here
                              ? t("user_profile_basic_info_date_present_text")
                              : formatIfValid(item.to as string)}
                          </p>
                        </div>
                        <div className={"flex items-center"}>
                          <button
                            className={
                              "flex h-[40px] w-[40px] items-center justify-center md:block"
                            }
                            type={"button"}
                            onClick={() => handleBtnEditClick(index)}
                          >
                            <HiPencil />
                          </button>
                          <button
                            className={
                              "flex h-[40px] w-[40px] items-center justify-center md:block"
                            }
                            onClick={() => handleBtnDeleteClick(index)}
                            disabled={
                              isDeleting && isDeleting[index] ? true : false
                            }
                          >
                            {isDeleting && isDeleting[index] ? (
                              <VscLoading className="h-4 w-4 animate-spin text-black" />
                            ) : (
                              <HiTrash />
                            )}
                          </button>
                        </div>
                      </div>
                      {!!item && Number(item?.projects?.length) > 0 && (
                        <div className={"mt-2 space-y-2"}>
                          {(item.projects ?? []).map((project, index) => (
                            <div
                              key={index}
                              className={
                                "flex items-center gap-2 rounded bg-white p-2 text-sm"
                              }
                            >
                              <div>
                                <HiDocumentText />
                              </div>
                              <div className={"line-clamp-1 grow font-bold"}>
                                {project.project_name}
                              </div>
                              <div
                                className={"whitespace-nowrap text-gray-500"}
                              >
                                {project.project_time}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </ReactSortable>
            )}
          </div>
        )}
      </Modal.Body>
      <Modal.Footer className={"justify-end bg-gray-light"}>
        {edittingExperienceIndex >= 0 ? (
          <>
            <Button
              accent={"ghost"}
              onClick={() => handleBtnDeleteClick(edittingExperienceIndex)}
            >
              {t("user_profile_work_experience_modal_btn_delete_position")}
            </Button>

            <Button
              accent={"primary"}
              type={"submit"}
              form={"experience-form"}
              disabled={
                (form.values.projects || []).findIndex(
                  (item) => item.is_editting,
                ) > -1
              }
              trailingIcon={
                isSaving ? (
                  <VscLoading className="h-6 w-6 animate-spin text-white" />
                ) : (
                  ""
                )
              }
            >
              {t("user_profile_work_experience_modal_btn_save_position")}
            </Button>
          </>
        ) : (
          <Button
            accent={"primary"}
            onClick={() => handleBtnAddExperienceClick()}
            isBlock={isMobile}
          >
            {t("user_profile_work_experience_btn_add_position")}
          </Button>
        )}
      </Modal.Footer>
    </Modal>
  );
};

export default TheWorkExperienceFormModal;
