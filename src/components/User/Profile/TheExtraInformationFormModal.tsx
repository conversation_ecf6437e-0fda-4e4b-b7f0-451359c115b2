"use client";
import { FC, useEffect, useState } from "react";
import { Modal, Textarea } from "flowbite-react";

interface Props {
  openModal: boolean;
  onClose?: () => void;
}

const TheExtraInformationFormModal: FC<Props> = ({
  openModal = false,
  onClose,
}) => {
  const [isOpen, setIsOpen] = useState(openModal);
  useEffect(() => setIsOpen(openModal), [openModal]);

  return (
    <Modal
      show={isOpen}
      onClose={() => (onClose ? onClose() : "")}
      size={"3xl"}
    >
      <Modal.Header className={"bg-gray-light"}>
        <h1 className={"text-black"}>Skills</h1>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body>
        <div className="space-y-6">
          <Textarea />
        </div>
      </Modal.Body>
      <Modal.Footer className={"justify-end bg-gray-light"}>
        <button
          className={"rounded bg-primary px-11 py-3.5 font-bold text-white"}
        >
          Save skills
        </button>
      </Modal.Footer>
    </Modal>
  );
};

export default TheExtraInformationFormModal;
