"use client";

import React, { FC, useEffect, useState } from "react";
import { Modal, TextInput } from "flowbite-react";
import { HiOutlinePlus, HiPencil, HiTrash } from "react-icons/hi2";
import { ReactSortable, SortableEvent } from "react-sortablejs";
import { arrayMoveImmutable } from "array-move";
import { useFormik } from "formik";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile } from "@/services/userAPI";
import TextEditor from "@/components/TextEditor";
import ToastNotification from "@/components/Swal/ToastNotification";
import { Button } from "@/components/Button";
import { GrDrag } from "react-icons/gr";
import { useTranslations } from "next-intl";
import { AdditionalSchema } from "@/schemas/UserProfileSchema";
import { VscLoading } from "react-icons/vsc";
import { DeletingStatus } from "@/types/profile";
import { useProfilePopupContext } from "./TheProfilePage";
import { isMobile } from "react-device-detect";
interface Props {
  openModal: boolean;
  onClose?: () => void;
}

interface OtherFormValue {
  additional?: string;
  description?: string;
  is_editting?: boolean;
  is_new?: boolean;
  is_dirty?: boolean;
  id: number;
}

const TheOtherFormModal: FC<Props> = ({ openModal = false, onClose }) => {
  const t = useTranslations();
  const profilePopupContext = useProfilePopupContext();
  const [userProfile, setUserProfile] = useUserProfile();
  const [stateSortable, setStateSortable] = useState<OtherFormValue[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState<DeletingStatus>();

  const form = useFormik<OtherFormValue[]>({
    initialValues: [],
    validationSchema: AdditionalSchema(t),
    onSubmit: async (values) => {
      if (!userProfile) return;
      setIsSaving(true);
      const additionals = values.map((additional) => {
        return {
          id: 0,
          additional: additional.additional,
          description: additional.description,
          is_dirty: additional.is_dirty ?? false,
        };
      });

      patchUserProfile({
        additionals: additionals,
      })
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("user_profile_save_success_title"),
            description: t("user_profile_toast_save_success_message", {
              section: t("user_profile_other_title"),
            }),
            timer: 2000,
          });

          setUserProfile(response.data.data);

          const editingIndex = form.values.findIndex(
            (value: OtherFormValue) => value.is_editting == true,
          );
          form.setFieldValue(editingIndex + ".is_editting", false);
        })
        .finally(() => setIsSaving(false));
    },
  });

  useEffect(() => {
    if (!userProfile || !userProfile.additionals) return;
    form.resetForm({ values: [...userProfile.additionals] });
  }, [userProfile]);

  useEffect(() => {
    if (openModal) {
      if (!userProfile || !userProfile.additionals) return;
      form.resetForm({ values: [...userProfile.additionals] });
    }
  }, [openModal]);

  const handleAddNewAcitivityBtnClick = () => {
    form.setValues([
      ...form.values,
      {
        additional: "",
        description: "",
        is_editting: true,
        is_new: true,
        is_dirty: true,
        id: 0,
      },
    ]);
  };

  const handleDeleteActivityBtnClick = (index: number) => {
    setIsDeleting({ [index]: true });
    const additionals = [...form.values];
    additionals.splice(index, 1);
    patchUserProfile({
      additionals: additionals,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_other_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsDeleting({ [index]: false }));
  };

  const handleCancelReferenceBtnClick = async (index: number) => {
    const isNew = form.values[index].is_new ?? false;
    if (isNew) {
      const values = [...form.values];
      values.splice(index, 1);
      form.setValues(values);
    } else {
      form.setFieldValue(index + ".is_editting", false);
      form.setFieldValue(index + ".is_dirty", false);
    }

    form.setTouched({ [index]: { activity: false } } as any);
  };

  const hasEditing = () =>
    form.values.filter((value: OtherFormValue) => value.is_editting == true)
      .length > 0;

  const handleChangeArray = (event: SortableEvent) => {
    setIsSaving(true);

    const newAdditionals = arrayMoveImmutable(
      stateSortable,
      event?.newIndex as number,
      event?.oldIndex as number,
    );
    if (!!newAdditionals && newAdditionals.length === 0) return;

    patchUserProfile({
      additionals: newAdditionals,
    })
      .then((response) => {
        ToastNotification({
          icon: "success",
          title: t("user_profile_save_success_title"),
          description: t("user_profile_toast_save_success_message", {
            section: t("user_profile_other_title"),
          }),
          timer: 2000,
        });

        setUserProfile(response.data.data);
      })
      .finally(() => setIsSaving(false));
  };

  useEffect(() => {
    setStateSortable(form.values);
  }, [form.values]);

  return (
    <Modal
      show={openModal}
      onClose={() => (onClose ? onClose() : "")}
      size={"3xl"}
      className="custom-modal-profile"
    >
      <Modal.Header className={"bg-gray-light"}>
        <p className={"font-bold text-black"}>
          {t("user_profile_other_title")}
        </p>
        <p className={"text-base font-normal text-gray-400"}></p>
      </Modal.Header>
      <Modal.Body className="flex-1 overflow-auto p-4 md:p-6">
        <form
          className="space-y-6"
          onSubmit={form.handleSubmit}
          id={"certificate-form"}
        >
          <ReactSortable
            list={stateSortable.map((value) => ({ ...value }))}
            setList={setStateSortable}
            animation={100}
            onEnd={(evt) => handleChangeArray(evt)}
            disabled={
              isSaving || stateSortable.length < 2 || hasEditing() || isMobile
            }
            className="flex flex-col flex-wrap gap-4"
          >
            {stateSortable.map((additional, index) => (
              <div key={index} className={"bg-gray-light p-4"}>
                {additional.is_editting ? (
                  <div className={"grid grid-cols-2 gap-4"}>
                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"mb-1 block text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_other_modal_field_name")}{" "}
                        <span className={"font-normal text-primary"}>*</span>
                      </label>
                      <TextInput
                        value={form.values[index]?.additional ?? ""}
                        onChange={(event) =>
                          form.setFieldValue(
                            index + ".additional",
                            event.target.value,
                          )
                        }
                        placeholder={t(
                          "user_profile_other_modal_field_placeholder_name",
                        )}
                      />
                      {form.errors[index]?.additional &&
                      form.touched[index]?.additional ? (
                        <div className={"mt-1 text-sm text-primary-300"}>
                          {form.errors[index]?.additional}
                        </div>
                      ) : (
                        ""
                      )}
                    </div>

                    <div className={"form-group col-span-2"}>
                      <label
                        htmlFor=""
                        className={"mb-1 block text-sm font-bold text-gray-500"}
                      >
                        {t("user_profile_other_modal_field_description")}
                      </label>
                      <TextEditor
                        value={form.values[index]?.description ?? ""}
                        onChange={(value) =>
                          form.setFieldValue(index + ".description", value)
                        }
                        placeholder={t(
                          "user_profile_other_modal_field_placeholder_description",
                        )}
                        height={250}
                      />
                    </div>

                    <div className={"form-group col-span-2 flex justify-end"}>
                      <Button
                        accent={"ghost"}
                        type={"button"}
                        onClick={() => handleCancelReferenceBtnClick(index)}
                      >
                        {t("user_profile_other_modal_btn_delete")}
                      </Button>
                      <Button
                        type={"button"}
                        form={"certificate-form"}
                        accent={"primary"}
                        disabled={isSaving}
                        trailingIcon={
                          isSaving ? (
                            <VscLoading className="h-6 w-6 animate-spin text-white" />
                          ) : (
                            ""
                          )
                        }
                        onClick={async () => {
                          const errors = await form.validateForm();
                          form.setTouched({
                            [index]: { additional: true },
                          } as any);
                          if (!Object.values(errors).length) {
                            form.submitForm();
                          }
                        }}
                      >
                        {t("user_profile_activity_modal_btn_save_all")}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className={"flex items-center gap-2"}>
                      {!isMobile && (
                        <div>
                          <GrDrag />
                        </div>
                      )}

                      <div className={"flex grow"}>
                        <div className={"grow"}>
                          <p className={"font-bold"}>{additional.additional}</p>
                          <div
                            className="prose mt-2 w-full"
                            dangerouslySetInnerHTML={{
                              __html: additional.description ?? "",
                            }}
                          ></div>
                        </div>
                        <div className={"flex-none"}>
                          <button
                            className={"h-5 w-5"}
                            type={"button"}
                            disabled={hasEditing()}
                            onClick={() => {
                              form.setFieldValue(index + ".is_editting", true);
                              form.setFieldValue(index + ".is_dirty", true);
                            }}
                          >
                            <HiPencil
                              className={hasEditing() ? "text-gray-200" : ""}
                            />
                          </button>

                          <button
                            className={"ml-4 h-5 w-5"}
                            type={"button"}
                            disabled={
                              (isDeleting && isDeleting[index]
                                ? true
                                : false) || hasEditing()
                            }
                            onClick={() => handleDeleteActivityBtnClick(index)}
                          >
                            {isDeleting && isDeleting[index] ? (
                              <VscLoading className="h-4 w-4 animate-spin text-black" />
                            ) : (
                              <HiTrash
                                className={hasEditing() ? "text-gray-200" : ""}
                              />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </ReactSortable>
          {!form.values || !form.values.length || !hasEditing() ? (
            <div className={"flex justify-center"}>
              <Button
                type={"button"}
                onClick={() => handleAddNewAcitivityBtnClick()}
                accent={isMobile ? "primary" : "outline"}
                leadingIcon={<HiOutlinePlus />}
                isBlock={isMobile}
              >
                {t("user_profile_more_section_btn_add_other")}
              </Button>
            </div>
          ) : (
            ""
          )}
        </form>
      </Modal.Body>
    </Modal>
  );
};

export default TheOtherFormModal;
