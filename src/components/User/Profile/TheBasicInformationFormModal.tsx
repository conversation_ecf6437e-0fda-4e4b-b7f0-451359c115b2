"use client";
import React, { FC, useEffect, useState } from "react";
import { Modal, TextInput } from "flowbite-react";
import NoAvatar from "@/assets/images/no-avatar.svg";
import Image from "next/image";
import { HiCamera, HiExclamationCircle, HiXMark } from "react-icons/hi2";
import { useFormik } from "formik";
import useUserProfile from "@/hooks/useUserProfile";
import { patchUserProfile, uploadUserProfileAvatar } from "@/services/userAPI";
import Datepicker from "react-tailwindcss-datepicker";
import { classNames } from "@/utils";
import Select from "react-select";
import { getAllProvinces } from "@/services/areaAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import { BasicInformationSchema } from "@/schemas/UserProfileSchema";
import { useTranslations } from "next-intl";
import { useAppSelector } from "@/store";
import { VscLoading } from "react-icons/vsc";
import { Button } from "@/components/Button";
import Profile, { ProfileSkill } from "@/types/profile";
import { useProfilePopupContext } from "./TheProfilePage";
import { PROFILE_CONTEXT_TYPE, PROFILE_STATUS } from "@/contansts/userProfiles";
import { isMobile } from "react-device-detect";
interface Props {
  openModal: boolean;
  onClose?: () => void;
  stateTypeProp?: string;
  setIsUpdateSuccess?: (showOpenToWork: any) => void;
}

interface BasicInformationFormValue {
  display_name: string | null;
  birthday: string | null;
  gender: string | null;
  position: string | null;
  years_of_exp: number | null;
  phone: string | null;
  address: string | null;
  province_code: string | null;
  province_name: string | null;
  linkedin_link: string | null;
  github_link: string | null;
  avatar_url: string | null;
  technical_skills: ProfileSkill[];
}

const TheBasicInformationFormModal: FC<Props> = ({
  openModal = false,
  onClose,
  stateTypeProp = null,
  setIsUpdateSuccess,
}) => {
  const t = useTranslations();
  const [userProfile, setUserProfile] = useUserProfile();
  const userEmail = useAppSelector((state) => state.user?.user?.email ?? "");
  const [isUpdating, setIsUpdating] = useState(false);
  const [isShowBannerRemind, setIsShowBannerRemind] = useState(true);
  const skills = useAppSelector((state) => state.taxonomies.skills);
  const profilePopupContext = useProfilePopupContext();
  const form = useFormik<BasicInformationFormValue>({
    initialValues: {
      display_name: userProfile.display_name,
      birthday: userProfile.birthday,
      gender: userProfile.gender,
      position: userProfile.position,
      years_of_exp: userProfile.years_of_exp as number,
      phone: userProfile.phone,
      address: userProfile.address,
      province_code: userProfile.province_code,
      province_name: userProfile.province_name,
      linkedin_link: userProfile.linkedin_link,
      github_link: userProfile.github_link,
      avatar_url: userProfile.avatar_url,
      technical_skills: userProfile.skills?.technical_skills ?? [],
    },
    validationSchema: BasicInformationSchema(t),
    onSubmit: async (values) => {
      setIsUpdating(true);
      patchUserProfile({
        basic_info: {
          display_name: values.display_name,
          phone: values.phone,
          gender: values.gender,
          birthday: values.birthday,
          position: values.position,
          years_of_exp: values.years_of_exp,
          address: values.address,
          province_code: values.province_code,
          linkedin_link: values.linkedin_link,
          github_link: values.github_link,
          avatar_url: values.avatar_url,
        },
        skills: {
          technical_skills: values.technical_skills,
        },
      })
        .then((response) => {
          const profile: Profile = response.data.data;
          const isCheckManualFill =
            (profilePopupContext.state.type ===
              PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO ||
              stateTypeProp === PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO) &&
            [PROFILE_STATUS.standard, PROFILE_STATUS.booster].includes(
              profile.status ?? "",
            );
          setUserProfile(profile);

          if (isCheckManualFill) {
            profilePopupContext.dispatch(
              PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_TO_WORK,
            );
            ToastNotification({
              icon: "success",
              title: t("user_profile_save_success_title"),
              description: t("user_profile_activate_open_to_work_success"),
              timer: 2000,
              timerProgressBar: true,
            });
            if (
              stateTypeProp === PROFILE_CONTEXT_TYPE.TOGGLE_OPEN_BASIC_INFO &&
              setIsUpdateSuccess &&
              onClose
            ) {
              onClose();
              setIsUpdateSuccess(response.data.error == false);
            }
          } else {
            ToastNotification({
              icon: "success",
              title: t("user_profile_save_success_title"),
              description: t("user_profile_toast_save_success_message", {
                section: t("user_profile_basic_info_modal_header_title"),
              }),
              timer: 2000,
            });
            onClose && onClose();
          }
        })
        .finally(() => setIsUpdating(false));
    },
  });

  useEffect(() => {
    // Set values of forms
    form.resetForm({
      values: {
        display_name: userProfile.display_name,
        birthday: userProfile.birthday ?? "",
        gender: userProfile.gender ?? null,
        position: userProfile.position ?? "",
        years_of_exp: userProfile.years_of_exp ?? null,
        phone: userProfile.phone ?? "",
        address: userProfile.address ?? "",
        province_code: userProfile.province_code ?? "",
        province_name: userProfile.province_name ?? "",
        linkedin_link: userProfile.linkedin_link ?? "",
        github_link: userProfile.github_link ?? "",
        avatar_url: userProfile.avatar_url ?? null,
        technical_skills: userProfile.skills?.technical_skills ?? [],
      },
    });

    // Set value for birthday field
    if (!!userProfile.birthday) {
    }
  }, [userProfile]);

  const [provinceOptions, setProvinceOptions] = useState<
    { value: string; label: string }[]
  >([]);
  useEffect(() => {
    getAllProvinces().then((provinces: { id: string; text: string }[]) =>
      setProvinceOptions(
        provinces.map((province) => {
          return {
            label: province.text,
            value: province.id,
          };
        }),
      ),
    );
  }, []);

  const customKeyNotificationsForLocale = {
    "Avatar is required": "user_profile_basic_info_validate_avatar_required",
    "Avatar extension is not accepted. Please choose jpg, jpeg, png, bmp, tiff file.":
      "user_profile_basic_info_validate_avatar_mimes",
    "Avatar size is too big. Maximum is 5MB":
      "user_profile_basic_info_validate_avatar_max",
  };

  const handleUploadFile = (file: File) => {
    const formData = new FormData();
    formData.append("files", file);

    uploadUserProfileAvatar(formData)
      .then((response) => {
        form.setFieldValue("avatar_url", response.data.avatar_url);
      })
      .catch((error) => {
        const textErrors: Array<string> =
          error?.response?.data?.errors?.files.map(
            (value: keyof typeof customKeyNotificationsForLocale) =>
              t(customKeyNotificationsForLocale[value]),
          );

        if (!textErrors?.toString()) return;

        ToastNotification({
          icon: "error",
          title: "Error!",
          description: textErrors?.toString(),
          timer: 5000,
        });
      });
  };
  // handle close modal and set banner remind
  const handleClose = () => {
    if (onClose) onClose();
    setIsShowBannerRemind(true);
  };

  return (
    <div>
      <Modal
        style={
          isMobile
            ? {
                padding: 0,
                height: "auto", // Adjust height as needed
                position: "fixed",
                bottom: 0,
                left: 0,
                right: 0,
              }
            : {}
        }
        show={openModal}
        onClose={handleClose}
        size={"3xl"}
        className="custom-modal-profile h-screen"
      >
        <Modal.Header
          // style={{
          //
          // }}
          className={"rounded-tl-xl md:rounded"}
        >
          <p className={"font-bold text-black"}>
            {t("user_profile_basic_info_modal_header_title")}
          </p>
          <p className={"text-base font-normal text-gray-400"}>
            {t("user_profile_basic_info_modal_header_description")}
          </p>
        </Modal.Header>
        {isShowBannerRemind && userProfile.status == "new_star" && (
          <div
            id="banner-recommend-in-basic-infor-modal"
            className="h-auto w-full border-blue-500 bg-blue-50 px-4 py-2"
          >
            <div className="flex flex-row gap-2">
              <span>
                <HiExclamationCircle className="text-blue-600" size={24} />
              </span>
              <div className="flex flex-col">
                <span className="text-sm font-semibold text-neutral-950">
                  {t("information_missing_banner_title_basic_info_modal")}
                </span>
                <span className="text-sm font-normal text-neutral-500">
                  {t("information_missing_banner_description_basic_info_modal")}
                </span>
              </div>
              <span
                className="flex items-center"
                onClick={() => setIsShowBannerRemind(false)}
              >
                <HiXMark
                  size={18}
                  className="cursor-pointer text-neutral-900"
                />
              </span>
            </div>
          </div>
        )}
        <Modal.Body>
          <form id={"basic-information-form"} onSubmit={form.handleSubmit}>
            <div className="space-y-6">
              <h2 className={"text-sm font-bold uppercase text-gray-400"}>
                {t("user_profile_basic_info_modal_general_title")}
              </h2>
              <div className={"flex flex-wrap gap-3 md:gap-5"}>
                <div className="w-full md:w-auto">
                  <div
                    className={classNames(
                      isMobile ? "m-auto h-25 w-25" : "h-40 w-40",
                      "relative overflow-hidden rounded-full bg-gray-200",
                    )}
                  >
                    <Image
                      src={form.values.avatar_url ?? NoAvatar}
                      width={isMobile ? 100 : 160}
                      height={isMobile ? 100 : 160}
                      alt={"Avatar"}
                      className={classNames(
                        isMobile ? "h-25 w-25" : "h-40 w-40",
                        "rounded-full",
                      )}
                      priority={!!userProfile?.avatar_url ?? false}
                    />
                    <label
                      className={classNames(
                        isMobile ? "text-base" : "text-4xl",
                        "absolute bottom-0 flex w-full cursor-pointer justify-center py-2 text-white backdrop-blur",
                      )}
                    >
                      <input
                        type={"file"}
                        className={"hidden"}
                        onChange={(event) => {
                          if (event.target.files) {
                            handleUploadFile(event.target.files[0]);
                          }
                        }}
                      />
                      <HiCamera />
                    </label>
                  </div>
                </div>
                <div className={"grid grow grid-cols-2 gap-5"}>
                  <div className={"form-group col-span-2"}>
                    <label
                      htmlFor="display_name"
                      className={"text-sm font-bold text-gray-500"}
                    >
                      {t("user_profile_basic_info_modal_field_fullname")}
                      <span className={"font-normal text-primary"}>*</span>
                    </label>
                    <div className="relative">
                      <TextInput
                        id="display_name"
                        value={form.values.display_name as string}
                        placeholder={t(
                          "user_profile_basic_info_modal_field_placeholder_display_name",
                        )}
                        onChange={(event) =>
                          form.setFieldValue("display_name", event.target.value)
                        }
                        color={
                          !form.values.display_name ||
                          (form.errors.display_name &&
                            form.touched.display_name)
                            ? "failure"
                            : "gray"
                        }
                      />

                      {!form.values.display_name ||
                      (form.errors.display_name &&
                        form.touched.display_name) ? (
                        <HiExclamationCircle className="absolute right-3 top-3 text-primary-300" />
                      ) : (
                        ""
                      )}
                    </div>
                    {form.errors.display_name && form.touched.display_name ? (
                      <div className={"text-sm text-primary-300"}>
                        {form.errors.display_name}
                      </div>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className={"form-group col-span-2 md:col-span-1"}>
                    <label
                      htmlFor="birthday"
                      className={"text-sm font-bold text-gray-500"}
                    >
                      {t("user_profile_basic_info_modal_field_birthday")}
                    </label>
                    <Datepicker
                      useRange={false}
                      displayFormat="DD-MM-YYYY"
                      asSingle={true}
                      placeholder="DD/MM/YYYY"
                      onChange={(value) => {
                        form.setFieldValue("birthday", value?.startDate);
                      }}
                      value={{
                        startDate: form.values.birthday,
                        endDate: form.values.birthday,
                      }}
                      disabledDates={[
                        {
                          startDate: new Date(),
                          endDate: "9999-12-31",
                        },
                      ]}
                      popoverDirection="down"
                      inputId="birthday"
                    />
                    {form.errors.birthday && form.touched.birthday ? (
                      <div className={"text-sm text-primary-300"}>
                        {form.errors.birthday}
                      </div>
                    ) : (
                      ""
                    )}
                  </div>

                  <div className={"form-group col-span-2 md:col-span-1"}>
                    <label
                      htmlFor=""
                      className={"text-sm font-bold text-gray-500"}
                    >
                      {t("user_profile_basic_info_modal_field_sex")}
                    </label>
                    <div
                      className={
                        "grid grid-cols-3 justify-items-stretch overflow-hidden"
                      }
                    >
                      <button
                        type={"button"}
                        className={classNames(
                          "h-[40px] rounded-bl rounded-tl border",
                          form.values.gender == "Male"
                            ? "border-primary-300 bg-brand-100 text-primary-300"
                            : "",
                        )}
                        onClick={() => form.setFieldValue("gender", "Male")}
                      >
                        {t("user_profile_basic_info_modal_field_sex_male")}
                      </button>
                      <button
                        type={"button"}
                        className={classNames(
                          "h-[40px] border",
                          form.values.gender == "Female"
                            ? "border-primary-300 bg-brand-100 text-primary-300"
                            : "",
                        )}
                        onClick={() => form.setFieldValue("gender", "Female")}
                      >
                        {t("user_profile_basic_info_modal_field_sex_female")}
                      </button>
                      <button
                        type={"button"}
                        className={classNames(
                          "h-[40px] rounded-br rounded-tr border",
                          form.values.gender == "Homosexual"
                            ? "border-primary-300 bg-brand-100 text-primary-300"
                            : "",
                        )}
                        onClick={() =>
                          form.setFieldValue("gender", "Homosexual")
                        }
                      >
                        {t("user_profile_basic_info_modal_field_sex_na")}
                      </button>
                    </div>
                    {form.errors.gender && form.touched.gender ? (
                      <div className={"text-sm text-primary-300"}>
                        {form.errors.gender}
                      </div>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </div>

              <h2 className={"text-sm font-bold uppercase text-gray-400"}>
                {t("user_profile_basic_info_modal_technical_title")}
              </h2>

              <div className={"grid grid-cols-2 gap-3 md:gap-5"}>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor="position"
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_basic_info_modal_field_position")}
                    <span className={"font-normal text-primary"}>*</span>
                  </label>
                  <div className="relative">
                    <TextInput
                      value={form.values.position as string}
                      id="position"
                      placeholder={t(
                        "user_profile_basic_info_modal_field_placeholder_position",
                      )}
                      onChange={(event) =>
                        form.setFieldValue("position", event.target.value)
                      }
                      color={
                        !form.values.position ||
                        (form.errors.position && form.touched.position)
                          ? "failure"
                          : "gray"
                      }
                    />
                    {!form.values.position ||
                    (form.errors.position && form.touched.position) ? (
                      <HiExclamationCircle className="absolute right-3 top-3 text-primary-300" />
                    ) : (
                      ""
                    )}
                  </div>
                  {form.errors.position && form.touched.position ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.position}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor=""
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_basic_info_modal_field_exp")}
                    <span className={"font-normal text-primary"}>*</span>
                  </label>
                  <div className="relative">
                    <TextInput
                      value={form.values.years_of_exp ?? "null"}
                      min={0}
                      type="number"
                      onChange={(event) => {
                        if (
                          event.target.value &&
                          Number(event.target.value) < 0
                        )
                          return;
                        form.setFieldValue(
                          "years_of_exp",
                          event.target.value ?? "null",
                        );
                      }}
                      color={
                        !form.values.years_of_exp ||
                        (form.errors.years_of_exp && form.touched.years_of_exp)
                          ? "failure"
                          : "gray"
                      }
                      placeholder={t(
                        "user_profile_basic_info_modal_field_placeholder_years_of_exp",
                      )}
                    />
                    {!form.values.years_of_exp ||
                    (form.errors.years_of_exp && form.touched.years_of_exp) ? (
                      <HiExclamationCircle className="absolute right-3 top-3 text-primary-300" />
                    ) : (
                      ""
                    )}
                  </div>
                  {form.errors.years_of_exp && form.touched.years_of_exp ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.years_of_exp}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor=""
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_basic_info_modal_field_email")}
                  </label>
                  <TextInput value={userEmail} disabled />
                </div>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor=""
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_basic_info_modal_field_phone")}
                    <span className={"font-normal text-primary"}>*</span>
                  </label>
                  <div className="relative">
                    <TextInput
                      value={form.values.phone as string}
                      placeholder={t(
                        "user_profile_basic_info_modal_field_placeholder_phone",
                      )}
                      onChange={(event) =>
                        form.setFieldValue("phone", event.target.value)
                      }
                      color={
                        !form.values.phone ||
                        (form.errors.phone && form.touched.phone)
                          ? "failure"
                          : "gray"
                      }
                    />
                    {!form.values.phone ||
                    (form.errors.phone && form.touched.phone) ? (
                      <HiExclamationCircle className="absolute right-3 top-3 text-primary-300" />
                    ) : (
                      ""
                    )}
                  </div>
                  {form.errors.phone && form.touched.phone ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.phone}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor=""
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_basic_info_modal_field_address")}
                  </label>
                  <TextInput
                    value={form.values.address as string}
                    onChange={(event) =>
                      form.setFieldValue("address", event.target.value)
                    }
                    color={
                      form.errors.address && form.touched.address
                        ? "failure"
                        : "gray"
                    }
                    placeholder={t(
                      "user_profile_basic_info_modal_field_placeholder_address",
                    )}
                  />
                  {form.errors.address && form.touched.address ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.address}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor=""
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_basic_info_modal_field_province")}
                    <span className={"font-normal text-primary"}>*</span>
                  </label>
                  <div className="relative">
                    <Select
                      options={provinceOptions}
                      classNamePrefix="select"
                      value={{
                        value: form.values.province_code,
                        label: form.values.province_name,
                      }}
                      onChange={(value) => {
                        if (value === null) return;
                        form.setFieldValue("province_code", value.value);
                        form.setFieldValue("province_name", value.label);
                      }}
                      placeholder={t(
                        "user_profile_basic_info_modal_field_placeholder_province",
                      )}
                      className={`${
                        !form.values.province_code ||
                        (form.errors.province_code &&
                          form.touched.province_code)
                          ? "select-error"
                          : ""
                      }`}
                    />
                    {!form.values.province_code ||
                    (form.errors.province_code &&
                      form.touched.province_code) ? (
                      <HiExclamationCircle className="absolute right-3 top-3 text-primary-300" />
                    ) : (
                      ""
                    )}
                  </div>
                  {form.errors.province_code && form.touched.province_code ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.province_code}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor=""
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_basic_info_modal_field_socialite_link")}
                  </label>
                  <TextInput
                    value={form.values.linkedin_link as string}
                    placeholder="https://www.linkedin.com/username"
                    onChange={(event) =>
                      form.setFieldValue("linkedin_link", event.target.value)
                    }
                    color={
                      form.errors.linkedin_link && form.touched.linkedin_link
                        ? "failure"
                        : "gray"
                    }
                  />
                  {form.errors.linkedin_link && form.touched.linkedin_link ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.linkedin_link}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <div className={"form-group col-span-2 md:col-span-1"}>
                  <label
                    htmlFor=""
                    className={
                      "hidden text-sm font-bold text-gray-500 md:block"
                    }
                  >
                    &nbsp;
                  </label>
                  <TextInput
                    value={form.values.github_link as string}
                    placeholder="https://github.com/username"
                    onChange={(event) =>
                      form.setFieldValue("github_link", event.target.value)
                    }
                    color={
                      form.errors.github_link && form.touched.github_link
                        ? "failure"
                        : "gray"
                    }
                  />
                  {form.errors.github_link && form.touched.github_link ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.github_link}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <div className="form-group col-span-2">
                  <label
                    htmlFor=""
                    className={"text-sm font-bold text-gray-500"}
                  >
                    {t("user_profile_skill_modal_field_technical_skill")}
                    <span className={"font-normal text-primary"}>*</span>
                  </label>
                  <Select
                    menuPlacement="top"
                    maxMenuHeight={120}
                    isMulti={true}
                    options={skills.map((item) => {
                      return {
                        label: item.text,
                        value: item.id,
                      };
                    })}
                    classNamePrefix="select"
                    value={form.values.technical_skills.map((item) => {
                      return { value: item.skill_id, label: item.skill_name };
                    })}
                    onChange={(value) => {
                      form.setFieldValue(
                        "technical_skills",
                        value.map((item) => {
                          return {
                            skill_id: item.value,
                            skill_name: item.label,
                          };
                        }),
                      );
                    }}
                  />
                  {form.errors.technical_skills &&
                  form.touched.technical_skills ? (
                    <div className={"text-sm text-primary-300"}>
                      {form.errors.technical_skills.toString()}
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </div>
            </div>
          </form>
        </Modal.Body>
        <Modal.Footer className={"justify-end"}>
          <Button
            accent={"primary"}
            form={"basic-information-form"}
            disabled={isUpdating}
            isBlock={isMobile}
            trailingIcon={
              isUpdating ? (
                <VscLoading className="h-6 w-6 animate-spin text-white" />
              ) : (
                ""
              )
            }
            onClick={async () => {
              const errors = await form.validateForm();
              if (!Object.values(errors).length) {
                form.submitForm();
              } else {
                Object.keys(errors).forEach((value) => {
                  form.setFieldTouched(value);
                });
              }
              return;
            }}
          >
            {t("user_profile_basic_info_modal_btn_save")}
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default TheBasicInformationFormModal;
