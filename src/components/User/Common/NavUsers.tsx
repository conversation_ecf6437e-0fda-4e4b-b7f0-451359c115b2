"use client";

import { useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { classNames } from "@/utils";

const NavUsers = () => {
  const t = useTranslations();
  const pathname = usePathname();

  return (
    <nav className={"ms:px-3 container px-0 pb-5 md:py-8"}>
      <ul className={"flex justify-stretch overflow-auto rounded bg-white"}>
        <li
          className={classNames(
            pathname === "/users/profile"
              ? "border-b-4 border-brand-600"
              : "border-b",
            "group block w-full pr-3 transition-all duration-300 md:pr-0",
          )}
        >
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/profile?src=topdev.vn&medium=userprofilemenu`}
            className={classNames(
              pathname === "/users/profile" ? "font-bold" : "",
              "block whitespace-nowrap px-3 py-4 text-center text-sm text-primary transition-all duration-300 group-hover:font-bold md:whitespace-normal md:px-0 md:py-[22px] md:text-base",
            )}
          >
            {t("user_profile_menu_title_my_profile")}
          </a>
        </li>
        <li
          className={classNames(
            pathname === "/users/job-management"
              ? "border-b-4 border-brand-600"
              : "border-b",
            "group block w-full pr-3 transition-all duration-300 md:pr-0",
          )}
        >
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/job-management?src=topdev.vn&medium=userprofilemenu`}
            className={classNames(
              pathname === "/users/job-management" ? "font-bold" : "",
              "block whitespace-nowrap px-3 py-4 text-center text-sm text-primary transition-all duration-300 group-hover:font-bold md:whitespace-normal md:px-0 md:py-[22px] md:text-base",
            )}
          >
            {t("user_profile_menu_title_job_management")}
          </a>
        </li>
        <li
          className={classNames(
            pathname === "/users/my-cv"
              ? "border-b-4 border-brand-600"
              : "border-b",
            "group block w-full pr-3 transition-all duration-300 md:pr-0",
          )}
        >
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/my-cv?src=topdev.vn&medium=userprofilemenu`}
            className={classNames(
              pathname === "/users/my-cv" ? "font-bold" : "",
              "block whitespace-nowrap px-3 py-4 text-center text-sm text-primary transition-all duration-300 group-hover:font-bold md:whitespace-normal md:px-0 md:py-[22px] md:text-base",
            )}
          >
            {t("user_profile_menu_title_cv_management")}
          </a>
        </li>
         <li
          className={classNames(
            pathname === "/users/email-management" ? "border-b-2" : "border-b",
            "group block w-full pr-3 transition-all duration-300 hover:border-b-2 md:pr-0",
          )}
        >
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/email-management?src=topdev.vn&medium=userprofilemenu`}
            className={classNames(
              pathname === "/users/email-management" ? "font-bold" : "",
              "block whitespace-nowrap md:px-0 px-3 py-4 text-center text-sm text-primary transition-all duration-300 group-hover:font-bold md:whitespace-normal md:py-[22px] md:text-base",
            )}
          >
            {t("user_profile_menu_title_email_management")}
          </a>
        </li>
        <li
          className={classNames(
            pathname === "/users/jobs-applied"
              ? "border-b-4 border-brand-600"
              : "border-b",
            "group block w-full pr-3 transition-all duration-300 md:pr-0",
          )}
        >
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/jobs-applied?src=topdev.vn&medium=userprofilemenu`}
            className={classNames(
              pathname === "/users/jobs-applied" ? "font-bold" : "",
              "block whitespace-nowrap px-3 py-4 text-center text-sm text-primary transition-all duration-300 group-hover:font-bold md:whitespace-normal md:px-0 md:py-[22px] md:text-base",
            )}
          >
            {t("user_profile_menu_title_applied_jobs")}
          </a>
        </li>
        <li
          className={classNames(
            pathname === "/users/jobs-followed"
              ? "border-b-4 border-brand-600"
              : "border-b",
            "group block w-full pr-3 transition-all duration-300 md:pr-0",
          )}
        >
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/jobs-followed?src=topdev.vn&medium=userprofilemenu`}
            className={classNames(
              pathname === "/users/jobs-followed" ? "font-bold" : "",
              "block whitespace-nowrap px-3 py-4 text-center text-sm text-primary transition-all duration-300 group-hover:font-bold md:whitespace-normal md:px-0 md:py-[22px] md:text-base",
            )}
          >
            {t("user_profile_menu_title_following_jobs")}
          </a>
        </li>
        <li
          className={classNames(
            pathname === "/users/personality-test"
              ? "border-b-4 border-brand-600"
              : "border-b",
            "group block w-full transition-all duration-300 ",
          )}
        >
          <a
            href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/personality-test?src=topdev.vn&medium=userprofilemenu`}
            className={classNames(
              pathname === "/users/personality-test" ? "font-bold" : "",
              "block whitespace-nowrap px-3 py-4 text-center text-sm text-primary transition-all duration-300 group-hover:font-bold md:whitespace-normal md:px-0 md:py-[22px] md:text-base",
            )}
          >
            {t("user_profile_menu_title_personality_test")}
          </a>
        </li>
      </ul>
    </nav>
  );
};

export default NavUsers;
