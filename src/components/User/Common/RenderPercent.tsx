import React, { <PERSON> } from "react";
import classNames from "classnames";

type RenderBagePercentProps = {
  complete_percent: number;
};

const RenderBagePercent: FC<RenderBagePercentProps> = ({
  complete_percent,
}) => {
  if (complete_percent !== undefined) {
    return (
      <span
        className={classNames(
          "items-center rounded-full border px-2 text-center text-sm font-bold",
          complete_percent < 50
            ? "border-brand-700 bg-brand-100 text-brand-700"
            : "",
          complete_percent >= 50 && complete_percent < 100
            ? "border-blue-500 bg-blue-50 text-blue-500"
            : "",
          complete_percent === 100
            ? "border-green-700 bg-green-50 text-green-700"
            : "",
        )}
      >
        {complete_percent}%
      </span>
    );
  }
  return null;
};

export default RenderBagePercent;
