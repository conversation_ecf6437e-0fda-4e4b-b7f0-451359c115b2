import React, { <PERSON> } from "react";
import { useTranslations } from "next-intl";

type NoteStatusUserProps = {
  complete_percent: number;
};

const NoteStatusUser: React.FC<NoteStatusUserProps> = ({ complete_percent }) => {
  const t = useTranslations();
  return (
    <div className="card-body text-sm text-gray-400">
      <div className="text-sm text-gray-500">
        <div className="flex-wrap items-center text-[13px]">
          {complete_percent < 50
            ? t.rich("chances_finding_user_profile_newstar", {
              red: (chunks) => (
                <span className={"font-bold text text-blue-700"}>
                  {chunks}
                </span>
              ),
            })
            : t.rich("chances_finding_user_profile_standard", {
              red: (chunks) => (
                <span className={"font-bold text text-primary"}>
                  {chunks}
                </span>
              ),
            })}
        </div>
      </div>
    </div>
  );
};

export default NoteStatusUser;
