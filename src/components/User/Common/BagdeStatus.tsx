import {
  HiMiniStar,
  HiMiniBriefcase,
  HiMiniRocketLaunch,
} from "react-icons/hi2";
import { useTranslations } from "next-intl";

const BagdeStatus = ({ status }: { status: string | null }) => {
  const t = useTranslations();

  if (status == null || status == "new_star") {
    return (
      <button
        type={"button"}
        className={
          "bg-new-star-icon no-box-shadow inline-flex w-[80px] flex-none items-center justify-center gap-1 rounded-[22px] px-1 text-xs font-bold text-white"
        }
      >
        <span className={"flex items-center justify-center text-xs"}>
          <HiMiniStar width={12} height={12}/>
        </span>
        <span>{t("user_profile_status_new_star")}</span>
      </button>
    );
  }

  if (status == "standard") {
    return (
      <button
        type={"button"}
        className={
          "bg-standard-icon no-box-shadow inline-flex w-[80px] flex-none items-center justify-center gap-1 rounded-[22px] px-1 text-xs font-bold text-white"
        }
      >
        <span className={"flex items-center justify-center text-xs"}>
          <HiMiniBriefcase />
        </span>
        <span>{t("user_profile_status_standard")}</span>
      </button>
    );
  }

  return (
    <button
      type={"button"}
      className={
        "bg-booster-icon no-box-shadow inline-flex w-[80px] flex-none items-center justify-center gap-1 rounded-[22px] px-1 text-xs font-bold text-white"
      }
    >
      <span className={"flex items-center justify-center text-xs"}>
        <HiMiniRocketLaunch />
      </span>
      <span>{t("user_profile_status_booster")}</span>
    </button>
  );
};

export default BagdeStatus;
