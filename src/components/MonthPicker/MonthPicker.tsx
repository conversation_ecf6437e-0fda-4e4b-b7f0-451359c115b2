"use client";
import { useEffect, useRef, useState } from "react";
import dayjs from "dayjs";
import { monthsPicker, yearsPicker } from "@/utils/enums";
import { classNames } from "@/utils";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";

interface MonthPickerProps {
  selected?: string;
  startDate?: string;
  endDate?: string;
  disabled?: boolean;
  placeholder?: string;
  format?: string;
  separator?: string;
  change?: boolean;
  validation?: boolean;
  onChange: (value: string) => void;
}

const MonthPicker = ({
  selected = "1970-01-01",
  disabled,
  startDate,
  endDate,
  placeholder = "DD-MM-YYYY",
  separator = "-",
  format = "DD-MM-YYYY",
  change = false,
  validation = false,
  onChange,
}: MonthPickerProps) => {
  const datepicker = useRef(null);
  const [currentYear, setCurrentYear] = useState<string>("");
  const [valueSelected, setValueSelected] = useState<string>("");
  const [showAllYear, setShowAllYear] = useState<boolean>(false);
  const [show, setShow] = useState<boolean>(false);

  useEffect(() => {
    if (!selected || (selected === "null" && show)) {
      setValueSelected(dayjs().format("YYYY-MM"));
      setCurrentYear(dayjs().format("YYYY-MM")?.split(separator)?.[0]);
      onChange(dayjs().format("YYYY-MM") + separator + "01");
      return;
    }

    setValueSelected(selected);
    setCurrentYear(selected?.split(separator)?.[0]);
  }, [selected, show]);

  //Month in value checked
  const monthValue = Number(valueSelected.split(separator)?.[1])
    ? Number(valueSelected.split(separator)?.[1])
    : "01";

  //Year in value checked
  const yearValue = Number(valueSelected.split(separator)?.[0])
    ? Number(valueSelected.split(separator)?.[0])
    : "1970";

  //Convert month and year to full date format
  const converFullMonth = (month: string) =>
    Number(month) < 10 ? "0" + month : month;

  //Choose date
  const handleSetValue = (
    valueChange: string,
    kind: "year" | "month" = "month",
  ) => {
    let month = "";
    let value = "";

    if (kind != "year") {
      month = converFullMonth(valueChange);
      value = yearValue + separator + month + separator + "01";
      setCurrentYear(String(yearValue));
      setShow(false);
    }
    if (kind == "year") {
      month = converFullMonth(String(monthValue));
      value = valueChange + separator + month + separator + "01";
      setCurrentYear(valueChange);
    }
    onChange(value);
  };

  const handleClickContext = (event: any) => {
    if (
      !!datepicker?.current &&
      !(datepicker.current as any).contains(event.target)
    ) {
      setShow(false);
      setShowAllYear(false);
    }
  };

  if (typeof window !== "undefined")
    document.addEventListener("click", handleClickContext);

  //Check and disabled year
  const isCheck = (
    valueCheck: number,
    valueStart: number,
    valueEnd: number,
  ) => {
    if (!valueStart) {
      return false;
    }

    if (!valueEnd) {
      return valueStart > valueCheck && valueCheck < 99999;
    }

    return valueStart > valueCheck && valueCheck < valueEnd;
  };

  //Check and disabled month
  const isCheckMonth = (valueCheck: number) => {
    if (!startDate) return false;

    const startValue = dayjs(startDate).unix();
    const endValue = dayjs(endDate).unix();
    const getData = valueCheck ? valueCheck : Number(valueSelected);
    return startValue > getData && getData < endValue;
  };

  const getYearInData = (date: string | undefined, separator: string) => {
    return Number(date?.split(separator)?.[0]) ?? 0;
  };

  const prevYear = () => {
    const splitMonth = getYearInData(startDate, separator);
    const year = Number(yearValue) - 1;
    if (splitMonth > year) return;

    const yearLimit = yearsPicker?.[0];
    if (year >= yearLimit) handleSetValue(String(year), "year");
  };

  const nextYear = () => {
    const splitMonth = getYearInData(endDate, separator);
    const year = Number(yearValue) + 1;
    if (splitMonth < year) return;

    const yearLimit = yearsPicker?.[(yearsPicker.length ?? 1) - 1];
    if (year <= yearLimit) handleSetValue(String(year), "year");
  };

  const chooseYear = (year: number) => {
    if (
      (!!startDate && getYearInData(startDate, separator) > year) ||
      (!!endDate && getYearInData(endDate, separator) < year)
    ) {
      return;
    }

    handleSetValue(String(year), "year");
    setShow(false);
    setShowAllYear(false);
  };

  return (
    <>
      <div ref={datepicker} className="relative w-full text-gray-700">
        <input
          className={classNames(
            validation ? "border-red-500 bg-red-50 text-red-900" : "",
            "fontLight relative w-full rounded-lg border-gray-300 bg-white py-2.5 pl-2 pr-14 text-sm tracking-wide placeholder-gray-400 transition-all duration-300 focus:border-blue-500 focus:ring focus:ring-blue-500/20 disabled:cursor-not-allowed disabled:opacity-40 dark:border-slate-600 dark:bg-slate-800 dark:text-white/80",
          )}
          placeholder={placeholder}
          autoComplete="off"
          role="presentation"
          type="text"
          value={
            !!valueSelected && valueSelected !== "null"
              ? dayjs(valueSelected).format(format)
              : ""
          }
          onChange={(event) => !!change && onChange(event?.target?.value)}
          onFocus={() => setShow(true)}
          disabled={disabled}
        />
        <button
          type="button"
          className={classNames(
            validation ? "text-red-500" : "text-gray-400",
            "absolute  right-0 h-full px-3 focus:outline-none disabled:cursor-not-allowed disabled:opacity-40",
          )}
          onClick={() => setShow(true)}
        >
          <svg
            className="h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5m-9-6h.008v.008H12v-.008zM12 15h.008v.008H12V15zm0 2.25h.008v.008H12v-.008zM9.75 15h.008v.008H9.75V15zm0 2.25h.008v.008H9.75v-.008zM7.5 15h.008v.008H7.5V15zm0 2.25h.008v.008H7.5v-.008zm6.75-4.5h.008v.008h-.008v-.008zm0 2.25h.008v.008h-.008V15zm0 2.25h.008v.008h-.008v-.008zm2.25-4.5h.008v.008H16.5v-.008zm0 2.25h.008v.008H16.5V15z"
            ></path>
          </svg>
        </button>
        {!!show && (
          <div className="opacity-1 absolute z-[15] mt-[1px] block w-full max-w-[300px] translate-y-0 cursor-pointer select-none text-sm transition-all duration-300 ease-out sm:text-2xl lg:text-xs">
            <div className="mt-1 overflow-hidden rounded border border-gray-200 bg-white shadow-md dark:border-slate-600 dark:bg-slate-800 dark:text-white">
              <div className="header-picker flex w-full items-center justify-between bg-gray-100 px-6 py-3">
                <span
                  onClick={() => prevYear()}
                  className="flex h-7 w-7 items-center justify-center rounded text-lg hover:bg-primary hover:text-white"
                >
                  <IoIosArrowBack />
                </span>
                <ul
                  onClick={() => setShowAllYear((pre) => !pre)}
                  className="flex items-center justify-between text-base"
                >
                  {yearsPicker.map((year, index) => {
                    return (
                      <li
                        className={classNames(
                          yearValue == year ? "" : "hidden",
                        )}
                        key={index}
                      >
                        {year}
                      </li>
                    );
                  })}
                </ul>
                <span
                  onClick={() => nextYear()}
                  className="flex h-7 w-7 items-center justify-center rounded text-lg hover:bg-primary hover:text-white"
                >
                  <IoIosArrowForward />
                </span>
                {showAllYear && (
                  <ul className="absolute left-0 top-0 z-[5] flex h-full w-full flex-wrap items-center justify-between gap-1 overflow-auto bg-white p-4 text-base">
                    {yearsPicker.map((year, index) => {
                      const isCheckYear =
                        (!!startDate &&
                          getYearInData(startDate, separator) > year) ||
                        (!!endDate && getYearInData(endDate, separator) < year);

                      const convertClass =
                        "w-[18.5%] rounded  p-1 text-center text-sm hover:bg-slate-400 hover:text-white" +
                        (isCheckYear ? " opacity-50 cursor-not-allowed" : "") +
                        (yearValue == year
                          ? " bg-primary text-white"
                          : " bg-gray-100");
                      return (
                        <li
                          className={classNames(convertClass)}
                          onClick={() => chooseYear(year)}
                          key={index}
                        >
                          {year}
                        </li>
                      );
                    })}
                  </ul>
                )}
              </div>
              <div className="body-picker grid grid-cols-4 gap-3 p-4">
                {monthsPicker.map((month, index) => {
                  return (
                    <button
                      key={index}
                      className={classNames(
                        monthValue == index + 1
                          ? "bg-primary-100 text-primary-300"
                          : "",
                        isCheckMonth(
                          dayjs(
                            `${currentYear}${separator}${
                              index + 1
                            }${separator}01`,
                          ).unix(),
                        )
                          ? "opacity-50"
                          : "",
                        "rounded p-4 text-base text-gray-600 hover:bg-slate-400 hover:text-white",
                      )}
                      role="button"
                      onClick={() => handleSetValue(String(index + 1))}
                      disabled={isCheckMonth(
                        dayjs(
                          `${currentYear}${separator}${
                            index + 1
                          }${separator}01`,
                        ).unix(),
                      )}
                    >
                      {month}
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default MonthPicker;
