import { getRecommendJobs } from "@/services/jobAPI";
import { SearchMetaType } from "@/types/search";
import isDevice from "@/utils/device";
import { useTranslations } from "next-intl";
import CardInformativeList from "../Card/Job/CardInformativeList";
import CardInformativeListMobile from "../Card/Job/mobile/CardInformativeList.mobile";
import SearchNotFound from "../Search/SearchNotFound";
import LoadMoreRecommendJobs from "./LoadMoreData/LoadMoreRecommendJobs";
import LoadMoreRecommendJobsMobile from "./LoadMoreData/mobile/LoadMoreRecommendJobs.mobile";

const RecommendedJobTitle = () => {
  const t = useTranslations();
  return (
    <h3 className="text-base font-bold lg:text-3xl">
      {t("search_page_recommend_jobs_for_you")}
    </h3>
  );
};
type SearchNotFoundSectionProp = {
  meta?: null | SearchMetaType;
  keyword?: string; // Add keyword prop for SSR
};
const SearchNotFoundSection = async ({
  meta = null,
  keyword,
}: SearchNotFoundSectionProp) => {
  const recommendJobs = await getRecommendJobs(1);
  const device = isDevice();
  return (
    <section>
      <SearchNotFound meta={meta} keyword={keyword} />
      <div className="mt-8">
        <RecommendedJobTitle />
        <ul className="mt-4">
          {recommendJobs.data.map((jobItem, index) => {
            return (
              <li key={index} className="mt-4 first:mt-0">
                {device === "desktop" ? (
                  <CardInformativeList job={jobItem} />
                ) : (
                  <CardInformativeListMobile job={jobItem} />
                )}
              </li>
            );
          })}
        </ul>
        {device === "desktop" ? (
          <LoadMoreRecommendJobs
            currentPage={recommendJobs.meta.current_page}
            totalPage={recommendJobs.meta.last_page}
          />
        ) : (
          <LoadMoreRecommendJobsMobile
            currentPage={recommendJobs.meta.current_page}
            totalPage={recommendJobs.meta.last_page}
          />
        )}
      </div>
    </section>
  );
};

export default SearchNotFoundSection;
