"use client";
import { useTabContext } from "@/components/Tab/TabGroup";
import { CompanyType } from "@/types/company";
import { SearchFiltersType, SearchUnitsType, TabType } from "@/types/search";
import React, { FC } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import _ from "lodash";
import { Link } from "@/navigation";
import Image from "next/image";
import "swiper/css";
import { CardCompanyListMobile } from "@/components/Card/Company";
import { BRAND_LOGO } from "@/utils/image";
import LoadMoreResultCompaniesMobile from "../LoadMoreData/mobile/LoadMoreResultCompanies.mobile";
import { useTranslations } from "next-intl";
import { Lang } from "@/types/page";
interface Props {
  companies: CompanyType[];
  units: SearchUnitsType;
  filters: SearchFiltersType;
  locale: Lang,
}

const SearchResultCompaniesSectionMobile: FC<Props> = ({
  companies,
  units,
  filters,
  locale
}) => {
  const t = useTranslations();
  const { state } = useTabContext();

  if (state.type === TabType.TAB_COMPANY) {
    return (
      <section data-tab="tab-company" id="tab-company">
        <div>
          <h2 className="text-xl font-bold capitalize">
            {t("search_page_companies_information")}
          </h2>
          <ul className="mt-4">
            {companies.map((companyItem, index) => {
              return (
                <li key={index} className="mt-4 first:mt-0">
                  <CardCompanyListMobile locale={locale as Lang} companyData={companyItem} />
                </li>
              );
            })}
          </ul>
          {units.meta.companies.current_page <
            units.meta.companies.last_page && (
            <LoadMoreResultCompaniesMobile
              currentPage={units.meta.companies.current_page}
              totalPage={units.meta.companies.last_page}
              filters={filters}
              locale={locale as Lang}
            />
          )}
        </div>
      </section>
    );
  }
  return (
    <section className="mt-8">
      <h2 className="text-xl font-bold capitalize">
        {t("search_page_companies_information")}
      </h2>
      <div className="mt-4">
        <Swiper
          slidesPerView={3.5}
          spaceBetween={16}
          autoplay={{
            delay: 3000,
          }}
        >
          {_.chunk(companies, 2).map((companiesGroup, index) => {
            return (
              <SwiperSlide key={index}>
                {companiesGroup.map((companyItem, companyIndex) => {
                  return (
                    <div key={companyIndex} className="mt-4 first:mt-0">
                      <Link
                        href={{
                          pathname: "/companies/[slug]",
                          params: {
                            slug: companyItem.slug + "-" + companyItem.id,
                          },
                        }}
                        target="_blank"
                      >
                        <Image
                          src={companyItem.image_logo ?? ""}
                          alt={companyItem.display_name}
                          width={BRAND_LOGO.small.width}
                          loading="lazy"
                          height={BRAND_LOGO.small.height}
                          className="h-[66px] w-[5.5rem] max-w-full rounded bg-white object-contain p-2"
                        />
                      </Link>
                    </div>
                  );
                })}
              </SwiperSlide>
            );
          })}
        </Swiper>
      </div>
    </section>
  );
};

export default SearchResultCompaniesSectionMobile;
