"use client";
import React, { FC, useEffect } from "react";
import { getAccessToken } from "@/services/connector";
import { useTranslations } from "next-intl";

const JobTitle: FC<{ size: number }> = ({ size }) => {
  const t = useTranslations();

  const setTokenOnMobileApp = (token: string) => {
    setTimeout(() => {
      localStorage.setItem("accessToken", token);
    }, 500);
  };

  useEffect(() => {
    
    // Init access token
    getAccessToken();

    // Handle set access token
    (window as any).setAccessToken = async function (token: string) {
      // Set token
      if (token) {
        console.log(token, "token **************");

        if (localStorage.getItem('accessToken')) return;

        setTokenOnMobileApp(token);

        setTimeout(() => {
          // Reload screen
          window.location.reload();
        }, 1500);
      }
    };
  }, [size]);

  return <span>{size > 1 ? t("search_page_jobs") : t("search_page_job")}</span>;
};

export default JobTitle;
