import { getAllJobs } from "@/services/jobAPI";
import React from "react";
import CardInformativeListMobile from "../../Card/Job/mobile/CardInformativeList.mobile";
import { getAllTaxonomies } from "@/services/taxonomyAPI";
import LoadMoreJobMobile from "../../Job/mobile/LoadMoreJobMobile";
import { getLocaleForParams } from "@/utils/localeServer";
import JobTitle from "./JobTitle";

const AllJobsSectionMobile = async () => {
  const [taxonomies, allJobs] = await Promise.all([
    getAllTaxonomies(),
    getAllJobs(getLocaleForParams()),
  ]);

  return (
    <section className="mt-6">
      <h1 className="font-bold">
        <span className="text-primary">{allJobs.meta.total}</span>{" "}
        <JobTitle size={allJobs.meta.total} />
      </h1>
      <div className="mt-2">
        <ul>
          {allJobs.data.map((jobItem, index) => {
            return (
              <li key={index} className="mb-4">
                <CardInformativeListMobile job={jobItem} />
              </li>
            );
          })}
        </ul>
        <LoadMoreJobMobile
          pageSize={allJobs.meta.per_page}
          totalPage={allJobs.meta.last_page}
          taxonomies={taxonomies as any}
          currentPage={allJobs.meta.current_page}
        />
      </div>
    </section>
  );
};

export default AllJobsSectionMobile;
