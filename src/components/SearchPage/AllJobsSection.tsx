import { getAllJobs } from "@/services/jobAPI";
import { getAllTaxonomies } from "@/services/taxonomyAPI";
import React, { FC } from "react";
import CardInformativeList from "../Card/Job/CardInformativeList";
import Link from "@/components/Link/Link";
import Image from "next/image";
import LoadMoreJob from "../Job/LoadMoreJob";
import { useTranslations } from "next-intl";
import { getLocaleForParams } from "@/utils/localeServer";
import isDevice from "@/utils/device";
import AllJobsSectionMobile from "./mobile/AllJobsSectionMobile";
import Script from "next/script";

const JobTitle: FC<{ size: number }> = ({ size }) => {
  const t = useTranslations();
  return size > 1 ? <>{t("search_page_jobs")}</> : <>{t("search_page_job")}</>;
};

const AllJobsSection = async () => {
  const device = isDevice();
  const [taxonomies, allJobs] = await Promise.all([
    getAllTaxonomies(),
    getAllJobs(getLocaleForParams()),
  ]);

  if (device === "mobile") {
    return <AllJobsSectionMobile />;
  }
  return (
    <section>
      {/* all jobs */}
      <h1 className="text-xl font-bold text-black">
        <span className="text-primary">{allJobs.meta.total}</span>{" "}
        <JobTitle size={allJobs.meta.total} />
      </h1>
      <ul className="mt-4">
        {allJobs.data.slice(0, 5).map((jobItem, index) => {
          return (
            <li key={jobItem.id} className="mb-4 last:mb-0">
              <CardInformativeList job={jobItem} />
            </li>
          );
        })}
        <li className="mb-4 last:mb-0">
          <div
            id="div-gpt-ad-1698056646871-0"
            className="min-h-[102px] min-w-[832px]"
          >
            <Script id="ggad-search-middle">
              {`googletag.cmd.push(function() { googletag.display('div-gpt-ad-1698056646871-0'); });`}
            </Script>
          </div>
        </li>
        {allJobs.data.slice(5).map((jobItem, index) => {
          return (
            <li key={jobItem.id} className="mb-4 last:mb-0">
              <CardInformativeList job={jobItem} />
            </li>
          );
        })}
        <LoadMoreJob
          currentPage={allJobs.meta.current_page}
          totalPage={allJobs.meta.last_page}
          pageSize={allJobs.meta.per_page}
          taxonomies={taxonomies as any}
        />
      </ul>
    </section>
  );
};

export default AllJobsSection;
