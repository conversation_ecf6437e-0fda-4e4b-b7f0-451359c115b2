"use client";
import { getJobsFitForYou } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { getCurrentLocaleForParams } from "@/utils/locale";
import { useTranslations } from "next-intl";
import CardJobList from "../Card/Job/CardJobList";
import { useEffect, useState } from "react";

const JobFitTitle = () => {
  const t = useTranslations();
  return (
    <h2 className="text-lg font-semibold">
      {t("search_page_jobs_fit_for_you")}
    </h2>
  );
};

const JobsFitForYouSection = () => {
  const [jobs, setJobs] = useState<JobType[]>([]);

  useEffect(() => {
    getJobsFitForYou(getCurrentLocaleForParams())
      .then((response) => {
        setJobs(response.data.data as JobType[]);
      })
      .catch((error) => {
        setJobs([]);
      });

  }, []);

  return (
    <div className="mt-8 overflow-hidden rounded border border-solid border-gray-200">
      <div className="border-b border-solid border-gray-200 px-4 py-2">
        <JobFitTitle />
      </div>
      <div className="bg-white">
        <ul>
          {jobs && jobs.length > 0
            ? jobs.map((jobItem) => {
              console.log();
              return (
                <li
                  key={jobItem.id}
                  className="border-b border-solid border-gray-200 last:border-b-0"
                >
                  <CardJobList job={jobItem} />
                </li>
              );
            })
            : null}
        </ul>
      </div>
    </div>
  );
};

export default JobsFitForYouSection;
