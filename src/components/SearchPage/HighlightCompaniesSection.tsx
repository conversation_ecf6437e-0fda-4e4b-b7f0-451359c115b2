import React from "react";
import { CardCompanyBrandingSearchPage } from "../Card/Company";
import { getAllTaxonomies } from "@/services/taxonomyAPI";
import { getHighlightCompanies } from "@/services/companyAPI";
import { useTranslations } from "next-intl";
import { Lang } from "@/types/page";

const HighlightCompaniesTitle = () => {
  const t = useTranslations();
  return (
    <h2 className="text-xl font-bold">
      {t("search_page_highlight_companies")}
    </h2>
  );
};

const HighlightCompaniesSection = async ({locale}: {locale: Lang}) => {
  const taxonomies = await getAllTaxonomies();
  const highlightCompanies = await getHighlightCompanies();

  return (
    <div>
      <div>
        <HighlightCompaniesTitle />
      </div>
      <div className="mt-4">
        <CardCompanyBrandingSearchPage
          companyData={highlightCompanies.data[0]}
          taxonomies={taxonomies as any}
          locale={locale as Lang}
        />
      </div>
    </div>
  );
};

export default HighlightCompaniesSection;
