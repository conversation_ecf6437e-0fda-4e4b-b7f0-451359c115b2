"use client";

import { Button } from "@/components/Button";
import CardInformativeList from "@/components/Card/Job/CardInformativeList";
import Loading from "@/components/Common/Loading";
import { getJobBySkill } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { LocaleType } from "@/types/page";
import { useTranslations } from "next-intl";
import React, { FC, useState } from "react";

interface Props {
  currentPage: number;
  pageSize: number;
  skillId: number;
  exceptSkills: string;
  locale: LocaleType;
}

const LoadMoreJobBySkill: FC<Props> = (props) => {
  const { currentPage, pageSize, skillId, exceptSkills, locale } = props;
  const [page, setPage] = useState(currentPage + 1);
  const [jobs, setJobs] = useState<JobType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations();

  const handleLoadMore = () => {
    setIsLoading(true);
    getJobBySkill({
      skillId,
      currentPage: page,
      exceptSkills,
      locale,
      pageSize,
    })
      .then((response) => {
        const data = response.data;
        setJobs((prev) => [...prev, ...data]);
        setPage((prev) => prev + 1);
        setIsLoading(false);
      })
      .catch((error) => {
        setIsLoading(false);
      });
  };

  return (
    <div>
      {jobs.length > 0 && (
        <ul className="mt-4">
          {jobs.map((jobItem, index) => {
            return (
              <li key={index} className="mb-4 last:mb-0">
                <CardInformativeList job={jobItem} />
              </li>
            );
          })}
        </ul>
      )}
      {isLoading && (
        <div className="flex items-center justify-center py-4">
          <Loading />
        </div>
      )}
      {page < pageSize && (
        <div className="mx-auto mt-4 max-w-[238px]">
          <Button
            type="button"
            accent="outline"
            isBlock
            onClick={() => handleLoadMore()}
            size="md"
          >
            {t("detail_job_view_more")}
          </Button>
        </div>
      )}
    </div>
  );
};

export default LoadMoreJobBySkill;
