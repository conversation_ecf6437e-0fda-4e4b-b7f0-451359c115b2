"use client";

import { Button } from "@/components/Button";
import CardInformativeFreeList from "@/components/Card/Job/CardInformativeFreeList";
import CardInformativeList from "@/components/Card/Job/CardInformativeList";
import CardInformativeListMobile from "@/components/Card/Job/mobile/CardInformativeList.mobile";
import Loading from "@/components/Common/Loading";
import { DEFAULT_SEARCH_PARAMS } from "@/contansts/search";
import { SearchJobParamV2, searchJobV2 } from "@/services/searchAPI";
import { DeviceType } from "@/types/device";
import _ from "lodash";
import { JobType } from "@/types/job";
import { SearchQueriesType } from "@/types/search";
import { getCurrentLocaleForParams } from "@/utils/locale";
import { useTranslations } from "next-intl";
import { FC, useState, useRef } from "react";
import { useAppSelector } from "@/store";

interface Props {
  queries: SearchQueriesType;
  currentPage: number;
  totalPage: number;
  device: DeviceType;
}

const LoadMoreResultJobs: FC<Props> = (props) => {
  const { queries, currentPage, totalPage, device } = props;
  const [page, setPage] = useState(currentPage);
  const [jobs, setJobs] = useState<JobType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations();
  const pendingFreeJobRef = useRef<JobType | null>(null);

  const currentSearch = useAppSelector((state) => state.search.currentSearch);

  const handleLoadMore = () => {
    setIsLoading(true);

    const searchParams: SearchJobParamV2 = {
      keyword: currentSearch.keyword,
      ordering: currentSearch.sort_by,
      locale: getCurrentLocaleForParams(),
      region_ids: currentSearch.locations?.join(","),
      job_categories_ids: currentSearch.categories?.join(","),
      job_levels_ids: currentSearch.experience_levels?.join(","),
      benefit_ids: currentSearch.benefits?.join(","),
      company_size_ids: currentSearch.company_sizes?.join(","),
      job_types_ids: currentSearch.work_types?.join(","),
      salary_min: currentSearch.salary_min || undefined,
      salary_max: currentSearch.salary_max || undefined,
      contract_types_ids: currentSearch.contract_types?.join(","),
      company_industry_ids: currentSearch.company_industries?.join(","),
      page: page + 1,
      page_size: 15,
      "fields[job]": DEFAULT_SEARCH_PARAMS["fields[job]"],
      "fields[company]": DEFAULT_SEARCH_PARAMS["fields[company]"],
      _f: Math.round(Math.random() * 100000 + 10),
    };

    const cleanedParams = _.pickBy(searchParams, (v) => v !== undefined && v !== null && v !== "");

    searchJobV2(cleanedParams)
      .then((response) => {
        setJobs((prev) => [...prev, ...response.data]);
        setPage((prev) => prev + 1);
        setIsLoading(false);
      })
      .catch((error) => {
        console.error("Load more error:", error);
        setIsLoading(false);
      });
  };

  const renderJobs = () => {
    const result = [];
    let index = 0;

    if (pendingFreeJobRef.current) {
      const pendingJob = pendingFreeJobRef.current;
      pendingFreeJobRef.current = null;

      if (jobs.length > 0 && jobs[0].level === "free") {
        result.push(
          <li key={`group-${pendingJob.id}-${jobs[0].id}`} className="free-job mb-4 flex gap-3 last:mb-0">
            <CardInformativeFreeList job={pendingJob} hideImg={true} isDesktop={true} />
            <CardInformativeFreeList job={jobs[0]} hideImg={true} isDesktop={true} />
          </li>
        );
        index = 1;
      } else {
        result.push(
          <li key={pendingJob.id} className="free-job mb-4 flex gap-3 last:mb-0">
            <CardInformativeFreeList job={pendingJob} hideImg={true} isDesktop={true} />
          </li>
        );
      }
    }

    while (index < jobs.length) {
      const job = jobs[index];

      if (job.level === "free") {
        if (index + 1 < jobs.length && jobs[index + 1].level === "free") {
          result.push(
            <li key={`group-${job.id}-${jobs[index + 1].id}`} className="free-job mb-4 flex gap-3 last:mb-0">
              <CardInformativeFreeList job={job} hideImg={true} isDesktop={true} />
              <CardInformativeFreeList job={jobs[index + 1]} hideImg={true} isDesktop={true} />
            </li>
          );
          index += 2;
        } else if (index === jobs.length - 1) {
          result.push(
            <li key={job.id} className="free-job mb-4 flex gap-3 last:mb-0">
              <CardInformativeFreeList job={job} hideImg={true} isDesktop={true} />
            </li>
          );
          index++;
        } else {
          pendingFreeJobRef.current = job;
          index++;
        }
      } else {
        result.push(
          <li key={job.id} className="mb-4 last:mb-0">
            <CardInformativeList job={job} />
          </li>
        );
        index++;
      }
    }

    return result;
  };

  return (
    <div>
      {jobs.length > 0 && (
        <ul className="mt-2 lg:mt-4">
          {device === "mobile" ? (
            jobs.map((jobItem, index) => (
              <li key={index} className="mb-2 last:mb-0 lg:mb-4">
                {jobItem.level === "free" ? (
                  <CardInformativeFreeList job={jobItem} hideImg={true} />
                ) : (
                  <CardInformativeListMobile job={jobItem} />
                )}
              </li>
            ))
          ) : (
            renderJobs()
          )}
        </ul>
      )}
      {isLoading && (
        <div className="flex items-center justify-center py-2 lg:py-4">
          <Loading />
        </div>
      )}
      {page < totalPage && (
        <div className="mx-auto mt-4 text-center lg:max-w-[238px]">
          <Button
            type="button"
            accent="outline"
            isBlock={device === "mobile" ? false : true}
            onClick={() => handleLoadMore()}
            size={device === "mobile" ? "sm" : "md"}
          >
            {t("detail_job_view_more")}
          </Button>
        </div>
      )}
    </div>
  );
};

export default LoadMoreResultJobs;
