"use client";
import { Button } from "@/components/Button";
import { CardCompanyList } from "@/components/Card/Company";
import Loading from "@/components/Common/Loading";
import { loadMoreCompaniesResult } from "@/services/companyAPI";
import { CompanyType } from "@/types/company";
import { Lang } from "@/types/page";
import { SearchFiltersType } from "@/types/search";
import { useTranslations } from "next-intl";
import { FC, useCallback, useState } from "react";

interface Props {
  currentPage: number;
  totalPage: number;
  filters: SearchFiltersType;
  locale: Lang
}

const LoadMoreResultCompanies: FC<Props> = ({
  currentPage,
  totalPage,
  filters,
  locale
}) => {
  const [page, setPage] = useState(currentPage);
  const [companies, setCompanies] = useState<CompanyType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations();

  const handleLoadMoreCompany = async () => {
    setIsLoading(true);
    try {
      const response = await loadMoreCompaniesResult(
        strSkillIds(),
        strLocation(),
        strKeyword(),
        strSalaryRange(),
        page + 1,
      );
      const data = await response.data.data;
      setPage((prev) => prev + 1);
      setCompanies((prev) => [...prev, ...data]);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  const strSkillIds = useCallback(() => {
    return filters.skills_id.join(",");
  }, [filters]);

  const strLocation = useCallback(() => {
    return filters.region_ids;
  }, [filters]);

  const strKeyword = useCallback(() => {
    return filters.keyword;
  }, [filters]);

  const strSalaryRange = useCallback(() => {
    return filters.salary_range.join(",");
  }, [filters]);

  return (
    <div>
      {companies.length > 0 && (
        <ul>
          {companies.map((companyItem, index) => {
            return (
              <li key={index} className="mt-4">
                <CardCompanyList companyData={companyItem} locale={locale as Lang} />
              </li>
            );
          })}
        </ul>
      )}

      {isLoading && (
        <div className="flex items-center justify-center py-10">
          <Loading />
        </div>
      )}

      {page < totalPage && (
        <div className="mx-auto mt-4 max-w-[238px]">
          <Button
            accent="outline"
            type="button"
            isBlock
            onClick={() => handleLoadMoreCompany()}
          >
            {t("search_page_view_more_companies")}
          </Button>
        </div>
      )}
    </div>
  );
};

export default LoadMoreResultCompanies;
