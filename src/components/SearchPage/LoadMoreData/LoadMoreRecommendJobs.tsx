"use client";
import CardInformativeList from "@/components/Card/Job/CardInformativeList";
import Loading from "@/components/Common/Loading";
import { getRecommendJobs } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { TaxonomiesType } from "@/types/taxonomy";
import { getCurrentLocaleForParams } from "@/utils/locale";
import { FC, useCallback, useEffect, useRef, useState } from "react";
import { useIntersectionObserver } from "usehooks-ts";

interface Props {
  currentPage: number;
  totalPage: number;
}

const LoadMoreRecommendJobs: FC<Props> = ({ currentPage, totalPage }) => {
  const [page, setPage] = useState(currentPage + 1);
  const refLoadMore = useRef<HTMLDivElement>(null);
  const entry = useIntersectionObserver(refLoadMore, {});
  const isVisible = !!entry?.isIntersecting;
  const [jobs, setJobs] = useState<JobType[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isVisible || isLoading) {
      return;
    }
    if (page <= totalPage) {
      handleLoadMoreJob();
    }
  }, [isVisible, isLoading]);

  const handleLoadMoreJob = useCallback(async () => {
    try {
      const response = await getRecommendJobs(
        page,
      );
      const data = await response.data;
      setPage((prev) => prev + 1);
      setJobs((prev) => [...prev, ...data]);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  }, [page]);

  return (
    <div>
      {jobs.length > 0 && (
        <ul>
          {jobs.map((jobItem, index) => {
            return (
              <li key={index} className="mt-4">
                <CardInformativeList job={jobItem} />
              </li>
            );
          })}
        </ul>
      )}
      {page <= totalPage && (
        <div className="py-5">
          <Loading />
        </div>
      )}
      <div ref={refLoadMore}></div>
    </div>
  );
};

export default LoadMoreRecommendJobs;
