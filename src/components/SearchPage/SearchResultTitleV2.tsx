import React, { <PERSON> } from "react";
import ChipTag from "../Tag/ChipTag";
import { SearchMetaType, SearchUnitsType } from "@/types/search";
import isDevice from "@/utils/device";
import { useTranslations } from "next-intl";

// interface Props {
//   units: SearchUnitsType;
//   meta: SearchMetaType;
// }

interface Props {
  totalJob: number;
}

const SearchResultTitleV2: FC<Props> = ({ totalJob }) => {
  // const device = isDevice();
  const t = useTranslations();

  return (
    <h1 className="text-sm font-bold lg:text-xl">
      <span className="text-primary">{totalJob ?? 0}</span>{" "}
      {totalJob === 1
        ? t("search_page_job_text")
        : t("search_page_jobs_text")}

      {/* {meta.skills.length > 0 && (
        <span className="ml-2 inline-flex flex-wrap gap-2 font-normal">
          {meta.skills.map((skillItem) => {
            return (
              <ChipTag
                isHover={false}
                key={skillItem.id}
                title={skillItem.text}
                accent={device === "desktop" ? "line" : "blue"}
                size={device === "desktop" ? "md" : "sm"}
              />
            );
          })}
        </span>
      )}
      {meta.province_display && (
        <>
          {" "}
          <span className="inline-block font-normal">
            {t("search_page_in")}
          </span>{" "}
          <span className="font-normal">
            <ChipTag
              isHover={false}
              title={meta.province_display}
              accent={device === "desktop" ? "line" : "blue"}
              size={device === "desktop" ? "md" : "sm"}
            />
          </span>
        </>
      )}
      {meta.job_levels && meta.job_levels.length > 0 && (
        <>
          <span className="mx-2 inline-block font-normal">
            {" "}
            - {t("search_page_level")}:{" "}
          </span>
          <span className="inline-flex flex-wrap items-center gap-2 font-normal">
            {meta.job_levels.map((levelItem) => {
              return (
                <ChipTag
                  isHover={false}
                  key={levelItem.id}
                  title={levelItem.text}
                  accent={device === "desktop" ? "line" : "blue"}
                  size={device === "desktop" ? "md" : "sm"}
                />
              );
            })}
          </span>
        </>
      )}
      {meta.job_types && meta.job_types.length > 0 && (
        <>
          <span className="mx-2 inline-block font-normal">
            {" "}
            - {t("search_page_job_type")}:{" "}
          </span>
          <span className="inline-flex flex-wrap items-center gap-1 font-normal">
            {meta.job_types.map((typeItem) => {
              return (
                <ChipTag
                  isHover={false}
                  key={typeItem.id}
                  title={typeItem.text}
                  accent={device === "desktop" ? "line" : "blue"}
                  size={device === "desktop" ? "md" : "sm"}
                />
              );
            })}
          </span>
        </>
      )}
      {meta.contract_types && meta.contract_types.length > 0 && (
        <>
          <span className="mx-2 inline-block font-normal">
            {" "}
            - {t("search_page_contract_type")}:{" "}
          </span>
          <span className="inline-flex flex-wrap items-center gap-1 font-normal">
            {meta.contract_types.map((contractItem) => {
              return (
                <ChipTag
                  isHover={false}
                  key={contractItem.id}
                  title={contractItem.text}
                  accent={device === "desktop" ? "line" : "blue"}
                  size={device === "desktop" ? "md" : "sm"}
                />
              );
            })}
          </span>
        </>
      )} */}
    </h1>
  );
};

export default SearchResultTitleV2;
