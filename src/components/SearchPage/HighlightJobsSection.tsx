import { getHighlightJobs } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import isDevice, { isDesktop } from "@/utils/device";
import { getLocale, getLocaleForParams } from "@/utils/localeServer";
import { getTranslations } from "next-intl/server";
import Card<PERSON>obList from "../Card/Job/CardJobList";
import HighlightJobMobile from "../Slide/mobile/HighlightJob.mobile";
import _ from "lodash";

const HighlightJobsSection = async () => {
  const device = isDevice();
  const translate = await getTranslations();
  const highlightJobs = await getHighlightJobs(12, getLocaleForParams())
    .then((response) => {
      return { data: _.shuffle(response.data.data as JobType[]), error: false };
    })
    .catch(() => ({
      data: [],
      error: true,
    }));

  if (highlightJobs.error) {
    return null;
  }

  if (device === "mobile") {
    return <HighlightJobMobile data={highlightJobs.data.slice(0, 4)} />;
  }

  return (
    <section className="mt-8 rounded border border-solid border-primary-200">
      <div className="rounded-tl rounded-tr border-b border-solid border-primary-200 bg-primary-100 px-4 py-2">
        <h2 className="text-lg font-semibold">
          {translate("search_page_highlight_jobs")}
        </h2>
      </div>
      <ul>
        {highlightJobs &&
          highlightJobs.data.slice(0, 3).map((jobItem) => {
            return (
              <li
                key={jobItem.id}
                className="border-t border-solid border-gray-200 first:border-t-0"
              >
                <CardJobList job={jobItem} mediumPage={"jobhighlight"} />
              </li>
            );
          })}
      </ul>
    </section>
  );
};

export default HighlightJobsSection;
