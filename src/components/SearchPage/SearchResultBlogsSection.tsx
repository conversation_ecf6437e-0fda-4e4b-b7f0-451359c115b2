import { BlogType } from "@/types/blog";
import { SearchMetaType } from "@/types/search";
import Link from "@/components/Link/Link";
import React, { FC } from "react";
import CardBlogItem from "../Card/Blog/CardBlogItem";
import isDevice from "@/utils/device";
import CardBlogMain from "../Card/Blog/CardBlogMain";
import { useTranslations } from "next-intl";

interface Props {
  blogs: BlogType[];
  meta: SearchMetaType;
}
const SearchResultBlogsSection: FC<Props> = ({ blogs, meta }) => {
  const device = isDevice();
  const t = useTranslations();

  return (
    <section className="mt-8">
      <div className="flex justify-between">
        <h2 className="text-xl font-bold lg:text-3xl">
          {t("search_page_related_posts")}
        </h2>
        <Link
          target="_blank"
          href={{
            pathname: "/blog",
            query: {
              s: meta.keyword_display,
              src: "topdev_search",
              medium: "blogIT",
            },
          }}
          className="text-sm font-semibold text-primary underline lg:text-base"
        >
          {t("search_page_view_all")}
        </Link>
      </div>
      {device === "desktop" ? (
        <>
          <div className="mt-4 grid grid-cols-2 gap-6">
            <div className="col-span-1">
              {blogs.slice(0, 1).map((blogItem) => {
                return (
                  <CardBlogMain
                    key={blogItem.id}
                    post={blogItem}
                    srcPage="search"
                    mediumPage="blogIT"
                  />
                );
              })}
            </div>
            <div className="col-span-1 flex flex-col gap-6">
              {blogs.slice(1, 4).map((blogItem) => {
                return (
                  <div
                    key={blogItem.id}
                    className="border-b border-solid border-gray-200 pb-6 last:border-b-0 last:pb-0"
                  >
                    <CardBlogItem
                      post={blogItem}
                      srcPage="search"
                      mediumPage="blogIT"
                    />
                  </div>
                );
              })}
            </div>
          </div>
        </>
      ) : (
        <>
          <div className="mt-4">
            <ul>
              {blogs.map((blogItem, index) => {
                return (
                  <li
                    key={blogItem.id}
                    className="mt-4 border-b border-solid border-gray-200 pb-4 first:mt-0 last:border-none last:pb-0"
                  >
                    <CardBlogItem
                      post={blogItem}
                      srcPage="search"
                      mediumPage="blogIT"
                    />
                  </li>
                );
              })}
            </ul>
          </div>
        </>
      )}
    </section>
  );
};

export default SearchResultBlogsSection;
