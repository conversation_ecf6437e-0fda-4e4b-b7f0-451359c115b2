import { classNames } from "@/utils";
import React, { FC, useCallback } from "react";

interface Props {
  size?: "xs" | "sm" | "md";
}

const SkillTagLoading: FC<Props> = ({ size = "sm" }) => {
  const randomWidth = useCallback(() => {
    return Math.floor(Math.random() * 4 + 3);
  }, []);
  return (
    <span
      style={{ width: randomWidth() + "rem" }}
      className={classNames(
        "mr-2 inline-block animate-pulse bg-gray-200",
        size === "xs" ? "h-[1.625rem] rounded-sm" : "",
        size === "sm" ? "h-7 rounded" : "",
        size === "md" ? "h-[2.375rem] rounded" : "",
      )}
    ></span>
  );
};

export default SkillTagLoading;
