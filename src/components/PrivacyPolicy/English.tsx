import Link from "next/link";
import React from "react";

const English = () => {
  return (
    <main>
      <div id="resolve-complaints" className="bg-gray-200 py-10">
        <div className="container max-w-7xl px-5">
          <div className="lg:rounded-md bg-white p-5 lg:p-10">
            <h1 className="text-5xl text-primary">Privacy Policy</h1>
            <hr className="my-5 border-t border-solid border-gray-300" />
            <ul className="list-decimal pl-5">
              <li className="text-xl font-bold">
                <h2>Collection and Retention of Information</h2>
                <div className="mt-3 text-base font-normal">
                  <p>
                    TopDev collects personal information with user consent,
                    limited to the minimum necessary to provide its services.
                    All collected personal data is used solely for the purposes
                    communicated. Depending on the type of service provided by
                    TopDev, personal information is collected, used, and
                    retained as follows:
                  </p>
                  <table className="mb-3 w-fit">
                    <tbody>
                      <tr className="text-center align-baseline">
                        <th className="w-1/5 border border-black p-2">
                          Collected method
                        </th>
                        <th className="w-1/5 border border-black p-2">
                          Data collected
                        </th>
                        <th className="w-3/5 border border-black p-2">
                          Purpose
                        </th>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr>
                        <td
                          colSpan={3}
                          className="border border-black p-2 font-bold"
                        >
                          For Job Seekers:
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">
                          Registration via Google/GitHub/Apple ID
                        </td>
                        <td className="border border-black p-2">
                          Name, email, profile picture, language preferences.
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">
                              Create a user profile on TopDev and identify the
                              user.
                            </li>
                            <li className="pb-2">
                              Account management: TopDev provides users with a
                              dashboard to manage their application process and
                              use of TopDev&#39;s services.
                            </li>
                            <li className="pb-2">
                              Store resumes and job applications when users
                              apply for jobs, helping them manage their
                              profiles, application history, and streamline
                              future applications.
                            </li>
                            <li className="pb-2">
                              Update and add new resumes for job application
                              purposes.
                            </li>
                            <li className="pb-2">
                              Send notifications about the status of job
                              applications.
                            </li>
                            <li className="pb-2">
                              Provide information on new jobs, related job
                              opportunities, market updates, and IT knowledge.
                            </li>
                            <li className="pb-2">
                              Use available services on TopDev: job search, CV
                              creation, and workplace personality assessment.
                            </li>
                            <li className="pb-2">
                              Send notifications about changes to terms and
                              conditions, and handle complaints and requests.
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">
                          Website and Mobile App Activity
                        </td>
                        <td className="border border-black p-2">
                          Data on user interactions such as clicks, scrolls, and
                          navigation patterns.
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">
                              Improve the services provided by the{" "}
                              <Link
                                href="https://topdev.vn"
                                className="text-blue-600 underline"
                              >
                                topdev.vn
                              </Link>{" "}
                              website and mobile application
                            </li>
                            <li className="pb-2">
                              Analyze trends and statistics regarding the usage
                              of the website and mobile application
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">CV Creation</td>
                        <td className="border border-black p-2">
                          Personal details including name, email, phone number,
                          age, gender, current/past job titles, work/education
                          experience, social media links, language skills, and
                          contact preferences.
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">
                              Provide job search services and resume storage
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">
                          Job Applications
                        </td>
                        <td className="border border-black p-2">
                          Full name, email, phone number, applied job positions,
                          resumes, and other submitted data.
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">
                              Provide job search services
                            </li>
                            <li className="pb-2">
                              Send job application data and resumes to
                              employers, and allow employers to use this
                              information to contact candidates.
                            </li>
                            <li className="pb-2">
                              Store resumes and job applications to help users
                              manage their profiles, application history, and
                              apply more efficiently in future opportunities.
                            </li>
                            <li className="pb-2">
                              Send notifications regarding the status of job
                              applications.
                            </li>
                            <li className="pb-2">
                              Provide information on new jobs, related
                              opportunities, market updates, and IT knowledge.
                            </li>
                            <li className="pb-2">
                              TopDev may contact you through the communication
                              channels you have agreed to share with us
                              (including but not limited to: email, messaging
                              apps, etc.) for purposes such as verifying your
                              information and introducing suitable job
                              opportunities. Upon your consent and approval,
                              TopDev may use this information to suggest
                              potential job matches to you and to potential
                              employers. For communication that occurs outside
                              of the TopDev system, TopDev will store relevant
                              information appropriately to allow authorized
                              authorities to conduct any necessary verifications
                              if required.
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">
                          Workspace Personality Test
                        </td>
                        <td className="border border-black p-2">
                          Test results
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">
                              Process and store the results of the work
                              personality assessment. TopDev only processes and
                              stores the responses provided in the assessment
                              and does not share any personally identifiable
                              information with third parties. 4o
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                    <tr>
                      <td
                        colSpan={3}
                        className="border border-black p-2 font-bold"
                      >
                        For Employers
                      </td>
                    </tr>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">
                          Registration by username/email and password
                        </td>
                        <td className="border border-black p-2">
                          Username/email, password, company name,
                          representative&apos;s name, email, phone number.
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">
                              Verify identity and provide consulting/services
                              based on recruitment requests
                            </li>
                            <li className="pb-2">
                              Create and store employer profiles, including:
                              company information, job posting management, and
                              candidate management
                            </li>
                            <li className="pb-2">
                              Provide marketing information, such as HR
                              newsletters, job recruitment updates, and event
                              announcements
                            </li>
                            <li className="pb-2">
                              Send notifications regarding changes in terms and
                              conditions, complaint handling, and requests
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">
                          Job Posting Management
                        </td>
                        <td className="border border-black p-2">
                          Company name, phone number, email, company
                          information, multimedia content (images, other
                          enterprise-related content).
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">
                              Provide job posting and job listing management
                              services
                            </li>
                            <li className="pb-2">
                              Store company information and display such content
                              on TopDev’s Website
                            </li>
                            <li className="pb-2">
                              Update the status of job postings
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                    <tbody>
                      <tr className="align-baseline">
                        <td className="border border-black p-2">
                          Candidate Management
                        </td>
                        <td className="border border-black p-2">
                          Candidate names, phone numbers, emails, company
                          information, notes from candidate applications.
                        </td>
                        <td className="border border-black p-2">
                          <ul className="list-none">
                            <li className="pb-2">Update candidate status</li>
                            <li className="pb-2">
                              Provide candidate contact information
                            </li>
                            <li className="pb-2">
                              View candidate information when a user applies for
                              a job posted by the employer
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <p>
                    In addition, the collection of information to serve the
                    above purposes, TopDev is responsible for cooperating with
                    legal authorities to share your personal information for the
                    following purposes:
                  </p>
                  <ul className="ml-6">
                    <li className="flex space-x-3">
                      <span>- </span>
                      <span>
                        Complying with legal requirements, preventing fraud,
                        unauthorized transactions, and other legal
                        responsibilities, securing our website and application,
                        and enforcing our terms and conditions.
                      </span>
                    </li>
                    <li className="flex space-x-3">
                      <span>- </span>
                      <span>
                        In emergency situations, it is necessary to promptly
                        process relevant personal data to protect the life and
                        health of the data subject or others. The Personal Data
                        Controller, Personal Data Processor, Personal Data
                        Controller and Processor, or Third Party bears the
                        responsibility to prove such cases.
                      </span>
                    </li>
                    <li className="flex space-x-3">
                      <span>- </span>
                      <span>
                        Processing data by competent state agencies in cases of
                        emergencies concerning national defense, national
                        security, social order and safety, major disasters, and
                        dangerous epidemics; when there is a threat to security
                        or defense but it does not yet warrant the declaration
                        of a state of emergency; prevention of riots, terrorism,
                        crime, and legal violations as prescribed by law.
                      </span>
                    </li>
                  </ul>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">Data Retention Period</h2>
                <div className="mt-3 text-base font-normal">
                  <p>
                    Candidate and recruiter data will be stored
                    <span className="font-bold">
                      until a valid deletion request is made
                    </span>{" "}
                    The data is securely stored on TopDev’s servers located in
                    Vietnam.
                  </p>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">How TopDev Shares Data and With Whom</h2>
                <div className="mt-3 text-base font-normal">
                  <ul className="ml-6" style={{ listStyleType: "lower-alpha" }}>
                    <li className="font-bold">
                      How TopDev shares your personal information:
                      <ul className="font-normal">
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            With your consent: Because the information we
                            collect is your personal data, TopDev will not
                            disclose your personal information to others without
                            your consent.
                          </span>
                        </li>
                        <li>
                          <div className="flex space-x-3">
                            <span>- </span>
                            <span>
                              When legally required to share personal
                              information in cases such as:
                            </span>
                          </div>
                          <ul className="ml-6">
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Enforcing the terms and conditions outlined in
                                the Terms of Use and Privacy Policy that you
                                agreed to when registering an account and using
                                TopDev’s services.
                              </span>
                            </li>
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Responding to requests from competent
                                authorities, complying with laws, regulations,
                                and government requirements.
                              </span>
                            </li>
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Protecting the rights of other users during the
                                use of the website.
                              </span>
                            </li>
                          </ul>
                        </li>
                      </ul>
                    </li>
                    <li className="font-bold">
                      Parties to whom TopDev may share users’ personal
                      information:
                      <ul>
                        <li>
                          <div className="flex space-x-3">
                            <span>- </span>
                            <span>Recipient: Candidates</span>
                          </div>
                          <ul className="ml-6 font-normal">
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Purpose: To support candidates in their job
                                search and application process.
                              </span>
                            </li>
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Data shared: TopDev provides recruiter
                                information on the website including company
                                name, company operations information, company
                                website, images, and job postings.
                              </span>
                            </li>
                          </ul>
                        </li>
                        <li>
                          <div className="flex space-x-3">
                            <span>- </span>
                            <span>Recipient: Recruiters</span>
                          </div>
                          <ul className="ml-6 font-normal">
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Purpose: To support recruitment processes by
                                providing candidate information and application
                                documents to help recruiters evaluate and select
                                candidates.
                              </span>
                            </li>
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Data shared: Candidate information including
                                personal information, CVs, job applications, and
                                cover letters.
                              </span>
                            </li>
                          </ul>
                        </li>
                        <li>
                          <div className="flex space-x-3">
                            <span>- </span>
                            <span>Recipient: Service Providers</span>
                          </div>
                          <ul className="ml-6 font-normal">
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                Purpose: To perform services related to
                                improving the website operations, as well as
                                products, features to protect users, and
                                optimize services and user experience.
                              </span>
                            </li>
                            <li className="flex space-x-3">
                              <span>- </span>
                              <span>
                                We may also share your information with
                                affiliated companies, service providers, or
                                other parties to perform functions on our behalf
                                such as: analytics services (e.g., Google,
                                Saramin, or their affiliates), marketing and
                                advertising services (e.g., Meta Platforms,
                                Google, VNG Corporation, CocCoc, Propeller Ads,
                                or their affiliates), login/registration gateway
                                services (e.g., Google, Github, Apple ID), AI
                                tools (e.g., OpenAI or its affiliates), email
                                services (e.g., Google, Sendgrid, Netpion or
                                their affiliates), telecommunications services
                                (Viettel, Mobifone, Vinaphone). These
                                individuals and organizations will process
                                personal data shared by us. If the service
                                provider is located abroad, data processing will
                                occur outside Vietnam. When a third party
                                processes data on our behalf, we make reasonable
                                commercial efforts to require that the third
                                party complies with the provisions of this
                                Privacy Policy or implements additional security
                                measures to protect your information.
                              </span>
                            </li>
                          </ul>
                        </li>
                      </ul>
                    </li>
                  </ul>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">
                  Address of the entity collecting and managing personal
                  information
                </h2>
                <div className="mt-3 text-base font-normal">
                  <p>
                    For candidates and recruiters: TopDev is the data controller
                    of the information you provide when using the website,
                    application, and services of TopDev.
                  </p>
                  <ul className="ml-6 list-none">
                    <li>Entity Information:</li>
                    <li> Applancer Joint Stock Company</li>
                    <li>
                      Address: 12A Floor, AP Tower Building, 518B Dien Bien Phu,
                      Ward 21, Binh Thanh District, Ho Chi Minh City
                    </li>
                    <li> Phone: 028 62733496</li>
                    <li>
                      Email:{" "}
                      <Link
                        href="mailto:<EMAIL>"
                        className="text-blue-600 underline"
                      >
                        <EMAIL>
                      </Link>
                    </li>
                  </ul>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">
                  Means and Tools for Users to Access and Edit Their Personal
                  Data
                </h2>
                <div className="mt-3 text-base font-normal">
                  <p className="mt-3">
                    As a candidate, you may exercise the following rights at any
                    time:
                  </p>
                  <ul
                    className="ml-6 mt-3"
                    style={{ listStyleType: "lower-alpha" }}
                  >
                    <li className="mb-3">
                      Right of Access: You have the right to know about our
                      information activities. You also have the right to access
                      the categories of data we collect and to know with whom we
                      share the data collected from you.
                    </li>
                    <li className="mb-3">
                      Right to Rectification: You may edit certain personal data
                      through your account. You may also request us to modify,
                      update, or correct your data in certain cases by sending
                      an email to{" "}
                      <Link
                        href="mailto:<EMAIL>"
                        className="text-blue-600 underline"
                      >
                        <EMAIL>
                      </Link>
                    </li>
                    <li className="mb-3">
                      Right to Erasure: You have the right to request us to
                      delete your Personal Data by sending an email to{" "}
                      <Link
                        href="mailto:<EMAIL>"
                        className="text-blue-600 underline"
                      >
                        <EMAIL>
                      </Link>{" "}
                      from the email address linked to your account and
                      following the “Profile Management Instructions.”
                      <p className="mt-3">Data Deletion Procedure and Method</p>
                      <ul className="ml-6">
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            We will verify that your request is valid and
                            complete according to the requirements.
                          </span>
                        </li>
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Your personal data will cease to be displayed to our
                            third parties and will be completely deleted within
                            72 hours from the request.
                          </span>
                        </li>
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Personal data will be permanently deleted and
                            irrecoverable. A list of users who requested data
                            deletion will be maintained to prevent data
                            restoration from server backups.
                          </span>
                        </li>
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Some of your information may have been retained by
                            third parties before your request due to your prior
                            consent to share personal data. Note that TopDev
                            will notify the recruiters about the deletion
                            request; however, even if your account is
                            successfully deleted in TopDev’s system, we cannot
                            control the retention or use of your personal data
                            by recruiters or third parties who have accessed it.
                          </span>
                        </li>
                      </ul>
                    </li>
                    <li className="mb-3">
                      <ul>
                        <li className="mb-3">
                          Right to Withdraw Consent: When we process your
                          Personal Data based on your consent, you have the
                          right to withdraw that consent at any time.
                        </li>
                        <li className="mb-3">
                          You can refer to how to withdraw consent on your
                          “Profile Management” page.
                        </li>
                        <li className="mb-3">
                          In cases of withdrawal of job applications, TopDev
                          will notify the recruiters to stop accessing the
                          candidate. Your application information will still be
                          stored for record-keeping purposes between candidates
                          and recruiters.
                        </li>
                        <li className="mb-3">
                          Additionally, you may contact{" "}
                          <Link
                            href="mailto:<EMAIL>"
                            className="text-blue-600 underline"
                          >
                            <EMAIL>
                          </Link>{" "}
                          to notify us of your withdrawal of consent.
                        </li>
                        <li className="mb-3">
                          Please note that any processing carried out before
                          your withdrawal remains lawful. Withdrawal does not
                          affect the legality of processing conducted before
                          consent was withdrawn. (Clause 1, Article 12, Decree
                          13 on Personal Data Protection)
                        </li>
                      </ul>
                    </li>
                    <li className="mb-3">
                      <ul>
                        <li className="mb-3">
                          Right to Object: If you wish to opt out of receiving a
                          specific marketing email, you may click “Click here to
                          unsubscribe from TopDev emails” at the bottom of each
                          marketing email.
                        </li>
                        <li className="mb-3">
                          You may also opt out of all marketing communications
                          by sending an email to{" "}
                          <Link
                            href="mailto:<EMAIL>"
                            className="text-blue-600 underline"
                          >
                            <EMAIL>
                          </Link>
                          . We will comply with your request as soon as
                          practicable and in accordance with applicable laws.
                        </li>
                      </ul>
                    </li>
                  </ul>
                  <p className="mt-3">
                    As a recruiter, you may exercise the following rights at any
                    time:
                  </p>
                  <ul
                    className="ml-6 mt-3"
                    style={{ listStyleType: "lower-alpha" }}
                  >
                    <li className="mb-3">
                      Right to View Candidate Information: Recruiters have the
                      right to view candidate information when users apply for
                      positions posted by the recruiter.
                    </li>
                    <li className="mb-3">
                      Right to Complain about Disclosure of Personal Information
                      to Third Parties: Recruiters may file complaints to
                      TopDev’s management. Upon receipt, TopDev will verify the
                      information, respond with reasons, and guide members on
                      how to restore and secure their information.
                    </li>
                    <li className="mb-3">
                      Handling Requests for Withdrawal of Applications, CV
                      Deletion, or Account Deletion from Candidates. To comply
                      with personal data protection laws, recruiters must
                      strictly follow these procedures:
                      <ul className="ml-6 mt-3">
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Withdrawal of Application: TopDev will notify
                            recruiters by email about the candidate’s withdrawal
                            and require recruiters to cease all forms of contact
                            with the candidate.
                          </span>
                        </li>
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            CV Deletion: TopDev will notify recruiters by email
                            when a candidate deletes their CV. Deleting the CV
                            implies withdrawal of the application associated
                            with that CV. All candidate personal data will be
                            removed from the candidate management page.
                            Recruiters must delete all candidate personal
                            information and stop contacting the candidate by any
                            means. Failure to comply may lead to complaints for
                            which TopDev will not be responsible.
                          </span>
                        </li>
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Account Deletion: TopDev will notify recruiters by
                            email when a candidate deletes their account.
                            Deleting the account implies withdrawal from all
                            applications by that candidate. All candidate
                            personal data will be removed from the candidate
                            management page. Recruiters must delete all
                            candidate personal information and stop contacting
                            the candidate by any means. Failure to comply may
                            lead to complaints for which TopDev will not be
                            responsible.
                          </span>
                        </li>
                      </ul>
                    </li>
                  </ul>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">
                  Commitment to Customer Personal Information Confidentiality
                </h2>
                <div className="mt-3 text-base font-normal">
                  <ul>
                    <li className="mt-3">
                      Candidate and recruiter information on{" "}
                      <Link
                        href="https://topdev.vn"
                        className="text-blue-600 underline"
                      >
                        topdev.vn
                      </Link>{" "}
                      is strictly protected according to TopDev’s personal
                      information protection policy. Collection and use of
                      information are only conducted with the consent of the
                      individual unless otherwise required by law.
                    </li>
                    <li className="mt-3">
                      TopDev uses Secure Sockets Layer (SSL) software to protect
                      candidate/recruiter information during data transmission
                      by encrypting the information you enter.
                    </li>
                    <li className="mt-3">
                      Candidates and recruiters are responsible for protecting
                      their passwords, especially when sharing computers with
                      others. They must ensure to log out after using TopDev
                      services.
                    </li>
                    <li className="mt-3">
                      TopDev commits not to intentionally disclose or share
                      candidate/recruiter information without consent.
                    </li>
                    <li className="mt-3">
                      This privacy policy applies only to{" "}
                      <Link
                        href="https://topdev.vn"
                        className="text-blue-600 underline"
                      >
                        topdev.vn
                      </Link>{" "}
                      and does not cover third-party advertising or linked
                      websites. Candidates and recruiters should review and
                      distinguish the privacy policies of these websites.
                    </li>
                    <li className="mt-3">
                      If the server storing personal data is attacked causing
                      data loss, TopDev will notify authorities for timely
                      investigation and notify affected candidates/recruiters.
                    </li>
                    <li className="mt-3">
                      TopDev management requires members registering to provide
                      full personal information such as full name, contact
                      address, and email and assumes legal responsibility for
                      the accuracy of such information. TopDev does not assume
                      responsibility or resolve complaints related to rights if
                      the initial registration information is inaccurate.
                    </li>
                  </ul>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">Policy Updates</h2>
                <div className="mt-3 text-base font-normal">
                  <p>
                    We may amend this policy by releasing updated versions. If
                    changes significantly affect your rights, we will notify you
                    via the registered email and display notices on our website
                    before implementation. We encourage you to check this page
                    regularly for updates. Continued use of our services implies
                    acceptance of the updated policy. If you disagree, you
                    should cease using our services.
                  </p>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">
                  Complaint Handling Mechanism Regarding Personal Data
                </h2>
                <div className="mt-3 text-base font-normal">
                  <ul>
                    <li className="mt-3">
                      When customers provide personal data to us, they agree to
                      the terms above.{" "}
                      <Link
                        href="https://topdev.vn"
                        className="text-blue-600 underline"
                      >
                        TopDev
                      </Link>{" "}
                      commits to protecting personal data by all means,
                      including encryption to prevent unauthorized access, use,
                      or disclosure.
                    </li>
                    <li className="mt-3">
                      We recommend that customers protect their passwords and
                      avoid sharing them.
                    </li>
                    <li className="mt-3">
                      If customers report misuse of their personal data,{" "}
                      <Link
                        href="https://topdev.vn"
                        className="text-blue-600 underline"
                      >
                        TopDev
                      </Link>{" "}
                      will proceed as follows:
                    </li>
                  </ul>
                  <ul className="ml-6">
                    <li>
                      <span className="font-bold">Step 1: </span>Customer
                      submits feedback about unauthorized data use.
                    </li>
                    <li>
                      <span className="font-bold">Step 2: </span>
                      <Link
                        href="https://topdev.vn"
                        className="text-blue-600 underline"
                      >
                        TopDev
                      </Link>{" "}
                      Customer Care reviews and resolves with related parties.
                    </li>
                    <li>
                      <span className="font-bold">Step 3: </span>If beyond
                      TopDev’s control, we will escalate to authorities.
                    </li>
                  </ul>
                  <p>
                    We welcome feedback and inquiries on this Privacy Policy at{" "}
                    <Link
                      href="mailto:<EMAIL>"
                      className="text-blue-600 underline"
                    >
                      <EMAIL>
                    </Link>
                  </p>
                </div>
              </li>

              <li className="text-xl font-bold">
                <h2 className="mt-3">How We Use Cookies</h2>
                <div className="mt-3 text-base font-normal">
                  <p>
                    We use cookies to provide and improve our services. This
                    policy explains how we use cookies on our website.
                  </p>
                  <ul className="ml-6" style={{ listStyleType: "lower-alpha" }}>
                    <li className="mt-3">
                      What are Cookies?
                      <p>
                        Cookies are small files stored on your browser when you
                        visit websites, helping improve your experience.
                      </p>
                    </li>
                    <li className="mt-3">
                      Types of Cookies We Use:
                      <ul className="ml-6">
                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Necessary Cookies: Essential for services such as
                            account access and session management.
                          </span>
                        </li>

                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Analytical Cookies: Help us understand how you use
                            the site to improve your experience by collecting
                            data such as pages visited, time spent, and links
                            clicked.
                          </span>
                        </li>

                        <li className="flex space-x-3">
                          <span>- </span>
                          <span>
                            Third-Party Cookies: Services like Google Analytics
                            may set cookies on your browser when you visit our
                            site.
                          </span>
                        </li>
                      </ul>
                    </li>
                    <li className="mt-3">
                      Cookie Management:
                      <p>
                        You can adjust cookie settings in your browser to block
                        or delete cookies anytime, but this may affect your
                        experience and access to some features.
                      </p>
                    </li>
                    <li className="mt-3">
                      Consent:
                      <p>
                        By continuing to use our site, you consent to our use of
                        cookies as described.
                      </p>
                    </li>
                  </ul>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </main>
  );
};

export default English;
