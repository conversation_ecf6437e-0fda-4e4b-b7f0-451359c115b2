import Image from "next/image";
import React from "react";
import Link from "@/components/Link/Link";

const PersonalityTestCard = () => {
  return (
    <div className="rounded-lg border border-solid border-gray-200 p-4">
      <div className="relative">
        <Image
          src="https://topdev.vn/_nuxt/img/pt-test.ff66b03.svg"
          width={132}
          height={100}
          loading="lazy"
          alt="personality test"
          className="mx-auto block h-auto max-w-full"
        />
      </div>
      <h4 className="mt-4 text-sm font-semibold text-primary lg:text-base">
        Chỉ 5 phút, khám phá tính cách trong công việc của bạn
      </h4>
      <p className="mt-1 text-sm lg:text-base">
        Hi<PERSON><PERSON> rõ tính cách cá nhân để thuyết phục nhà tuyển dụng.
      </p>
      <div className="mt-2 flex items-center justify-center text-center">
        <Link
          target={"_blank"}
          href={"/page/trac-nghiem-tinh-cach"}
          className="flex items-center gap-1 text-sm font-semibold text-primary underline lg:text-base"
        >
          Khám phá
          <svg
            width={21}
            height={8}
            viewBox="0 0 21 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M0.49707 4.27424V3.27424C0.49707 2.9981 0.720997 2.77424 0.997224 2.77424H16.502V0.77424C16.5048 0.574203 16.6266 0.395096 16.8117 0.318924C16.9967 0.242753 17.2094 0.284181 17.3523 0.42424L20.3532 3.42424C20.545 3.62205 20.545 3.93643 20.3532 4.13424L17.3523 7.13424C17.2081 7.27554 16.993 7.31631 16.8071 7.23759C16.6212 7.15886 16.5008 6.97609 16.502 6.77424V4.77424H0.997224C0.720997 4.77424 0.49707 4.55038 0.49707 4.27424Z"
              fill="#DD3F24"
            />
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default PersonalityTestCard;
