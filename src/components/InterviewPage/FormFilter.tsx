"use client";
import {
  getAllPositions,
  getAllSkills,
  getQnaListTaxonomies,
} from "@/services/interviewAPI";
import { TaxonomyType } from "@/types/taxonomy";
import { useRouter } from "next/navigation";
import { FC, useEffect, useState } from "react";
import { Button } from "../Button";
import SelectItem from "./SelectGroup/SelectItem";

interface Props {
  skills: TaxonomyType[];
  positions: TaxonomyType[];
}

const DEFAULT_POSITION: TaxonomyType = {
  id: 0,
  text: "Tất cả vị trí",
  banner_url: "",
  feature: "",
  image: "",
  slug: "",
  sort_order: 0,
  taxonomy: "skills",
  text_en: "",
  text_vi: "",
  thumbnail_url: "",
};
const DEFAULT_SKILL: TaxonomyType = {
  id: 0,
  text: "Tất cả kĩ năng",
  banner_url: "",
  feature: "",
  image: "",
  slug: "",
  sort_order: 0,
  taxonomy: "skills",
  text_en: "",
  text_vi: "",
  thumbnail_url: "",
};

const FormFilter: FC<Props> = ({ skills, positions }) => {
  const [filterData, setFilterData] = useState<{
    skill: TaxonomyType | null;
    position: TaxonomyType | null;
  }>({
    skill: null,
    position: null,
  });
  const [isLoading, setIsLoading] = useState(false);

  const [options, setOptions] = useState({
    skills: skills,
    positions: positions,
  });

  useEffect(() => {
    setOptions((prev) => ({
      ...prev,
      positions: [DEFAULT_POSITION, ...prev.positions],
      skills: [DEFAULT_SKILL, ...prev.skills],
    }));
  }, []);

  const handleSelectData = async (
    value: TaxonomyType,
    type: "qa_skills" | "positions",
  ) => {
    setIsLoading(true);
    if (type === "positions") {
      if (value.id !== 0) {
        await getQnaListTaxonomies(value.id, "qa_skills")
          .then((response) => {
            setOptions((prev) => ({
              ...prev,
              skills: response.data.data.qa_skills,
            }));
            setOptions((prev) => ({
              ...prev,
              skills: [DEFAULT_SKILL, ...prev.skills],
            }));
          })
          .catch((error) => {
            console.error(error);
          });
      } else {
        await getAllSkills().then((response) => {
          setOptions((prev) => ({
            ...prev,
            skills: response.data.data.qa_skills,
          }));
          setOptions((prev) => ({
            ...prev,
            skills: [DEFAULT_SKILL, ...prev.skills],
          }));
        });
      }
    } else {
      if (value.id !== 0) {
        await getQnaListTaxonomies(value.id, "positions")
          .then((response) => {
            setOptions((prev) => ({
              ...prev,
              positions: response.data.data.positions,
            }));
            setOptions((prev) => ({
              ...prev,
              positions: [DEFAULT_POSITION, ...prev.positions],
            }));
          })
          .catch((error) => {
            console.error(error);
          });
      } else {
        await getAllPositions().then((response) => {
          setOptions((prev) => ({
            ...prev,
            positions: response.data.data.positions,
          }));
          setOptions((prev) => ({
            ...prev,
            positions: [DEFAULT_POSITION, ...prev.positions],
          }));
        });
      }
    }
    const updatedType = type === "qa_skills" ? "skill" : "position";
    setIsLoading(false);
    setFilterData((prev) => ({ ...prev, [updatedType]: value }));
  };

  const router = useRouter();

  const handleSubmit = async () => {
    let path = "/interview";
    if (
      filterData.skill !== null &&
      filterData.skill?.id !== 0 &&
      filterData.position !== null &&
      filterData.position?.id !== 0
    ) {
      path = `/interview/${filterData.position.slug},${filterData.skill.slug}-p${filterData.position.id}s${filterData.skill.id}`;
    } else if (filterData.skill !== null && filterData.skill.id !== 0) {
      path = `/interview/${filterData.skill.slug}-s${filterData.skill.id}`;
    } else if (filterData.position !== null && filterData.position.id !== 0) {
      path = `/interview/${filterData.position.slug}-p${filterData.position.id}`;
    }
    router.push(path);
  };

  useEffect(() => {
    const slug = window.location.href;
    const parameters = slug?.split("-")?.pop() as string | undefined;
    let positionId = parameters?.match(/p(\d+)/)?.[1] ?? null;
    let skillId = parameters?.match(/s(\d+)/)?.[1] ?? null;
    let position: TaxonomyType | null = null;
    let skill: TaxonomyType | null = null;
    if (!positionId) {
      positionId = null;
    } else {
      position =
        positions.find((item) => item.id === parseInt(positionId as string)) ??
        null;
      setFilterData((prev) => ({
        ...prev,
        position: position,
      }));
    }

    if (!skillId) {
      skillId = null;
    } else {
      skill =
        skills.find((item) => item.id === parseInt(skillId as string)) ?? null;
      setFilterData((prev) => ({
        ...prev,
        skill: skill,
      }));
    }

    if (position !== null) {
      getQnaListTaxonomies(position?.id as number, "qa_skills")
        .then((response) => {
          setOptions((prev) => ({
            ...prev,
            skills: response.data.data.qa_skills,
          }));
          setOptions((prev) => ({
            ...prev,
            skills: [DEFAULT_SKILL, ...prev.skills],
          }));
        })
        .catch((error) => {
          console.error(error);
        });
    }

    if (skill !== null) {
      getQnaListTaxonomies(skill?.id, "positions")
        .then((response) => {
          setOptions((prev) => ({
            ...prev,
            positions: response.data.data.positions,
          }));
          setOptions((prev) => ({
            ...prev,
            positions: [DEFAULT_POSITION, ...prev.positions],
          }));
        })
        .catch((error) => {
          console.error(error);
        });
    }
  }, [skills, positions]);

  return (
    <div className="items-center justify-center lg:flex">
      <div className="items-center gap-2 space-y-3 rounded-lg px-3 py-2 text-black lg:flex lg:space-y-0 lg:bg-white lg:shadow-md">
        <div className="flex-1">
          <SelectItem
            onSelect={(value) => handleSelectData(value, "positions")}
            options={options.positions}
            value={filterData.position}
            placeholder="Position"
          />
        </div>
        <div className="flex-1">
          <SelectItem
            onSelect={(value) => handleSelectData(value, "qa_skills")}
            options={options.skills}
            value={filterData.skill}
            placeholder="Skill"
          />
        </div>
        <div className="flex-1">
          <Button onClick={handleSubmit} accent="primary" isBlock disabled={isLoading}>
            Tìm
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FormFilter;
