"use client";
import { InterviewType } from "@/types/interview";
import { TaxonomyType } from "@/types/taxonomy";
import { Dispatch, FC, ReactNode, createContext, useReducer } from "react";

type InterviewActionType =
  | {
      type: "UPDATE_QUESTIONS";
      payload: InterviewType[];
    }
  | {
      type: "UPDATE_CURRENT_PAGE_TECHNICAL";
      payload: number;
    }
  | {
      type: "UPDATE_CURRENT_PAGE_SOFT_SKILL";
      payload: number;
    }
  | {
      type: "SCROLL_TO_QUESTION";
      payload: {
        type: "technical" | "soft-skill";
        index: number;
      };
    };

interface InterviewState {
  questions: InterviewType[];
  currentQuestion: Pick<InterviewType, "id"> | null;
  skills: TaxonomyType[];
  positions: TaxonomyType[];
  currentPageTechnical: number;
  currentPageSoftSkill: number;
  scrollTechnicalIndex: number;
  scrollSoftSkillIndex: number;
}

const initialState: InterviewState = {
  questions: [],
  currentQuestion: null,
  skills: [],
  positions: [],
  currentPageTechnical: 1,
  currentPageSoftSkill: 1,
  scrollTechnicalIndex: 0,
  scrollSoftSkillIndex: 0,
};

const interviewReducer = (
  state: InterviewState = initialState,
  action: InterviewActionType,
) => {
  switch (action.type) {
    case "UPDATE_QUESTIONS": {
      return {
        ...state,
        questions: action.payload,
      };
    }
    case "UPDATE_CURRENT_PAGE_TECHNICAL": {
      return { ...state, currentPageTechnical: action.payload };
    }
    case "UPDATE_CURRENT_PAGE_SOFT_SKILL": {
      return { ...state, currentPageSoftSkill: action.payload };
    }
    case "SCROLL_TO_QUESTION": {
      switch (action.payload.type) {
        case "technical": {
          const currentPage =
            state.currentPageTechnical * 5 < action.payload.index + 1
              ? Math.ceil((action.payload.index + 1) / 5)
              : state.currentPageTechnical;
          return {
            ...state,
            currentPageTechnical: currentPage,
            scrollTechnicalIndex: action.payload.index,
          };
        }
        case "soft-skill": {
          const currentPage =
            state.currentPageSoftSkill * 5 < action.payload.index + 1
              ? Math.ceil((action.payload.index + 1) / 5)
              : state.currentPageSoftSkill;
          return {
            ...state,
            currentPageSoftSkill: currentPage,
            scrollSoftSkillIndex: action.payload.index,
          };
        }
        default:
          return state;
      }
    }
    default:
      return state;
  }
};

type InterviewContextType = {
  state: InterviewState;
  dispatch: Dispatch<InterviewActionType>;
};

export const InterviewContext = createContext<InterviewContextType>({
  state: initialState,
  dispatch: () => {},
});

const InterviewProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(interviewReducer, initialState);
  return (
    <InterviewContext.Provider value={{ state, dispatch }}>
      {children}
    </InterviewContext.Provider>
  );
};

export default InterviewProvider;
