"use client";

import { InterviewType, SoftSkillType } from "@/types/interview";
import React, { FC, useContext, useRef } from "react";
import { FaListUl } from "react-icons/fa6";
import { useOnClickOutside, useToggle } from "usehooks-ts";
import { HiX } from "react-icons/hi";
import { classNames } from "@/utils";
import { InterviewContext } from "./Context/InterviewContext";
import { IoMdBookmarks } from "react-icons/io";

interface Props {
  technicalQuestions: InterviewType[];
  softSkillQuestions: SoftSkillType[];
}
const QuestionMenuMobile: FC<Props> = ({
  technicalQuestions,
  softSkillQuestions,
}) => {
  const { dispatch } = useContext(InterviewContext);
  const menuRef = useRef<HTMLDivElement>(null);
  const [isToggle, onToggle, setToggle] = useToggle(false);
  useOnClickOutside(menuRef, () => setToggle(false), "mousedown");

  const handleScrollToQuestion = (
    index: number,
    type: "technical" | "soft-skill",
  ) => {
    dispatch({
      type: "SCROLL_TO_QUESTION",
      payload: {
        type: type,
        index: index,
      },
    });
  };

  return (
    <div className="relative block lg:hidden">
      <div className="bg-gray-100">
        <div
          className="container flex items-center gap-4 p-4 font-bold"
          onClick={onToggle}
        >
          <span>
            <FaListUl />
          </span>
          <h3>Danh sách câu hỏi</h3>
        </div>
      </div>
      <div
        className={classNames(
          "fixed left-0 top-0 z-50 h-screen w-full bg-black/50 transition-all",
          isToggle ? "visible opacity-100" : "invisible opacity-0",
        )}
      ></div>
      <div
        ref={menuRef}
        className={classNames(
          "fixed left-0 top-0 z-[51] h-screen w-80 max-w-[100vw] bg-white p-4 shadow-lg transition-all",
          "delay-100",
          isToggle
            ? "visible -translate-x-0 opacity-100"
            : "invisible -translate-x-full opacity-0",
        )}
      >
        <div className="relative">
          <button
            type="button"
            onClick={onToggle}
            className="absolute right-0 top-0 z-10"
          >
            <HiX />
          </button>
          <h4 className="font-base flex items-center gap-2 font-bold text-primary">
            <IoMdBookmarks />
            Bộ câu hỏi
          </h4>
          <h5 className="font-bold">
            Câu hỏi kỹ thuật{" "}
            <span className="font-normal text-gray-400">
              ({technicalQuestions.length})
            </span>
          </h5>
          <div className="scrollbar-primary mt-1 h-[40dvh] overflow-y-auto overscroll-contain text-sm">
            <ul>
              {technicalQuestions.map((item, index) => {
                return (
                  <li
                    className="p-2 transition-all hover:bg-primary-100"
                    key={item.id}
                    onClick={() => handleScrollToQuestion(index, "technical")}
                  >
                    {item.title}
                  </li>
                );
              })}
            </ul>
          </div>
          <h5 className="mt-5 font-bold">
            Câu hỏi kỹ năng mềm{" "}
            <span className="font-normal text-gray-400">
              ({softSkillQuestions.length})
            </span>
          </h5>
          <div className="scrollbar-primary h-[40dvh] overflow-y-auto overscroll-contain text-sm">
            <ul>
              {softSkillQuestions.map((item, index) => {
                return (
                  <li
                    className="p-2 transition-all hover:bg-primary-100"
                    key={index}
                    onClick={() => handleScrollToQuestion(index, "soft-skill")}
                  >
                    {item.title}
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuestionMenuMobile;
