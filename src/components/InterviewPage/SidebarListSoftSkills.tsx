"use client";
import { SOFT_SKILL_DATA } from "@/contansts/it-report";
import React, { FC, useContext } from "react";
import { InterviewContext } from "./Context/InterviewContext";

const SidebarListSoftSkills: FC = () => {
  const { dispatch } = useContext(InterviewContext);
  const handleScrollToQuestion = (
    index: number,
    type: "technical" | "soft-skill",
  ) => {
    dispatch({
      type: "SCROLL_TO_QUESTION",
      payload: {
        type: type,
        index,
      },
    });
  };
  return (
    <div>
      <p className="text-base font-semibold">
        <PERSON>âu hỏi kỹ năng mềm <span>({SOFT_SKILL_DATA.length})</span>
      </p>
      <div className="scrollbar-primary max-h-[20rem] overflow-y-auto lg:max-h-[15.875rem]">
        <ul>
          {SOFT_SKILL_DATA.map((skillItem, index) => {
            return (
              <li
                key={index}
                className="cursor-pointer p-2 hover:bg-primary-100"
                onClick={() => handleScrollToQuestion(index, "soft-skill")}
              >
                {skillItem.title}
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default SidebarListSoftSkills;
