"use client";
import { TaxonomyType } from "@/types/taxonomy";
import { classNames } from "@/utils";
import React, { ChangeEvent, FC, useEffect, useRef, useState } from "react";
import { HiMiniChevronDown, HiOutlineBriefcase } from "react-icons/hi2";
import { useOnClickOutside, useToggle } from "usehooks-ts";
import slugify from "@sindresorhus/slugify";

interface Props {
  options: TaxonomyType[];
  value: TaxonomyType | null;
  onSelect: (taxonomy: TaxonomyType) => void;
  placeholder: string;
}

const SelectItem: FC<Props> = (props) => {
  const { options, value, onSelect, placeholder } = props;
  const [isToggle, onToggle, setToggle] = useToggle(false);
  const selectRef = useRef<HTMLDivElement>(null);
  const [filteredData, setFilteredData] =
    React.useState<TaxonomyType[]>(options);
  const [searchData, setSearchData] = useState("");

  useEffect(() => {
    if (searchData) {
      const dataFiltered = options.filter((option) =>
        slugify(option.text.toLowerCase()).includes(searchData.toLowerCase()),
      );
      setFilteredData(dataFiltered);
    } else {
      setFilteredData(options);
    }
  }, [searchData]);

  useEffect(() => {
    setFilteredData(options);
  }, [options]);

  const handleChangeFilter = (e: ChangeEvent<HTMLInputElement>) => {
    const filterValue = e.target.value;
    setSearchData(filterValue);
  };

  const handleClickOutside = () => {
    setToggle(false);
  };

  useOnClickOutside(selectRef, handleClickOutside);
  return (
    <div className="relative select-none" ref={selectRef}>
      <div
        className="flex w-full cursor-pointer items-center justify-between gap-2 rounded-lg bg-white px-4 py-3 transition-all hover:bg-gray-100 lg:w-[170px] lg:rounded-none lg:p-2"
        onClick={onToggle}
      >
        <div className="flex flex-1 items-center gap-2">
          <div>
            <HiOutlineBriefcase />
          </div>
          <div className="line-clamp-1 flex-1">
            {value?.text ?? placeholder}
          </div>
        </div>
        <div
          className={classNames(
            isToggle ? "rotate-180" : "rotate-0",
            "transition-all",
          )}
        >
          <HiMiniChevronDown />
        </div>
      </div>
      <div
        className={classNames(
          "absolute left-0 top-full z-20 w-full overflow-hidden rounded border border-solid border-gray-300 bg-white lg:w-72",
          isToggle ? "visible opacity-100" : "invisible opacity-0",
        )}
      >
        <div className="relative">
          <div className="relative p-4">
            <input
              autoComplete="off"
              onChange={handleChangeFilter}
              type="text"
              name="position"
              value={searchData}
              id="position"
              placeholder="Position"
              className="w-full rounded border border-solid border-gray-300 bg-white focus:border-black focus:ring-black"
            />
          </div>
          <div className="pb-2">
            <ul className="scrollbar-primary max-h-[350px] overflow-y-auto overscroll-contain">
              {filteredData.length > 0 &&
                filteredData.map((item, index) => {
                  return (
                    <li key={index}>
                      <div
                        className={classNames(
                          "flex cursor-pointer items-center gap-2 px-4 py-2 transition-all",
                          value?.id === item.id
                            ? "bg-primary-100"
                            : "hover:bg-primary-100",
                        )}
                        onClick={() => {
                          onSelect(item);
                          onToggle();
                        }}
                      >
                        <input
                          readOnly
                          type="radio"
                          checked={value?.id === item.id}
                          className="checked:bg-primary checked:accent-primary"
                        />
                        <span className="line-clamp-1">{item.text}</span>
                      </div>
                    </li>
                  );
                })}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectItem;
