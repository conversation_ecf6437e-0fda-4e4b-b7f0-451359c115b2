"use client";
import { InterviewType } from "@/types/interview";
import dynamic from "next/dynamic";
import { FC, useContext, useEffect } from "react";
import { Button } from "../Button";
import { InterviewContext } from "./Context/InterviewContext";
import QuestionLoading from "./QuetionLoading";
const InterviewItem = dynamic(() => import("./InterviewItem"), {
  ssr: false,
  loading: () => <QuestionLoading />,
});

interface Props {
  questions: InterviewType[];
}

const ContainerQuestions: FC<Props> = ({ questions }) => {
  const { state, dispatch } = useContext(InterviewContext);
  const { currentPageTechnical } = state;

  useEffect(() => {
    if (state.scrollTechnicalIndex) {
      const scrollTimeout = setTimeout(() => {
        const dataId = document.querySelector(
          `[data-id="technical-${state.scrollTechnicalIndex}"]`,
        );
        if (dataId) {
          dataId.scrollIntoView({
            block: "center",
            behavior: "smooth",
          });
        }
        clearTimeout(scrollTimeout);
      }, 1000);
    }
  }, [state.scrollTechnicalIndex]);

  const handleViewMore = () => {
    dispatch({
      type: "UPDATE_CURRENT_PAGE_TECHNICAL",
      payload: currentPageTechnical + 1,
    });
  };

  return (
    <div className="mt-6 scroll-pt-10">
      <ul>
        {questions.slice(0, currentPageTechnical * 5).map((item, index) => {
          return (
            <li
              key={index}
              className="mb-4 last:mb-0"
              data-id={`technical-${index}`}
            >
              <InterviewItem interviewData={item} />
            </li>
          );
        })}
      </ul>
      {currentPageTechnical * 5 < questions.length && (
        <div className="mx-auto mt-4 w-40">
          <Button accent="primary" isBlock onClick={handleViewMore}>
            Xem thêm
          </Button>
        </div>
      )}
    </div>
  );
};

export default ContainerQuestions;
