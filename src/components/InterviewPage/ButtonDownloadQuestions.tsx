"use client";
import React, { FC } from "react";
import { postDownloadPdfQna } from "@/services/interviewAPI";
import { InterviewType } from "@/types/interview";
import { useAppSelector } from "@/store";
import { openLoginPopup } from "@/utils";

interface Props {
  questions: InterviewType[]; // Thay thế "any" bằng kiểu dữ liệu ch<PERSON>h x<PERSON>c c<PERSON>a questions nếu có
}

const ButtonDownloadQuestions: FC<Props> = ({questions})  => {
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const handleDownloadPdfQna = async() => {
    if (!isLoggedIn) {
      openLoginPopup();
      return;
    }

    postDownloadPdfQna({questions: questions.map(question => question.id)})
      .then(response => {
        if (response && response.data) {
          const blob = new Blob([response.data], { type: 'application/pdf' });
          const a = document.createElement("a");
          const url = window.URL.createObjectURL(blob);
          a.addEventListener("click", (event) => {
            event.stopPropagation();
          });
          a.href = url;
          a.download = 'Questions.pdf';
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        }
      })
      .catch(error => {
        console.error(error);
      });
  };

  return (
    <button
      onClick={handleDownloadPdfQna}
      type="button"
      className="block w-full rounded border border-solid border-primary py-2 text-base font-semibold text-primary transition-all hover:bg-primary hover:text-white"
    >
      Tải bộ câu hỏi
    </button>
  );
};

export default ButtonDownloadQuestions;
