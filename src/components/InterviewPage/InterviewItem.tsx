import { InterviewType } from "@/types/interview";
import { classNames, openLoginPopup } from "@/utils";
import { convertTextToHTML } from "@/utils/string";
import Image from "next/image";
import Link from "next/link";
import { FC, MouseEvent, useRef, useState } from "react";
import { HiMinus, HiPlus } from "react-icons/hi2";
import slugify from "slugify";
import { useOnClickOutside } from "usehooks-ts";
import { useAppSelector } from "@/store";

interface Props {
  interviewData: Pick<InterviewType, "title" | "answer">;
}
const InterviewItem: FC<Props> = ({ interviewData }) => {
  const [isOpen, setIsOpen] = useState(false);
  const itemRef = useRef<HTMLDivElement>(null);
  const openRef = useRef<HTMLButtonElement>(null);
  const currentPath =
    typeof window !== "undefined" ? window.location.pathname : null;
  let slug = "";
  if (currentPath !== null) {
    const locale = currentPath.split("/")[1];
    slug =
      "/" +
      locale +
      "/" +
      slugify(interviewData.title, {
        lower: true,
        strict: true,
        locale: "vi",
      });
  }
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);

  const handleClickOpen = (e: MouseEvent) => {
    if (!isLoggedIn) {
      openLoginPopup();
      return;
    }
    setIsOpen((prev) => !prev);
  };

  useOnClickOutside(itemRef, () => setIsOpen(false), "mouseup");

  return (
    <div
      ref={itemRef}
      className={classNames(
        "tab-item group/item tab-item rounded-xl border border-solid border-gray-300 bg-white p-3 transition-all lg:p-4",
        isOpen
          ? "shadow-md shadow-black/20"
          : "hover:shadow-md hover:shadow-black/20",
      )}
    >
      <div className="flex items-start gap-2">
        <div className="h-7 w-7 lg:h-8 lg:w-8">
          <Image
            src="https://topdev.vn/_nuxt/img/question-icon.2852caa.svg"
            alt="question"
            width={32}
            height={32}
            loading="lazy"
            className="h-7 w-7 max-w-full rounded-full object-contain lg:h-8 lg:w-8"
          />
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-semibold text-black lg:text-base">
            <Link href={slug}>{interviewData.title}</Link>
          </h3>
          <div className="mt-2 rounded-lg bg-gray-100 p-2 md:p-4">
            <div className="flex items-start gap-3 overflow-hidden transition-all duration-500 lg:gap-4">
              <div className="flex-1">
                <div
                  className={classNames(
                    "prose-base whitespace-normal break-words break-all text-sm leading-tight lg:text-base",
                    isOpen ? "line-clamp-none" : "line-clamp-2",
                  )}
                >
                  <div
                    dangerouslySetInnerHTML={{
                      __html: convertTextToHTML(interviewData.answer),
                    }}
                  ></div>
                </div>
              </div>
              <button
                ref={openRef}
                onClick={handleClickOpen}
                type="button"
                className={classNames(
                  "flex h-6 w-6 items-center justify-center rounded-full bg-white text-xl transition-all",
                  isOpen
                    ? "text-primary"
                    : "text-black hover:bg-primary hover:text-white",
                )}
              >
                {isOpen ? <HiMinus /> : <HiPlus />}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InterviewItem;
