import { BlogType } from "@/types/blog";
import { BlogTypePicked } from "@/types/interview";
import Link from "next/link";
import React, { FC } from "react";

interface Props {
  blogs: BlogTypePicked[];
}
const ListBlogs: FC<Props> = ({ blogs }) => {
  return (
    <div>
      <p className="text-sm font-semibold lg:text-base">
        Các bài viết liên quan
      </p>
      <hr className="!my-3 !block" />
      <ul className="divide-y divide-gray-100">
        {blogs.map((blogItem, index) => {
          return (
            <li key={index}>
              <Link
                target="_blank"
                title={blogItem.post_title}
                href={blogItem.permalink}
                className="block px-0 py-2 text-sm !text-black transition hover:!text-primary lg:!p-2 lg:text-base"
              >
                {blogItem.post_title}
              </Link>
            </li>
          );
        })}
      </ul>
      <div className="!py-3 text-center">
        <a
          href="https://topdev.vn/blog/"
          target="_blank"
          className="text-sm font-semibold !text-primary underline lg:text-base"
        >
          Xem thêm
        </a>
      </div>
    </div>
  );
};

export default ListBlogs;
