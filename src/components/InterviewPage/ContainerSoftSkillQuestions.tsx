"use client";

import dynamic from "next/dynamic";
import { FC, useContext, useEffect } from "react";
import { Button } from "../Button";
import { InterviewContext } from "./Context/InterviewContext";
import QuestionLoading from "./QuetionLoading";
const InterviewItem = dynamic(() => import("./InterviewItem"), {
  ssr: false,
  loading: () => <QuestionLoading />,
});

interface Props {
  questions: { title: string; answer: string }[];
}

const ContainerSoftSkillQuestions: FC<Props> = ({ questions }) => {
  const { state, dispatch } = useContext(InterviewContext);
  const { currentPageSoftSkill } = state;

  useEffect(() => {
    if (state.scrollSoftSkillIndex) {
      const scrollTimeout = setTimeout(() => {
        const dataId = document.querySelector(
          `[data-id="skill-${state.scrollSoftSkillIndex}"]`,
        );
        if (dataId) {
          dataId.scrollIntoView({
            block: "center",
            behavior: "smooth",
          });
        }
        clearTimeout(scrollTimeout);
      }, 1000);
    }
  }, [state.scrollSoftSkillIndex]);

  const handleViewMore = () => {
    dispatch({
      type: "UPDATE_CURRENT_PAGE_SOFT_SKILL",
      payload: currentPageSoftSkill + 1,
    });
  };

  return (
    <div className="mt-6">
      <h2 className="font-semibold uppercase">
        CÂU HỎI KỸ NĂNG MỀM 💼{" "}
        <span className="text-gray-400">({questions.length})</span>
      </h2>
      <div className="mt-6">
        <ul>
          {questions
            .slice(0, currentPageSoftSkill * 5)
            .map((skillItem, index) => {
              return (
                <li key={index} className="mb-4 last:mb-0" data-id={`skill-${index}`}>
                  <InterviewItem interviewData={skillItem} />
                </li>
              );
            })}
        </ul>
        {currentPageSoftSkill * 5 < questions.length && (
          <div className="mx-auto mt-4 w-40">
            <Button accent="primary" isBlock onClick={handleViewMore}>
              Xem thêm
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContainerSoftSkillQuestions;
