"use client";
import { InterviewType } from "@/types/interview";
import { FC, useContext } from "react";
import { InterviewContext } from "./Context/InterviewContext";

interface Props {
  questions: InterviewType[];
}

const SidebarListQuestions: FC<Props> = ({ questions }) => {
  const { dispatch } = useContext(InterviewContext);

  const handleScrollToQuestion = (
    index: number,
    type: "technical" | "soft-skill",
  ) => {
    dispatch({
      type: "SCROLL_TO_QUESTION",
      payload: {
        type: type,
        index,
      },
    });
  };

  return (
    <ul>
      {questions.map((question, index) => (
        <li
          key={question.id}
          onClick={() => handleScrollToQuestion(index, "technical")}
          className="cursor-pointer rounded p-2 hover:bg-primary-100"
        >
          {question.title}
        </li>
      ))}
    </ul>
  );
};

export default SidebarListQuestions;
