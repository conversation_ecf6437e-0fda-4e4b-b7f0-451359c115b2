import Image from "next/image";
import React from "react";

const CreateCvOnlineBanner = () => {
  return (
    <div>
      <div className="mb-5 mt-7 items-center gap-5 text-sm lg:text-base rounded-lg bg-gradient-to-r from-primary-200 to-white px-5 py-4 lg:flex lg:gap-10">
        <Image
          src="https://topdev.vn/_nuxt/img/resume-passed.9e0c76c.svg"
          width="127"
          height="121"
          alt="resume passed"
          className="h-auto max-w-full"
        />
        <div className="mt-4 flex-1 lg:mt-0">
          <h3 className="text-base lg:text-lg font-semibold leading-tight text-primary">
            Bạn chưa có CV để ứng tuyển?
          </h3>
          <p className="mt-1">
            Hãy để TopDev giúp bạn xây dựng hoàn chỉnh CV chuẩn developer để
            tăng khả năng tiến vào vòng phỏng vấn.
          </p>
          <div className="mt-2">
            <a
              href="https://topdev.vn/tao-cv-online/"
              target="_blank"
              className="inline-block rounded-[0.25rem] border border-solid border-primary bg-white px-4 py-2 text-center text-primary transition-all hover:bg-primary hover:text-white lg:w-[14.0625rem] lg:px-0"
            >
              Tạo CV ngay
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateCvOnlineBanner;
