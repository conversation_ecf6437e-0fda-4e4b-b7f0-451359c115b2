"use client";

import { JobType } from "@/types/job";
import _ from "lodash";
import Image from "next/image";
import Link from "next/link";
import { FC } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import SalaryInfo from "../Card/Job/SalaryInfo";

interface Props {
  jobs: JobType[];
}

const JobsFitForYouSection: FC<Props> = ({ jobs }) => {
  const data = _.chunk(jobs, 9);

  return (
    <div className="mt-10">
      <h4 className="text-center text-xl font-semibold text-primary lg:text-2xl">
        Việc làm phù hợp với bạn
      </h4>
      <div className="relative mt-4 pb-5">
        <Swiper
          slidesPerView={1}
          spaceBetween={16}
          loop
          modules={[Pagination, Navigation]}
          pagination={{ el: ".pagination", clickable: true }}
          navigation={{
            nextEl: ".nextEl",
            prevEl: ".prevEl",
          }}
        >
          {data.map((arrJob, arrIndex) => {
            return (
              <SwiperSlide key={arrIndex}>
                <div className="grid grid-cols-1 grid-rows-3 gap-4 sm:grid-cols-2 md:!p-5 lg:grid-cols-3">
                  {arrJob.map((item, jobIndex) => {
                    return (
                      <div
                        key={jobIndex}
                        className="flex min-h-[138px] gap-4 rounded-lg border p-4 transition-all hover:shadow-lg hover:shadow-black/10"
                      >
                        <div>
                          <Link
                            href={item.company ? item.company.detail_url : "#"}
                            className="inline-block h-[4.5rem] w-[5.5rem] p-2"
                          >
                            {item.company && item.company.image_logo && (
                              <Image
                                src={item.company.image_logo}
                                width={128}
                                height={72}
                                alt={item.company.display_name}
                                className="h-full w-full max-w-full object-contain"
                              />
                            )}
                          </Link>
                          <p className="mt-2 text-center text-sm">
                            {item.refreshed.since}
                          </p>
                        </div>
                        <div className="flex-auto">
                          <h5 className="h-11">
                            <Link
                              href={item.detail_url}
                              className="line-clamp-2 text-sm font-semibold transition-all hover:text-primary lg:text-base"
                            >
                              {item.title}
                            </Link>
                          </h5>
                          <div className="mt-1 line-clamp-1 text-sm text-green-500 lg:text-base">
                            <SalaryInfo
                              value={item.salary.value}
                              loginClass=""
                            />
                          </div>
                          <p className="mt-1 line-clamp-1 text-sm lg:text-base">
                            {item.addresses.sort_addresses}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </SwiperSlide>
            );
          })}
          <div className="mt-4 flex items-center justify-center gap-3 text-2xl">
            <button type="button" className="prevEl text-primary">
              <HiChevronLeft />
            </button>
            <div className="relative flex items-center justify-center">
              <div className="pagination flex items-center justify-center gap-1"></div>
            </div>
            <button type="button" className="nextEl text-primary">
              <HiChevronRight />
            </button>
          </div>
        </Swiper>
      </div>
    </div>
  );
};

export default JobsFitForYouSection;
