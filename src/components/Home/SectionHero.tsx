import React from "react";
import MainSearch from "@/components/Home/MainSearch";
import SearchTitleAnimation from "@/components/Search/SearchTitleAnimation";
import Image from "next/image";
import CreateCVIcon from "@/assets/images/icons/create-cv.svg";
import PersonalityTestIcon from "@/assets/images/icons/personality-test.svg";
import ItJobFresherIcon from "@/assets/images/icons/it-jobs-fresher.svg";
import QnaForInterviewIcon from "@/assets/images/icons/qna-for-interview.svg";
import ArrowRightIcon from "@/assets/images/icons/arrow-right.svg";
import ConvertCv from "@/assets/images/icons/convert-cv.svg";
import { isDesktop } from "@/utils/device";
import { YOUR_SEARCH_KEYWORDS } from "@/contansts/keywords";
import Link from "@/components/Link/Link";
import { useTranslations } from "next-intl";
import ReactDOM, { PreloadAs } from "react-dom";

/**
 * Defined Child Component
 */
const QuickLinkBar = () => {
  const t = useTranslations();

  return (
    <div
      className={
        "relative z-10 flex max-h-[186px] max-w-[1260xp] justify-center gap-8 overflow-hidden rounded-[8px] bg-white p-4 align-middle font-sans shadow-md"
      }
    >
      {/* Create CV */}
      <div
        className={
          "group relative cursor-pointer items-center gap-3 p-4 text-left hover:rounded-[8px] hover:bg-neutral-100 hover:transition hover:duration-150 hover:ease-linear "
        }
      >
        <Link
          className="flex flex-col"
          href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/my-cv?openPopupCreate=1&src=topdev.vn&medium=toolbox `}
        >
          <Image
            src={CreateCVIcon}
            alt={"Create CV icon"}
            width={20}
            height={20}
            className="h-7 w-7 rounded-lg bg-slate-100 p-1 group-hover:bg-white"
          />

          <span className="mt-[7px] text-[18px] font-semibold leading-6 tracking-[0.09px] text-[#424242] ">
            {t("home_create_cv")}
          </span>
          <span className="mt-1 text-[14px] leading-5 tracking-[0.14px] text-[#757575] ">
            {t("home_create_cv_description")}
          </span>
          <div className="mt-2 flex gap-2 text-left group-hover:visible group-hover:transition group-hover:duration-150 group-hover:ease-linear">
            <button className="justify-center text-center text-[14px] font-semibold leading-5 tracking-[0.14px] text-primary underline">
              {t("home_toolbox_create_now")}
            </button>
            <Image
              src={ArrowRightIcon}
              width={12}
              height={14}
              alt="Arrow Learn More Icon"
            />
          </div>
        </Link>
      </div>
      {/* Create CV */}

      {/* Convert CV */}
      <div
        className={
          "group relative cursor-pointer items-center gap-3 p-4 text-left hover:rounded-[8px] hover:bg-neutral-100 hover:transition hover:duration-150 hover:ease-linear "
        }
      >
        <Link
          href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/profile?isShowConvertCVModal=1&src=topdev.vn&medium=toolbox `}
          className="flex flex-col"
        >
          <div className="relative">
            <div className="max-w-7 max-h-7 rounded-lg">
              <Image
                src={ConvertCv}
                alt={"Convert CV icon"}
                width={20}
                height={20}
                className=" h-7 w-7 rounded-lg bg-slate-100  p-1  group-hover:bg-white"
              />
            </div>
            <span className="m-w-[18px] bold py-auto absolute -top-[2px] left-[12px] z-10 max-h-[12px] rounded-lg border border-primary bg-primary-100 px-[5.2px] text-[8px] font-bold uppercase leading-[12px] text-primary">
              {t("common_new")}
            </span>
          </div>
          <span className="mt-[7px] text-[18px] font-semibold leading-6 tracking-[0.09px] text-[#424242]">
            {t("home_convert_cv")}
          </span>
          <span className="mt-1 text-[14px]  leading-5 tracking-[0.14px] text-[#757575]">
            {t("home_convert_cv_description")}
          </span>
          <div className="mt-2 flex gap-2 text-left group-hover:visible group-hover:transition group-hover:duration-150 group-hover:ease-linear">
            <button className="justify-center text-center text-[14px] font-semibold leading-5 tracking-[0.14px] text-primary underline">
              {t("home_toolbox_convert_now")}
            </button>
            <Image
              src={ArrowRightIcon}
              width={12}
              height={14}
              alt="Arrow Learn More Icon"
            />
          </div>
        </Link>
      </div>
      {/* Convert CV */}

      {/* Personal Test */}
      <div
        className={
          "group relative cursor-pointer items-center gap-3 p-4 text-left hover:rounded-[8px] hover:bg-neutral-100 hover:transition hover:duration-150 hover:ease-linear "
        }
      >
        <Link
          href={`${process.env.NEXT_PUBLIC_BASE_URL}/users/personality-test/prepare?src=topdev.vn&medium=toolbox`}
          className="flex flex-col"
        >
          <Image
            src={PersonalityTestIcon}
            alt={"Persionality Test icon"}
            width={20}
            height={20}
            className="h-7 w-7 rounded-lg bg-slate-100 p-1 group-hover:bg-white"
          />

          <span className="mt-[7px] text-[18px] font-semibold leading-6 tracking-[0.09px] text-[#424242]">
            {t("home_personality_test")}
          </span>
          <span className="mt-1 text-[14px]  leading-5 tracking-[0.14px] text-[#757575] ">
            {t("home_personality_test_description")}
          </span>
          <div className="mt-2 flex gap-2 text-left group-hover:visible group-hover:transition group-hover:duration-150 group-hover:ease-linear">
            <button className="justify-center text-center text-[14px] font-semibold leading-5 tracking-[0.14px] text-primary underline">
              {t("home_toolbox_take_the_test")}
            </button>
            <Image
              src={ArrowRightIcon}
              width={12}
              height={14}
              alt="Arrow Learn More Icon"
            />
          </div>
        </Link>
      </div>
      {/* Personal Test */}

      {/* IT Jobs for Fresher */}
      <div
        className={
          "group relative cursor-pointer items-center gap-3 p-4 text-left hover:rounded-[8px] hover:bg-neutral-100 hover:transition hover:duration-150 hover:ease-linear "
        }
      >
        <Link
          href={`${process.env.NEXT_PUBLIC_BASE_URL}/top-viec-lam-it-fresher?src=topdev.vn&medium=toolbox`}
          className={"flex flex-col"}
        >
          <Image
            src={ItJobFresherIcon}
            alt={"IT Job Fresher icon"}
            width={20}
            height={20}
            className="h-7 w-7 rounded-lg bg-slate-100 p-1 group-hover:bg-white"
          />

          <span className="mt-[7px] text-[18px] font-semibold leading-6 tracking-[0.09px] text-[#424242]">
            {t("home_it_jobs_for_fresher")}
          </span>
          <span className="mt-1 text-[14px] leading-5 tracking-[0.14px] text-[#757575]">
            {t("home_it_jobs_for_fresher_description")}
          </span>
          <div className="mt-2 flex gap-2 text-left group-hover:visible group-hover:transition group-hover:duration-150 group-hover:ease-linear">
            <button className="justify-center text-center text-[14px] font-semibold leading-5 tracking-[0.14px] text-primary underline">
              {t("home_toolbox_explore_jobs")}
            </button>
            <Image
              src={ArrowRightIcon}
              width={12}
              height={14}
              alt="Arrow Learn More Icon"
            />
          </div>
        </Link>
      </div>
      {/* IT Jobs for Fresher */}

      {/* QnA for interviews */}
      <div
        className={
          "group relative cursor-pointer items-center gap-3 p-4 text-left hover:rounded-[8px] hover:bg-neutral-100 hover:transition hover:duration-150 hover:ease-linear "
        }
      >
        <Link
          href={`${process.env.NEXT_PUBLIC_BASE_URL}/interview?src=topdev.vn&medium=toolbox`}
          className={"flex flex-col"}
        >
          <Image
            src={QnaForInterviewIcon}
            alt={"Blogs icon"}
            width={20}
            height={20}
            className="h-7 w-7 rounded-lg bg-slate-100 p-1 group-hover:bg-white"
          />
          <span className="mt-[7px] text-[18px] font-semibold leading-6 tracking-[0.09px] text-[#424242]">
            {t("home_interview_questions")}
          </span>
          <span className="mt-1 w-[188px] text-[14px] leading-5 tracking-[0.14px] text-[#757575]">
            {t("home_interview_questions_description")}
          </span>
          <div className="mt-2 flex gap-2 text-left group-hover:visible group-hover:transition group-hover:duration-150 group-hover:ease-linear">
            <button className="justify-center text-center text-[14px] font-semibold leading-5 tracking-[0.14px] text-primary underline">
              {t("home_learn_more")}
            </button>
            <Image
              src={ArrowRightIcon}
              width={12}
              height={14}
              alt="Arrow Learn More Icon"
            />
          </div>
        </Link>
      </div>
      {/* QnA for interviews */}
    </div>
  );
};

const RoundedBackground = () => {
  return (
    <div
      className={
        "absolute bottom-0 z-0 h-[69px] w-full rounded-tl-[54px] rounded-tr-[54px] bg-white"
      }
    ></div>
  );
};

ReactDOM.preload("https://c.topdevvn.com/v4/assets/images/bg-search.webp", {
  /** @ts-ignore */
  as: "image",
});

export default function SectionHero() {
  return (
    <section id={"hero-container"}>
      <div
        style={{ backgroundSize: "cover" }}
        className="bg-cover relative min-h-[178px] bg-gradient-to-br from-red-100 to-orange-200 bg-left bg-no-repeat py-6 lg:min-h-[480px] lg:bg-[url('https://c.topdevvn.com/v4/assets/images/bg-search.jpg')] lg:pb-0 lg:pt-6"
      >
        <div className="container">
          <div className={"mb-3 flex flex-col gap-4 lg:mb-16 lg:min-h-[184px]"}>
            {isDesktop() ? (
              <SearchTitleAnimation initData={YOUR_SEARCH_KEYWORDS} />
            ) : (
              ""
            )}
            <MainSearch />
          </div>
          {isDesktop() ? <QuickLinkBar /> : ""}
        </div>
        {isDesktop() ? <RoundedBackground /> : ""}
      </div>
    </section>
  );
}
