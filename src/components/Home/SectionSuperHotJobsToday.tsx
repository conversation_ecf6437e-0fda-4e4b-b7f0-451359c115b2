"use client";

import { useTranslations } from "next-intl";
import Image from "next/image";
import { isDesktop } from "react-device-detect";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import NextIcon from "@/assets/images/icons/pagination-next.svg";
import PrevIcon from "@/assets/images/icons/pagination-prev.svg";
import CardJobGrid from "@/components/Card/Job/CardJobGrid";
import { JobType } from "@/types/job";
import { useEffect, useState } from "react";
import { getHomepageSuperHotJobsToday } from "@/services/jobAPI";
import { getCurrentLocaleForParams } from "@/utils/locale";

export default function SectionSuperHotJobsToday() {
  const t = useTranslations();
  const [jobs, setJobs] = useState<JobType[]>([]);

  /**
   * Load Superhotjobs today
   */
  useEffect(() => {
    getHomepageSuperHotJobsToday(getCurrentLocaleForParams())
      .then((data) => {
        setJobs(data);
      });
  }, []);

  return (
    <section
      id="super-hot-jobs-today-container"
      className="min-h-[332px] py-6 lg:min-h-[485px] lg:py-8"
    >
      <div className="container flex flex-col gap-4 lg:gap-8">
        <h2 className="text-xl font-bold lg:text-4xl">
          {t.rich("home_super_hot_jobs_today", {
            red: (chunk) => <span className="text-primary">{chunk}</span>,
          })}
        </h2>
        <div className="overflow-x-hidden">
          {jobs.length > 0 ? (
            <div className="min-w-[908px]">
              <Swiper
                grabCursor={true}
                slidesPerGroup={isDesktop ? 3 : 1}
                slidesPerView={3}
                className="w-full"
                loop={true}
                modules={[Pagination, Navigation, Autoplay]}
                autoplay={{
                  delay: 5000,
                  disableOnInteraction: false,
                }}
                pagination={
                  isDesktop ? { clickable: true, el: ".pagination" } : false
                }
                navigation={
                  isDesktop
                    ? {
                        nextEl: ".nextEl",
                        prevEl: ".preEl",
                      }
                    : false
                }
                spaceBetween={isDesktop ? "24px" : "12px"}
              >
                {jobs.map((job, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <CardJobGrid job={job} />
                    </SwiperSlide>
                  );
                })}
                {isDesktop ? (
                  <div className="swiper-footer hidden items-center justify-center lg:flex">
                    <div className="preEl">
                      <Image
                        src={PrevIcon}
                        alt={"Previous"}
                        width={7}
                        height={8}
                      />
                    </div>
                    <div className="pagination"></div>
                    <div className="nextEl">
                      <Image src={NextIcon} alt={"Next"} width={7} height={8} />
                    </div>
                  </div>
                ) : (
                  ""
                )}
              </Swiper>
            </div>
          ) : (
            ""
          )}
        </div>
      </div>
    </section>
  );
}
