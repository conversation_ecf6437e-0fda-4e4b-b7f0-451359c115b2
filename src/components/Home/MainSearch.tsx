"use client";

import { TaxonomiesType } from "@/types/taxonomy";
import GlobalMainSearch from "@/components/Search/MainSearch";
import { useAppSelector } from "@/store";

export default function MainSearch() {
  const taxonomies = useAppSelector((state) => state.taxonomies);

  return (
    <GlobalMainSearch
      hideFilter={true}
      hideQuickFilter={false}
      taxonomies={taxonomies as TaxonomiesType}
      isHome
    />
  );
}
