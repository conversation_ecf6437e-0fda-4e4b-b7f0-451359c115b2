"use client";

import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Grid, Navigation, Pagination } from "swiper/modules";

import CardJobList from "@/components/Card/Job/CardJobList";
import { isDesktop } from "react-device-detect";
import { JobType } from "@/types/job";
import { getHomepageNewJobs } from "@/services/jobAPI";
import Image from "next/image";
import PrevIcon from "@/assets/images/icons/pagination-prev.svg";
import NextIcon from "@/assets/images/icons/pagination-next.svg";
import { useTranslations } from "next-intl";
import Link from "@/components/Link/Link";
import { getCurrentLocaleForParams } from "@/utils/locale";

export default function SectionNewJobs() {
  const t = useTranslations();
  const [jobs, setJobs] = useState<JobType[]>([]);
  useEffect(() => {
    getHomepageNewJobs(
      isDesktop ? undefined : 10,
      getCurrentLocaleForParams(),
    ).then((data) => setJobs(data));
  }, []);

  return (
    <section
      id="new-jobs-container"
      className="min-h-[324px] bg-neutral-100 pt-4 pb-8 lg:min-h-[586px] lg:py-8"
    >
      <div className="container flex flex-col gap-4 lg:gap-8">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold lg:text-4xl">
            {t("home_new_jobs")}
          </h2>
          <Link
            href={`/${t("slug_it_jobs")}?src=topdev_home&medium=newjobs`}
            className="text-sm font-bold text-primary underline hover:text-primary-400 md:text-base"
          >
            {t("home_view_all")}
          </Link>
        </div>
        <div className="overflow-x-hidden">
          {jobs.length > 0 ? (
            <div className="lg:min-w-[984px]">
              <Swiper
                grabCursor={true}
                slidesPerView={isDesktop ? 3 : 1.1}
                slidesPerGroup={isDesktop ? 3 : 1}
                grid={{
                  rows: isDesktop ? 3 : 2,
                  fill: "row",
                }}
                className="w-full"
                modules={isDesktop ? [Grid, Pagination, Navigation] : [Grid]}
                spaceBetween={isDesktop ? 24 : 12}
                pagination={
                  isDesktop ? { clickable: true, el: ".pagination" } : false
                }
                navigation={
                  isDesktop
                    ? {
                        nextEl: ".nextEl",
                        prevEl: ".preEl",
                      }
                    : false
                }
              >
                {jobs.map((job, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <CardJobList job={job} classMinHeight={"min-h-[117px]"} srcPage={"home"} mediumPage={"newjobs"} />
                    </SwiperSlide>
                  );
                })}

                {isDesktop && (
                  <div className="swiper-footer items-center justify-center lg:flex">
                    <div className="preEl">
                      <Image
                        src={PrevIcon}
                        alt={"Previous"}
                        width={7}
                        height={8}
                      />
                    </div>
                    <div className="pagination"></div>
                    <div className="nextEl">
                      <Image src={NextIcon} alt={"Next"} width={7} height={8} />
                    </div>
                  </div>
                )}
              </Swiper>
            </div>
          ) : null}
        </div>
      </div>
    </section>
  );
}
