"use client";

import { Autoplay, Navigation } from "swiper/modules";
import { Swiper, SwiperSlide, useSwiper } from "swiper/react";
import { CompanyType } from "@/types/company";
import { useTranslations } from "next-intl";
import { Swiper as SwiperRef } from "swiper";
import Image from "next/image";
import { Link } from "@/navigation";
import { HiMiniChevronLeft, HiMiniChevronRight } from "react-icons/hi2";
import { useEffect, useRef, useState } from "react";
import { getHomepageFeaturedCompanies } from "@/services/companyAPI";
import { isDesktop } from "react-device-detect";
import _ from "lodash";

export default function SectionFeaturedCompanies() {
  const t = useTranslations();
  const NUM_OF_SLIDES_REQUIRED = 6;
  const swiperRef = useRef<SwiperRef>();
  const [companies, setCompanies] = useState<CompanyType[]>([]);
  useEffect(() => {
    getHomepageFeaturedCompanies().then((response) => {
      if (
        response.length >= NUM_OF_SLIDES_REQUIRED &&
        response.length < NUM_OF_SLIDES_REQUIRED * 2
      ) {
        setCompanies([
          ...response,
          ...response.slice(0, NUM_OF_SLIDES_REQUIRED * 2 - response.length),
        ]);
        return;
      }
      setCompanies(response);
    });
  }, []);

  return (
    <section
      id="featured-companies-container"
      className="flex min-h-[172px] select-none flex-col gap-8 bg-neutral-100 py-8 lg:bg-white lg:py-20"
    >
      <div className="container flex flex-col gap-3.5">
        <h2 className="text-xl font-bold lg:text-4xl">
          {t("home_featured_companies")}
        </h2>
        <div>
          {companies.length > 0 ? (
            <div className={"flex items-center"}>
              {isDesktop && companies.length >= 6 ? (
                <div
                  className={"w-14 flex-none"}
                  onClick={() => swiperRef.current?.slidePrev()}
                >
                  <div className="preElFeatured z-10 w-14 flex-none cursor-pointer rounded border border-transparent p-2 text-primary-300 transition-all hover:border-primary-200 hover:bg-white  lg:p-4">
                    <HiMiniChevronLeft className="h-6 w-6" />
                  </div>
                </div>
              ) : null}
              <Swiper
                grabCursor={true}
                slidesPerView={isDesktop ? 6 : 3.5}
                centerInsufficientSlides={companies.length < 6}
                loop={true}
                className="!lg:px-14 relative w-full !px-0"
                autoplay={{
                  delay: 5000,
                }}
                modules={isDesktop ? [Autoplay, Navigation] : [Autoplay]}
                spaceBetween={isDesktop ? "0px" : "16px"}
                pagination={!isDesktop ? undefined : false}
                onSwiper={(swiper) => (swiperRef.current = swiper)}
              >
                {companies.map((company, index) => {
                  return (
                    <SwiperSlide
                      className="rounded bg-white p-2 text-center lg:bg-transparent"
                      key={index}
                    >
                      <div className="m-auto flex h-[50px] w-[72px] items-center justify-center rounded bg-white transition-all hover:shadow-sm md:h-[112px] md:w-full">
                        <Link
                          href={{
                            pathname: "/companies/[slug]",
                            params: {
                              slug: company.slug + "-" + company.id,
                            },
                            query: { src: "topdev_home", medium: "featuredcompanies" },
                          }}
                        >
                          {company.image_logo && (
                            <Image
                              src={company.image_logo as string}
                              alt={company.display_name as string}
                              width={isDesktop ? 160 : 56}
                              height={isDesktop ? 112 : 50}
                              className={`${
                                isDesktop ? "max-h-[112px]" : "max-h-[50px]"
                              } max-w-full object-contain`}
                            />
                          )}
                        </Link>
                      </div>
                    </SwiperSlide>
                  );
                })}
              </Swiper>
              {isDesktop && companies.length >= 6 ? (
                <div className={"w-14 flex-none"}>
                  <div
                    onClick={() => {
                      swiperRef.current?.slideNext();
                    }}
                    className="nextElFeatured z-10 cursor-pointer rounded border border-transparent p-2 text-primary-300 transition-all hover:border-primary-200 hover:bg-white md:p-4"
                  >
                    <HiMiniChevronRight className="h-6 w-6" />
                  </div>
                </div>
              ) : null}
            </div>
          ) : null}
        </div>
      </div>
    </section>
  );
}
