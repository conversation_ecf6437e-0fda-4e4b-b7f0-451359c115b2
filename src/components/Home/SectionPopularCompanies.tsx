"use client";

import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";

// Swiper modules
import { Grid, Navigation, Pagination } from "swiper/modules";

import dynamic from "next/dynamic";
import { isDesktop } from "react-device-detect";
import { JobType } from "@/types/job";
import { getHomepagePopularCompanyJobs } from "@/services/jobAPI";
import Image from "next/image";
import PrevIcon from "@/assets/images/icons/pagination-prev.svg";
import NextIcon from "@/assets/images/icons/pagination-next.svg";
import { useTranslations } from "next-intl";
import { getCurrentLocaleForParams } from "@/utils/locale";

//Card Infor mative Grid
const CardInformativeGrid = dynamic(
  () => import("@/components/Card/Job/CardInformativeGrid"),
);

export default function SectionPopularCompanies() {
  const t = useTranslations();
  const [jobs, setJobs] = useState<JobType[]>([]);
  useEffect(() => {
    getHomepagePopularCompanyJobs(
      !isDesktop ? 12 : undefined,
      getCurrentLocaleForParams(),
    ).then((data) => {
      setJobs(data.jobs);
    });
  }, []);

  return (
    <section
      id={"popular-companies-container"}
      className={
        "min-h-[464px] bg-neutral-100 pb-4 pt-8 lg:min-h-fit lg:bg-white lg:py-14"
      }
    >
      <div className={"container flex flex-col gap-4 lg:gap-8"}>
        <h2 className={"text-xl font-bold lg:text-4xl"}>
          {t.rich("home_popular_companies", {
            red: (chunk) => <span className={"text-primary"}>{chunk}</span>,
          })}
        </h2>

        <div className={"overflow-x-hidden"}>
          {jobs.length > 0 ? (
            <div className="md:min-w-[750px]">
              <Swiper
                grabCursor={true}
                slidesPerView={isDesktop ? 3 : 1.5}
                slidesPerGroup={isDesktop ? 3 : 1}
                spaceBetween={isDesktop ? 24 : 12}
                grid={{
                  rows: isDesktop ? 3 : 2,
                  fill: "row",
                }}
                className="w-full"
                modules={isDesktop ? [Grid, Pagination, Navigation] : [Grid]}
                pagination={
                  isDesktop ? { clickable: true, el: ".pagination" } : false
                }
                navigation={
                  isDesktop
                    ? {
                        nextEl: ".nextEl",
                        prevEl: ".preEl",
                      }
                    : false
                }
              >
                {jobs.map((job, index) => {
                  return (
                    <SwiperSlide key={index}>
                      <CardInformativeGrid
                        index={index}
                        outline={true}
                        job={job}
                        display={isDesktop ? "horizontal" : "vertical"}
                        highlight={isDesktop ? true : false}
                        home={true}
                        className="h-full"
                        srcPage={"home"}
                        mediumPage={"popularcompanies"}
                      />
                    </SwiperSlide>
                  );
                })}

                <div
                  className={
                    "swiper-footer hidden items-center justify-center lg:flex"
                  }
                >
                  <div className={"preEl"}>
                    <Image
                      src={PrevIcon}
                      alt={"Previous"}
                      width={7}
                      height={8}
                    />
                  </div>
                  <div className={"pagination"}></div>
                  <div className={"nextEl"}>
                    <Image src={NextIcon} alt={"Next"} width={7} height={8} />
                  </div>
                </div>
              </Swiper>
            </div>
          ) : (
            ""
          )}
        </div>
      </div>
    </section>
  );
}
