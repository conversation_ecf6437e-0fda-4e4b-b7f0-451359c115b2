"use client";

import React, { useEffect, useState } from "react";
import Link from "@/components/Link/Link";
import { isDesktop } from "react-device-detect";
import { BlogType } from "@/types/blog";
import { getHomeBlogPosts } from "@/services/blogAPI";
import { useTranslations } from "next-intl";
import CardBlogMain from "@/components/Card/Blog/CardBlogMain";
import CardBlogItem from "@/components/Card/Blog/CardBlogItem";
import { classNames } from "@/utils";

export default function SectionBlogIT() {
  const t = useTranslations();
  const [posts, setPosts] = useState<BlogType[]>([]);
  const [isClient, setIsClient] = useState<boolean>(false);
  useEffect(() => {
    setIsClient(true);
    getHomeBlogPosts(4).then((data) => setPosts(data));
  }, []);

  return (
    <section
      id={"blog-it-container"}
      className={"min-h-0 py-6 lg:min-h-[588px] lg:pb-7 lg:pt-14"}
    >
      <div className={"container flex flex-col gap-0 lg:gap-8"}>
        <div className={"flex items-center justify-between"}>
          <h2 className={"text-xl font-bold lg:text-4xl"}>Blog IT</h2>
          <Link
            href={"https://topdev.vn/blog/?src=topdev_home&medium=blogIT"}
            className={
              "text-sm font-bold text-primary underline hover:text-primary-400 lg:text-base"
            }
          >
            {t("home_view_all")}
          </Link>
        </div>

        <div className={"grid divide-y lg:grid-cols-2 lg:gap-6 lg:divide-y-0"}>
          {isDesktop && isClient && (
            <div className={"pb-6"}>
              {posts.length > 0 ? (
                <CardBlogMain
                  post={posts[0]}
                  srcPage={"home"}
                  mediumPage={"blogIT"}
                />
              ) : (
                ""
              )}
            </div>
          )}
          <div className={"divide-y"}>
            {isDesktop ? (
              <>
                <div
                  className={classNames(
                    !isDesktop && isClient ? "" : "pt-6",
                    "pb-6 lg:pt-0",
                  )}
                >
                  {posts[1] ? (
                    <CardBlogItem
                      post={posts[1]}
                      srcPage={"home"}
                      mediumPage={"blogIT"}
                    />
                  ) : null}
                </div>
                <div className={"py-6"}>
                  {posts[2] ? (
                    <CardBlogItem
                      post={posts[2]}
                      srcPage={"home"}
                      mediumPage={"blogIT"}
                    />
                  ) : null}
                </div>
                <div className={"pt-6"}>
                  {posts[3] ? (
                    <CardBlogItem
                      post={posts[3]}
                      srcPage={"home"}
                      mediumPage={"blogIT"}
                    />
                  ) : null}
                </div>
              </>
            ) : (
              <>
                {posts.map((postItem, index) => {
                  return (
                    <div className={"py-6 last:pb-0"} key={index}>
                      <CardBlogItem
                        post={postItem}
                        srcPage={"home"}
                        mediumPage={"blogIT"}
                      />
                    </div>
                  );
                })}
              </>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
