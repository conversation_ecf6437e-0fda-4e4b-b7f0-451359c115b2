"use client";

import React, { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react"; // Import Swiper styles

import {
  EffectCoverflow,
  Navigation,
  Pagination,
  Autoplay,
} from "swiper/modules";
import { CardCompanyBranding } from "@/components/Card/Company";
import { isDesktop } from "react-device-detect";
import { getHomepageSpotlightCompanies } from "@/services/companyAPI";
import { CompanyType } from "@/types/company";
import { useAppDispatch } from "@/store";
import Image from "next/image";
import NextIcon from "@/assets/images/icons/pagination-next.svg";
import PrevIcon from "@/assets/images/icons/pagination-prev.svg";
import { useTranslations } from "next-intl";
import { gtag } from "@/utils";
import { getCurrentLocaleForParams } from "@/utils/locale";

export default function SectionSuperSpotlightCompanies() {
  const t = useTranslations();

  const [companies, setCompanies] = useState<CompanyType[]>([]);
  const dispatch = useAppDispatch();

  useEffect(() => {
    // Load main dat
    getHomepageSpotlightCompanies(getCurrentLocaleForParams()).then(
      (response) => setCompanies(response),
    );
  }, [dispatch]);

  useEffect(() => {
    gtag({ event: "event_name" });
  }, []);

  return (
    <section
      id={"super-spotlight-companies-container"}
      className={
        "min-h-[320px] bg-neutral-100 pt-8 lg:min-h-[482px] lg:bg-white lg:pt-10"
      }
    >
      <div className={"container flex flex-col gap-4 lg:gap-8"}>
        <h1 className={"text-xl font-bold lg:text-4xl"}>
          {t.rich("home_super_spotlight_companies", {
            red: (chunk) => <span className={"text-primary"}>{chunk}</span>,
          })}
        </h1>

        {companies.length > 0 ? (
          <Swiper
            grabCursor={true}
            centeredSlides={true}
            slidesPerView={"auto"}
            loop={true}
            effect={isDesktop ? "coverflow" : ""}
            autoplay={{
              delay: 5000,
            }}
            coverflowEffect={
              isDesktop
                ? {
                    rotate: 0,
                    stretch: 0,
                    depth: 200,
                    modifier: 1,
                    slideShadows: false,
                  }
                : {}
            }
            pagination={
              isDesktop ? { clickable: true, el: ".pagination" } : undefined
            }
            navigation={{
              nextEl: ".nextEl",
              prevEl: ".preEl",
            }}
            modules={
              isDesktop
                ? [EffectCoverflow, Pagination, Navigation, Autoplay]
                : [Autoplay, Pagination, Navigation]
            }
            className={"w-full"}
            id={"swiper-splotlight-companies"}
          >
            {companies.map((company) => {
              return (
                <SwiperSlide key={company.id}>
                  <div
                    className={"w-full px-2 pb-[4.75rem] lg:w-[832px] lg:pb-24"}
                  >
                    <CardCompanyBranding
                      companyData={company}
                      srcPage={"home"}
                    />
                  </div>
                </SwiperSlide>
              );
            })}

            <div className={"swiper-footer flex items-center justify-center"}>
              <div
                className={
                  "preEl absolute left-0 top-1/2 z-10 inline-flex h-9 w-9 items-center justify-center rounded-full bg-primary-100 lg:static lg:block lg:h-auto lg:w-auto lg:bg-transparent"
                }
              >
                <Image src={PrevIcon} alt={"Previous"} width={7} height={8} />
              </div>
              {isDesktop ? <div className={"pagination"}></div> : null}
              <div
                className={
                  "nextEl absolute right-0 top-1/2 z-10 inline-flex h-9 w-9 items-center justify-center rounded-full bg-primary-100 lg:static lg:block lg:h-auto lg:w-auto lg:bg-transparent"
                }
              >
                <Image src={NextIcon} alt={"Next"} width={7} height={8} />
              </div>
            </div>
          </Swiper>
        ) : (
          ""
        )}
      </div>
    </section>
  );
}
