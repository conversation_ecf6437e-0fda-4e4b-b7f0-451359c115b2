"use client";

import { useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";

// Import Swiper modules
import { Grid, Navigation, Pagination } from "swiper/modules";

import NextIcon from "@/assets/images/icons/pagination-next.svg";
import PrevIcon from "@/assets/images/icons/pagination-prev.svg";
import CardInformativeGrid from "@/components/Card/Job/CardInformativeGrid";
import { getHomepageHighlightCompanyJobs } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import { getCurrentLocaleForParams } from "@/utils/locale";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { isDesktop } from "react-device-detect";
import _ from "lodash";

export default function SectionHighlightCompanies() {
  const t = useTranslations();
  const [jobs, setJobs] = useState<JobType[]>([]);
  useEffect(() => {
    getHomepageHighlightCompanyJobs(
      isDesktop ? undefined : 12,
      getCurrentLocaleForParams(),
    ).then((data) => {
      const shuffleData = _.shuffle(data);
      setJobs(shuffleData);
    });
  }, []);

  return (
    <section
      id={"highlight-companies-container"}
      className={
        "min-h-[488px] bg-neutral-100 py-4 lg:min-h-[665px] lg:bg-white lg:py-14"
      }
    >
      <div className={"container flex flex-col gap-4 lg:gap-8"}>
        <h2 className={"text-xl font-bold lg:text-4xl"}>
          {t.rich("home_highlight_companies", {
            red: (chunk) => <span className={"text-primary"}>{chunk}</span>,
          })}
        </h2>

        <div className={"overflow-x-hidden"}>
          {jobs.length > 0 ? (
            <div className="md:min-w-[720px]">
              <Swiper
                grabCursor={true}
                slidesPerView={isDesktop ? 4 : 1.5}
                slidesPerGroup={isDesktop ? 4 : 1}
                spaceBetween={isDesktop ? 24 : 12}
                grid={{
                  rows: 2,
                  fill: "row",
                }}
                className={"w-full"}
                modules={isDesktop ? [Grid, Pagination, Navigation] : [Grid]}
                pagination={
                  isDesktop ? { clickable: true, el: ".pagination" } : false
                }
                autoHeight={false}
                navigation={
                  isDesktop
                    ? {
                        nextEl: ".nextEl",
                        prevEl: ".preEl",
                      }
                    : false
                }
              >
                {jobs.map((job, index) => {
                  return (
                    <SwiperSlide key={index} className="h-auto">
                      <CardInformativeGrid
                        index={index}
                        outline={true}
                        job={job}
                        display={"vertical"}
                        highlight={false}
                        home={true}
                        className="h-full"
                        srcPage={"home"}
                        mediumPage={"highlightcompanies"}
                      />
                    </SwiperSlide>
                  );
                })}

                {isDesktop && (
                  <div
                    className={
                      "swiper-footer hidden items-center justify-center md:flex"
                    }
                  >
                    <div className={"preEl"}>
                      <Image
                        src={PrevIcon}
                        alt={"Previous"}
                        width={7}
                        height={8}
                      />
                    </div>
                    <div className={"pagination"}></div>
                    <div className={"nextEl"}>
                      <Image src={NextIcon} alt={"Next"} width={7} height={8} />
                    </div>
                  </div>
                )}
              </Swiper>
            </div>
          ) : null}
        </div>
      </div>
    </section>
  );
}
