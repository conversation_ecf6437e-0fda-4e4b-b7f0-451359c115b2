import React, { <PERSON> } from "react";
import Link from "@/components/Link/Link";
import { classNames } from "@/utils";

interface Props {
  title?: string;
  url?: string;
  quantity?: number;
  className?: string;
  size?: "xs" | "sm" | "md";
}

const SkillTag: FC<Props> = ({ title, quantity, url, size = "md" }) => {
  if (quantity) {
    return (
      <p
        className={classNames(
          "inline-flex items-center justify-center rounded border border-solid border-transparent bg-blue-light text-blue-dark",
          size === "xs" ? "h-[1.625rem] px-2 text-xs" : "",
          size === "sm" ? "h-7 px-2 text-sm" : "",
          size === "md" ? "h-[2.375rem] px-3" : "",
        )}
      >
        +{quantity}
      </p>
    );
  }
  if (!url) {
    return null;
  }
  return (
    <Link
      href={url}
      className={classNames(
        `inline-flex items-center justify-center rounded border border-solid border-transparent bg-blue-light text-blue-dark hover:border-blue-dark`,
        size === "xs" ? "h-[1.625rem] px-2 text-xs" : "",
        size === "sm" ? "h-7 px-2 text-sm" : "",
        size === "md" ? "h-[2.375rem] px-3" : "",
      )}
    >
      {title}
    </Link>
  );
};

export default SkillTag;
