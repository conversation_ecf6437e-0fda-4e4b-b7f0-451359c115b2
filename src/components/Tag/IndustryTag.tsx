import React, { <PERSON> } from "react";
import { classNames } from "@/utils";

interface Props {
  title?: string;
  quantity?: number;
  className?: string;
}

const IndustryTag: FC<Props> = ({ title, quantity }) => {
  if (quantity) {
    return <p className={classNames("inline-block")}>(+{quantity})</p>;
  }
  return <p className={classNames(`group inline-block`)}>{title}</p>;
};

export default IndustryTag;
