"use client";

import Link from "@/components/Link/Link";
import ChipTag from "@/components/Tag/ChipTag";
import useTaxonomy from "@/utils/taxonomies";
import { useTranslations } from "next-intl";
import { FC } from "react";
import { isDesktop } from "react-device-detect";

const SkillTagTaxonomy: FC<{
  skillId: number;
  srcPage?: string;
  size?: "md" | "xs" | "sm";
  screenView?: string;
}> = ({
  skillId,
  srcPage,
  size,
  screenView
}) => {
  const taxonomy = useTaxonomy();
  const t = useTranslations();
  const skillTaxonomy = taxonomy(skillId, "skills");
  const srcQueryParam = 'topdev_' + (srcPage ? srcPage : 'search');

  if (screenView === 'mobile') {
    return (
      <Link
        href={`/${t(
          "slug_it_jobs",
        )}/${skillTaxonomy?.slug}-kt${skillTaxonomy?.id}?src=${srcQueryParam}&medium=skilltag&view=app`}
        className={"mr-2 inline-block"}
      >
        <ChipTag
          title={skillTaxonomy?.text as string}
          size={size ? size : isDesktop ? "sm" : "xs"}
        />
      </Link>
    );
  }

  return (
    <Link
      href={`/${t(
        "slug_it_jobs",
      )}/${skillTaxonomy?.slug}-kt${skillTaxonomy?.id}?src=${srcQueryParam}&medium=skilltag`}
      className={"mr-2 inline-block"}
    >
      <ChipTag
        title={skillTaxonomy?.text as string}
        size={size ? size : isDesktop ? "sm" : "xs"}
      />
    </Link>
  );
};

export default SkillTagTaxonomy;
