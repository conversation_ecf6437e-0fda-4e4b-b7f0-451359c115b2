import React, { <PERSON> } from "react";
import Image from "next/image";
import { HiFire } from "react-icons/hi2";
import { classNames } from "@/utils";

interface Props {
  size?: "sm" | "md";
}

const HotJobTag: FC<Props> = ({ size = "md" }) => {
  return (
    <div className="relative">
      <Image
        src={
          size === "md"
            ? "https://cdn.topdev.vn/v4/assets/images/hotjobs-icon.svg"
            : "https://cdn.topdev.vn/v4/assets/images/hotjobs-mobile-icon.svg"
        }
        alt="Hot job"
        width={size === "md" ? 160 : 128}
        height={size === "md" ? 40 : 32}
        className="h-8 w-32 object-contain object-left lg:h-10 lg:w-40"
        loading="lazy"
      />
      <div
        className={classNames(
          "absolute bottom-1.5 left-5 flex flex-nowrap items-center justify-center gap-1 whitespace-nowrap text-white lg:bottom-2",
          size === "md" ? "text-base" : "text-xs",
        )}
      >
        <span className="text-xl">
          <HiFire />
        </span>
        <span className="font-bold">HOT JOB</span>
      </div>
    </div>
  );
};

export default HotJobTag;
