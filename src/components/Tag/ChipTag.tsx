"use client";
import React, { <PERSON>, MouseEvent, ReactNode } from "react";
import { classNames } from "@/utils";

interface ChipTagType {
  title: string;
  accent?: "blue" | "solid" | "line" | "neutral-line" | "neutral-solid";
  size?: "xs" | "sm" | "md";
  onClick?: (event: MouseEvent<HTMLSpanElement>) => void;
  tailingIcon?: ReactNode;
  onClickIcon?: (event: MouseEvent<HTMLSpanElement>) => void;
  isShowMobile?: boolean;
  isHover?: boolean;
}

const ChipTag: FC<ChipTagType> = ({
  title,
  accent = "blue",
  size = "md",
  tailingIcon,
  isHover = true,
  ...props
}) => {
  return (
    <span
      onClick={(e) => props.onClick && props.onClick(e)}
      className={classNames(
        "whitespace-nowrap rounded border border-solid font-normal transition-all",
        tailingIcon
          ? `group/tw-chip inline-flex items-center justify-center gap-0 overflow-hidden ${
              isHover ? "hover:gap-2" : ""
            }`
          : "inline-flex items-center justify-center",
        accent === "blue"
          ? `border-blue-light text-blue-dark ${
              isHover ? "bg-blue-light hover:border-blue-dark" : "bg-blue-light"
            }`
          : "",
        accent === "solid"
          ? `border-blue-dark text-white ${
              isHover ? "bg-blue-dark hover:bg-blue-light" : "bg-blue-light"
            }`
          : "",
        accent === "line"
          ? `border-blue-dark text-blue-dark ${
              isHover ? "bg-transparent hover:bg-blue-light" : "bg-blue-light"
            }`
          : "",
        accent === "neutral-line"
          ? `border-gray-300 text-gray-600 ${
              isHover ? "bg-transparent hover:bg-gray-300" : "bg-blue-light"
            }`
          : "",
        accent === "neutral-solid"
          ? "border-gray-200 bg-gray-200 text-gray-600"
          : "",
        size === "xs" ? "h-[1.625rem] px-2 text-xs" : "",
        size === "sm"
          ? "h-[1.625rem] px-2 text-xs md:h-7 md:px-2 md:text-sm"
          : "",
        size === "md"
          ? "h-[1.625rem] px-2 text-xs md:h-7 md:px-2 md:text-sm lg:h-[2.375rem] lg:px-3 lg:text-base"
          : "",
      )}
    >
      {title}
      {tailingIcon && (
        <span
          onClick={(event) => {
            if (props.onClickIcon) {
              props.onClickIcon(event);
            }
          }}
          className={classNames(
            "max-w-0 overflow-hidden transition-all group-hover/tw-chip:max-w-none",
            props.onClickIcon ? "cursor-pointer" : "",
            props.isShowMobile
              ? "max-w-none overflow-auto"
              : "max-w-0 overflow-hidden",
          )}
        >
          {tailingIcon}
        </span>
      )}
    </span>
  );
};

export default ChipTag;
