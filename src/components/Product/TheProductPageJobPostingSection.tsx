import Image from "next/image";
import { getLocale, getTranslations } from "next-intl/server";
import { getAllProducts } from "@/services/productsAPI";
import { ProductProp, ResponseApiProducts } from "@/types/products";
import { PRODUCT_CATEGORYS } from "@/contansts/payment";
import ProductJobPostingItem from "@/components/Product/ProductJobPostingItem";
import {
  localeConvert,
  localeConvertBenefits,
} from "@/utils/localeConvert";

export default async function TheProductPageJobPostingSection() {
  const t = await getTranslations();
  const locale = await getLocale();

  const products = (await getAllProducts(
    PRODUCT_CATEGORYS.PACKAGE,
    20,
    1,
    620,
    450,
  )) as ResponseApiProducts;
  if (!products) return;
  return (
    <section id="section-recruitment" className="pb-5 md:pb-20">
      <div className="container">
        <div className="mb-4 flex flex-col gap-4 md:mb-12">
          <h3 className="inline-flex w-fit items-center gap-2 rounded-[32px] bg-white py-2 pl-2 pr-6">
            <span className="flex h-[42px] w-[42px] items-center justify-center rounded-full bg-gray-100">
              <Image
                src="/v4/assets/images/online-payment/documents.png"
                alt="documents"
                width={25}
                height={27}
                loading="lazy"
              />
            </span>
            <p className="mb-0 text-3xl font-bold text-gray-600">
              {t("products_title_section_recruitment")}
            </p>
          </h3>
          <p className="mb-0 text-lg font-normal text-gray-500">
            {t("products_description_section_recruitment")}
          </p>
        </div>
        <div className="flex flex-col gap-4 md:gap-20">
          {products?.data?.map((value: ProductProp, index: number) => {
            return (
              <ProductJobPostingItem
                key={index}
                id={value.id}
                benefits={localeConvertBenefits(value, "benefits", locale)}
                name={localeConvert(value, "name", locale)}
                price={value.price}
                description={localeConvert(value, "description", locale)}
                anchoring_price={value.anchoring_price}
                media_url={value.media_url}
              />
            );
          })}
        </div>
        <ul className="mt-4 italic leading-7 text-gray-400 md:mt-20">
          <li>{t("products_description_product_job_posting_1")}</li>
          <li>{t("products_description_product_job_posting_2")}</li>
          <li>{t("products_description_product_job_posting_3")}</li>
        </ul>
      </div>
    </section>
  );
}
