"use client";

import Image from "next/image";
import { QuickNavigationButtonProp } from "@/types/products";

export default function QuickNavigationButton({
  title,
  url,
  image,
}: QuickNavigationButtonProp) {
  //Onclick scroll section
  const onClickSroll = () => {
    const section = document.getElementById(url);
    section?.scrollIntoView({ behavior: "smooth" });
    window.history.pushState(null, "", "/products#" + url);
  };
  //End onclick scroll section

  return (
    <div
      onClick={() => onClickSroll()}
      data-cy="item-quick-navigation-button"
      className="group/item item-quick-navigation-button md:m-0 m-auto max-w-[230px] w-full flex min-w-[194px] cursor-pointer flex-col gap-4 rounded-xl p-4 transition-all duration-300 hover:bg-white hover:shadow-md"
    >
      <div className="group-hover/item:shadow-none m-auto flex h-[100px] w-[100px] items-center justify-center rounded-[10.5px] bg-white p-[10.5px] shadow-sm transition-all duration-300 group-hover/item:bg-gray-100">
        <div className="relative h-[66px] w-[66px]">
          <Image
            width={66}
            height={66}
            src={image}
            alt={title}
            loading="lazy"
          />
        </div>
      </div>
      <h3 className="mb-0 md:min-h-[48px] text-center text-lg font-semibold uppercase">
        {title}
      </h3>
    </div>
  );
}
