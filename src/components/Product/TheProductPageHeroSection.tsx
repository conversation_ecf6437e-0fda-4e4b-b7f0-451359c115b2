import { useTranslations } from "next-intl";

export default function TheProductPageHeroSection() {
  const t = useTranslations();

  return (
    <section id="section-hero">
      <div className="banner relative py-5 md:py-24">
        <div className="container">
          <div className="flex flex-col items-center gap-6 text-center">
            <div className="w-full max-w-[830px] text-xl font-semibold md:text-5xl">
              <h1>
                {t.rich("products_title_section_hero", {
                  primary: (chunks) => (
                    <span className="text-primary">{chunks}</span>
                  ),
                  br: () => <br />,
                })}
              </h1>
            </div>
            <p className="mb-0 max-w-[700px] text-lg font-normal text-gray-600">
              {t("products_description_section_hero")}
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
