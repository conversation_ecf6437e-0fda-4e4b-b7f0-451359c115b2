import { formatPriceVND, classNames } from "@/utils";

export default function ProductRewardItem({
  id,
  name,
  price,
  media_url,
  description,
  activeProduct,
  setActiveProduct,
}: {
  id: number;
  name: string;
  media_url?: string;
  description?: string;
  price: number;
  activeProduct: {
    id_product: number;
    media_url: string;
    description: string;
  };
  setActiveProduct(activeProduct: {
    id_product: number;
    media_url: string;
    description: string;
  }): void;
}) {
  const handleChooseProduct = (id_product: number, media_url: string, description: string) => {
    setActiveProduct({ id_product, media_url, description });
  };

  return (
    <div
      data-cy="Product-Reward"
      onClick={() => handleChooseProduct(id, media_url as string, description as string)}
      className={classNames(
        activeProduct.id_product == id ? "active" : "",
        "item-reward flex cursor-pointer flex-col gap-1 rounded-[15px] border-t-[5px] border-gray-300 bg-white p-4 shadow-[0px_8px_16px_0px_rgba(95,47,38,0.15)]",
      )}
    >
      <h3 className="line-clamp-3 min-h-[84px] text-xl font-bold">{name}</h3>
      <span className="text-2xl min-h-[30px] products-price font-semibold text-primary">
        {!!price && formatPriceVND(price ?? 0)}
      </span>
    </div>
  );
}
