import dynamic from "next/dynamic";
import TheProductPageHeroSection from "@/components/Product/TheProductPageHeroSection";
import TheProductPageQuickNavigationSection from "@/components/Product/TheProductPageQuickNavigationSection";
import TheProductPageMiddleBannerSection from "@/components/Product/TheProductPageMiddleBannerSection";
import TheProductPageTopBannerSection from "@/components/Product/TheProductPageTopBannerSection";
import TheProductPageJobPostingSection from "@/components/Product/TheProductPageJobPostingSection";
import TheProductPageSearchCandidateSection from "@/components/Product/TheProductPageSearchCandidateSection";
import TheProductPageRewardSection from "@/components/Product/TheProductPageRewardSection";
import { getAllProducts } from "@/services/productsAPI";
import { ResponseApiProducts } from "@/types/products";
import { PRODUCT_CATEGORYS } from "@/contansts/payment";
const PopupLimitQuantity = dynamic(
  () => import("@/components/Product/PopupLimitQuantity"),
  { ssr: true },
);

export default async function TheProductsPage() {
  const rewardProducts = await getAllProducts(PRODUCT_CATEGORYS.REWARD, 30, 1, 510, 540) as ResponseApiProducts;

  return (
    <div className="bg-gray-100">
      <TheProductPageHeroSection />
      <TheProductPageQuickNavigationSection />
      <TheProductPageTopBannerSection />
      <TheProductPageSearchCandidateSection />
      <TheProductPageMiddleBannerSection />
      <TheProductPageJobPostingSection />
      {rewardProducts && (
        <TheProductPageRewardSection products={rewardProducts.data}/>
      )}
      <PopupLimitQuantity />
    </div>
  );
}
