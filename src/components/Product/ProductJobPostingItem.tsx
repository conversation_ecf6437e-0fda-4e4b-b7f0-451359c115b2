"use client";

import Image from "next/image";
import { useEffect, useState, useRef } from "react";
import { ProductProp } from "@/types/products";
import AddToCartButton from "@/components/Button/AddToCartButton";
import BuyNowButton from "@/components/Button/BuyNowButton";
import ViewMoreBenifitJobPosting from "@/components/Button/ViewMoreBenifitJobPosting";
import { classNames, formatPriceVND } from "@/utils";

export default function ProductJobPostingItem({
  id,
  benefits,
  name,
  description,
  price,
  anchoring_price,
  media_url,
}: ProductProp) {
  const limitHeight = 222;
  const listBenifits = useRef<HTMLUListElement>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isViewMore, setIsViewMore] = useState<boolean>(false);
  const [isHidden, setIsHidden] = useState<boolean>(false);

  useEffect(() => {
    if (Number(listBenifits?.current?.clientHeight) > limitHeight) {
      setIsHidden(true);
      setIsViewMore(true);
    }

    setIsLoading(true);
  }, []);

  return (
    <div
      data-cy="Product-Job-Posting"
      className="align-self-stretch items-product flex flex-col items-start gap-3 md:flex-row md:gap-[130px] "
    >
      <div className="w-full md:w-[49%]">
        <Image
          src={
            !!media_url
              ? media_url
              : "/v4/assets/images/online-payment/banner-default.png"
          }
          alt={name}
          width="620"
          height="450"
          className="md:rounded-[70px]"
          loading="lazy"
        />
      </div>
      <div className="flex w-full flex-1 flex-col items-start gap-1 md:w-[49%]">
        <h3 className="mb-0 text-lg font-semibold md:text-2xl">{name}</h3>
        <div className="align-self-stretch flex w-full flex-col items-start">
          <div className="products-price mb-1 flex items-center gap-2 font-bold text-primary md:text-3xl">
            {formatPriceVND(price)}
            {!!anchoring_price && (
              <span className="products-pridiscount-pricece font-normal text-gray-500 line-through md:text-2xl">
                {formatPriceVND(anchoring_price)}
              </span>
            )}
          </div>

          {!!description && (
            <div
              className="products-description italic"
              dangerouslySetInnerHTML={{ __html: description }}
            ></div>
          )}
          <div className="my-4 flex w-full max-w-[372px] gap-2">
            <BuyNowButton
              product_id={id}
              price={price}
              anchoring_price={anchoring_price}
              button_text="products_job_posting_payment"
            />
            <AddToCartButton
              product_id={id}
              price={price}
              anchoring_price={anchoring_price}
            />
          </div>
          {!!benefits && benefits.length > 0 ? (
            <>
              <ul
                ref={listBenifits}
                className={classNames(
                  !!isHidden ? "max-h-[222px] overflow-hidden" : "",
                  "align-self-stretch box-benefits flex flex-col gap-2",
                )}
              >
                {benefits.map((value, index) => {
                  return (
                    <li
                      className="pl-7 text-gray-500"
                      key={index}
                      dangerouslySetInnerHTML={{ __html: value }}
                    ></li>
                  );
                })}
              </ul>

              {!!isLoading && !!isViewMore && (
                <ViewMoreBenifitJobPosting
                  setIsHidden={setIsHidden}
                  isHidden={isHidden}
                />
              )}
            </>
          ) : (
            ""
          )}
        </div>
      </div>
    </div>
  );
}
