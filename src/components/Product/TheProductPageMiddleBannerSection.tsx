import ProductPageBanner from "@/components/Product/ProductPageBanner";

const getMiddleBanner = async () => {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/get_banner_promote_app?type=payment_product_middle_banner&device=desktop&width=1260&height=135`,
    { cache: "no-cache" },
  )
    .then((res) => res.json())
    .then((data) => data?.data?.pop())
    .catch((err) => console.log(err));

  return res;
};

export default async function TheProductPageMiddleBannerSection() {
  const data = await getMiddleBanner();
  if (data && data?.hidden) return;

  return (
    <ProductPageBanner link={data?.link} title={data?.title} image={data?.image} />
  );
}
