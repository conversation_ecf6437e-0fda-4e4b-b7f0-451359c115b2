import { getTranslations, getLocale } from "next-intl/server";
import Image from "next/image";
import { ProductProp, ResponseApiProducts } from "@/types/products";
import { PRODUCT_CATEGORYS } from "@/contansts/payment";
import ProductSearchCandidateItem from "@/components/Product/ProductSearchCandidateItem";
import { getAllProducts } from "@/services/productsAPI";
import {
  localeConvert,
  localeConvertBenefits,
} from "@/utils/localeConvert";

export default async function TheProductPageSearchCandidateSection() {
  const t = await getTranslations();
  const locale = await getLocale();

  const products = (await getAllProducts(
    PRODUCT_CATEGORYS.CREDIT,
    20,
    1,
  )) as ResponseApiProducts;

  if (!products) return <></>;

  return (
    <section id="section-candidate-cv" className="pb-5 md:pb-20">
      <div className="container">
        <div className="mb-4 flex flex-col gap-4 md:mb-12">
          <h3 className="inline-flex w-fit items-center gap-2 rounded-[32px] bg-white py-2 pl-2 pr-6">
            <span className="flex h-[42px] w-[42px] items-center justify-center rounded-full bg-gray-100">
              <Image
                src={"/v4/assets/images/online-payment/circle-stack.png"}
                alt="circle stack"
                width={34}
                height={34}
                loading="lazy"
              />
            </span>
            <p className="mb-0 text-3xl font-bold text-gray-600">
              {t("products_title_section_candidate_cv")}
            </p>
          </h3>
          <p className="mb-0 text-lg font-normal text-gray-500">
            {t("products_description_section_candidate_cv")}
          </p>
        </div>
        <div className="flex flex-wrap gap-4 md:gap-8">
          {products?.data?.map((value: ProductProp, index: number) => {
            return (
              <ProductSearchCandidateItem
                key={index}
                id={value.id}
                benefits={
                  localeConvertBenefits(value,"benefits",locale )
                }
                name={localeConvert(value, "name", locale)}
                price={value.price}
                description={localeConvert(value, "description", locale)}
                anchoring_price={value.anchoring_price}
              />
            );
          })}
        </div>
      </div>
    </section>
  );
}
