import Image from "next/image";
import Link from "../Link/Link";

interface ProductPageBannerProp {
  link: string;
  image: string;
  title: string;
}

export default function ProductPageBanner({
  link,
  image,
  title,
}: ProductPageBannerProp) {
  if (!image) return <></>;

  const srcImage = !!image
    ? image
    : "";

  return (
    <section data-cy="section-banner" className="section-banner pb-5 md:pb-20">
      <Link href={link ?? ""}>
        <Image
          className="m-auto max-w-full rounded-[10px]"
          src={srcImage}
          alt={title}
          width={1260}
          height={136}
          priority
        />
      </Link>
    </section>
  );
}
