"use client";

import { Modal } from "flowbite-react";
import { Button } from "@/components/Button";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useAppSelector, useAppDispatch } from "@/store";
import { setShowPopupLimitQuantity } from "@/store/slices/cartSlide";

export default function PopupLimitQuantity() {
  const t = useTranslations();
  const dispatch = useAppDispatch();
  const showPopup = useAppSelector(
    (state) => state.cart.showPopupLimitQuantity,
  );

  return (
      <Modal
        show={showPopup}
        onClose={() => dispatch(setShowPopupLimitQuantity(false))}
        size={"xl"}
      >
        <Modal.Header
          className={"relative z-10 border-none bg-transparent py-1"}
        ></Modal.Header>
        <Modal.Body className="-mt-5 pt-0">
          <div className="flex flex-col gap-4 text-center">
            <Image
              src={"/v4/assets/images/online-payment/banner-limit-quantity.png"}
              alt="banner"
              width={159}
              height={150}
              className="m-auto h-[150px] w-[159px]"
            />
            <h3 className="text-2xl font-semibold">
              {t("products_popup_limit_quantity_title")}
            </h3>
            <p className="">{t("products_popup_limit_quantity_description")}</p>
          </div>
        </Modal.Body>
        <Modal.Footer className={"justify-between"}>
          <div className="flex items-center gap-2">
            <Image
              src={
                "/v4/assets/images/online-payment/phone-popup-limit-quantity.png"
              }
              alt="phone"
              width={40}
              height={40}
              className="h-10 w-10"
              quality={100}
            />
            <div>
              <p className="font-semibold">
                {t("products_popup_limit_quantity_contact_now")}
              </p>
              <h3 className="text-lg font-bold">0888 1555 00</h3>
            </div>
          </div>
          <Button
            onClick={() => dispatch(setShowPopupLimitQuantity(false))}
            type={"submit"}
            size="lg"
            accent={"secondary"}
          >
            {t("products_popup_limit_quantity_confirm")}
          </Button>
        </Modal.Footer>
      </Modal>
  );
}
