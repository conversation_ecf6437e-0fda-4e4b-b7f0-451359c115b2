"use client";

import Image from "next/image";
import { useEffect, useLayoutEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { isDesktop } from "react-device-detect";
import { Swiper, SwiperSlide } from "swiper/react";
import { Grid, Pagination, Navigation } from "swiper/modules";
import NextIcon from "@/assets/images/icons/pagination-next.svg";
import PrevIcon from "@/assets/images/icons/pagination-prev.svg";
import ProductRewardItem from "@/components/Product/ProductRewardItem";
import { ProductRewards } from "@/types/products";
import { localeConvert } from "@/utils/localeConvert";
import { getCurrentLocale } from "@/utils/locale";
interface PropsProduct {
  id_product: number;
  media_url: string;
  description: string;
}

export default function TheProductPageRewardSection({
  products,
}: {
  products: ProductRewards[];
}) {
  const t = useTranslations();
  const [isClient, setIsClient] = useState<boolean>(false);
  const [activeProduct, setActiveProduct] = useState<PropsProduct>({
    id_product: 0,
    media_url: "",
    description: "",
  });
  const [chunckProduct, setChunckProduct] = useState<
    Array<Array<ProductRewards>>
  >([
    [
      {
        id: 0,
        name: "",
        description: "",
        media_url: "",
        price: 0,
        translables: {
          en: {
            name: "",
            description: "",
          },
          vi: {
            name: "",
            description: "",
          },
        },
      },
    ],
  ]);

  useLayoutEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    let numberChunk = 10;
    if (!isDesktop) numberChunk = 4;

    if (!!products && products.length === 0) return;

    setActiveProduct({
      id_product: products?.[0]?.id ?? 0,
      media_url: products?.[0]?.media_url ?? "",
      description: products?.[0]?.description ?? "",
    });

    let valueChunk = [];
    for (let i = 0; i < products.length; i += numberChunk) {
      valueChunk.push(products.slice(i, i + numberChunk));
    }

    if (valueChunk.length > 0) setChunckProduct(valueChunk);
  }, [products, isDesktop]);

  const locale = getCurrentLocale();
  return (
    <section id="section-topDev-rewards" className="bg-white py-12">
      <div className="container">
        <div className="mb-4 flex flex-col gap-4 md:mb-12">
          <h3 className="inline-flex w-fit items-center gap-2 rounded-[32px] bg-gray-100 py-2 pl-2 pr-6">
            <span className="flex h-[42px] w-[42px] items-center justify-center rounded-full bg-white">
              <Image
                width={41}
                height={42}
                src={"/v4/assets/images/online-payment/diamond-rotate.png"}
                alt="Diamond Rotate"
                loading="lazy"
              />
            </span>
            <p className="mb-0 text-3xl font-bold text-gray-600">
              {t("products_title_section_topde_rewards")}
            </p>
          </h3>
          <p className="mb-0 text-lg font-normal text-gray-500">
            {t("products_description_section_topde_rewards")}
          </p>
        </div>
        <div className="flex flex-col gap-6 md:flex-row">
          <div className="w-full overflow-x-hidden md:w-[53%]">
            {!!chunckProduct && chunckProduct.length > 0 && (
              <div>
                <Swiper
                  grabCursor={true}
                  slidesPerView={1}
                  spaceBetween={isDesktop ? 24 : 12}
                  loop={false}
                  className={"w-full"}
                  modules={isDesktop ? [Grid, Pagination, Navigation] : [Grid]}
                  pagination={
                    isDesktop
                      ? { enabled: true, clickable: true, el: ".pagination" }
                      : false
                  }
                  navigation={{
                    nextEl: ".nextEl",
                    prevEl: ".preEl",
                  }}
                  autoHeight={false}
                >
                  {chunckProduct.map((products: ProductRewards[], index) => {
                    return (
                      <SwiperSlide
                        key={index}
                        className="item-swiper-slide h-auto"
                      >
                        <div className="flex h-auto w-full flex-wrap gap-y-4 md:gap-6">
                          {products.map((product) => {
                            return (
                              <ProductRewardItem
                                activeProduct={activeProduct}
                                setActiveProduct={setActiveProduct}
                                key={product.id}
                                id={product.id}
                                name={localeConvert(
                                  product,
                                  "name",
                                  locale,
                                )}
                                price={product.price}
                                media_url={product.media_url}
                                description={localeConvert(
                                  product,
                                  "description",
                                  locale,
                                )}
                              />
                            );
                          })}
                        </div>
                      </SwiperSlide>
                    );
                  })}

                  <div
                    className={
                      "swiper-footer !mt-6 hidden items-center justify-center md:flex"
                    }
                  >
                    <div className="preEl mr-4">
                      <Image
                        src={PrevIcon}
                        alt="Previous"
                        width={7}
                        height={8}
                        loading="lazy"
                        className="h-[8px] w-[7px]"
                      />
                    </div>
                    <div className="pagination"></div>
                    <div className="nextEl">
                      <Image
                        src={NextIcon}
                        alt="Next"
                        width={7}
                        height={8}
                        loading="lazy"
                        className="h-[8px] w-[7px]"
                      />
                    </div>
                  </div>
                </Swiper>
              </div>
            )}
          </div>
          <div className="w-full md:w-[47%]">
            {!activeProduct.media_url ? (
              <div>
                <div className="h-full min-h-[320px] w-full animate-pulse bg-gray-200 md:min-h-[540px]"></div>
                <div className="mt-4 italic text-gray-400">
                  {activeProduct.description && (
                    <div
                      className="mt-4 italic text-gray-400"
                      dangerouslySetInnerHTML={{
                        __html: activeProduct.description,
                      }}
                    ></div>
                  )}
                </div>
              </div>
            ) : (
              <div>
                <Image
                  src={activeProduct.media_url}
                  className="min-h-[320px] md:min-h-[540px]"
                  alt="Banner"
                  loading="lazy"
                  width={510}
                  height={540}
                  quality={100}
                  unoptimized={true}
                />
                {activeProduct.description && (
                  <div
                    className="mt-4 italic text-gray-400"
                    dangerouslySetInnerHTML={{
                      __html: activeProduct.description,
                    }}
                  ></div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
