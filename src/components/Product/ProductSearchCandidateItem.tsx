"use client";

import React, { useEffect, useState, useRef } from "react";
import { ProductProp } from "@/types/products";
import { classNames, formatPriceVND } from "@/utils";
import AddToCartButton from "@/components/Button/AddToCartButton";
import ViewMoreBenifitJobPosting from "@/components/Button/ViewMoreBenifitJobPosting";
import BuyNowButton from "@/components/Button/BuyNowButton";

export default function ProductSearchCandidateItem({
  id,
  name,
  description,
  benefits,
  price,
  anchoring_price,
}: ProductProp) {
  const limitHeight = 198;
  const listBenifits = useRef<HTMLUListElement>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isViewMore, setIsViewMore] = useState<boolean>(false);
  const [isHidden, setIsHidden] = useState<boolean>(false);

  useEffect(() => {
    if (Number(listBenifits?.current?.clientHeight) > limitHeight) {
      setIsHidden(true);
      setIsViewMore(true);
    }

    setIsLoading(true);
  }, []);

  return (
    <div
      data-cy="Product-Search-Candidate"
      className="items-product-vertical items-product"
    >
      <div className="align-self-stretch flex flex-col items-start rounded-[16px] border-t-4 border-[#ffa800] bg-white pb-6 shadow-[0px_8px_16px_0px_rgba(95,47,38,0.15)]">
        <div className="align-self-stretch flex flex-col items-start justify-center gap-1 p-4 pt-6">
          <h3 className="mb-0 text-lg font-semibold md:text-2xl">{name}</h3>
          <div className="products-price flex flex-wrap items-center gap-2 font-bold text-primary md:text-3xl">
            {formatPriceVND(price)}
            {!!anchoring_price && (
              <span className="font-normal text-gray-500 line-through md:text-2xl">
                {formatPriceVND(anchoring_price)}
              </span>
            )}
          </div>

          {!!description && (
            <div className="group relative">
              <div className="tooltip-custom invisible absolute bottom-[110%] z-10 inline-block rounded-lg bg-gray-900 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300 group-hover:visible group-hover:opacity-100 dark:bg-gray-700">
                <div
                  dangerouslySetInnerHTML={{
                    __html: description,
                  }}
                ></div>
                <div className="absolute -bottom-1 left-[calc(50%_-_4px)] z-10 h-2 w-2 rotate-45 bg-gray-900 dark:bg-gray-700">
                  &nbsp;
                </div>
              </div>
              <div
                className="products-description line-clamp-2 min-h-[44px] pr-[5px] italic text-gray-400"
                dangerouslySetInnerHTML={{
                  __html: description,
                }}
              ></div>
            </div>
          )}
        </div>
        <div className="flex w-full gap-2 px-4">
          <BuyNowButton
            product_id={id}
            price={price}
            anchoring_price={anchoring_price}
          />
          <AddToCartButton
            product_id={id}
            price={price}
            anchoring_price={anchoring_price}
          />
        </div>
      </div>

      {!!benefits && benefits.length > 0 ? (
          <>
            <div className="align-self-stretch flex min-h-[270px] flex-col items-start gap-2 rounded-[16px] border-t border-dashed border-t-gray-200 bg-white p-4 pt-6 shadow-[0px_8px_16px_0px_rgba(95,47,38,0.15)]">
              <ul
                ref={listBenifits}
                className={classNames(
                  !!isHidden ? "max-h-[198px] overflow-hidden" : "",
                  "align-self-stretch box-benefits flex flex-col gap-2",
                )}
              >
                {benefits.map((value, index) => {
                  return (
                    <li className="pl-7 text-gray-500" key={index}
                    dangerouslySetInnerHTML={{
                      __html: value,
                    }}
                    >                
                    </li>
                  );
                })}
              </ul>
              {!!isLoading && !!isViewMore && (
                <div className="w-full text-center">
                  <ViewMoreBenifitJobPosting
                    setIsHidden={setIsHidden}
                    isHidden={isHidden}
                  />
                </div>
              )}
            </div>
          </>
        ) : (
          ""
        )}

    </div>
  );
}
