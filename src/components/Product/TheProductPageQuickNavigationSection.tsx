import { QuickNavigationButtonProp } from "@/types/products";
import QuickNavigationButton from "@/components/Product/QuickNavigationButton";
import { useTranslations } from "next-intl";

export default function TheProductPageQuickNavigationSection() {
  const t = useTranslations();

  const listNavigationButton: QuickNavigationButtonProp[] = [
    {
      image: "/v4/assets/images/online-payment/find-candidate-profiles.png",
      title: t("products_find_candidate_cv"),
      url: "section-candidate-cv",
    },
    {
      image: "/v4/assets/images/online-payment/published-recruitment.png",
      title: t("products_published_recruitment"),
      url: "section-recruitment",
    },
    {
      image: "/v4/assets/images/online-payment/topdev-gift.png",
      title: t("products_topdev_rewards_button"),
      url: "section-topDev-rewards",
    },
  ];

  return (
    <section id="quick-navigation-section">
      <div className="container py-5 md:py-20">
        <div className="flex flex-col justify-center gap-4 sm:flex-row md:gap-24">
          {listNavigationButton.map(
            (value: QuickNavigationButtonProp, index) => {
              return <QuickNavigationButton title={value.title} url={value.url} image={value.image} key={index} />;
            },
          )}
        </div>
      </div>
    </section>
  );
}
