"use client";

import { usePathname } from "next/navigation";
import React, { useEffect, useState } from "react";
import { fetchUserDataApi } from "@/services/userAPI";
import {
  createOrUpdateProfile,
  updateIsLoggedIn,
  setFollowedJobs,
  setAppliedJobs,
} from "@/store/slices/userSlice"; // Redux
import { useAppDispatch, useAppSelector } from "@/store"; // Redux
import { useAuthStore } from "@/stores/useAuthStore"; // Zustand
import useAddToCart from "@/hooks/useAddToCart";

export default function UserProvider({
  children,
}: {
  children: React.ReactNode;
}): React.JSX.Element {
  const dispatch = useAppDispatch();
  const pathname = usePathname();
  const isLoggedInRedux = useAppSelector((state) => state?.user?.isLoggedIn);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { checkFlowCartForUser } = useAddToCart();

  // Zustand setters
  const setUserData = useAuthStore((s) => s.setUserData);
  const clearUser = useAuthStore((s) => s.clearUser);

  useEffect(() => {
    const setUserProfileData = async () => {
      try {
        const fields = ["profile", "applied_jobs", "followed_jobs"].join(",");

        const { data }: any = await fetchUserDataApi({ fields });
        if (!!data && !data?.error) {
          // Redux
          dispatch(createOrUpdateProfile(data?.data?.profile));
          dispatch(updateIsLoggedIn(true));
          dispatch(setFollowedJobs(data?.data?.followed_jobs));
          dispatch(setAppliedJobs(data?.data?.applied_jobs));

          // Zustand
          setUserData({
            profile: data?.data?.profile,
            followedJobs: data?.data?.followed_jobs,
            appliedJobs: data?.data?.applied_jobs,
          });
        }
      } catch (error) {
        // Redux
        dispatch(createOrUpdateProfile({}));
        dispatch(updateIsLoggedIn(false));
        dispatch(setFollowedJobs([]));
        dispatch(setAppliedJobs([]));

        // Zustand
        clearUser();
      }

      setIsLoading(false);
    };

    setUserProfileData();
  }, [dispatch, setUserData, clearUser]);

  useEffect(() => {
    if (isLoading) return;
    const urlRedirectLogin =
      process.env.NEXT_PUBLIC_OAUTH2_URL_ACCOUNT +
      "?redirect_uri=" +
      encodeURIComponent(window.location.href);

    // Cart flow
    if (pathname.includes("/carts") || pathname.includes("/result")) {
      if (!isLoggedInRedux) {
        window.location.href = urlRedirectLogin;
      } else {
        const confirm = checkFlowCartForUser();
        if (!confirm)
          setTimeout(() => (window.location.href = "/products"), 3000);
      }
    }

    // User routes
    if (pathname.includes("/users")) {
      if (!isLoggedInRedux) {
        window.location.href = urlRedirectLogin;
      }
    }
  }, [isLoggedInRedux, isLoading, pathname, checkFlowCartForUser]);

  return <>{children}</>;
}
