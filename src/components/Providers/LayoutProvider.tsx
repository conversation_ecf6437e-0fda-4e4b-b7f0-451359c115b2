"use client";

import React, { useEffect, useLayoutEffect, useState } from "react";
import dynamic from "next/dynamic";
import { useAppDispatch } from "@/store";
import { usePathname } from "next/navigation";
import { isUrlParamExists } from "@/utils";
import { Flowbite } from "flowbite-react";
import { TOPDEV_FLOWBITE_THEME } from "@/contansts/themes";
import { useSelectedLayoutSegment } from "next/navigation";
import { setEventsPage } from "@/store/slices/settingSlice";
import { useAppSelector } from "@/store";
import { PROFILE_OPEN_TO_WORK } from "@/contansts/userProfiles";


const PushNotificationOTWPopup = dynamic(
  () => import("@/components/Modal/PushNotificationOTWPopup"),
);

export default function LayoutProvider({
  children,
}: {
  children: React.ReactNode;
}): React.JSX.Element {
  const user = useAppSelector((state) => state?.user?.user);
  const [isShowPushPopup, setIsShowPushPopup] = useState<boolean>(false);
  const segment = useSelectedLayoutSegment();
  const dispatch = useAppDispatch();
  const path = usePathname();
  const getLocalStorage =
    typeof localStorage != "undefined" ? localStorage : undefined;

  // TODO: close window after logged
  if (typeof window !== "undefined") {
    if (isUrlParamExists("openPopupCreateNewTab")) {
      window.open(
        process.env.NEXT_PUBLIC_CV_BUILDER_URL +
        "?openPopupCreate=1&referer=" +
        path,
        "__blank",
      );
    }
    // Check open create cv
    else if (isUrlParamExists("openPopupCreate")) {
      const url = new URL(window.opener.location.href);
      url.searchParams.append("openPopupCreateNewTab", "1");
      window.opener.location.href = url.toString();

      // Close window
      window.close();
    }
  }

  useEffect(() => {
    if (segment) {
      dispatch(setEventsPage(segment as string));
    }

  }, [segment, dispatch]);

  const timeShowPopup = getLocalStorage?.getItem("timeShowPopup");

  useLayoutEffect(() => {
    if (Object.values(user).length == 0) return;

    const isNotOTW =
      user?.status_works?.at(0) === PROFILE_OPEN_TO_WORK.NOT_OPEN_TO_WORK ||
      user?.status_works?.length == 0;

    const checkTime = !!timeShowPopup
      ? (Date.now() - parseInt(timeShowPopup)) / (1000 * 60 * 60)
      : timeShowPopup;

    if (path == "/users/job-management") {
      setIsShowPushPopup(false);
      return;
    }

    if (
      path.includes("/users") &&
      isNotOTW &&
      (Number(checkTime) >= 24 || !timeShowPopup)
    ) {
      setIsShowPushPopup(true);
      return;
    }
  }, [user, path, timeShowPopup]);

  const onClose = () => {
    localStorage?.setItem("timeShowPopup", Date.now().toString());
    setIsShowPushPopup(false);
  };

  return (
    <Flowbite theme={{ theme: TOPDEV_FLOWBITE_THEME }}>
      <>
        <PushNotificationOTWPopup
          openModal={isShowPushPopup}
          onClose={onClose}
        />
        {children}
      </>
    </Flowbite>
  );
}
