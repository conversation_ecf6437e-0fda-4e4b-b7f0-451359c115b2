"use client";
import schemaFormRecruit from "@/schemas/SchemaFormRecruit";
import { postContactRecruit } from "@/services/recruitAPI";
import { RecruitFormType } from "@/types/recruit";
import { getFormData } from "@/utils";
import { useFormik } from "formik";
import { useTranslations } from "next-intl";
import React, { useRef } from "react";
import Swal from "sweetalert2";
import ReCA<PERSON>TC<PERSON> from "react-google-recaptcha";

const ContactFormRecruit = () => {
  const t = useTranslations();
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
  const enable = process.env.NEXT_PUBLIC_RECAPTCHA_ENABLE;
  const {
    setFieldValue,
    resetForm,
    setSubmitting,
    isSubmitting,
    handleSubmit,
    values,
    errors: errorForm,
    touched: touchedForm,
  } = useFormik<RecruitFormType>({
    initialValues: {
      company: "",
      name: "",
      position: "",
      email: "",
      phone: "",
      where_referral: "",
      description: "",
      policy_required: false,
      notification_required: false,
    },
    validationSchema: schemaFormRecruit(),
    onSubmit: async (values: RecruitFormType) => {
      handleSubmitFormContact(values);
    },
  });

  const handleSubmitFormContact = async (values: RecruitFormType) => {
    const token = await recaptchaRef?.current?.executeAsync();
    const data: any = {
      token: token || "",
      company: values.company,
      name: values.name,
      position: values.position,
      email: values.email,
      phone: values.phone,
      where_referral: values.where_referral,
      description: values.description,
      policy_required: values.policy_required,
      notification_required: values.notification_required,
    };
    const formData = getFormData(data);
    try {
      const { data } = await postContactRecruit(formData);
      if (data.error === false) {
        setSubmitting(false);
        Swal.fire({
          title: "Success!",
          text: "Bạn đã đăng ký thành công!",
          icon: "success",
          confirmButtonColor: "#DD3F24",
        });
        resetForm({
          values: {
            company: "",
            name: "",
            position: "",
            email: "",
            phone: "",
            where_referral: "0",
            description: "",
            policy_required: false,
            notification_required: false,
          },
        });
      } else {
        Swal.fire({
          title: "Error!",
          text: "Đăng ký không thành công!",
          icon: "error",
          cancelButtonColor: "#DD3F24",
        });
      }
    } catch (err) {
      Swal.fire({
        title: "Error!",
        text: "Đăng ký không thành công!",
        icon: "error",
        cancelButtonColor: "#DD3F24",
      });
    }
  };

  return (
    <>
      <div className="max-w-2xl rounded-lg bg-orange-200 bg-gradient-to-r from-orange-200 to-orange-100 p-8 opacity-100">
        <div
          className="flex h-full w-full flex-col rounded-tl-lg rounded-tr-lg bg-gray-100 p-4 align-middle"
          style={{
            boxShadow: "0px 3.645px 3.645px 0px rgba(134, 134, 134, 0.25)",
          }}
        >
          <h4 className="text-2xl font-semibold text-black">
            {t("recruit_title_contact_form")}
          </h4>
          <span className="text-xl font-normal text-gray-400">
            {t("recruit_description_contact_form")}
          </span>
        </div>
        <div
          className="rounded-bl-lg rounded-br-lg bg-white p-4 align-middle shadow-lg"
          style={{
            boxShadow: "0px 3.645px 3.645px 0px rgba(134, 134, 134, 0.25)",
          }}
        >
          <form
            method="POST"
            id="form-contact-recruit"
            onSubmit={handleSubmit}
            autoComplete="off"
          >
            <div className="mt-6">
              <label
                htmlFor="company"
                className="mb-1 inline-block text-base font-bold text-gray-500"
              >
                {t("recruit_contact_form_company_name")}
              </label>
              <input
                type="text"
                name="company"
                id="company"
                className="w-full rounded border border-solid border-gray-300"
                placeholder={t("recruit_contact_form_company_name_placeholder")}
                required
                value={values.company}
                onChange={(e) => setFieldValue("company", e.target.value)}
              />
              {errorForm.company && touchedForm.company && (
                <p className="text-sm italic text-red-600">
                  (*) {errorForm.company ?? ""}
                </p>
              )}
            </div>
            <div className="mt-4 grid-cols-12 gap-6 lg:grid">
              <div className="col-span-6">
                <label
                  htmlFor="name"
                  className="mb-1 inline-block text-base font-bold text-gray-500"
                >
                  {t("recruit_contact_form_your_name")}
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  className="w-full rounded border border-solid border-gray-300"
                  placeholder={t("recruit_contact_form_your_name_placeholder")}
                  required
                  value={values.name}
                  onChange={(e) => setFieldValue("name", e.target.value)}
                />
                {errorForm.name && touchedForm.name && (
                  <p className="text-sm italic text-red-600">
                    (*) {errorForm.name ?? ""}
                  </p>
                )}
              </div>
              <div className="col-span-6">
                <label
                  htmlFor="position"
                  className="mb-1 inline-block text-base font-bold text-gray-500"
                >
                  {t("recruit_contact_form_your_position")}
                </label>
                <input
                  type="text"
                  name="position"
                  id="position"
                  className="w-full rounded border border-solid border-gray-300"
                  placeholder={t(
                    "recruit_contact_form_your_position_placeholder",
                  )}
                  required
                  value={values.position}
                  onChange={(e) => setFieldValue("position", e.target.value)}
                />
                {errorForm.position && touchedForm.position && (
                  <p className="text-sm italic text-red-600">
                    (*) {errorForm.position ?? ""}
                  </p>
                )}
              </div>
            </div>
            <div className="mt-4 grid-cols-12 gap-6 lg:grid">
              <div className="col-span-6">
                <label
                  htmlFor="email"
                  className="mb-1 inline-block text-base font-bold text-gray-500"
                >
                  {t.rich("recruit_contact_form_your_email", {
                    small: (chunk) => (
                      <span className={"text-xs font-normal text-gray-500"}>
                        {chunk}
                      </span>
                    ),
                  })}
                </label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  className="w-full rounded border border-solid border-gray-300"
                  placeholder={t("recruit_contact_form_your_email_placeholder")}
                  required
                  value={values.email}
                  onChange={(e) => setFieldValue("email", e.target.value)}
                />
                {errorForm.email && touchedForm.email && (
                  <p className="text-sm italic text-red-600">
                    (*) {errorForm.email ?? ""}
                  </p>
                )}
              </div>
              <div className="col-span-6">
                <label
                  htmlFor="phone"
                  className="mb-1 inline-block text-base font-bold text-gray-500"
                >
                  {t("recruit_contact_form_your_phone")}
                </label>
                <input
                  type="tel"
                  name="phone"
                  id="phone"
                  className="w-full rounded border border-solid border-gray-300"
                  placeholder={t("recruit_contact_form_your_phone_placeholder")}
                  required
                  value={values.phone}
                  onChange={(e) => setFieldValue("phone", e.target.value)}
                />
                {errorForm.phone && touchedForm.phone && (
                  <p className="text-sm italic text-red-600">
                    (*) {errorForm.phone ?? ""}
                  </p>
                )}
              </div>
            </div>
            <div className="mt-5">
              <label
                htmlFor="where_referral"
                className="mb-1 inline-block text-base font-bold text-gray-500"
              >
                {t("recruit_contact_form_your_question")}
              </label>
              <select
                name="where_referral"
                required
                onChange={(event) =>
                  setFieldValue("where_referral", event.target.value)
                }
                className="w-full rounded border border-solid border-gray-300"
              >
                <option value="">
                  {t("recruit_contact_form_your_question_placeholder")}
                </option>
                <option value="1">Facebook/Fanpage TopDev</option>
                <option value="2">LinkedIn</option>
                <option value="3">Search</option>
                <option value="4">Email</option>
                <option value="5">Referral</option>
                <option value="6">Newspaper</option>
                <option value="7">InnoEx/ BSSC</option>
                <option value="8">Other</option>
              </select>
              {errorForm.where_referral && touchedForm.where_referral && (
                <p className="text-sm italic text-red-600">
                  (*) {errorForm.where_referral ?? ""}
                </p>
              )}
            </div>
            <div className="mt-5">
              <label
                htmlFor="description"
                className="mb-1 inline-block text-base font-bold text-gray-500"
              >
                {t("recruit_contact_form_your_question_description")}
              </label>
              <textarea
                name="description"
                id="description"
                cols={1}
                rows={1}
                value={values.description}
                onChange={(event) =>
                  setFieldValue("description", event.target.value)
                }
                className="w-full rounded border border-solid border-gray-300"
                placeholder={t(
                  "recruit_contact_form_your_question_description_placeholder",
                )}
                required
              ></textarea>
              {errorForm.description && touchedForm.description && (
                <p className="text-sm italic text-red-600">
                  (*) {errorForm.description ?? ""}
                </p>
              )}
            </div>
            <div className="mt-5">
              <div className="flex flex-row">
                <input
                  type="checkbox"
                  id="term-and-condition-1"
                  name="term-and-condition-1"
                  checked={values.policy_required}
                  onChange={(event) => {
                    setFieldValue("policy_required", event.target.checked);
                  }}
                />
                <label
                  htmlFor="term-and-condition-1"
                  className="ml-3 text-base font-normal text-gray-500"
                >
                  <span>{t("recruit_contact_form_checkbox_term")}</span>
                  <br />
                  <span>
                    {t.rich("recruit_contact_form_term", {
                      a: (chunk) => (
                        <a
                          className="text-sm font-semibold italic hover:text-primary"
                          href={
                            process.env.NEXT_PUBLIC_BASE_URL +
                            "/term-of-services?src=topdev.vn&medium=submenu"
                          }
                        >
                          {chunk}
                        </a>
                      ),
                    })}
                  </span>
                  <span>
                    {t.rich("recruit_contact_form_policy", {
                      a: (chunk) => (
                        <a
                          className="text-sm font-semibold italic hover:text-primary"
                          href={
                            process.env.NEXT_PUBLIC_BASE_URL +
                            "/privacy-policy?src=topdev.vn&medium=submenu"
                          }
                        >
                          {chunk}
                        </a>
                      ),
                    })}
                  </span>
                </label>
              </div>
              <div className="mt-4 flex flex-row">
                <input
                  type="checkbox"
                  id="term-and-condition-2"
                  name="term-and-condition-2"
                  checked={values.notification_required}
                  onChange={(event) => {
                    setFieldValue(
                      "notification_required",
                      event.target.checked,
                    );
                  }}
                />
                <label
                  htmlFor="term-and-condition-2"
                  className="ml-3 text-base font-normal text-gray-500"
                >
                  {t("recruit_contact_form_checkbox_notification")}
                </label>
              </div>
            </div>
            {errorForm.policy_required ||
            (errorForm.notification_required && touchedForm.policy_required) ||
            touchedForm.notification_required ? (
              <p className="text-sm italic text-red-600">
                {errorForm.policy_required || errorForm.notification_required}
              </p>
            ) : (
              ""
            )}

            <div className="mt-5">
              <button
                id="btn-submit"
                form="form-contact-recruit"
                type="submit"
                className={
                  "w-full border border-solid border-primary bg-primary py-3 text-white transition-all hover:bg-transparent hover:text-primary hover:shadow-md" +
                  (isSubmitting
                    ? " bg-gray-600 hover:border-gray-600 hover:bg-gray-600 hover:text-white"
                    : "")
                }
              >
                {isSubmitting
                  ? `${t("recruit_contact_form_waitting_submit")}`
                  : `${t("recruit_contact_form_submit")}`}
              </button>
            </div>
            {siteKey && enable ? (
              <ReCAPTCHA
                ref={recaptchaRef}
                size="invisible"
                sitekey={siteKey}
              />
            ) : (
              <></>
            )}
          </form>
        </div>
      </div>
    </>
  );
};

export default ContactFormRecruit;
