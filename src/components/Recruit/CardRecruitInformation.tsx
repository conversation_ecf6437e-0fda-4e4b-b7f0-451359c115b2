import Image from "next/image";
import React, { FC } from "react";

interface PropsCardRecruitInfor {
  image: string;
  title: string;
  description: string;
}
const CardRecruitInformation: FC<PropsCardRecruitInfor> = ({
  image,
  title,
  description,
}) => {
  return (
    <>
      <div className="mt-2">
        <div className="flex items-stretch justify-between text-center">
          <Image
            src={image}
            alt={title}
            width={165}
            height={100}
            className="mx-auto"
          />
        </div>
        <div className="mt-4 flex flex-col px-2 text-left">
          <span className="h-auto w-auto text-base font-bold text-gray-600 lg:h-[44px]">
            {title}
          </span>
          <span className="text-sm font-normal text-gray-400">
            {description}
          </span>
        </div>
      </div>
    </>
  );
};

export default CardRecruitInformation;
