"use client";
import { companies } from "@/utils/recruit";
import { Swiper, SwiperSlide } from "swiper/react";
import { isDesktop } from "react-device-detect";
import Image from "next/image";
import PrevIcon from "@/assets/images/icons/pagination-prev.svg";
import NextIcon from "@/assets/images/icons/pagination-next.svg";
import { Link } from "@/navigation";
import { Autoplay, Grid, Navigation, Pagination } from "swiper/modules";

const CompanyLogoSlider = () => {
  return (
    <>
      {companies ? (
        <div className="h-auto w-auto">
          <Swiper
            grabCursor={true}
            slidesPerView={isDesktop ? 6 : 3}
            slidesPerGroup={isDesktop ? 6 : 3}
            grid={{
              rows: isDesktop ? 3 : 2,
              fill: "row",
            }}
            className="flex h-auto w-auto items-center justify-center"
            modules={
              isDesktop ? [Grid, Pagination, Navigation, Autoplay] : [Grid]
            }
            spaceBetween={isDesktop ? 16 : 12}
            pagination={
              isDesktop ? { clickable: true, el: ".pagination" } : false
            }
            navigation={
              isDesktop
                ? {
                    nextEl: ".nextEl",
                    prevEl: ".preEl",
                  }
                : false
            }
            autoplay={{
              delay: 2000,
            }}
          >
            {companies &&
              companies.map((company, index) => {
                return (
                  <SwiperSlide key={index}>
                    <Link
                      href={{
                        pathname: "/companies/[slug]",
                        params: { slug: company?.slug + "" },
                      }}
                      target="_blank"
                    >
                      <div className="flex h-[70px] w-[100px] items-center justify-center p-4">
                        <Image
                          src={company?.logo ?? null}
                          width={100}
                          height={70}
                          alt={company?.name}
                          title={company.name}
                          loading="lazy"
                        />
                      </div>
                    </Link>
                  </SwiperSlide>
                );
              })}

            {isDesktop && (
              <div className="swiper-footer items-center justify-center lg:flex">
                <div className="preEl">
                  <Image src={PrevIcon} alt={"Previous"} width="7" height="8" />
                </div>
                <div className="pagination"></div>
                <div className="nextEl">
                  <Image src={NextIcon} alt={"Next"} width="7" height="8" />
                </div>
              </div>
            )}
          </Swiper>
        </div>
      ) : null}
    </>
  );
};

export default CompanyLogoSlider;
