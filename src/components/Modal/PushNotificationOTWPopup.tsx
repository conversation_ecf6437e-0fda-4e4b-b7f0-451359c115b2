"use client";
import { IoClose } from "react-icons/io5";
import React, { FC } from "react";
import { Modal } from "flowbite-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { Button } from "@/components/Button";
import NotificationOTW from "@/assets/images/job-management/notification-OTW.png";

interface Props {
  openModal: boolean;
  onClose(): void;
}

const PushNotificationOTWPopup: FC<Props> = ({
  openModal = false,
  onClose,
}) => {
  const t = useTranslations();

  const navaigationJobManagement = () => {
    window.location.href = "/users/job-management";
  };

  return (
    <Modal
      show={openModal}
      onClose={() => (onClose ? onClose() : "")}
      size={"xl"}
    >
      <Modal.Body className="relative overflow-hidden rounded-xl px-0 pb-5 pt-0 md:pb-9">
        <span
          onClick={onClose}
          className="absolute right-3 top-3 z-10 cursor-pointer rounded hover:bg-white"
        >
          <IoClose className="h-6 w-6" />
        </span>
        <div className="overflow-hidden">
          <div className="flex justify-center overflow-hidden bg-gradient-push-notification-OTW pb-5 pt-10 md:-mx-[70px] md:w-[684px] md:rounded-bl-[10000px] md:rounded-br-[10000px]">
            <div className="flex max-w-[458px] items-center gap-4 px-2">
              <Image src={NotificationOTW} alt="notification-otw" />
              <h2 className="font-bold text-gray-600 md:text-lg">
                {t("user_profile_title_push_notification_otw_popup")}
              </h2>
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4 px-5 pt-5 md:px-12">
          <div className="flex items-start gap-2 text-gray-500 md:text-base">
            <Image
              src={"/v4/assets/images/apply_job/icon-double-checked.png"}
              alt="Icon Explore"
              width={22}
              height={13}
              className="mt-1"
            />
            {t("user_profile_content_push_notification_otw_popup_1")}
          </div>
          <div className="flex items-start gap-2 text-gray-500 md:text-base">
            <Image
              src={"/v4/assets/images/apply_job/icon-double-checked.png"}
              alt="Icon Explore"
              width={22}
              height={13}
              className="mt-1"
            />
            {t("user_profile_content_push_notification_otw_popup_2")}
          </div>
        </div>
        <div className="mt-5 px-5 md:mt-6 md:px-12">
          <Button
            onClick={navaigationJobManagement}
            accent={"primary"}
            type={"button"}
            isBlock
          >
            {t("user_profile_button_push_notification_otw_popup")}
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default PushNotificationOTWPopup;
