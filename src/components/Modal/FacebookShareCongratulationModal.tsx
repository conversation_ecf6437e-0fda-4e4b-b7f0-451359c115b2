"use client";
import { Modal } from "flowbite-react";
import { IoClose } from "react-icons/io5";

export default function FacebookShareCongratulationModal({
  show,
  handleCloseModal,
}: {
  show: boolean;
  handleCloseModal: () => void;
}) {
  return (
    <Modal show={show} onClose={() => handleCloseModal()}>
      <Modal.Body className="flex items-center justify-center p-0 bg-none">
        <div className="relative w-full overflow-hidden rounded">
          <button
            className="absolute right-3 top-3 z-10 cursor-pointer rounded hover:bg-white"
            onClick={() => handleCloseModal()}
          >
            <IoClose className="h-6 w-6" />
          </button>

          <img
            src="https://c.topdevvn.com/uploads/2025/08/08/popup_chucmung.png"
            className="w-full"
          />
        </div>
      </Modal.Body>
    </Modal>
  );
}
