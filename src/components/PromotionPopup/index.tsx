import Image from "next/image";

interface PromotionPopupProps {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  imageAlt?: string;
}

const PromotionPopup = ({
  isOpen,
  onClose,
  imageUrl,
  imageAlt = "Promotion Image",
}: PromotionPopupProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      <div id="Apply_CV_Success" className="relative max-w-xl shadow-lg">
        <div onClick={onClose} className="cursor-pointer">
          <Image
            src={imageUrl}
            alt={imageAlt}
            width={500}
            height={300}
            className="rounded-lg"
          />
        </div>
      </div>
    </div>
  );
};

export default PromotionPopup;
