"use client";

import { getMoreJobs } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import React, { FC, useCallback, useEffect, useRef, useState } from "react";
import { useIntersectionObserver } from "usehooks-ts";
import CardInformativeList from "../Card/Job/CardInformativeList";
import { TaxonomiesType } from "@/types/taxonomy";
import { getCurrentLocaleForParams } from "@/utils/locale";

interface Props {
  pageSize: number;
  totalPage: number;
  currentPage: number;
  taxonomies: TaxonomiesType;
}

const LoadMoreJob: FC<Props> = ({
  pageSize,
  totalPage,
  currentPage,
  taxonomies,
}) => {
  const [jobs, setJobs] = useState<JobType[]>([]);
  const jobSectionRef = useRef<HTMLDivElement | null>(null);
  const entry = useIntersectionObserver(jobSectionRef, {});
  const isVisible = !!entry?.isIntersecting;
  const [page, setPage] = useState(currentPage + 1);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isVisible || isLoading) {
      return;
    }
    if (page <= totalPage) {
      handleLoadMoreJob();
    }
  }, [isVisible, isLoading]);

  const handleLoadMoreJob = useCallback(async () => {
    try {
      const response = await getMoreJobs(page, getCurrentLocaleForParams());
      const data = await response.data;
      setPage((prev) => prev + 1);
      setJobs((prev) => [...prev, ...data]);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  }, [page]);

  return (
    <div>
      <ul>
        {jobs.map((jobItem, index) => {
          return (
            <li key={jobItem.id} className="mb-4 last:mb-0">
              <CardInformativeList job={jobItem} />
            </li>
          );
        })}
      </ul>
      {isLoading && (
        <div className="flex items-center justify-center py-5">
          <div className="inline-block h-10 w-10 animate-spin rounded-full border-4 border-solid border-black border-t-transparent"></div>
        </div>
      )}
      <div ref={jobSectionRef}></div>
    </div>
  );
};

export default LoadMoreJob;
