"use client";
import React, { FC, useState } from "react";
import Toggle from "../Toggle";
import { addSimilarJobs, getSimilarJobs } from "@/services/jobAPI";
import { useAppSelector } from "@/store";
import { classNames, openLoginPopup } from "@/utils";
import ToastNotification from "../Swal/ToastNotification";
import { useTranslations } from "next-intl";
import { SimilarJobType } from "@/types/job";

interface Props {
  skillId: string;
  keyword: string;
}

const JobAlertForSearch: FC<Props> = ({ skillId, keyword }) => {
  const [isActive, setIsActive] = useState(false);
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);
  const t = useTranslations();

  const handleGetSimilarJobs = async () => {
    try {
      const skills: any[] = skillId.split(",").filter((item) => item);
      let similarDataSubmit: { id: number; skill_id: number }[] = [];
      let mergedArr = [];
      const similarJobsData: SimilarJobType[] = await getSimilarJobs().then(
        (response) => {
          return response.data.data.announced_jobs;
        },
      );
      similarDataSubmit = similarJobsData.map((item) => ({
        id: item.id,
        skill_id: item.skill.id,
      }));
      const skillsArr = skills.map((item) => {
        const isExist = similarDataSubmit.find((it) => it.id === item);
        if (isExist) {
          return isExist;
        }
        return {
          skill_id: item,
        };
      });
      if (similarJobsData && similarJobsData.length > 0) {
        const ids = new Set(similarDataSubmit.map((item) => item.skill_id));
        mergedArr = [
          ...similarDataSubmit,
          ...skillsArr.filter((item) => !ids.has(item.skill_id)),
        ].slice(-5);
      } else {
        mergedArr = skillsArr;
      }
      addSimilarJobs(mergedArr)
        .then((response) => {
          ToastNotification({
            icon: "success",
            title: t("search_page_success"),
            description: t("search_page_your_selected_skill_has_recored", {
              keyword,
            }),
          });
          setIsActive(true);
        })
        .catch(() => {
          throw Error("Registry failed!");
        });
    } catch (error) {
      console.log("error: ", error);
      ToastNotification({
        icon: "error",
        title: "Không thành công",
        description: "Đăng ký nhận thông báo chưa được ghi nhận!",
      });
    }
  };
  return (
    <div
      className={classNames(
        "flex items-center justify-between px-5 py-4 transition-all",
        isActive ? "bg-white" : "bg-gray-200",
      )}
    >
      <p className="text-sm font-semibold lg:text-lg">
        {t("search_page_get_job_alert_for_search")}
      </p>
      {isLoggedIn ? (
        <Toggle
          checked={isActive}
          onChange={(checked) => {
            handleGetSimilarJobs();
          }}
          size="small"
        />
      ) : (
        <Toggle
          checked={isActive}
          onChange={(checked) => {
            openLoginPopup();
          }}
          size="small"
        />
      )}
    </div>
  );
};

export default JobAlertForSearch;
