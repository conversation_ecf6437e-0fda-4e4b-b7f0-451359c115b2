"use client";

import { getMoreJobs } from "@/services/jobAPI";
import { JobType } from "@/types/job";
import React, { FC, useCallback, useEffect, useRef, useState } from "react";
import { useIntersectionObserver } from "usehooks-ts";
import { TaxonomiesType } from "@/types/taxonomy";
import CardInformativeListMobile from "@/components/Card/Job/mobile/CardInformativeList.mobile";
import Loading from "@/components/Common/Loading";
import { getCurrentLocaleForParams } from "@/utils/locale";

interface Props {
  pageSize: number;
  totalPage: number;
  currentPage: number;
  taxonomies: TaxonomiesType;
}

const LoadMoreJobMobile: FC<Props> = ({ pageSize, totalPage, currentPage }) => {
  const [jobs, setJobs] = useState<JobType[]>([]);
  const jobSectionRef = useRef<HTMLDivElement | null>(null);
  const entry = useIntersectionObserver(jobSectionRef, {});
  const isVisible = !!entry?.isIntersecting;
  const [page, setPage] = useState(currentPage + 1);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isVisible || isLoading) {
      return;
    }
    if (page <= totalPage) {
      handleLoadMoreJob();
    }
  }, [isVisible, isLoading]);

  const handleLoadMoreJob = useCallback(async () => {
    try {
      const response = await getMoreJobs(page, getCurrentLocaleForParams());
      const data = await response.data;
      setPage((prev) => prev + 1);
      setJobs((prev) => [...prev, ...data]);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  }, [page]);

  return (
    <div>
      <ul>
        {jobs.map((jobItem, index) => {
          return (
            <li key={index} className="mb-4 last:mb-0">
              <CardInformativeListMobile job={jobItem} />
            </li>
          );
        })}
      </ul>
      {isLoading && (
        <div className="py-5">
          <Loading />
        </div>
      )}
      <div ref={jobSectionRef}></div>
    </div>
  );
};

export default LoadMoreJobMobile;
