import CardInformativeListMobile from "@/components/Card/Job/mobile/CardInformativeList.mobile";
import LoadMoreJobBySkill from "@/components/SearchPage/LoadMoreData/LoadMoreJobBySkill";
import ChipTag from "@/components/Tag/ChipTag";
import { getJobBySkill } from "@/services/jobAPI";
import { getLocaleForParams } from "@/utils/localeServer";
import { useTranslations } from "next-intl";
import { FC } from "react";

const SectionTitle: FC<{ size: number }> = ({ size }) => {
  const t = useTranslations();
  return <span>{size > 1 ? t("search_page_jobs") : t("search_page_job")}</span>;
};

interface JobBySkillProps {
  title: string;
  exceptSkills: string;
  skillId: number;
}

const JobBySkillSection: FC<JobBySkillProps> = async (props) => {
  const { title, skillId, exceptSkills } = props;
  const { data, meta } = await getJobBySkill({
    skillId,
    exceptSkills,
    currentPage: 1,
    pageSize: 5,
    locale: getLocaleForParams(),
  });

  return (
    <section>
      <h2 className="text-xl font-bold text-black">
        <span className="text-primary">{meta.total}</span>{" "}
        <SectionTitle size={meta.total} />
        <span className="ml-2 inline-flex gap-2 font-normal">
          <ChipTag title={title} accent="line" size="md" />
        </span>
      </h2>
      <ul className="mt-4">
        {data.map((jobItem, index) => {
          return (
            <li key={index} className="mb-4 last:mb-0">
              <CardInformativeListMobile job={jobItem} />
            </li>
          );
        })}
      </ul>
      {meta.current_page < meta.last_page && (
        <LoadMoreJobBySkill
          currentPage={meta.current_page}
          pageSize={meta.last_page}
          exceptSkills={exceptSkills}
          skillId={skillId}
          locale={getLocaleForParams()}
        />
      )}
    </section>
  );
};

export default JobBySkillSection;
