import React, { <PERSON> } from "react";
import Card<PERSON>obList from "../Card/Job/CardJobList";
import { JobType } from "@/types/job";
import { TaxonomiesType } from "@/types/taxonomy";

interface HighlightJobType {
  data: JobType[];
  taxonomies: TaxonomiesType;
}

const HighlightJobSidebar: FC<HighlightJobType> = ({ data, taxonomies }) => {
  return (
    <div className="rounded border border-solid border-primary-200">
      <div className="border-b border-solid border-primary-200 bg-primary-100 px-4 py-2">
        <h3 className="text-lg font-semibold">Highlight jobs</h3>
      </div>
      <ul>
        {data.map((jobItem, index) => {
          return (
            <li
              key={index}
              className="border-t border-solid border-gray-200 first:border-t-0"
            >
              <CardJobList job={jobItem} mediumPage={"jobhighlight"} />
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default HighlightJobSidebar;
