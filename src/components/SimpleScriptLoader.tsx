"use client";

import <PERSON><PERSON><PERSON> from "next/script";
import { useState } from "react";

interface SimpleScriptLoaderProps {
  taScript?: string;
}

/**
 * Simple script loader that just ensures proper loading order
 * No integration logic - just loads scripts sequentially
 */
export default function SimpleScriptLoader({
  taScript,
}: SimpleScriptLoaderProps) {
  const [isModalScriptLoaded, setIsModalScriptLoaded] = useState(false);

  const handleModalScriptReady = () => {
    setIsModalScriptLoaded(true);
  };

  const handleTrackingScriptReady = () => {
    console.log("Tracking script loaded - both scripts ready");
  };

  return (
    <>
      {/* Load modal script first */}
      {/* <Script
        src="https://assets.topdev.vn/uploads/ts/index.js?v=10"
        strategy="afterInteractive"
        id="modal-scripts"
        onReady={handleModalScriptReady}
      /> */}

      {/* Load tracking script after modal script */}
      {taScript && isModalScriptLoaded && (
        <Script
          src={taScript}
          strategy="lazyOnload" // This ensures it loads after afterInteractive scripts
          id="tracking-script"
          onReady={handleTrackingScriptReady}
        />
      )}
    </>
  );
}
