import NextLink, { LinkProps } from "next/link";
import React, { FC } from "react";

interface Props
  extends LinkProps,
    Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps>,
    React.RefAttributes<HTMLAnchorElement> {
  children?: React.ReactNode;
}

const Link: FC<Props> = ({ children, prefetch, ...props }) => {
  return (
    <NextLink prefetch={prefetch ?? false} {...props}>
      {children}
    </NextLink>
  );
};

export default Link;
