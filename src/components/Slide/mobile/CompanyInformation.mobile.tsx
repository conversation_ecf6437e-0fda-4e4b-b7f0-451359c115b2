"use client";

import Image from "next/image";
import Link from "@/components/Link/Link";
import React from "react";
import "swiper/css";
import { Swiper, SwiperSlide } from "swiper/react";

const CompanyInformation = () => {
  return (
    <Swiper slidesPerView={"auto"} spaceBetween={24} loop>
      {Array.from({ length: 10 }).map((item, index) => {
        return (
          <SwiperSlide key={index} className="!w-[5.5rem]">
            <div className="flex flex-col gap-6">
              <Link href="#" className="inline-block">
                {/* <Image
                  src={fakeImage({
                    width: 88,
                    height: 66,
                    text: `company ${index}`,
                  })}
                  alt="Fake image"
                  width={88}
                  height={66}
                  className="h-16 w-24 rounded object-cover"
                /> */}
              </Link>
              <Link href="#" className="inline-block">
                {/* <Image
                  src={fakeImage({
                    width: 88,
                    height: 66,
                    text: `company ${index}`,
                  })}
                  alt="Fake image"
                  width={88}
                  height={66}
                  className="h-16 w-24 rounded object-cover"
                /> */}
              </Link>
            </div>
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
};

export default CompanyInformation;
