"use client";
import CardInformativeGridMobile from "@/components/Card/Job/mobile/CardInformativeGrid.mobile";
import { JobType } from "@/types/job";
import { useTranslations } from "next-intl";
import { FC, useRef } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi2";
import "swiper/css";
import "swiper/css/pagination";
import { Swiper, SwiperRef, SwiperSlide } from "swiper/react";
interface Props {
  data: JobType[];
}

const HighlightJobMobile: FC<Props> = ({ data }) => {
  const t = useTranslations();
  const swiperRef = useRef<SwiperRef>(null);

  const handleSlidePrev = () => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slidePrev();
    }
  };
  const handleNextSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slideNext();
    }
  };
  return (
    <div>
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">{t("search_page_highlight_jobs")}</h2>
        <div className="flex items-center">
          <button
            onClick={handleSlidePrev}
            type="button"
            className="button flex h-6 w-6 items-center justify-center text-lg transition-all hover:text-primary"
          >
            <HiChevronLeft />
          </button>
          <button
            onClick={handleNextSlide}
            type="button"
            className="button flex h-6 w-6 items-center justify-center text-lg transition-all hover:text-primary"
          >
            <HiChevronRight />
          </button>
        </div>
      </div>
      <div className="mt-4">
        <Swiper spaceBetween={16} loop slidesPerView={1.5} ref={swiperRef}>
          {data.map((item, index) => {
            return (
              <SwiperSlide className="h-full" key={index}>
                <CardInformativeGridMobile job={item} />
              </SwiperSlide>
            );
          })}
        </Swiper>
      </div>
    </div>
  );
};

export default HighlightJobMobile;
