"use client";

import { Link } from "@/navigation";
import { useTranslations } from "next-intl";
import { isDesktop } from "react-device-detect";
import { RiArrowRightSLine } from "react-icons/ri";

interface JobOpeningsButtonProps {
  companySlug: string;
  companyId: number;
  numberJobs: number;
  srcPage?: string;
  mediumPage?: string;
}

const JobOpeningsButton = ({
  numberJobs,
  companyId,
  companySlug,
  srcPage,
  mediumPage,
}: JobOpeningsButtonProps) => {
  const t = useTranslations();
  const srcPageQueryParam = `topdev_${srcPage ?? "search"}`;
  const mediumPageQueryParam = mediumPage ?? "searchresult";

  if (numberJobs < 1) return <></>;
  return (
    <Link
      target={isDesktop ? "" : "_blank"}
      href={{
        pathname: "/companies/[slug]",
        params: { slug: companySlug + "-" + companyId },
        query: { src: srcPageQueryParam, medium: mediumPageQueryParam },
      }}
      className="font-medium text-primary-300"
    >
      <span
        className={`${isDesktop ? "text-sm" : "text-xs"} font-medium underline`}
      >
        {numberJobs}{" "}
        {numberJobs > 1 ? t("company_jobs_opening") : t("company_job_opening")}
      </span>
      <RiArrowRightSLine className="ml-2 inline-block" />
    </Link>
  );
};

export default JobOpeningsButton;
