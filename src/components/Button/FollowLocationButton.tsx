"use client";
import { Tooltip } from "flowbite-react";
import React, { FC, ReactNode } from "react";

type FollowButtonTypeExt = {
  children: ReactNode;
  fullAddress?: string;
};

const FollowLocationButton: FC<FollowButtonTypeExt> = ({
  fullAddress,
  children,
}) => {
  return (
    <Tooltip
      content={fullAddress}
      style="light"
      placement="bottom"
      theme={{
        style: {
          light: "bg-white text-[#5D5D5D] w-[328px] font-normal whitespace-normal text-base text-center",
        },
      }}
    >
      {children}
    </Tooltip>
  );
};

export default FollowLocationButton;
