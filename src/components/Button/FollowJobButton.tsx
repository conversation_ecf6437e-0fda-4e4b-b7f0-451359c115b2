"use client";

import { FollowButton } from "@/components/Button/index";
import ToastNotification from "@/components/Swal/ToastNotification";
import { followJob } from "@/services/connector";
import { followJob<PERSON><PERSON> } from "@/services/jobAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import { setFollowedJobs } from "@/store/slices/userSlice";
import { openLoginPopup } from "@/utils";
import { useTranslations } from "next-intl";
import Swal from "sweetalert2";

/**
 * Renders a button component for following/unfollowing a job.
 *
 * @component
 * @param {Object} props - The component props.
 * @param {number} props.jobId - The ID of the job.
 * @param {string} props.jobTitle - The title of the job.
 * @param {boolean} [props.active] - Deprecated. Whether the button is active or not.
 * @returns {JSX.Element} The rendered FollowJobButton component.
 */
const FollowJobButton = ({
  jobId,
  jobTitle,
  active,
  screenView,
}: {
  jobId: number;
  jobTitle: string;
  /**
   * @deprecated Will remove later
   */
  active?: boolean;
  screenView?: string;
}): JSX.Element => {
  const t = useTranslations();
  const dispath = useAppDispatch();
  const user = useAppSelector((state) => state.user.user);
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);
  const followedJobs = useAppSelector((state) => state.user.followed_jobs);

  const handleFollowMobileApp = (jobId: number) => {
    if (!localStorage.getItem("accessToken")) {
      followJob(jobId);
    } else {
      if (
        user.roles &&
        user.roles?.length > 0 &&
        user.roles[0] === "employer"
      ) {
        Swal.fire({
          title: t("common_sorry"),
          text: t("detail_job_page_not_right_access"),
          icon: "warning",
          confirmButtonColor: "#DD3F24",
        });

        return;
      }

      followJobApi(jobId).then((response) => {
        const { data } = response.data;

        // setIsFollow(data.is_followed);
        if (data.is_followed) {
          dispath(setFollowedJobs([...followedJobs, jobId]));
        } else {
          dispath(setFollowedJobs(followedJobs.filter((id) => id !== jobId)));
        }

        if (data.is_followed) {
          ToastNotification({
            icon: "success",
            title: t("detail_job_page_your_are_followed_job", {
              jobTitle: jobTitle,
            }),
            timer: 3000,
            timerProgressBar: true,
          });
        } else {
          ToastNotification({
            icon: "success",
            title: t("detail_job_page_your_are_unfollow_job", {
              jobTitle: jobTitle,
            }),
            timer: 3000,
            timerProgressBar: true,
          });
        }
      });
    }
  };

  const handleBtnFollowClick = () => {
    // Handle Follow Job Mobile View
    if (screenView === "mobile") {
      handleFollowMobileApp(jobId);
      return;
    }

    if (!isLoggedIn) {
      openLoginPopup([
        {
          name: "referring_name",
          value: "save_job",
        },
      ]);
      return;
    }

    if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
      Swal.fire({
        title: t("common_sorry"),
        text: t("detail_job_page_not_right_access"),
        icon: "warning",
        confirmButtonColor: "#DD3F24",
      });

      return;
    }

    followJobApi(jobId).then((response) => {
      const { data } = response.data;

      // setIsFollow(data.is_followed);
      if (data.is_followed) {
        dispath(setFollowedJobs([...followedJobs, jobId]));
      } else {
        dispath(setFollowedJobs(followedJobs.filter((id) => id !== jobId)));
      }

      if (data.is_followed) {
        ToastNotification({
          icon: "success",
          title: t("detail_job_page_your_are_followed_job", {
            jobTitle: jobTitle,
          }),
          timer: 3000,
          timerProgressBar: true,
        });
      } else {
        ToastNotification({
          icon: "success",
          title: t("detail_job_page_your_are_unfollow_job", {
            jobTitle: jobTitle,
          }),
          timer: 3000,
          timerProgressBar: true,
        });
      }
    });
  };

  return (
    <FollowButton
      isActive={followedJobs.includes(jobId)}
      onChange={() => {
        handleBtnFollowClick();
      }}
      screenView={screenView}
      jobId={jobId}
    />
  );
};

export default FollowJobButton;
