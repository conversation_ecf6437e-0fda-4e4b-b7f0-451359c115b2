import Image from "next/image";
import useAddToCart from "@/hooks/useAddToCart";
import { ButtonAddToCartProp } from "@/types/products";

export default function AddToCartButton({
  product_id,
  price,
  anchoring_price
}: Omit<ButtonAddToCartProp, "kind">) {
  const { loadingCart, handleAddToCart } = useAddToCart();
  return (
    <button
      disabled={loadingCart}
      data-cy="add-to-cart"
      onClick={() => handleAddToCart({ product_id, kind: "add", price, anchoring_price })}
      className="inline-flex h-9 w-11 items-center justify-center gap-1 rounded border border-solid border-primary bg-transparent text-sm font-semibold text-primary transition-all hover:bg-primary-100 disabled:cursor-not-allowed disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 lg:h-14 lg:w-[70px] lg:gap-3"
    >
      <Image
        src={"/v4/assets/images/online-payment/cart-plus.png"}
        alt="Cart Plus"
        width={22}
        height={20}
      />
    </button>
  );
}
