import React, { <PERSON> } from "react";
import { ButtonType } from "./types";
import classNames from "@/utils/classNames";
import { isMobile } from "react-device-detect";

const Button: FC<ButtonType> = ({
  children,
  onClick,
  accent = "default",
  type = "button",
  size = "md",
  loading = false,
  isBlock = false,
  id = "",
  form = "",
  disabled = false,
  className,
  ...props
}) => {
  return (
    <button
      id={id}
      type={type}
      disabled={disabled}
      className={classNames(
        "inline-flex items-center justify-center gap-1 border border-solid text-sm transition-all disabled:cursor-not-allowed lg:gap-3 lg:text-base",
        accent === "primary"
          ? "border-primary bg-primary text-white hover:border-primary-400 hover:bg-primary-400 disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100"
          : "",
        accent === "secondary"
          ? "border-gray-200 bg-gray-200 text-gray-600 hover:border-gray-300 hover:bg-gray-300 dark:border-white dark:text-white"
          : "",
        accent === "applyMobile"
          ? `${
              isMobile ? "w-1/2" : "w-56"
            } h-auto border-primary bg-primary px-14 py-4 text-white hover:border-primary-400 hover:bg-primary-400 md:w-auto`
          : "",
        accent === "disableMobile"
          ? `${
              isMobile ? "w-1/2" : "w-56"
            } h-auto border-gray-200 bg-gray-200 px-14 py-4 text-white md:w-auto`
          : "",

        accent === "outline"
          ? "border-primary bg-transparent text-primary hover:bg-primary-100 dark:border-white dark:text-white"
          : "",
        accent === "disable" ? "border-gray-200 bg-gray-200 text-white" : "",
        accent === "ghost"
          ? "border-transparent bg-transparent text-gray-600 dark:text-white"
          : "",
        accent === "cancelMobile"
          ? `${
              isMobile ? "w-1/2" : "w-56"
            } h-auto border-transparent bg-transparent px-14 py-4 font-bold text-gray-600 dark:text-white md:w-auto`
          : "",
        accent === "link"
          ? "border-transparent bg-transparent text-primary dark:text-white"
          : "",
        size === "sm" ? "h-9 rounded-sm px-4 text-sm font-bold" : "",
        size === "md"
          ? `${props.fontSize} h-9 rounded px-4 font-semibold lg:h-12 lg:px-6`
          : "",
        size === "lg" ? "h-9 rounded px-4 font-semibold lg:h-14 lg:px-8" : "",
        isBlock ? "w-full" : "",
        className ? className : ""
      )}
      onClick={onClick}
      form={form}
    >
      {props.leadingIcon && (
        <span
          className={classNames(
            "flex items-center justify-center text-lg lg:text-2xl",
            size === "sm" ? "h-4 w-4" : "",
            size === "md" ? "h-5 w-5" : "",
            size === "lg" ? "h-6 w-6" : "",
          )}
        >
          {props.leadingIcon}
        </span>
      )}
      <span
        className={classNames(
          accent === "link" ? "underline" : "",
          props.hiddenTextMobile ? "hidden lg:block" : "",
        )}
      >
        {children}
      </span>
      {props.trailingIcon && (
        <span
          className={classNames(
            "flex items-center justify-center text-lg lg:text-2xl",
            size === "sm" ? "h-4 w-4" : "",
            size === "md" ? "h-5 w-5" : "",
            size === "lg" ? "h-6 w-6" : "",
          )}
        >
          {props.trailingIcon}
        </span>
      )}
    </button>
  );
};

export default Button;
