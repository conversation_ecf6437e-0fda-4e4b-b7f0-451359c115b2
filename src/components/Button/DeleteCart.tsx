import { HiOutlineTrash } from "react-icons/hi2";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { useTranslations } from "next-intl";
import Swal from "sweetalert2";
import useCart from "@/hooks/useCart";
import { deleteToCart, getCarts } from "@/services/cartAPI";
import { useState } from "react";

export default function DeleteCart({ productId }: { productId: number }) {
  const [loading, setLoading] = useState<boolean>(false);
  const t = useTranslations();
  const [cart, setCartData, recallCart] = useCart();

  const handleDeleteCart = async (productId: number) => {
    if (!productId) return;

    const isWarning = await recallCart();
    if (!Boolean(isWarning)) {
      return;
    }

    Swal.fire({
      title: t("carts_toast_delete_cart_title"),
      icon: "question",
      showCancelButton: true,
      confirmButtonText: t("carts_toast_delete_cart_confirm"),
      confirmButtonColor: "#dd3f24",
      cancelButtonText: t("carts_toast_delete_cart_cancel"),
    }).then(async (result) => {
      if (result.isConfirmed) {
        setLoading(true);
        await deleteToCart(productId).finally(() => setLoading(false));
        await recallCart();
      }
    });
  };

  return (
    <button
      data-cy="delete-cart"
      onClick={() => handleDeleteCart(productId)}
      className="cursor-pointer"
    >
      {loading ? (
        <AiOutlineLoading3Quarters className="inline-block h-6 w-6 animate-spin" />
      ) : (
        <HiOutlineTrash className="inline-block h-6 w-6" />
      )}
    </button>
  );
}
