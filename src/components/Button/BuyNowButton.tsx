import Button from "@/components/Button/Button";
import useAddToCart from "@/hooks/useAddToCart";
import { ButtonAddToCartProp } from "@/types/products";
import { useTranslations } from "next-intl";

export default function BuyNowButton({
  product_id,
  price,
  anchoring_price,
  button_text = "products_buy_now",
}: Omit<ButtonAddToCartProp, "kind">) {
  const t = useTranslations();
  const { loadingCart, handleAddToCart } = useAddToCart();

  return (
    <div data-cy="buy-now" className="w-full">
      <Button
        onClick={() =>
          handleAddToCart({
            product_id,
            kind: "buyNow",
            price,
            anchoring_price,
          })
        }
        isBlock
        accent="primary"
        size="lg"
        disabled={loadingCart}
        data-cy="buy-now"
      >
        {t(button_text)}
      </Button>
    </div>
  );
}
