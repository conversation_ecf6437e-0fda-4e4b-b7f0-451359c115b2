"use client";
import classNames from "@/utils/classNames";
import { Tooltip } from "flowbite-react";
import { useTranslations } from "next-intl";
import React, { FC, useEffect, useState } from "react";
import { HiBookmark, HiOutlineBookmark } from "react-icons/hi2";
import { FollowButtonType } from "./types";
import { followJob } from "@/services/connector";
import { useAppSelector } from "@/store";
import { openLoginPopup } from "@/utils";

const FollowButton: FC<
  FollowButtonType & { screenView?: string; jobId?: string | number }
> = ({
  isActive = false,
  size = "md",
  onChange,
  fontSize,
  screenView,
  jobId,
}) => {
  const t = useTranslations();
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);

  const [isFollow, setIsFollow] = useState(false);
  useEffect(() => {
    setIsFollow(isActive);
  }, [isActive]);

  const handleChange = () => {
    if (onChange) {
      onChange(!isFollow);
    }
  };

  if (screenView === "mobile") {
    return (
      <button
        onClick={(event) => {
          if (!isLoggedIn) {
            openLoginPopup([
              {
                name: "referring_name",
                value: "save_job",
              },
            ]);
          }
          if (!localStorage.getItem("accessToken")) {
            followJob(jobId as any);
            event.preventDefault();
            return;
          }

          handleChange();
          event.preventDefault();
        }}
        role="button"
        aria-label="Follow button"
      >
        {isFollow ? (
          <span
            className={classNames(
              `cursor-pointer select-none text-xl leading-none text-primary-300 ${fontSize}`,
              size === "md" ? "lg:text-2xl" : "lg:text-base",
            )}
          >
            <HiBookmark />
          </span>
        ) : (
          <span
            className={classNames(
              `text-gray cursor-pointer select-none text-xl leading-none hover:text-primary-300 ${fontSize}`,
              size === "md" ? "lg:text-2xl" : "lg:text-base",
            )}
          >
            <HiOutlineBookmark />
          </span>
        )}
      </button>
    );
  }

  return (
    <Tooltip
      content={t("common_follow")}
      theme={{
        style: {
          dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
        },
      }}
    >
      <button
        onClick={(event) => {
          event.preventDefault();
          handleChange();
        }}
        role="button"
        aria-label="Follow button"
      >
        {isFollow ? (
          <span
            className={classNames(
              `cursor-pointer select-none text-xl leading-none text-primary-300 ${fontSize}`,
              size === "md" ? "lg:text-2xl" : "lg:text-base",
            )}
          >
            <HiBookmark />
          </span>
        ) : (
          <span
            className={classNames(
              `text-gray cursor-pointer select-none text-xl leading-none hover:text-primary-300 ${fontSize}`,
              size === "md" ? "lg:text-2xl" : "lg:text-base",
            )}
          >
            <HiOutlineBookmark />
          </span>
        )}
      </button>
    </Tooltip>
  );
};

export default FollowButton;
