// components/ShareButton.tsx
"use client";

import useShareFacebookCongratModal from "@/hooks/useShareFacebookCongratModal";
import { shareFacebookApply } from "@/services/promotion";
import { HiShare } from "react-icons/hi2";
import FacebookShareCongratulationModal from "../Modal/FacebookShareCongratulationModal";

// Đ<PERSON>nh nghĩa kiểu cho props của component
interface ShareButtonProps {
  shareUrl: string;
  jobId?: number;
}

const ShareButtonMobile = ({ shareUrl, jobId }: ShareButtonProps) => {
  const { show, handleCloseModal, handleOpenModal } =
    useShareFacebookCongratModal();

  const share = () => {
    const currentUrl = process.env.NEXT_PUBLIC_BASE_URL + shareUrl;

    const popupShareWindow = window.open(
      "https://www.facebook.com/dialog/share?app_id=1597211654569359&display=popup&href=" +
        encodeURIComponent(currentUrl) + 
        "&redirect_uri=" + encodeURIComponent('https://api.topdev.vn/promotions/share-facebook/callback?redirect_uri=' + encodeURIComponent(currentUrl)),
      "",
      "left=0,top=0,width=650,height=420,personalbar=0,toolbar=0,scrollbars=0,resizable=0",
    );

    if (!popupShareWindow) {
      alert("Vui lòng cho phép cửa sổ pop-up để thực hiện chia sẻ.");
      return;
    }

    if (jobId) {
      const handleFocus = () => {
        shareFacebookApply(jobId).then(({ data }) => {
          if (data?.success) {
            handleOpenModal();
          }
        });

        window.removeEventListener("focus", handleFocus);
      };

      window.addEventListener("focus", handleFocus);
    }
  };

  return (
    <>
      <div
        onClick={() => share()}
        className="inline-block cursor-pointer hover:text-primary-300"
      >
        <HiShare className="h-6 w-6" />
      </div>

      <FacebookShareCongratulationModal
        show={show}
        handleCloseModal={handleCloseModal}
      />
    </>
  );
};

export default ShareButtonMobile;
