"use client";
import classNames from "@/utils/classNames";
import { Tooltip } from "flowbite-react";
import { useTranslations } from "next-intl";
import React, { FC, useEffect, useState } from "react";
import { HiBookmark, HiOutlineBookmark } from "react-icons/hi2";
import { FollowButtonType } from "./types";

const FollowNewButton: FC<FollowButtonType> = ({
  isActive = false,
  size = "md",
  type = "job",
  onChange,
}) => {
  const t = useTranslations();
  const [isFollow, setIsFollow] = useState(false);
  useEffect(() => {
    setIsFollow(isActive);
  }, [isActive]);

  const handleChange = () => {
    if (onChange) {
      onChange(!isFollow);
    }
  };

  return (
    <Tooltip
      content={t("common_follow")}
      theme={{
        style: {
          dark: "bg-gray-900 text-white dark:bg-gray-700 whitespace-nowrap",
        },
      }}
    >
      <button
        onClick={(event) => {
          event.preventDefault();
          handleChange();
        }}
        role="button"
        className="w-[48px] h-[48px] border border-[#DD3F24] rounded-[4px] flex items-center justify-center"
        aria-label="Follow button"
      >
        {isFollow ? (
          <span
            className={classNames(
              "cursor-pointer select-none text-xl leading-none text-[#DD3F24]",
              size === "md" ? "lg:text-[20px]" : "lg:text-base",
            )}
          >
            <HiBookmark />
          </span>
        ) : (
          <span
            className={classNames(
              "cursor-pointer select-none text-xl leading-none text-[#DD3F24] hover:text-primary-300",
              size === "md" ? "lg:text-[20px]" : "lg:text-base",
            )}
          >
            <HiOutlineBookmark />
          </span>
        )}
      </button>
    </Tooltip>
  );
};

export default FollowNewButton;
