import { ReactN<PERSON>, MouseEvent } from "react";
export interface FollowButtonType {
  isActive: boolean;
  onChange?: (status: boolean) => void;
  size?: "md" | "sm";
  type?: "job" | "company";
  fontSize?: string;
}

export interface ButtonType {
  children: ReactNode;
  onClick?: (event: MouseEvent<HTMLButtonElement>) => void;
  accent?: "primary" | "secondary" | "outline" | "disable" | "ghost" | "link"| "applyMobile" | "disableMobile" | "cancelMobile";
  type?: "button" | "reset" | "submit";
  size?: "sm" | "md" | "lg";
  leadingIcon?: ReactNode;
  trailingIcon?: ReactNode;
  loading?: boolean;
  isBlock?: boolean;
  id?: string;
  form?: string;
  disabled?: boolean;
  gapStyle?: string;
  fontSize?: string;
  hiddenTextMobile?: boolean;
  className?: string;
}
