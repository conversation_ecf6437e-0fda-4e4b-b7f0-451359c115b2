"use client";
import React from "react";
import { useTranslations } from "next-intl";
import { JobType } from "@/types/job";
import Clock from "../Icons/Clock";

const ExpireButton = ({
  size,
  job,
}: {
  size?: "small" | "default";
  job?: JobType & any;
}) => {
  //style button
  let styleSubmitApply =
    size === "small" ? "lg:h-9 h-9" : "lg:h-14 h-14 rounded";

  const t = useTranslations();

  if (!job?.expires) return null;

  return (
    <div
      className={`-mt-4 pt-1 flex h-11 w-full items-center justify-center gap-3 bg-[#fff] text-sm font-normal text-[#4f4f4f] dark:border-white dark:text-white lg:h-14 ${styleSubmitApply}`}
    >
      <span>
        <Clock />
      </span>
      <span>
        {t("job_expire_in")} <span className="font-semibold">{job?.expires?.since}</span>
      </span>
    </div>
  );
};

export default ExpireButton;
