"use client";
import { classNames } from "@/utils";
import React, { useState } from "react";
import { BiSolidToTop } from "react-icons/bi";
import { useEventListener } from "usehooks-ts";
const ScrollTopButton = () => {
  const [isActive, setIsActive] = useState(false);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "smooth",
    });
  };

  const handleScroll = () => {
    if (window.scrollY > 200) {
      setIsActive(true);
    } else {
      setIsActive(false);
    }
  };

  useEventListener("scroll", handleScroll);

  return (
    <button
      className={classNames(
        "fixed bottom-[11.5rem] right-6 z-50 inline-flex h-12 w-12 items-center justify-center rounded border border-solid border-primary-200 bg-primary/80 text-2xl text-white transition-all hover:bg-primary lg:bottom-32 lg:right-10",
        isActive ? "visible opacity-100" : "invisible opacity-0",
      )}
      type="button"
      id="go-to-top"
      onClick={() => handleScrollToTop()}
    >
      <BiSolidToTop />
    </button>
  );
};

export default ScrollTopButton;
