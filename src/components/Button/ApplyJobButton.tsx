"use client";

import ToastNotification from "@/components/Swal/ToastNotification";
import { tracking } from "@/services/activity";
import { useAppDispatch, useAppSelector } from "@/store";
import { createOrUpdateCurrentJob } from "@/store/slices/jobSlice";
import { setIsShowApplyJob } from "@/store/slices/settingSlice";
import { Promotion } from "@/types/applyButton";
import { JobType } from "@/types/job";
import { Lang } from "@/types/page";
import { gtag } from "@/utils";
import {
  JOB_DISPLAY_STATUS_CLOSED,
  JOB_DISPLAY_STATUS_IN_REVIEW,
} from "@/utils/enums";
import { useTranslations } from "next-intl";
import { useRouter, useSearchParams } from "next/navigation";
import { useCallback } from "react";
import { IoIosInformationCircle } from "react-icons/io";
import dayjs from "dayjs";
import { URL_JOB_SEEKER } from "@/contansts/auth";
const EngApplyEvent = ({ isSecondStep = false }) => {
  if (isSecondStep) {
    return (
      <span className="block text-[8px]/[8px] lg:text-[8px]/[16px]">
        <span className="font-normal">Apply and get a chance to win </span>a
        MacBook, AirPods, and many other exciting prizes.
        <a
          href="https://topdev.vn/page/apply-nhan-qua"
          className="ml-1 inline-block translate-y-[1px]"
        >
          <IoIosInformationCircle />
        </a>
      </span>
    );
  }
  return (
    <span className="block text-[8px]/[8px] lg:text-[8px]/[16px]">
      <span className="font-normal">Get a 10k voucher instantly</span> and a
      chance to win a MacBook Air M3!
      <a
        href="https://topdev.vn/page/apply-nhan-qua"
        className="ml-1 inline-block translate-y-[1px]"
      >
        <IoIosInformationCircle />
      </a>
    </span>
  );
};
const VieApplyEvent = ({ isSecondStep = false }) => {
  if (isSecondStep) {
    return (
      <span className="block text-[8px]/[8px] lg:text-[8px]/[16px]">
        <span className="font-normal">
          Ứng tuyển để nhận cơ hội trúng MacBook, Air pod{" "}
        </span>{" "}
        và nhiều phần quà khác
        <a
          href="https://topdev.vn/page/apply-nhan-qua"
          className="ml-1 inline-block translate-y-[1px]"
        >
          <IoIosInformationCircle />
        </a>
      </span>
    );
  }
  return (
    <span className="block text-[8px]/[8px] lg:text-[8px]/[16px]">
      <span className="font-normal">Có ngay voucher 10k</span> cùng cơ hội nhận
      Macbook Air M3
      <a
        href="https://topdev.vn/page/apply-nhan-qua"
        className="ml-1 inline-block translate-y-[1px]"
      >
        <IoIosInformationCircle />
      </a>
    </span>
  );
};

const ApplyJobButton = ({
  size,
  statusDisplay,
  jobId,
  detailUrl,
  companyDisplayName,
  jobTypesStr,
  job,
  locale = "vi",
  voucherApply,
}: {
  size?: "small" | "default";
  statusDisplay: string;
  isApplied: Boolean;
  jobId: number;
  detailUrl: string;
  companyDisplayName?: string;
  jobTypesStr: string;
  job?: JobType;
  locale?: Lang;
  voucherApply?: Promotion;
}) => {
  const dispatch = useAppDispatch();
  const t = useTranslations();
  const searchParams = useSearchParams();
  const user = useAppSelector((state) => state?.user?.user);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const appliedJobs = useAppSelector((state) => state.user.applied_jobs);
  const router = useRouter();
  const isJobApplied = useCallback(
    () => appliedJobs?.includes(jobId),
    [appliedJobs, jobId],
  );
  const handleOpenLogin = () => {
    const url = `${URL_JOB_SEEKER}?redirect_uri=${window.location.href}`;
    router.push(url);
  };
  const isValidVoucher =
    voucherApply?.started_at &&
    voucherApply?.ended_at &&
    dayjs().isAfter(dayjs(voucherApply.started_at)) &&
    dayjs().isBefore(dayjs(voucherApply.ended_at));
  const isSecondStep =
    voucherApply?.has_reward_available === false ||
    (voucherApply?.has_reward_available === true &&
      voucherApply?.has_user_already_received_reward === true);
  const handleApplyJob = (jobId: number) => {
    window.tta && window.tta("applyCV");

    if (user.roles && user.roles[0] === "employer") {
      ToastNotification({
        icon: "warning",
        title: t("common_sorry"),
        description: t("detail_job_page_not_right_access"),
        timer: 2000,
      });
      return;
    }

    if (!isLoggedIn) {
      // const currentUrl = new URL(window.location.href);
      // currentUrl.searchParams.append("isShowApplyJob", "1");

      // window.location.href =
      //   process.env.NEXT_PUBLIC_OAUTH2_URL_ACCOUNT +
      //   "?referring_name=login_apply&redirect_uri=" +
      //   encodeURIComponent(currentUrl.toString());
      handleOpenLogin();
      return;
    }

    document.body.classList.add("layer-popup");

    if (job) {
      dispatch(createOrUpdateCurrentJob(job));
    }
    dispatch(setIsShowApplyJob(true));

    const params: string = searchParams.toString();
    const checkValueIsset = params.includes("source=ApplyNow")
      ? "?" + params
      : "?source=ApplyNow&" + params;

    const newPathName =
      location.pathname + (!!params ? checkValueIsset : "?source=ApplyNow");

    history.pushState({}, "", newPathName);

    // Tracking apply-now
    tracking({
      job_ids: jobId,
      collection: "apply-now",
      tracking_variant: "forced-login",
    });

    window.tta && window.tta("applyCVWithLogin");

    // Push GTM on click Apply Now
    gtag({
      event: "eec.jobApply",
      ecommerce: {
        add: {
          actionField: { list: "ApplyCV" },
          products: [
            {
              name: detailUrl,
              id: jobId,
              brand: companyDisplayName,
              category: "Job",
              variant: jobTypesStr,
            },
          ],
        },
      },
    });
  };
  //style button
  let styleSubmitApply = size === "small" ? "lg:h-9 h-9" : "lg:h-14 h-[44px]";
  //Start show button in review
  if (statusDisplay === JOB_DISPLAY_STATUS_IN_REVIEW) {
    return (
      <button className="h-9 w-full rounded border-gray-200 bg-gray-200 font-semibold text-gray-600 hover:border-gray-300 hover:bg-gray-300 dark:border-white dark:text-white lg:h-14">
        {t("detail_job_page_in_review")}
      </button>
    );
  }
  //End show button in review

  //Start show button isApplied or Closed
  if (isJobApplied()) {
    return (
      <button
        className={`h-11 w-full rounded border-gray-200 bg-gray-200 font-semibold text-gray-600 hover:border-gray-300 hover:bg-gray-300 dark:border-white dark:text-white lg:h-14 ${styleSubmitApply}`}
      >
        {t("detail_job_page_applied")}
      </button>
    );
  } else if (statusDisplay === JOB_DISPLAY_STATUS_CLOSED) {
    return (
      <button
        className={`h-11 w-full rounded border-gray-200 bg-gray-200 font-semibold text-gray-600 hover:border-gray-300 hover:bg-gray-300 dark:border-white dark:text-white lg:h-14 ${styleSubmitApply}`}
      >
        {t("detail_job_page_expired")}
      </button>
    );
  }

  if (voucherApply && isValidVoucher)
    return (
      <button
        onClick={() => handleApplyJob(jobId)}
        id="applyCV"
        style={{
          backgroundImage:
            "url(https://c.topdevvn.com/uploads/2025/05/29/BG_BUTTON_1.png)",
          backgroundRepeat: "no-repeat",
          backgroundSize: "cover",
        }}
        className={`bg-contain w-full rounded text-[18px] font-semibold text-white disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 ${styleSubmitApply}`}
      >
        {t("detail_job_page_apply_now")}
        {locale === "vi" ? (
          <VieApplyEvent isSecondStep={isSecondStep} />
        ) : (
          <EngApplyEvent isSecondStep={isSecondStep} />
        )}
      </button>
    );
  return (
    <button
      onClick={() => handleApplyJob(jobId)}
      id="applyCV"
      className={`w-full rounded border-primary bg-primary font-semibold text-white hover:border-primary-400 hover:bg-primary-400 disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 ${styleSubmitApply}`}
    >
      {t("detail_job_page_apply_now")}
    </button>
  );
};

export default ApplyJobButton;
