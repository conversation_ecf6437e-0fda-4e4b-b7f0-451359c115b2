import { useTranslations } from "next-intl";

export default function ViewMoreBenifitJobPosting({
  isHidden,
  setIsHidden,
}: {
  isHidden: boolean;
  setIsHidden(isHidden: boolean): void;
}) {
  const t = useTranslations();

  return (
    <span
      onClick={() => setIsHidden(!isHidden)}
      data-cy="read-more"
      className="read-more inline-block cursor-pointer font-semibold text-gray-600 underline hover:text-primary"
    >
      {!!isHidden ? t("products_view_more") : t("products_collapse")}
    </span>
  );
}
