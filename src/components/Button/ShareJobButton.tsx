// components/ShareButton.tsx
"use client";

import useShareFacebookCongratModal from "@/hooks/useShareFacebookCongratModal";
import { shareFacebookApply } from "@/services/promotion";
import { HiShare } from "react-icons/hi2";
import FacebookShareCongratulationModal from "../Modal/FacebookShareCongratulationModal";

// Định nghĩa kiểu cho props của component
interface ShareButtonProps {
  shareUrl: string;
  jobId?: number;
}

const ShareButton = ({ shareUrl, jobId }: ShareButtonProps) => {
  const { show, handleCloseModal, handleOpenModal } = useShareFacebookCongratModal();

  const share = () => {
    const currentUrl = process.env.NEXT_PUBLIC_BASE_URL + shareUrl;

    const popupShareWindow = window.open(
      "http://facebook.com/sharer/sharer.php?u=" +
        encodeURIComponent(currentUrl),
      "",
      "left=0,top=0,width=650,height=420,personalbar=0,toolbar=0,scrollbars=0,resizable=0",
    );

    if (!popupShareWindow) {
      alert("Vui lòng cho phép cửa sổ pop-up để thực hiện chia sẻ.");
      return;
    }

    if (jobId) {
      const handleFocus = () => {
        shareFacebookApply(jobId).then(({ data }) => {
          if (data?.success) {
            handleOpenModal();
          }
        });

        window.removeEventListener("focus", handleFocus);
      };

      window.addEventListener("focus", handleFocus);
    }
  };

  return (
    <>
      <div
        onClick={() => share()}
        className="flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-[4px] border border-[#DD3F24] text-2xl text-primary-300 hover:text-primary-300"
        id="shareFB"
      >
        <button className="flex h-[48px] w-[48px] items-center justify-center">
          <HiShare />
        </button>
      </div>

      <FacebookShareCongratulationModal show={show} handleCloseModal={handleCloseModal}/>
    </>
  );
};

export default ShareButton;
