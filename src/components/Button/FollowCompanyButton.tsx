"use client";

import React, { FC, useState } from "react";
import Swal from "sweetalert2";
import { useTranslations } from "next-intl";
import { FollowButton } from "@/components/Button";
import { followCompany } from "@/services/companyAPI";
import { useAppSelector } from "@/store";
import ToastNotification from "@/components/Swal/ToastNotification";
import { openLoginPopup } from "@/utils";

const FollowCompanyButton: FC<{
  companyId: number;
  companyName: string;
  active: boolean;
  size?: "md" | "sm";
}> = ({ companyId, companyName, active, size }) => {
  const t = useTranslations();
  const [isFollow, setIsFollow] = useState(active);
  const user = useAppSelector((state) => state.user.user);
  const isLoggedIn = useAppSelector((state) => state.user.isLoggedIn);

  const handleBtnFollowClick = () => {
    if (!isLoggedIn) {
      openLoginPopup();
      return;
    }

    if (user.roles && user.roles?.length > 0 && user.roles[0] === "employer") {
      Swal.fire({
        title: t("common_sorry"),
        text: t("detail_job_page_not_right_access"),
        icon: "warning",
        confirmButtonColor: "#DD3F24",
      });

      return;
    }

    followCompany(companyId).then((data) => {
      setIsFollow(data.is_followed);

      if (data.is_followed) {
        ToastNotification({
          icon: "success",
          title: t("company_you_have_followed_company", {
            company: companyName,
          }),
          timer: 3000,
          timerProgressBar: true,
        });
      } else {
        ToastNotification({
          icon: "success",
          title: t("company_you_have_unfollowed_company", {
            company: companyName,
          }),
          timer: 3000,
          timerProgressBar: true,
        });
      }
    });
  };

  return (
    <FollowButton
      size={size}
      isActive={isFollow}
      onChange={() => {
        handleBtnFollowClick();
      }}
    />
  );
};

export default React.memo(FollowCompanyButton);
