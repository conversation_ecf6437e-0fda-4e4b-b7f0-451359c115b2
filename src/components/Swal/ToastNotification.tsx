import Swal, { SweetAlertIcon, SweetAlertPosition } from "sweetalert2";

interface ToastProps {
  icon?: SweetAlertIcon;
  title?: string;
  description?: string;
  position?: SweetAlertPosition;
  timer?: number;
  timerProgressBar?: boolean;
}

const ToastNotification = ({
  icon,
  title,
  description,
  position = "top-end",
  timer = 3000,
  timerProgressBar = true,
}: ToastProps) => {
  const Toast = Swal.mixin({
    toast: true,
    position: position,
    showConfirmButton: false,
    timer: timer,
    timerProgressBar: timerProgressBar,
    didOpen: (toast) => {
      toast.addEventListener("mouseenter", Swal.stopTimer);
      toast.addEventListener("mouseleave", Swal.resumeTimer);
    },
    customClass: {
      popup: "!p-2",
      title: "title-custom-toast",
      htmlContainer: "html-container-custom-toast",
    },
  });

  Toast.fire({
    icon: icon,
    title: title,
    html: description,
  });
};

export default ToastNotification;
