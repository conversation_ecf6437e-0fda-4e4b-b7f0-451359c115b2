import { useAppSelector } from "@/store";
import { useTranslations } from "next-intl";
import { CiEraser } from "react-icons/ci";
import { HiServer } from "react-icons/hi2";

export default function FilterModalSalary() {
  const searchValues = useAppSelector((state) => state.search.formSearch);
  const t = useTranslations();
  return (
    <div className="salary-container rounded bg-[#F6F6F6] px-6 py-4">
      <div className="flex items-center gap-2">
        <HiServer color="#DD3F24" />
        <div className="color-[#4F4F4F] flex-grow">
          <span className="font-bold text-[#DD3F24]">Salary</span>
        </div>
        <div className="flex items-center text-xs">
          <button className="flex items-center gap-1">
            <CiEraser />
            <span>{t("search_page_clear_all")}</span>
          </button>
        </div>
      </div>

      <div className="salary-range flex w-full items-center gap-2">
        <div>
          <input
            type="text"
            placeholder={t("search_page_min")}
            value={searchValues.salary_min || ""}
            className="w-full"
          />
        </div>
        <span>-</span>
        <div>
          <input
            type="text"
            placeholder={t("search_page_max")}
            value={searchValues.salary_max || ""}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
