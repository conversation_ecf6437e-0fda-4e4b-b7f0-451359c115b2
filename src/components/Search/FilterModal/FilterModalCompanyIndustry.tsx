import { useOutsideClick } from "@/hooks/useOutsideClick";
import { useAppDispatch, useAppSelector } from "@/store";
import { setCompanyIndustries } from "@/store/slices/searchSlice";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import React, { useMemo, useRef } from "react";
import { FaCircleXmark } from "react-icons/fa6";
import { HiGift } from "react-icons/hi2";

export default function FilterModalCompanyIndustry() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const searchValues = useAppSelector((state) => state.search.formSearch);
  const dispatch = useAppDispatch();
  const t = useTranslations();
  const [companyIndustryKeyword, setCompanyIndustryKeyword] =
    React.useState("");
  const [isShow, setIsShow] = React.useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  useOutsideClick(dropdownRef, () => {
    setIsShow(false);
  });

  const companyIndustries = useMemo(
    () =>
      taxonomies?.industries.map((item) => {
        return { value: item.id, text: item.text };
      }),
    [taxonomies],
  );
  return (
    <div className="job-benefit-container flex flex-col gap-6 rounded bg-[#F6F6F6] px-6 py-4">
      <div className="flex items-center gap-2">
        <HiGift color="#DD3F24" />
        <div className="flex-grow">
          <span className="font-bold text-[#DD3F24]">
            {t("search_page_industries")}
          </span>{" "}
          ({t("search_page_from_dropdown")})
        </div>
      </div>

      <div className="selected-options flex flex-wrap gap-2">
        {searchValues?.company_industries?.map((companyIndustry) => (
          <div
            key={companyIndustry}
            className="rounded bg-[#DD3F24] px-6 py-3 text-white"
          >
            <div className="flex items-center gap-2">
              <span className="text-sm">
                {
                  companyIndustries.find(
                    (b) => b.value + "" === companyIndustry,
                  )?.text
                }
              </span>
              <button
                onClick={() => {
                  dispatch(
                    setCompanyIndustries(
                      searchValues.company_industries.filter(
                        (b) => b !== companyIndustry,
                      ),
                    ),
                  );
                }}
              >
                <FaCircleXmark color="#fff" />
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="relative z-20">
        <input
          type="text"
          placeholder={t("search_page_placeholder_industries")}
          className="w-full rounded border border-[#DD3F24] p-2 shadow-sm"
          onChange={(e) => setCompanyIndustryKeyword(e.target.value)}
          onFocus={() => setIsShow(true)}
        />
      </div>

      <div
        className={classNames("relative h-[150px]", isShow ? "" : "hidden")}
        ref={dropdownRef}
      >
        <div className="absolute -top-7 left-1/2 z-10 w-11/12 -translate-x-1/2 border border-[#E3E3E3] bg-white p-2">
          <ul className="h-[150px] overflow-y-scroll">
            {companyIndustries
              ?.filter(
                (c) =>
                  companyIndustryKeyword === "" ||
                  c.text
                    .toLowerCase()
                    .includes(companyIndustryKeyword.toLowerCase()),
              )
              .map((companyIndustry) => (
                <li
                  onClick={() => {
                    dispatch(
                      setCompanyIndustries(
                        searchValues.company_industries.includes(
                          companyIndustry.value + "",
                        )
                          ? searchValues.company_industries.filter(
                              (b) => b !== companyIndustry.value + "",
                            )
                          : [
                              ...searchValues.company_industries,
                              companyIndustry.value + "",
                            ],
                      ),
                    );
                  }}
                  key={companyIndustry.value}
                  className={classNames(
                    "px-3 py-1 hover:cursor-pointer hover:bg-[#FEF4F2] hover:text-[#DD3F24]",
                    searchValues.company_industries.includes(
                      companyIndustry.value + "",
                    )
                      ? "bg-[#FEF4F2] text-[#DD3F24]"
                      : "",
                  )}
                >
                  {companyIndustry.text}
                </li>
              ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
