import { useAppDispatch, useAppSelector } from "@/store";
import { setContractTypes } from "@/store/slices/searchSlice";
import { useTranslations } from "next-intl";
import React, { useMemo } from "react";
import { HiServer } from "react-icons/hi2";

export default function FilterModalContractType() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const searchValues = useAppSelector((state) => state.search.formSearch);
  const dispatch = useAppDispatch();
  const t = useTranslations();

  const contractTypes = useMemo(
    () =>
      taxonomies?.contract_types.map((item) => {
        return { value: item.id, text: item.text };
      }),
    [taxonomies],
  );

  return (
    <div className="contract-type-container">
      <div className="flex gap-2">
        <HiServer color="#DD3F24" />
        <div className="flex-grow">
          <span className="font-bold text-[#DD3F24]">
            {t("search_page_contract_type")}
          </span>
        </div>
      </div>
      <ul className="grid grid-cols-2">
        {contractTypes.map((contractType) => (
          <li key={contractType.value}>
            <label className="flex w-full items-center gap-2 px-4 py-2 hover:cursor-pointer hover:bg-gray-100">
              <input
                type="radio"
                name="contract-type"
                value={contractType.value}
                className="h-5 w-5 rounded-full outline-none ring-0 checked:bg-primary checked:text-primary checked:accent-primary focus:border-none focus:shadow-transparent focus:outline-none focus:ring-0"
                checked={searchValues.contract_types.includes(
                  contractType.value + "",
                )}
                onChange={(e) => {
                  dispatch(setContractTypes([e.target.value]));
                }}
              />
              <span>{contractType.text}</span>
            </label>
          </li>
        ))}
      </ul>
    </div>
  );
}
