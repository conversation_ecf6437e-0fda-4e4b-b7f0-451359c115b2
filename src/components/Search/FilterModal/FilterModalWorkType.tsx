import { useAppDispatch, useAppSelector } from "@/store";
import { setWorkTypes } from "@/store/slices/searchSlice";
import { useLocale, useTranslations } from "next-intl";
import React, { useMemo } from "react";
import { HiServer } from "react-icons/hi2";

export default function FilterModalWorkType() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const searchValues = useAppSelector((state) => state.search.formSearch);
  const dispatch = useAppDispatch();
  const t = useTranslations();
  const locale = useLocale();
  const jobTypes = useMemo(
    () =>
      taxonomies?.job_types.map((item) => {
        return {
          value: item.id,
          text: locale === "vi" ? item.text_vi : item.text_en,
        };
      }),
    [taxonomies, locale],
  );

  return (
    <div className="work-type-container">
      <div className="flex gap-2">
        <HiServer color="#DD3F24" />
        <div className="flex-grow">
          <span className="font-bold text-[#DD3F24]">
            {t("search_page_work_type")}
          </span>
        </div>
      </div>
      <ul className="grid grid-cols-2">
        {jobTypes.map((jobType) => (
          <li key={jobType.value}>
            <label className="flex w-full items-center gap-2 px-4 py-2 hover:cursor-pointer hover:bg-gray-100">
              <input
                type="radio"
                name="job-type"
                value={jobType.value}
                className="h-5 w-5 rounded-full outline-none ring-0 checked:bg-primary checked:text-primary checked:accent-primary focus:border-none focus:shadow-transparent focus:outline-none focus:ring-0"
                checked={searchValues.work_types.includes(jobType.value + "")}
                onChange={(e) => {
                  dispatch(setWorkTypes([e.target.value]));
                }}
              />
              <span>{jobType.text}</span>
            </label>
          </li>
        ))}
      </ul>
    </div>
  );
}
