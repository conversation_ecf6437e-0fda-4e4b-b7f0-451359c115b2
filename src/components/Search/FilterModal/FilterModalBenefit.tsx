import { useOutsideClick } from "@/hooks/useOutsideClick";
import { useAppDispatch, useAppSelector } from "@/store";
import { setBenefits } from "@/store/slices/searchSlice";
import { classNames } from "@/utils";
import { useLocale, useTranslations } from "next-intl";
import React, { useMemo, useRef } from "react";
import { FaCircleXmark } from "react-icons/fa6";
import { HiGift } from "react-icons/hi2";

export default function FilterModalBenefit() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const searchValues = useAppSelector((state) => state.search.formSearch);
  const dispatch = useAppDispatch();
  const [searchBenefitKeyword, setSearchBenefitKeyword] = React.useState("");
  const [isShow, setIsShow] = React.useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const t = useTranslations();
  const locale = useLocale();
  useOutsideClick(dropdownRef, () => {
    setIsShow(false);
  });

  const benefits = useMemo(
    () =>
      taxonomies?.benefits.map((item) => {
        return {
          value: item.id,
          text: locale === "vi" ? item.text_vi : item.text_en,
        };
      }),
    [taxonomies, locale],
  );

  return (
    <div className="job-benefit-container flex flex-col gap-6 rounded bg-[#F6F6F6] px-6 py-4">
      <div className="flex items-center gap-2">
        <HiGift color="#DD3F24" />
        <div className="flex-grow">
          <span className="font-bold text-[#DD3F24]">
            {t("search_page_benefits")}
          </span>{" "}
          ({t("search_page_from_dropdown")})
        </div>
      </div>

      <div className="selected-options flex flex-wrap gap-2">
        {searchValues?.benefits?.map((benefit) => (
          <div
            key={benefit}
            className="rounded bg-[#DD3F24] px-6 py-3 text-white"
          >
            <div className="flex items-center gap-2">
              <span className="text-sm">
                {benefits.find((b) => b.value + "" === benefit)?.text}
              </span>
              <button
                onClick={() => {
                  dispatch(
                    setBenefits(
                      searchValues.benefits.filter((b) => b !== benefit),
                    ),
                  );
                }}
              >
                <FaCircleXmark color="#fff" />
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="relative z-20">
        <input
          type="text"
          placeholder={t("search_page_placeholder_benefits")}
          className="w-full rounded border border-[#DD3F24] p-2 shadow-sm"
          value={searchBenefitKeyword}
          onChange={(e) => setSearchBenefitKeyword(e.target.value)}
          onFocus={() => setIsShow(true)}
        />
      </div>

      <div
        className={classNames("relative h-[150px]", isShow ? "" : "hidden")}
        ref={dropdownRef}
      >
        <div className="absolute -top-7 left-1/2 z-10 w-11/12 -translate-x-1/2 border border-[#E3E3E3] bg-white p-2">
          <ul className="h-[150px] overflow-y-scroll">
            {benefits
              ?.filter(
                (b) =>
                  searchBenefitKeyword === "" ||
                  b.text
                    .toLowerCase()
                    .includes(searchBenefitKeyword.toLowerCase()),
              )
              .map((benefit) => (
                <li
                  onClick={() => {
                    dispatch(
                      setBenefits(
                        searchValues.benefits.includes(benefit.value + "")
                          ? searchValues.benefits.filter(
                              (b) => b !== benefit.value + "",
                            )
                          : [...searchValues.benefits, benefit.value + ""],
                      ),
                    );
                  }}
                  key={benefit.value}
                  className={classNames(
                    "px-3 py-1 hover:cursor-pointer hover:bg-[#FEF4F2] hover:text-[#DD3F24]",
                    searchValues.benefits.includes(benefit.value + "")
                      ? "bg-[#FEF4F2] text-[#DD3F24]"
                      : "",
                  )}
                >
                  {benefit.text}
                </li>
              ))}
          </ul>
        </div>
      </div>
    </div>
  );
}
