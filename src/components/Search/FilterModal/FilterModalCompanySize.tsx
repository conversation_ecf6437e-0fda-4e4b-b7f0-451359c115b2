import { useAppDispatch, useAppSelector } from "@/store";
import { setCompanySizes } from "@/store/slices/searchSlice";
import { useLocale, useTranslations } from "next-intl";
import { useMemo } from "react";
import { HiServer } from "react-icons/hi2";

export default function FilterModalCompanySize() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const searchValues = useAppSelector((state) => state.search.formSearch);
  const dispatch = useAppDispatch();
  const locale = useLocale();
  const t = useTranslations();
  const companySizes = useMemo(
    () =>
      taxonomies?.num_employees.map((item) => {
        return {
          value: item.id,
          text: locale === "vi" ? item.text_vi : item.text_en,
        };
      }),
    [taxonomies, locale],
  );
  return (
    <div className="company-size-container">
      <div className="flex gap-2">
        <HiServer color="#DD3F24" />
        <div className="flex-grow">
          <span className="font-bold text-[#DD3F24]">
            {t("search_page_company_size")}
          </span>
        </div>
      </div>
      <ul className="grid grid-cols-2">
        {companySizes.map((companySize) => (
          <li key={companySize.value}>
            <label className="flex w-full items-center gap-2 px-4 py-2 hover:cursor-pointer hover:bg-gray-100">
              <input
                type="radio"
                name="company-size"
                value={companySize.value}
                className="h-5 w-5 rounded-full outline-none ring-0 checked:bg-primary checked:text-primary checked:accent-primary focus:border-none focus:shadow-transparent focus:outline-none focus:ring-0"
                checked={searchValues.company_sizes.includes(
                  companySize.value + "",
                )}
                onChange={(e) => {
                  dispatch(setCompanySizes([e.target.value]));
                }}
              />
              <span>{companySize.text}</span>
            </label>
          </li>
        ))}
      </ul>
    </div>
  );
}
