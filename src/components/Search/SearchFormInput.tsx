import { onSearchClick } from "@/hooks/useSearch";
import { getAllProvincesCache } from "@/services/areaAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  setKeyword,
  setLocations as setLocationRedux,
} from "@/store/slices/searchSlice";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { FaSearch } from "react-icons/fa";
import { HiLocationMarker } from "react-icons/hi";
import { HiOutlineChevronDown } from "react-icons/hi2";
import { useLocale } from "next-intl";

const LocationFilter = () => {
  const t = useTranslations();
  const dispatch = useAppDispatch();
  const locationValues = useAppSelector(
    (state) => state.search.formSearch.locations,
  );
  const [isOpen, setIsOpen] = useState(false);
  const [locations, setLocations] = useState<
    { label: string; value: string }[]
  >([]);
  useEffect(() => {
    const fetchLocations = async () => {
      try {
        const data = await getAllProvincesCache();
        setLocations(
          data.map((province: { id: string; text: string }) => {
            return {
              label: province.text,
              value: province.id,
            };
          }),
        );
      } catch (error) {
        console.error("Failed to fetch locations:", error);
      }
    };
    fetchLocations();
  }, []);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex w-full items-center gap-2"
      >
        <div className="h-4 w-4">
          <HiLocationMarker width={24} height={24} color="#dd3f24" />
        </div>
        {locationValues.length === 0 ? (
          <span className="flex-grow text-left text-[#dd3f24]">
            {t("search_page_location")}
          </span>
        ) : (
          <span className="line-clamp-1 flex-grow text-left text-[#dd3f24]">
            {locations
              .filter((item) => locationValues.includes(item.value))
              .map((item) => item.label)
              .join(", ")}
          </span>
        )}

        <HiOutlineChevronDown />
      </button>

      <div
        className={classNames(
          "absolute top-10 z-50 max-h-40 w-max overflow-y-auto rounded bg-white shadow-sm",
          !isOpen ? "hidden" : "",
        )}
      >
        <ul className="w-full">
          {locations.map((location) => (
            <li
              key={location.value}
              className="hover:cursor-pointer hover:bg-gray-100"
            >
              <label className="flex items-center gap-2  px-4 py-2 hover:cursor-pointer">
                <input
                  type="checkbox"
                  name="location"
                  value={location.value}
                  className="h-5 w-5 rounded outline-none ring-0 checked:bg-primary checked:text-primary checked:accent-primary focus:border-none focus:shadow-transparent focus:outline-none focus:ring-0"
                  checked={locationValues.includes(location.value)}
                  onChange={() => {
                    dispatch(
                      setLocationRedux(
                        locationValues.includes(location.value)
                          ? locationValues.filter(
                              (item) => item !== location.value,
                            )
                          : [...locationValues, location.value],
                      ),
                    );
                  }}
                />
                <span>{location.label}</span>
              </label>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default function SearchFormInput() {
  const keyword = useAppSelector((state) => state.search.formSearch.keyword);
  const searchValues = useAppSelector((state) => state.search.formSearch);
  const dispatch = useAppDispatch();
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div className="relative flex h-[72px] w-full items-center justify-center rounded bg-white px-14">
      <div className="grid w-full grid-cols-12">
        <div className="col-span-8 flex items-center ">
          <FaSearch width={24} height={24} color="#dd3f24" />
          <input
            type="text"
            className="focus:shadow-none h-6 w-full border-0 border-r-2 border-red-300 focus:border-0 focus:border-r-2 focus:border-red-300"
            placeholder={t("main_search_search_placeholder")}
            value={keyword}
            onChange={(event) => {
              dispatch(setKeyword(event.target.value));
            }}
            onKeyDown={(event) => {
              if (event.key === "Enter") {
                onSearchClick(searchValues, locale);
              }
            }}
          />
        </div>

        <div className="col-span-2 flex items-center pl-2">
          <LocationFilter />
        </div>

        <div className="col-span-2 pl-2">
          <button
            onClick={() => {
              onSearchClick(searchValues, locale);
            }}
            className="flex h-8 w-full items-center justify-center rounded border border-[#DD3F24] bg-[#DD3F24] text-white"
          >
            <span className="hidden lg:inline-block">
              {t("main_search_search")}
            </span>
          </button>
        </div>
      </div>

      <div className="absolute left-0 top-[82px] z-50 flex hidden flex-col gap-2 rounded bg-white px-7 py-6 shadow-sm">
        <div className="font-bold text-[#dd3f24]">Popular keywords</div>

        <ul>
          <li>asdfasdf</li>
          <li>asdfasdf</li>
          <li>asdfasdf</li>
          <li>asdfasdf</li>
          <li>asdfasdf</li>
        </ul>
      </div>
    </div>
  );
}
