"use client";
import { getSuggestedKeywords } from "@/services/searchAPI";
import {
  FilterParams,
  KeywordSearchType,
  LocationType,
  SearchFiltersType,
  SearchMetaType,
  SuggestedType,
} from "@/types/search";
import { TaxonomiesType, TaxonomyType } from "@/types/taxonomy";
import classNames from "@/utils/classNames";
import { LIST_LOCATIONS } from "@/utils/enums";
import {
  fakeIdNotInTaxonomies,
  getResultSearch,
  slugify,
  toVnSlug,
} from "@/utils/search";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import {
  ChangeEvent,
  FC,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { isDesktop } from "react-device-detect";
import { FaFilterCircleXmark } from "react-icons/fa6";
import { HiOutlineSearch } from "react-icons/hi";
import { HiXMark } from "react-icons/hi2";
import { useDebounce } from "usehooks-ts";
import { Button } from "../Button";
import SelectLoading from "../Select/SelectLoading";
import QuickFilterBar from "./QuickFilterBar";
import { CompanySearchType } from "@/types/company";

const SelectMultipleFilter = dynamic(
  () => import("../Select/SelectMultipleFilter"),
  { ssr: false, loading: () => <SelectLoading /> },
);
const SelectSingleLocation = dynamic(
  () => import("../Select/SelectSingleLocation"),
  { ssr: false, loading: () => <SelectLoading /> },
);
const ChipTag = dynamic(() => import("../Tag/ChipTag"));

interface MainSearchProps {
  filters?: SearchFiltersType;
  meta?: SearchMetaType;
  taxonomies: TaxonomiesType;
  hideFilter?: boolean;
  hideQuickFilter?: boolean;
  isHome?: boolean;
}

const MainSearch: FC<MainSearchProps> = (props) => {
  const {
    filters,
    meta,
    taxonomies,
    isHome,
    hideFilter = false,
    hideQuickFilter = true,
  } = props;
  const t = useTranslations();
  const [keywordSearched, setKeywordSearched] = useState<KeywordSearchType[]>(
    [],
  );
  const searchRef = useRef<HTMLInputElement>(null);
  const [search, setSearch] = useState("");
  const [suggestData, setSuggestData] = useState<SuggestedType[]>([]);
  const suggestRef = useRef<HTMLDivElement>(null);
  const [isOpenSuggest, setIsOpenSuggest] = useState(false);
  const [location, setLocation] = useState<LocationType>(LIST_LOCATIONS[0]);
  const [jobLevels, setJobLevels] = useState<TaxonomyType[]>([]);
  const [jobType, setJobType] = useState<TaxonomyType[]>([]);
  const [contractType, setContractType] = useState<TaxonomyType[]>([]);
  const debouncedSuggest = useDebounce<string>(search, 500);

  useEffect(() => {
    if (filters && meta) {
      let searchedArr: KeywordSearchType[] = [];
      const taxonomiesSkill: TaxonomyType[] = meta.skills || [];
      try {
        // init keyword searched in taxonomies
        if (taxonomiesSkill && taxonomiesSkill.length > 0) {
          searchedArr = [
            ...searchedArr,
            ...taxonomiesSkill
              .filter((item) => item.taxonomy === "skills")
              .map((item) => ({
                id: item.id,
                slug: item.slug,
                keyword: item.text,
                taxonomy: item.taxonomy,
              }))
          ];
        }

        // init companies
        const companies: CompanySearchType[] = meta.companies || [];
        if (companies && companies.length > 0) {
          searchedArr = [
            ...searchedArr,
            ...companies
              .map((item) => ({
                id: item.id,
                slug: slugify(item.display_name),
                keyword: item.display_name,
                taxonomy: 'company',
              }))
          ];
        }

        // init location
        if (filters.region_ids) {
          setLocation(
            LIST_LOCATIONS.find(
              (item) => item.id === filters.region_ids,
            ) as LocationType,
          );
        }
        // init job type
        if (filters.job_types_ids) {
          const jobTypeData = filters.job_types_ids.reduce(
            (result: TaxonomyType[], item) => {
              const value = taxonomies.job_types.find((it) => it.id === item);
              if (value) {
                return [...result, value];
              }
              return result;
            },
            [],
          );
          setJobType(jobTypeData);
        }
        // init job level
        if (filters.job_levels_ids) {
          const jobLevelData = filters.job_levels_ids.reduce(
            (result: TaxonomyType[], item) => {
              const value = taxonomies.job_levels.find((it) => it.id === item);
              if (value) {
                return [...result, value];
              }
              return result;
            },
            [],
          );
          setJobLevels(jobLevelData);
        }
        //init contract type
        if (filters.contract_types_ids) {
          const jobContractData = filters.contract_types_ids.reduce(
            (result: TaxonomyType[], item) => {
              const value = taxonomies.contract_types.find(
                (it) => it.id === item,
              );
              if (value) {
                return [...result, value];
              }
              return result;
            },
            [],
          );
          setContractType(jobContractData);
        }
        // init keyword searched not in taxonomies
        if (filters.keyword && filters.keyword.trim()) {
          const keywordArr = filters.keyword.split(",");
          keywordArr.forEach((item) => {
            const removeSlug = item.replace("-", " ");
            searchedArr = [
              ...searchedArr,
              {
                id: fakeIdNotInTaxonomies(),
                slug: toVnSlug(item),
                keyword: removeSlug,
                taxonomy: "skills",
              },
            ];
          });
        }
        setKeywordSearched(searchedArr);
      } catch (error) {}
    }
  }, [filters, meta]);

  useEffect(() => {
    document.addEventListener("click", handleClickDocument);
    return () => {
      document.removeEventListener("click", handleClickDocument);
    };
  }, [isOpenSuggest]);

  useEffect(() => {
    handleSuggestedKeywords();
  }, [debouncedSuggest]);

  const handleClickDocument = (event: globalThis.MouseEvent) => {
    if (
      isOpenSuggest &&
      suggestRef.current &&
      !suggestRef.current.contains(event.target as Node)
    ) {
      setIsOpenSuggest((prev) => !prev);
    }
  };

  const findTaxonomyKeyword = useCallback(
    (slug: string) => {
      return taxonomies["skills"].find((item) => item.slug === slug);
    },
    [taxonomies],
  );

  const handleSuggestedKeywords = async () => {
    if (search) {
      try {
        const data = await getSuggestedKeywords(search);
        setSuggestData(data);
        setIsOpenSuggest(true);
      } catch (error) {
        throw Error("Can not get suggested keywords!");
      }
    } else {
      setIsOpenSuggest(false);
      setSuggestData([]);
    }
  };

  const handleChangeSearch = async (event: ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
  };

  // Check user input is in taxonomies or not
  const getDataFromFiltersWithoutJobLevels = (slug: string) => {
    return (
      taxonomies["job_types"].find((item) => item.slug === slug) ||
      taxonomies["contract_types"].find((item) => item.slug === slug)
    );
  };

  const getDataFromJobLevels = (slug: string) => {
    return taxonomies["job_levels"].find((item) => item.slug === slug);
  };

  // Check if the user input information is in the filter list or not, return a new filter list
  const handleFilterDataFromInput = (
    slug: string,
    initialFilters?: FilterParams,
  ): FilterParams | null => {
    let searchFilters: FilterParams = initialFilters || {
      job_salary: [],
      job_type: jobType,
      job_level: jobLevels,
      job_industry: [],
      job_contract: contractType,
    };
    const dataInJobLevels = getDataFromJobLevels(slug);
    if (dataInJobLevels) {
      return {
        ...searchFilters,
        job_level: [...searchFilters.job_level, dataInJobLevels],
      };
    } else {
      const dataInFiltersWithoutJobLevels =
        getDataFromFiltersWithoutJobLevels(slug);
      if (dataInFiltersWithoutJobLevels) {
        switch (dataInFiltersWithoutJobLevels.taxonomy) {
          case "job_types": {
            return {
              ...searchFilters,
              job_type: [
                ...searchFilters.job_type,
                dataInFiltersWithoutJobLevels,
              ],
            };
          }
          case "contract_types": {
            return {
              ...searchFilters,
              job_contract: [
                ...searchFilters.job_contract,
                dataInFiltersWithoutJobLevels,
              ],
            };
          }
        }
      }
    }
    return null;
  };

  // Process taxonomy from the list of suggestions or keywords entered by the user, returning a new list of keywords and filters
  const handleInputData = async (
    taxonomy?: Pick<TaxonomyType, "id" | "text" | "slug" | "taxonomy">,
  ) => {
    let searchKeywords: KeywordSearchType[] = keywordSearched;
    let searchFilters: FilterParams = {
      job_salary: [],
      job_type: jobType,
      job_level: jobLevels,
      job_industry: [],
      job_contract: contractType,
    };

    if (taxonomy) {
      const filterData = handleFilterDataFromInput(taxonomy.slug, {
        ...searchFilters,
      });
      if (filterData) {
        searchFilters = { ...searchFilters, ...filterData };
      } else {
        // Không nằm trong filter
        searchKeywords = [
          ...keywordSearched,
          {
            id: taxonomy.id,
            keyword: taxonomy.text,
            slug: taxonomy.slug,
            taxonomy: taxonomy.taxonomy,
          },
        ];
      }
    } else {
      // process taxonomy from keywords
      if (search && search.trim()) {
        let dataAppend: KeywordSearchType = {
          id: fakeIdNotInTaxonomies(),
          keyword: search,
          slug: "",
          taxonomy: "skills",
        };

        /**
         * For specific keyword, we need to convert it to an alternative keyword
         * TODO: it's better to have a dictionary to map these keyword
         */
        let searchKeyword = search;
        if (searchKeyword.trim().toLowerCase() == "c++") {
          searchKeyword = "c plus plus";
        }

        const filterData = handleFilterDataFromInput(slugify(searchKeyword), {
          ...searchFilters,
        });

        if (filterData) {
          searchFilters = { ...searchFilters, ...filterData };
        } else {
          const keywordData = await findTaxonomyKeyword(slugify(searchKeyword));
          if (keywordData) {
            dataAppend = {
              ...dataAppend,
              id: keywordData.id,
              keyword: keywordData.text,
              slug: keywordData.slug,
            };
          } else {
            const searchToAppend = toVnSlug(search);
            dataAppend = {
              ...dataAppend,
              slug: searchToAppend,
            };
          }
          searchKeywords = [...keywordSearched, dataAppend];
        }
      }

      setKeywordSearched(searchKeywords);
      searchKeywords = searchKeywords.reduce(
        (result: KeywordSearchType[], item) => {
          const filterData = handleFilterDataFromInput(item.slug, {
            ...searchFilters,
          });
          if (filterData) {
            searchFilters = {
              ...searchFilters,
              ...filterData,
            };
            return result;
          }
          return [...result, item];
        },
        [],
      );
    }
    return {
      searchKeywords,
      searchFilters,
    };
  };

  const handleSubmit = async (taxonomy?: TaxonomyType, isSuggest?: boolean) => {
    try {
      const { searchKeywords, searchFilters } = await handleInputData(taxonomy);
      setSearch("");
      const searchSlug = getResultSearch(
        searchKeywords,
        location,
        searchFilters,
      );

      const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
      const searchUrl = new URL(`${t("slug_it_jobs")}/${searchSlug}`, baseUrl);

      const params = new URLSearchParams();

      // check is home route
      if (isHome) {
        params.append('src', 'topdev_home');
        params.append('medium', isSuggest ? 'suggestkeyword' : 'search');
      }

      searchUrl.search = params.toString();

      document.location.href = searchUrl.toString();
    } catch (error) {}
  };

  const appendKeywordSearched = (
    id: string | number = "",
    keyword: string = "",
    slug: string = "",
    taxonomy: string = "skills",
    focus: boolean = false,
  ) => {
    if ((search && search.trim()) || focus) {
      setKeywordSearched((prev) => {
        return [...prev, { id, keyword, slug: slug.toLowerCase(), taxonomy }];
      });
    }
  };

  const removeKeywordSearched = (id: string | number) => {
    try {
      setKeywordSearched((prev) => prev.filter((item) => item.id !== id));
      if (contractType.length) {
        setContractType((prev) => {
          return prev.filter((item) => item.toString() !== id);
        });
      }
      if (jobLevels.length) {
        setJobLevels((prev) => {
          return prev.filter((item) => item.toString() !== id);
        });
      }
      if (jobType.length) {
        setJobType((prev) => {
          return prev.filter((item) => item.toString() !== id);
        });
      }
    } catch (error) {}
  };

  const handleClearFilter = () => {
    setLocation(LIST_LOCATIONS[0]);
    setJobLevels([]);
    setJobType([]);
    setContractType([]);
  };

  const handleClickInputSearch = () => {
    if (suggestData.length && !isOpenSuggest) {
      setIsOpenSuggest(true);
    }
  };

  const handleClickSuggest = (suggestItem: SuggestedType) => {
    setSearch("");
    appendKeywordSearched(
      suggestItem.id,
      suggestItem.keyword,
      suggestItem.slug,
      suggestItem.taxonomy,
      true,
    );
  };

  return (
    <>
      <form
        className=""
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
        autoComplete="off"
      >
        <div className={`${isHome ? "" : "container"}`}>
          <div className="relative rounded border border-solid border-white bg-white p-2 shadow-sm">
            <div className="flex items-center gap-2">
              <div className="flex flex-1 flex-wrap items-center gap-2 pl-2">
                {keywordSearched.length > 0 && (
                  <ul className="flex flex-wrap items-center gap-2">
                    {keywordSearched.map((item, index) => {
                      return (
                        <li key={index}>
                          <ChipTag
                            accent="neutral-solid"
                            title={item.keyword}
                            tailingIcon={<HiXMark />}
                            isShowMobile={true}
                            onClickIcon={() => {
                              if (item.id) {
                                removeKeywordSearched(item.id);
                              }
                            }}
                          />
                        </li>
                      );
                    })}
                  </ul>
                )}
                <input
                  type="text"
                  name="search"
                  ref={searchRef}
                  id="search"
                  className="w-full min-w-[10rem] flex-1 border-none text-sm outline-none focus:border-none focus:outline-none focus:ring-0 lg:text-base"
                  placeholder={t("main_search_search_placeholder")}
                  onChange={handleChangeSearch}
                  value={search}
                  onClick={handleClickInputSearch}
                />
                {isOpenSuggest && (
                  <div
                    ref={suggestRef}
                    className={classNames(
                      "absolute left-0 top-full z-20 max-h-80 w-full overflow-y-auto overscroll-contain border border-solid border-gray-200 bg-white transition-all",
                    )}
                  >
                    {
                      <ul>
                        {suggestData.map((suggestItem, index) => {
                          return (
                            <li
                              key={index}
                              className="cursor-pointer overscroll-contain border-b border-solid border-gray-200 px-4 py-2.5 transition-all last:border-b-0 hover:bg-gray-200 hover:text-primary"
                              onClick={() => handleClickSuggest(suggestItem)}
                            >
                              {suggestItem.keyword}
                            </li>
                          );
                        })}
                      </ul>
                    }
                  </div>
                )}
              </div>
              <div className="">
                <Button
                  accent="primary"
                  leadingIcon={<HiOutlineSearch />}
                  size="lg"
                  onClick={() => handleSubmit()}
                  gapStyle={isDesktop ? "" : "gap-0"}
                >
                  {t("search_page_search")}
                </Button>
              </div>
            </div>
          </div>

          {!hideFilter && (
            <div className="mt-4 flex gap-4">
              <div className="grid flex-1 grid-cols-4 gap-4">
                <div className="col-span-1">
                  <SelectSingleLocation
                    value={location}
                    placeholder={t("search_page_location")}
                    onSelect={(item) => setLocation(item)}
                    options={LIST_LOCATIONS}
                  />
                </div>
                <div className="col-span-1">
                  <SelectMultipleFilter
                    value={jobLevels}
                    placeholder={t("search_page_all_levels")}
                    onSelect={(item) => setJobLevels(item)}
                    prefix={t("search_page_level")}
                    options={taxonomies.job_levels}
                  />
                </div>
                <div className="col-span-1">
                  <SelectMultipleFilter
                    value={jobType}
                    placeholder={t("search_page_all_job_types")}
                    onSelect={(item) => setJobType(item)}
                    prefix={t("search_page_job_type")}
                    options={taxonomies.job_types}
                  />
                </div>
                <div className="col-span-1">
                  <SelectMultipleFilter
                    value={contractType}
                    placeholder={t("search_page_all_contract_types")}
                    onSelect={(item) => setContractType(item)}
                    prefix={t("search_page_contract_type")}
                    options={taxonomies.contract_types}
                  />
                </div>
              </div>
              <div>
                <div className="col-span-1">
                  <Button
                    onClick={() => handleClearFilter()}
                    accent="secondary"
                    size="md"
                    leadingIcon={<FaFilterCircleXmark />}
                  >
                    {t("search_page_clear_filters")}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
        {/* End Filter */}
      </form>

      {/* Quick filter bar */}
      {!hideQuickFilter && (
        <QuickFilterBar
          onKeywordSelect={(keyword: TaxonomyType) => handleSubmit(keyword, true)}
        />
      )}
      {/* End Quick filter bar */}
    </>
  );
};

export default MainSearch;
