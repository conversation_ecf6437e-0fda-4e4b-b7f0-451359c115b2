"use client";

import { onSearchClick } from "@/hooks/useSearch";
import { useAppDispatch, useAppSelector } from "@/store";
import { clearSearch, resetSearch } from "@/store/slices/searchSlice";
import { classNames } from "@/utils";
import React from "react";
import { HiArchive, HiOutlineX } from "react-icons/hi";
import FilterModalBenefit from "./FilterModal/FilterModalBenefit";
import FilterModalCompanyIndustry from "./FilterModal/FilterModalCompanyIndustry";
import FilterModalCompanySize from "./FilterModal/FilterModalCompanySize";
import FilterModalContractType from "./FilterModal/FilterModalContractType";
import FilterModalExperienceLevel from "./FilterModal/FilterModalExperienceLevel";
import FilterModalSalary from "./FilterModal/FilterModalSalary";
import FilterModalWorkType from "./FilterModal/FilterModalWorkType";
import { useTranslations } from "next-intl";

const Button = ({ onClick }: { onClick: () => void }) => {
  const t = useTranslations();
  return (
    <button
      onClick={onClick}
      className="flex h-8 items-center justify-center rounded-full border border-[#DD3F24] bg-[#DD3F24] text-white"
    >
      <div className="relative flex h-[22px] items-center gap-4 px-4 lg:px-8">
        <HiArchive />
        <span className="hidden lg:inline-block">
          {t("search_page_filters")}
        </span>
      </div>
    </button>
  );
};

const Modal = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const searchState = useAppSelector((state) => state.search.formSearch);
  const dispatch = useAppDispatch();
  const t = useTranslations();

  return (
    <div
      className={classNames(
        "xs:h-full xs:left-0 fixed top-0 z-[999999] w-full rounded bg-white p-8 shadow-sm lg:right-10 lg:top-10 lg:w-[500px]",
        isOpen ? "" : "hidden",
      )}
    >
      <div className="flex items-center justify-between">
        <div className="text-2xl text-[#DD3F24]">
          {t("search_page_filters")}
        </div>
        <HiOutlineX
          className="cursor-pointer"
          onClick={() => {
            dispatch(resetSearch());
            onClose();
          }}
        />
      </div>

      <div className="flex max-h-[70vh] flex-col gap-4 overflow-y-scroll">
        <FilterModalSalary />
        <FilterModalBenefit />
        <FilterModalExperienceLevel />
        <FilterModalWorkType />
        <FilterModalContractType />
        <FilterModalCompanySize />
        <FilterModalCompanyIndustry />
      </div>

      <div className="button-container mt-4 flex justify-end gap-2">
        <button
          className="rounded bg-[#E7E7E7] px-6 py-2 text-sm text-[#B0B0B0]"
          onClick={() => {
            dispatch(clearSearch());
          }}
        >
          {t("search_page_clear_filters")}
        </button>
        <button
          className="rounded bg-[#DD3F24] px-6 py-2 text-sm text-white"
          onClick={() => onSearchClick(searchState)}
        >
          {t("search_page_apply")}
        </button>
      </div>
    </div>
  );
};

export default function SearchAllFilter() {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <>
      <Button onClick={() => setIsOpen(!isOpen)} />
      <Modal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
}
