"use client";
import React, { FC, useEffect, useRef, useState } from "react";
import { useTranslations } from "next-intl";

const SearchTitleAnimation: FC<{ initData?: string[] }> = ({
  initData = [],
}) => {
  const t = useTranslations();
  const [data, setData] = useState(["ReactJS", "PHP", "Javascript", "Laravel"]);
  const [currentData, setCurrentData] = useState(0);
  const textRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setCurrentData((prev) => {
        if (prev + 1 >= data?.length) {
          return 0;
        }
        return prev + 1;
      });
    }, 3000);
    return () => {
      clearTimeout(timeout);
    };
  }, [currentData, data]);

  useEffect(() => {
    if (initData.length == 0) {
      return;
    }

    setData(initData);
  }, [initData]);

  return (
    <div className="flex items-center gap-1 text-2xl font-semibold">
      <p>{t("main_search_search_for")}</p>
      <div className="bg-primary px-2 py-2 text-white" ref={textRef}>
        {data[currentData]}
      </div>
    </div>
  );
};

export default SearchTitleAnimation;
