"use client";
import { Button } from "@/components/Button";
import { useHeaderContext } from "@/components/Common/mobiles/HeaderSearchContext";
import { TaxonomyType } from "@/types/taxonomy";
import { classNames } from "@/utils";
import { useTranslations } from "next-intl";
import { FC, useRef } from "react";
import { HiOutlineChevronLeft } from "react-icons/hi2";
import { useOnClickOutside } from "usehooks-ts";

interface Props {
  value: TaxonomyType[];
  options: TaxonomyType[];
  isOpen: boolean;
  onClose: () => void;
  onSelect: (value: TaxonomyType[]) => void;
  onBack: () => void;
  filterTitle: string;
}

const MultipleFilterMobile: FC<Props> = (props) => {
  const { value, options, isOpen, onClose, onSelect, onBack } = props;
  const t = useTranslations();
  const locationRef = useRef<HTMLDivElement>(null);
  const { dispatch } = useHeaderContext();

  const handleClickOutside = () => {
    onClose();
  };

  useOnClickOutside(locationRef, handleClickOutside);

  const checkSelected = (item: TaxonomyType) => {
    return !!value.find((selectedItem) => selectedItem.id === item.id);
  };

  const handleSelectItem = (item: TaxonomyType) => {
    const isExisted = !!value.find(
      (selectedItem) => selectedItem.id === item.id,
    );
    if (isExisted) {
      onSelect(value.filter((selectedItem) => selectedItem.id !== item.id));
    } else {
      onSelect([...value, item]);
    }
  };

  return (
    <div
      className={classNames(
        "fixed left-0 top-0 z-[9999] h-screen w-full bg-gray-200/50 transition-all",
        isOpen ? "visible opacity-100" : "invisible opacity-0",
      )}
    >
      <div
        className={classNames(
          "fixed bottom-0 left-0 w-full overflow-hidden rounded-tl-xl rounded-tr-xl bg-white p-4 transition-all",
          isOpen ? "translate-y-0" : "translate-y-full",
        )}
        ref={locationRef}
      >
        <div className="flex items-center gap-4 py-4">
          <button
            className="flex h-6 w-6 items-center justify-center text-xl text-gray-600"
            type="button"
            onClick={() => onBack()}
          >
            <HiOutlineChevronLeft />
          </button>
          <h3 className="text-xl font-bold">{props.filterTitle}</h3>
        </div>
        <ul>
          {options.map((item, index) => {
            return (
              <li key={item.id} onClick={() => handleSelectItem(item)}>
                <div className="flex items-center justify-between px-2 py-4 transition-all hover:bg-gray-100">
                  <p className="line-clamp-1 flex-1 text-sm">{item.text}</p>
                  <input
                    type="checkbox"
                    className="h-5 w-5 rounded outline-none ring-0 checked:bg-primary checked:text-primary checked:accent-primary focus:border-none focus:shadow-transparent focus:outline-none focus:ring-0"
                    checked={checkSelected(item)}
                    readOnly
                  />
                </div>
              </li>
            );
          })}
        </ul>
        <hr className="my-4" />
        <div>
          <Button
            accent="secondary"
            type="button"
            isBlock
            onClick={() =>
              dispatch({ type: "UPDATE_FIRE_SUBMIT", payload: true })
            }
          >
            {t("search_page_show_results")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MultipleFilterMobile;
