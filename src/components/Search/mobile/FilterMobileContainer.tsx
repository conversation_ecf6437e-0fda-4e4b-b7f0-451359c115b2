"use client";
import { useHeaderContext } from "@/components/Common/mobiles/HeaderSearchContext";
import { TaxonomiesType } from "@/types/taxonomy";
import { LIST_LOCATIONS } from "@/utils/enums";
import dynamic from "next/dynamic";
import { FC } from "react";
import FilterGroupMobile from "./FilterGroupMobile";
import { useTranslations } from "next-intl";
const FilterLocationMobile = dynamic(() => import("./FilterLocationMobile"), {
  ssr: false,
});
const MultipleFilterMobile = dynamic(() => import("./MultipleFilterMobile"), {
  ssr: false,
});

const FilterMobileContainer: FC<{ taxonomies: TaxonomiesType }> = ({
  taxonomies,
}) => {
  const { state, dispatch } = useHeaderContext();
  const t = useTranslations();

  return (
    <>
      {/* Filter group */}
      <FilterGroupMobile
        onClose={() =>
          dispatch({
            type: "UPDATE_FILTER_GROUP",
            payload: { ...state.filterGroup, isOpen: false },
          })
        }
        title={t("search_page_filters")}
      />
      {/* Location */}
      <FilterLocationMobile
        isOpen={state.locationState.isOpen}
        onClose={() =>
          dispatch({
            type: "UPDATE_LOCATION",
            payload: { ...state.locationState, isOpen: false },
          })
        }
        value={state.locationState.value}
        onSelect={(value) =>
          dispatch({
            type: "UPDATE_LOCATION",
            payload: { ...state.locationState, value },
          })
        }
        options={LIST_LOCATIONS}
        filterTitle={t("search_page_location")}
        onBack={() => {
          dispatch({ type: "CLOSE_FILTER" });
          dispatch({
            type: "UPDATE_FILTER_GROUP",
            payload: { ...state.filterGroup, isOpen: true },
          });
        }}
      />
      {/* Job level */}
      <MultipleFilterMobile
        isOpen={state.jobLevelState.isOpen}
        onClose={() =>
          dispatch({
            type: "UPDATE_JOB_LEVEL",
            payload: { ...state.jobLevelState, isOpen: false },
          })
        }
        value={state.jobLevelState.value}
        onSelect={(value) =>
          dispatch({
            type: "UPDATE_JOB_LEVEL",
            payload: { ...state.jobLevelState, value },
          })
        }
        options={taxonomies.job_levels}
        filterTitle={t("search_page_level")}
        onBack={() => {
          dispatch({ type: "CLOSE_FILTER" });
          dispatch({
            type: "UPDATE_FILTER_GROUP",
            payload: { ...state.filterGroup, isOpen: true },
          });
        }}
      />
      {/* Job type */}
      <MultipleFilterMobile
        isOpen={state.jobTypeState.isOpen}
        onClose={() =>
          dispatch({
            type: "UPDATE_JOB_TYPE",
            payload: { ...state.jobTypeState, isOpen: false },
          })
        }
        value={state.jobTypeState.value}
        onSelect={(value) =>
          dispatch({
            type: "UPDATE_JOB_TYPE",
            payload: { ...state.jobTypeState, value },
          })
        }
        options={taxonomies.job_types}
        filterTitle={t("search_page_job_type")}
        onBack={() => {
          dispatch({ type: "CLOSE_FILTER" });
          dispatch({
            type: "UPDATE_FILTER_GROUP",
            payload: { ...state.filterGroup, isOpen: true },
          });
        }}
      />
      {/* Contract type */}
      <MultipleFilterMobile
        isOpen={state.contractTypeState.isOpen}
        onClose={() =>
          dispatch({
            type: "UPDATE_CONTRACT_TYPE",
            payload: { ...state.contractTypeState, isOpen: false },
          })
        }
        value={state.contractTypeState.value}
        onSelect={(value) =>
          dispatch({
            type: "UPDATE_CONTRACT_TYPE",
            payload: { ...state.contractTypeState, value },
          })
        }
        options={taxonomies.contract_types}
        filterTitle={t("search_page_contract_type")}
        onBack={() => {
          dispatch({ type: "CLOSE_FILTER" });
          dispatch({
            type: "UPDATE_FILTER_GROUP",
            payload: { ...state.filterGroup, isOpen: true },
          });
        }}
      />
    </>
  );
};

export default FilterMobileContainer;
