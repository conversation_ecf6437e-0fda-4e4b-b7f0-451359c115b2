"use client";
import { Button } from "@/components/Button";
import { useHeaderContext } from "@/components/Common/mobiles/HeaderSearchContext";
import { LocationType } from "@/types/search";
import { classNames } from "@/utils";
import { getCurrentLocale } from "@/utils/locale";
import { useTranslations } from "next-intl";
import { FC, useRef } from "react";
import { HiOutlineChevronLeft } from "react-icons/hi2";
import { useOnClickOutside } from "usehooks-ts";

interface Props {
  value: LocationType;
  options: LocationType[];
  isOpen: boolean;
  onClose: () => void;
  onSelect: (value: LocationType) => void;
  onBack: () => void;
  filterTitle: string;
}

const FilterLocationMobile: FC<Props> = (props) => {
  const { value, options, isOpen, onClose, onSelect, onBack } = props;
  const t = useTranslations();
  const locationRef = useRef<HTMLDivElement>(null);
  const { dispatch } = useHeaderContext();

  const handleClickOutside = () => {
    onClose();
  };

  useOnClickOutside(locationRef, handleClickOutside);
  return (
    <div
      className={classNames(
        "fixed left-0 top-0 z-[9999] h-screen w-full bg-gray-200/50 transition-all",
        isOpen ? "visible opacity-100" : "invisible opacity-0",
      )}
    >
      <div
        className={classNames(
          "fixed bottom-0 left-0 w-full overflow-hidden rounded-tl-xl rounded-tr-xl bg-white p-4 transition-all",
          isOpen ? "translate-y-0" : "translate-y-full",
        )}
        ref={locationRef}
      >
        <div className="flex items-center gap-4 py-4">
          <button
            className="flex h-6 w-6 items-center justify-center text-xl text-gray-600"
            type="button"
            onClick={() => onBack()}
          >
            <HiOutlineChevronLeft />
          </button>
          <h3 className="text-xl font-bold">{props.filterTitle}</h3>
        </div>
        <ul>
          {options.map((item, index) => {
            return (
              <li onClick={() => onSelect(item)} key={item.id}>
                <div className="flex items-center justify-between px-2 py-4 transition-all hover:bg-gray-100">
                  <p className="line-clamp-1 flex-1 text-sm">
                    {getCurrentLocale() === "vi" ? item.text_vi : item.text_en}
                  </p>
                  <input
                    type="radio"
                    readOnly
                    checked={value?.id === item.id}
                    className="h-4 w-4 rounded-full checked:bg-primary checked:accent-primary checked:hover:border-none checked:hover:bg-primary checked:hover:outline-none checked:hover:ring-0 focus:border-none focus:bg-primary focus:outline-none"
                  />
                </div>
              </li>
            );
          })}
        </ul>
        <hr className="my-4" />
        <div>
          <Button
            accent="secondary"
            type="button"
            isBlock
            onClick={() =>
              dispatch({ type: "UPDATE_FIRE_SUBMIT", payload: true })
            }
          >
            {t("search_page_show_results")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FilterLocationMobile;
