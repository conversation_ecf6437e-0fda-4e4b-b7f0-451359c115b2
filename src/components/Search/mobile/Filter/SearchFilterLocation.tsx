"use client";

import React from "react";
import SearchFilterCheckboxSelect from "../../SearchFilterCheckboxSelect";
import { useAppSelector } from "@/store";

export default function SearchFilterLocation() {
  const locationValues = useAppSelector(
    (state) => state.search.formSearch.locations,
  );
  const locations = [
    { value: "-1", text: "All locations" },
    { value: "79", text: "Ho Chi Minh" },
    { value: "01", text: "Ha Noi" },
    { value: "48", text: "Da Nang" },
  ];

  return (
    <SearchFilterCheckboxSelect
      label="Locations"
      options={locations}
      name="location"
      values={locationValues}
      onChange={() => {}}
    />
  );
}
