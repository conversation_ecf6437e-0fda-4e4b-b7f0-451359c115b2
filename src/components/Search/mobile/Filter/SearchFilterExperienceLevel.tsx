"use client";

import React, { useMemo } from "react";
import SearchFilterRadioSelect from "../../SearchFilterRadioSelect";
import { useAppSelector } from "@/store";
import { onSearchClick } from "@/hooks/useSearch";

export default function SearchFilterExperienceLevel() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const jobLevels = useMemo(
    () =>
      taxonomies?.job_levels.map((item) => {
        return { value: item.id + "", text: item.text };
      }),
    [taxonomies],
  );
  const experienceLevels = useAppSelector((state) => state.search.formSearch.experience_levels);
  const searchState = useAppSelector((state) => state.search.formSearch);

  return (
    <SearchFilterRadioSelect
      label={experienceLevels.length > 0 ? taxonomies?.job_levels.find((e) => e.id === parseInt(experienceLevels[0]))?.text + "" : "Experience Level"}
      options={jobLevels}
      name="location"
      values={experienceLevels}
      onChange={(event) => {
      }}
      onReset={() => {
      }}
    />
  );
}
