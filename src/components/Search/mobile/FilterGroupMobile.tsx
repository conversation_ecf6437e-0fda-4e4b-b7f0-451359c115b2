"use client";
import { Button } from "@/components/Button";
import { useHeaderContext } from "@/components/Common/mobiles/HeaderSearchContext";
import { classNames } from "@/utils";
import { isEnglish } from "@/utils/locale";
import { useTranslations } from "next-intl";
import { FC, useRef } from "react";
import { useOnClickOutside } from "usehooks-ts";

interface Props {
  onClose: () => void;
  title: string;
}

const FilterGroupMobile: FC<Props> = (props) => {
  const t = useTranslations();
  const { onClose, title } = props;
  const {
    state: {
      filterGroup: { isOpen },
      contractTypeState,
      jobLevelState,
      jobTypeState,
      locationState,
      filterClosed,
    },
    dispatch,
  } = useHeaderContext();
  const filterGroupRef = useRef<HTMLDivElement>(null);

  const handleClickOutside = () => {
    if (!filterClosed) {
      onClose();
    }
  };

  useOnClickOutside(filterGroupRef, handleClickOutside);
  return (
    <div
      className={classNames(
        "fixed left-0 top-0 z-[9998] h-screen w-full bg-gray-200/50 transition-all",
        isOpen ? "visible opacity-100" : "invisible opacity-0",
      )}
    >
      <div
        className={classNames(
          "fixed bottom-0 left-0 w-full overflow-hidden rounded-tl-xl rounded-tr-xl bg-white p-4 transition-all",
          isOpen ? "translate-y-0" : "translate-y-full",
        )}
        ref={filterGroupRef}
      >
        <div className="flex items-center justify-between gap-4 py-4">
          <h3 className="text-xl font-bold">{title}</h3>
          <button
            onClick={() => dispatch({ type: "CLEAR_FILTER" })}
            type="button"
            className="flex h-8 items-center justify-center px-4"
          >
            {t("search_page_reset_filter")}
          </button>
        </div>
        <ul>
          <li>
            <div
              className="flex items-center justify-between px-2 py-4"
              onClick={() =>
                dispatch({
                  type: "UPDATE_LOCATION",
                  payload: { ...locationState, isOpen: true },
                })
              }
            >
              <p>{t("search_page_location")}</p>
              <p className="text-gray-400">
                {isEnglish()
                  ? locationState.value.text_en
                  : locationState.value.text}
              </p>
            </div>
          </li>
          <li>
            <div
              className="flex items-center justify-between px-2 py-4"
              onClick={() =>
                dispatch({
                  type: "UPDATE_JOB_LEVEL",
                  payload: { ...jobLevelState, isOpen: true },
                })
              }
            >
              <p>{t("search_page_level")}</p>
              <p className="text-gray-400">
                {jobLevelState.value.length
                  ? jobLevelState.value.length === 1
                    ? jobLevelState.value[0].text
                    : `${t("search_page_level")} (+${
                        jobLevelState.value.length - 1
                      })`
                  : t("search_page_all_levels")}
              </p>
            </div>
          </li>
          <li>
            <div
              className="flex items-center justify-between px-2 py-4"
              onClick={() =>
                dispatch({
                  type: "UPDATE_JOB_TYPE",
                  payload: { ...jobTypeState, isOpen: true },
                })
              }
            >
              <p>{t("search_page_job_type")}</p>
              <p className="text-gray-400">
                {jobTypeState.value.length
                  ? jobTypeState.value.length === 1
                    ? jobTypeState.value[0].text
                    : `${t("search_page_job_type")} (+${
                        jobTypeState.value.length - 1
                      })`
                  : t("search_page_all_job_types")}
              </p>
            </div>
          </li>
          <li>
            <div
              className="flex items-center justify-between px-2 py-4"
              onClick={() =>
                dispatch({
                  type: "UPDATE_CONTRACT_TYPE",
                  payload: { ...contractTypeState, isOpen: true },
                })
              }
            >
              <p>{t("search_page_contract_type")}</p>
              <p className="text-gray-400">
                {contractTypeState.value.length
                  ? contractTypeState.value.length === 1
                    ? contractTypeState.value[0].text
                    : `${t("search_page_contract_type")} (+${
                        contractTypeState.value.length - 1
                      })`
                  : t("search_page_all_contract_types")}
              </p>
            </div>
          </li>
        </ul>
        <hr className="my-4" />
        <div>
          <Button
            accent="secondary"
            type="button"
            isBlock
            onClick={() =>
              dispatch({ type: "UPDATE_FIRE_SUBMIT", payload: true })
            }
          >
            {t("search_page_show_results")}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FilterGroupMobile;
