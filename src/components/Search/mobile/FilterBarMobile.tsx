"use client";
import { useHeaderContext } from "@/components/Common/mobiles/HeaderSearchContext";
import { classNames } from "@/utils";
import { isEnglish } from "@/utils/locale";
import { useTranslations } from "next-intl";
import { FaCaretDown } from "react-icons/fa";
import { HiX } from "react-icons/hi";
import { HiAdjustmentsHorizontal } from "react-icons/hi2";

const FilterBarMobile = () => {
  const t = useTranslations();
  const {
    state: {
      contractTypeState,
      jobLevelState,
      jobTypeState,
      locationState,
      filterGroup,
    },
    dispatch,
  } = useHeaderContext();

  const getTotalFilter = () => {
    const totalFilter = [
      locationState,
      contractTypeState,
      jobLevelState,
      jobTypeState,
    ].filter((item) => {
      if ("text" in item.value) {
        return item.value.slug;
      } else {
        return item.value.length > 0;
      }
    }).length;
    return totalFilter;
  };

  return (
    <div className="flex items-center gap-2 overflow-x-auto py-4 scrollbar-none">
      <button
        type="button"
        className={classNames(
          "inline-flex h-8 items-center gap-2 whitespace-nowrap rounded bg-gray-200 px-2 py-1.5 text-sm text-gray-600 transition-all lg:text-base",
        )}
        onClick={() =>
          dispatch({
            type: "UPDATE_FILTER_GROUP",
            payload: { ...filterGroup, isOpen: true },
          })
        }
      >
        <span
          className={classNames(
            "inline-flex items-center justify-center",
            getTotalFilter()
              ? "h-[1.125rem] w-[1.125rem] rounded bg-gray-600 text-sm text-white"
              : "h-4 w-4 text-base",
          )}
        >
          {getTotalFilter() ? getTotalFilter() : <HiAdjustmentsHorizontal />}
        </span>
        <span>{t("search_page_filters")}</span>
      </button>
      <button
        onClick={() =>
          dispatch({
            type: "UPDATE_LOCATION",
            payload: { ...locationState, isOpen: true },
          })
        }
        type="button"
        className={classNames(
          "inline-flex h-8 items-center gap-2 whitespace-nowrap rounded px-2 py-1.5 text-sm text-gray-600 transition-all lg:text-base",
          locationState.value.slug ? "bg-gray-600 text-white" : "bg-gray-200",
        )}
      >
        <span>
          {locationState.value.slug
            ? isEnglish()
              ? locationState.value.text_en
              : locationState.value.text_vi
            : t("search_page_location")}
        </span>
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            locationState.isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </button>
      <button
        onClick={() =>
          dispatch({
            type: "UPDATE_JOB_LEVEL",
            payload: { ...jobLevelState, isOpen: true },
          })
        }
        type="button"
        className={classNames(
          "inline-flex h-8 items-center gap-2 whitespace-nowrap rounded px-2 py-1.5 text-sm text-gray-600 transition-all lg:text-base",
          jobLevelState.value.length ? "bg-gray-600 text-white" : "bg-gray-200",
        )}
      >
        {jobLevelState.value.length > 0 ? (
          <span className="inline-flex gap-1">
            <span>{t("search_page_level")}</span>
            <span>(+{jobLevelState.value.length})</span>
          </span>
        ) : (
          <>
            <span>{t("search_page_all_levels")}</span>
          </>
        )}
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            jobLevelState.isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </button>
      <button
        onClick={() =>
          dispatch({
            type: "UPDATE_JOB_TYPE",
            payload: { ...jobTypeState, isOpen: true },
          })
        }
        type="button"
        className={classNames(
          "inline-flex h-8 items-center gap-2 whitespace-nowrap rounded px-2 py-1.5 text-sm text-gray-600 transition-all lg:text-base",
          jobTypeState.value.length ? "bg-gray-600 text-white" : "bg-gray-200",
        )}
      >
        {jobTypeState.value.length > 0 ? (
          <span className="inline-flex gap-1">
            <span>{t("search_page_job_type")}</span>
            <span>(+{jobTypeState.value.length})</span>
          </span>
        ) : (
          <>
            <span>{t("search_page_all_job_types")}</span>
          </>
        )}
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            jobTypeState.isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </button>
      <button
        onClick={() =>
          dispatch({
            type: "UPDATE_CONTRACT_TYPE",
            payload: { ...contractTypeState, isOpen: true },
          })
        }
        type="button"
        className={classNames(
          "inline-flex h-8 items-center gap-2 whitespace-nowrap rounded px-2 py-1.5 text-sm text-gray-600 transition-all lg:text-base",
          contractTypeState.value.length
            ? "bg-gray-600 text-white"
            : "bg-gray-200",
        )}
      >
        {contractTypeState.value.length > 0 ? (
          <span className="inline-flex gap-1">
            <span>{t("search_page_contract_type")}</span>
            <span>(+{contractTypeState.value.length})</span>
          </span>
        ) : (
          <>
            <span>{t("search_page_all_contract_types")}</span>
          </>
        )}
        <span
          className={classNames(
            "inline-flex h-6 w-6 items-center justify-center transition-all ease-out",
            contractTypeState.isOpen ? "rotate-180" : "rotate-0",
          )}
        >
          <FaCaretDown />
        </span>
      </button>
      <button
        onClick={() => dispatch({ type: "CLEAR_FILTER" })}
        type="button"
        className={classNames(
          "inline-flex h-8 items-center gap-2 whitespace-nowrap rounded bg-gray-200 px-2 py-1.5 text-sm text-gray-600 lg:text-base",
        )}
      >
        <span>{t("search_page_reset_filter")}</span>
        <span className="inline-flex h-4 w-4 items-center justify-center text-base">
          <HiX />
        </span>
      </button>
    </div>
  );
};

export default FilterBarMobile;
