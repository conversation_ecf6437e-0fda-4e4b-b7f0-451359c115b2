import { useOutsideClick } from "@/hooks/useOutsideClick";
import { classNames } from "@/utils";
import React, { useRef } from "react";
import { RiArrowDownSFill } from "react-icons/ri";

export default function SearchFilterCheckboxSelect({
  label,
  icon,
  options,
  name,
  onChange,
  onReset,
  values,
}: {
  label?: string;
  icon?: JSX.Element;
  options?: { value: string | number; text: string }[];
  name?: string;
  onChange(event: React.ChangeEvent<HTMLInputElement>): void;
  onReset?(event: React.ChangeEvent<HTMLInputElement>): void;
  values?: string[];
}) {
  const [isOpen, setIsOpen] = React.useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  useOutsideClick(dropdownRef, () => {
    setIsOpen(false);
  });

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex h-8 items-center justify-center rounded-full border border-[#DD3F24] bg-white text-[#DD3F24]"
      >
        <div className="flex h-[22px] items-center gap-1 px-1 pr-2">
          {icon ? <span className="ml-2">{icon}</span> : ""}
          {values && values?.length == 0 ? (
            <span className="flex-grow text-xs lg:text-base">{label}</span>
          ) : (
            ""
          )}
          {values && values?.length == 1 ? (
            <span
              className={classNames(
                "flex-grow rounded-full px-2 text-xs lg:text-base",
                values?.length ?? 0 > 0 ? "bg-[#FEF4F2]" : "",
              )}
            >
              {label}
            </span>
          ) : (
            ""
          )}
          {values && values?.length > 1 ? (
            <>
              <span
                className={classNames(
                  "flex-grow rounded-full px-2 text-xs lg:text-base",
                  values?.length ?? 0 > 0 ? "bg-[#FEF4F2]" : "",
                )}
              >
                {label}
              </span>
              <span
                className={classNames(
                  "flex-grow rounded-full px-2 text-xs lg:text-base",
                  values?.length ?? 0 > 0 ? "bg-[#FEF4F2]" : "",
                )}
              >
                +{values?.length - 1}
              </span>
            </>
          ) : (
            ""
          )}
          {values && values?.length > 0 ? (
            <button
              className="rounded px-1 text-xs hover:bg-slate-200"
              onClick={(e) => {
                setIsOpen(false);

                onReset && onReset(e as any);
              }}
            >
              x
            </button>
          ) : (
            <RiArrowDownSFill
              color="#DD3F24"
              className={classNames(isOpen ? "rotate-180" : "")}
            />
          )}
        </div>
      </button>

      <ul
        className={classNames(
          "absolute left-0 top-9 z-50 w-56 flex-col gap-2 rounded bg-white py-3 shadow-sm",
          isOpen ? "flex" : "hidden",
        )}
      >
        {options?.map((option) => (
          <li key={option.value}>
            <label className="flex items-center gap-2 px-4 py-2 hover:cursor-pointer hover:bg-gray-100">
              <input
                type="checkbox"
                name={name}
                className="h-5 w-5 rounded outline-none ring-0 checked:bg-primary checked:text-primary checked:accent-primary focus:border-none focus:shadow-transparent focus:outline-none focus:ring-0"
                value={option.value}
                onChange={onChange}
                checked={values?.includes(option.value + "")}
              />
              <span>{option.text}</span>
            </label>
          </li>
        ))}
      </ul>
    </div>
  );
}
