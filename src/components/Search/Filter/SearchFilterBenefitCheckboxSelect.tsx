import { useAppDispatch, useAppSelector } from "@/store";
import { setBenefits } from "@/store/slices/searchSlice";
import { useLocale, useTranslations } from "next-intl";
import { useMemo } from "react";
import { FaGift } from "react-icons/fa6";
import SearchFilterCheckboxSelect from "../SearchFilterCheckboxSelect";

export default function SearchFilterBenefitCheckboxSelect() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const t = useTranslations();
  const dispatch = useAppDispatch();
  const selectedBenefits = useAppSelector(
    (state) => state.search.formSearch.benefits,
  );
  const locale = useLocale();

  const benefits = useMemo(
    () =>
      taxonomies?.benefits.map((item) => {
        return {
          value: item.id + "",
          text: locale === "vi" ? item.text_vi : item.text_en,
        };
      }),
    [taxonomies, locale],
  );

  return (
    <SearchFilterCheckboxSelect
      icon={
        selectedBenefits.length == 0 ? <FaGift color="#DD3F24" /> : undefined
      }
      label={
        selectedBenefits.length > 0
          ? taxonomies?.benefits.find(
              (e) => e.id === parseInt(selectedBenefits[0]),
            )?.text + ""
          : t("company_benefits")
      }
      options={benefits}
      name="benefit"
      values={selectedBenefits}
      onReset={(event) => {
        dispatch(setBenefits([]));
      }}
      onChange={(event) => {
        if (selectedBenefits.includes(event.target.value)) {
          return dispatch(
            setBenefits(
              selectedBenefits.filter((item) => item !== event.target.value),
            ),
          );
        }

        dispatch(setBenefits([...selectedBenefits, event.target.value]));
      }}
    />
  );
}
