import { useAppDispatch, useAppSelector } from "@/store";
import { setExperienceLevels } from "@/store/slices/searchSlice";
import { useMemo } from "react";
import { HiChartBar } from "react-icons/hi2";
import SearchFilterRadioSelect from "../SearchFilterRadioSelect";
import { useLocale, useTranslations } from "next-intl";

export default function SearchFilterExperienceLevelRadioSelect() {
  const dispatch = useAppDispatch();
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const locale = useLocale();

  const t = useTranslations();
  const jobLevels = useMemo(
    () =>
      taxonomies?.job_levels.map((item) => {
        return {
          value: item.id + "",
          text: locale === "vi" ? item.text_vi : item.text_en,
        };
      }),
    [taxonomies, locale],
  );
  const experienceLevels = useAppSelector(
    (state) => state.search.formSearch.experience_levels,
  );

  return (
    <SearchFilterRadioSelect
      label={
        experienceLevels.length > 0
          ? taxonomies?.job_levels.find(
              (e) => e.id === parseInt(experienceLevels[0]),
            )?.text + ""
          : t("search_page_all_levels")
      }
      icon={
        experienceLevels.length == 0 ? (
          <HiChartBar color="#DD3F24" />
        ) : undefined
      }
      options={jobLevels}
      name="job_level"
      onChange={(event) => {
        dispatch(setExperienceLevels([event.target.value]));
      }}
      onReset={(event) => {
        dispatch(setExperienceLevels([]));
      }}
      values={experienceLevels}
    />
  );
}
