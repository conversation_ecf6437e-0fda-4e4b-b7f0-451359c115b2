import { useAppDispatch, useAppSelector } from "@/store";
import { setWorkTypes } from "@/store/slices/searchSlice";
import { useLocale, useTranslations } from "next-intl";
import { useMemo } from "react";
import { BsPersonWorkspace } from "react-icons/bs";
import SearchFilterRadioSelect from "../SearchFilterRadioSelect";

export default function SearchFilterWorkTypeRadioSelect() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const dispatch = useAppDispatch();
  const t = useTranslations();
  const workTypes = useAppSelector(
    (state) => state.search.formSearch.work_types,
  );
  const locale = useLocale();
  const jobTypes = useMemo(
    () =>
      taxonomies?.job_types.map((item) => {
        return {
          value: item.id + "",
          text: locale === "vi" ? item.text_vi : item.text_en,
        };
      }),
    [taxonomies, locale],
  );

  return (
    <SearchFilterRadioSelect
      icon={
        workTypes.length == 0 ? (
          <BsPersonWorkspace color="#DD3F24" />
        ) : undefined
      }
      label={
        workTypes.length > 0
          ? taxonomies?.job_types.find((e) => e.id === parseInt(workTypes[0]))
              ?.text + ""
          : t("search_page_work_type")
      }
      options={jobTypes}
      name="job_type"
      onChange={(event) => {
        dispatch(setWorkTypes([event.target.value]));
      }}
      onReset={(event) => {
        dispatch(setWorkTypes([]));
      }}
      values={workTypes}
    />
  );
}
