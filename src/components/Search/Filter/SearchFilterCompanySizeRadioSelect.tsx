import { useAppDispatch, useAppSelector } from "@/store";
import { setCompanySizes } from "@/store/slices/searchSlice";
import { useLocale, useTranslations } from "next-intl";
import { useMemo } from "react";
import { HiBuildingOffice2 } from "react-icons/hi2";
import SearchFilterRadioSelect from "../SearchFilterRadioSelect";

export default function SearchFilterCompanySizeRadioSelect() {
  const taxonomies = useAppSelector((state) => state.taxonomies);
  const dispatch = useAppDispatch();
  const companySizesValue = useAppSelector(
    (state) => state.search.formSearch.company_sizes,
  );
  const t = useTranslations();
  const locale = useLocale();
  const companySizes = useMemo(
    () =>
      taxonomies?.num_employees.map((item) => {
        return {
          value: item.id + "",
          text: locale === "vi" ? item.text_vi : item.text_en,
        };
      }),
    [taxonomies, locale],
  );

  return (
    <SearchFilterRadioSelect
      label={
        companySizesValue.length > 0
          ? taxonomies?.num_employees.find(
              (e) => e.id === parseInt(companySizesValue[0]),
            )?.text + ""
          : t("search_page_company_size")
      }
      icon={
        companySizesValue.length == 0 ? (
          <HiBuildingOffice2 color="#DD3F24" />
        ) : undefined
      }
      options={companySizes}
      name="company_size"
      onChange={(event) => {
        dispatch(setCompanySizes([event.target.value]));
      }}
      onReset={(event) => {
        dispatch(setCompanySizes([]));
      }}
      values={companySizesValue}
    />
  );
}
