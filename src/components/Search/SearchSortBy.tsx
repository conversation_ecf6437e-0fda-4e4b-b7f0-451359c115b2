"use client";

import { onSearchClick } from "@/hooks/useSearch";
import { useAppDispatch, useAppSelector } from "@/store";
import { setSortBy } from "@/store/slices/searchSlice";
import { classNames } from "@/utils";
import React from "react";

const Button = ({ label, onClick, isActive }: { label: string; isActive: boolean; onClick?: () => void }) => {
  return (
    <button
      onClick={onClick}
      className={classNames(
        "shadow-xs whitespace-nowrap rounded-full bg-white px-6 py-1 text-sm",
        isActive ? "border border-[#DD3F24] text-[#DD3F24]" : "",
      )}
    >
      {label}
    </button>
  );
};

export default function SearchSortBy() {
  const dispatch = useAppDispatch();
  const orderingState = useAppSelector((state) => state.search.formSearch.sort_by);
  const searchState = useAppSelector((state) => state.search.formSearch);

  return (
    <div className="mt-4 flex items-center gap-4 overflow-x-auto">
      <span className="hidden text-sm text-[#DD3F24] lg:block">Sort by</span>
      <Button
        label="Salary (High - Low)"
        isActive={orderingState === "high_low_salary"}
        onClick={() => {
          if (orderingState === "high_low_salary") {
            return onSearchClick({...searchState, sort_by: null})
          }

          onSearchClick({...searchState, sort_by: "high_low_salary"})
        }}
      />
      <Button
        label="Salary (Low - High)"
        isActive={orderingState === "low_high_salary"}
        onClick={() => {
          if (orderingState === "low_high_salary") {
            return onSearchClick({...searchState, sort_by: null})
          }

          onSearchClick({...searchState, sort_by: "low_high_salary"})
        }}
      />
      <Button
        label="Date posted (latest)"
        isActive={orderingState === "newest_refresh"}
        onClick={() => {
          if (orderingState === "newest_refresh") {
            return onSearchClick({...searchState, sort_by: null})
          }

          onSearchClick({...searchState, sort_by: "newest_refresh"})
        }}
      />
      <Button
        label="Date posted (oldest)"
        isActive={orderingState === "oldest_refresh"}
        onClick={() => {
          if (orderingState === "oldest_refresh") {
            return onSearchClick({...searchState, sort_by: null})
          }

          onSearchClick({...searchState, sort_by: "oldest_refresh"})
        }}
      />
    </div>
  );
}
