"use client";

import SearchFilterBenefitCheckboxSelect from "./Filter/SearchFilterBenefitCheckboxSelect";
import SearchFilterCompanySizeRadioSelect from "./Filter/SearchFilterCompanySizeRadioSelect";
import SearchFilterExperienceLevelRadioSelect from "./Filter/SearchFilterExperienceLevelRadioSelect";
import SearchFilterWorkTypeRadioSelect from "./Filter/SearchFilterWorkTypeRadioSelect";
import SearchAllFilter from "./SearchAllFilter";
import SearchFormCategorySelect from "./SearchFormCategorySelect";
import SearchFormInput from "./SearchFormInput";

export default function SearchForm() {
  return (
    <div className="flex flex-col gap-6">
      {/* Search form input and categories select */}
      <div className="flex gap-4 ">
        {/* Categories select */}
        <div>
          <SearchFormCategorySelect />
        </div>
        {/* End Categories select */}

        {/* Search form input */}
        <div className="flex-grow">
          <SearchFormInput />
        </div>
        {/* End Search form input */}
      </div>
      {/* End Search form input and categories select */}

      {/* Filters */}
      <div className="flex gap-2">
        <div>
          <SearchFilterExperienceLevelRadioSelect />
        </div>
        <div>
          <SearchFilterBenefitCheckboxSelect />
        </div>
        <div>
          <SearchFilterCompanySizeRadioSelect />
        </div>
        <div>
          <SearchFilterWorkTypeRadioSelect />
        </div>
        <div className="border-l border-[#FDB1A4] pl-2">
          <SearchAllFilter />
        </div>
      </div>
      {/* End Filters */}
    </div>
  );
}
