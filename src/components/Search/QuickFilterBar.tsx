import { SUGGESTED_KEYWORDS } from "@/contansts/keywords";
import { TaxonomyType } from "@/types/taxonomy";
import { useTranslations } from "next-intl";
import { FC } from "react";
import { classNames } from "@/utils";

interface Props {
  onKeywordSelect: (keyword: TaxonomyType) => void;
}

const QuickFilterBar: FC<Props> = ({ onKeywordSelect }) => {
  const t = useTranslations();

  const handleKeywordBtnClick = (keyword: TaxonomyType) => {
    onKeywordSelect(keyword);
  };

  return (
    <div className={"items-center gap-4 md:flex"}>
      <span className={"mb-3 block text-sm md:mb-0 md:text-base"}>
        {t("main_search_suggested_keywords")}:
      </span>
      <div className={"flex flex-wrap gap-2 md:flex-nowrap md:gap-4"}>
        {SUGGESTED_KEYWORDS.map((keyword, index) => {
          return (
            <button
              key={keyword.id}
              tabIndex={-1}
              className={
                classNames(
                  "rounded border border-gray-300 bg-white px-2 py-1 text-xs hover:bg-neutral-100 md:block md:px-3 md:py-2 md:text-base",
                  index >= 5 ? "hidden md:block" : "",
                )
              }
              onClick={() => {
                handleKeywordBtnClick(keyword);
              }}
            >
              {keyword.text}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default QuickFilterBar;
