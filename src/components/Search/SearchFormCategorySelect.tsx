"use client";

import { onSearchClick } from "@/hooks/useSearch";
import { fetchJobCategories } from "@/services/jobAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  setCategories as setCategoriesReducer,
  setCountRoles,
} from "@/store/slices/searchSlice";
import { Category, Role } from "@/types/job";
import { classNames } from "@/utils";
import { getSelectedCategories } from "@/utils/categoryUtils";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { HiChevronRight } from "react-icons/hi2";

// Constants

const CLOSED = "closed";
const OPENED = "opened";

const CATEGORY_MENU_CONFIG = {
  MENU_WIDTH: 255,
  ROLES_WIDTH: 600,
  MAX_COUNT_DISPLAY: 99,
} as const;

const IMAGES = {
  HAMBURGER: "https://c.topdevvn.com/uploads/2025/05/14/hamberger.svg",
  EXPERIENCE_LEVEL:
    "https://c.topdevvn.com/uploads/2025/05/14/experience-level.svg",
} as const;

const Button = ({ onClick }: { onClick: () => void }) => {
  const t = useTranslations();
  const countRoles = useAppSelector((state) => state.search.countRoles);
  const displayCount =
    countRoles > CATEGORY_MENU_CONFIG.MAX_COUNT_DISPLAY
      ? `${CATEGORY_MENU_CONFIG.MAX_COUNT_DISPLAY}+`
      : countRoles;

  return (
    <button
      onClick={onClick}
      className="flex h-[72px] w-44 items-center justify-center rounded bg-[#dd3f24]"
    >
      <div className="flex gap-2 text-white">
        <Image
          src={IMAGES.HAMBURGER}
          alt="Menu"
          width={16}
          height={16}
          loading="lazy"
        />
        {t("search_page_categories")} ({displayCount})
      </div>
    </button>
  );
};

const CategoryItem = ({
  category,
  isSelected,
  children,
}: {
  category: Category;
  isSelected: boolean;
  children: React.ReactNode;
}) => {
  return (
    <div
      className={classNames(
        "group flex h-8 items-center justify-between rounded pl-2 hover:cursor-pointer hover:bg-[#FEF4F2]",
        isSelected ? "bg-[#FEF4F2]" : "",
      )}
    >
      <div className="flex items-center whitespace-nowrap">
        <Image
          src={IMAGES.EXPERIENCE_LEVEL}
          alt="Category icon"
          width={10}
          height={10}
          loading="lazy"
        />
        <span
          className={classNames(
            "ml-[4px] inline-block flex-grow text-[14px]/[18px] group-hover:text-[#DD3F24]",
            isSelected ? "text-[#DD3F24]" : "",
          )}
        >
          {category.name}
        </span>
        <span className="ml-[2px] inline-block text-[14px]/[18px] font-medium text-[#DD3F24]">
          {children}
        </span>
      </div>
      <HiChevronRight className="-translate-x-2" />
    </div>
  );
};

const CategoryRole = ({
  role,
  onClick,
  isSelected = false,
}: {
  role: Role;
  onClick: () => void;
  isSelected: boolean;
}) => {
  return (
    <button
      onClick={onClick}
      className={classNames(
        "mb-3 mr-3 inline-block items-center rounded-full px-8 py-2 text-xs",
        isSelected
          ? "bg-[#FEF4F2] font-medium text-[#DD3F24]"
          : " bg-[#F6F6F6]",
      )}
    >
      {role.name}
    </button>
  );
};

interface CategoryMenuProps {
  isVisible: boolean;
  onClose: () => void;
  triggerEvent: string | null;
}

const CategoryMenu = ({
  isVisible,
  onClose,
  triggerEvent,
}: CategoryMenuProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [activeRoles, setActiveRoles] = useState<number[]>([]);
  const [activeCategories, setActiveCategories] = useState<string[]>([]);
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const categoryIds = searchParams.get("job_categories_ids");
  const t = useTranslations();
  const ids = useMemo(
    () => (categoryIds ? categoryIds.split(",").map(Number) : []),
    [categoryIds],
  );

  useEffect(() => {
    fetchJobCategories().then((response) => {
      setCategories(response);
    });
  }, []);
  useEffect(() => {
    dispatch(setCountRoles(activeRoles.length));
  }, [activeRoles, dispatch]);

  const [selectedCategory, setSelectedCategory] = useState<Category>();
  const categoryMap = useMemo(() => {
    if (!Array.isArray(categories)) return {};

    return categories.reduce((acc: { [key: string]: number[] }, category) => {
      acc[category.name] = category.roles?.map((role) => role.id) || [];
      return acc;
    }, {});
  }, [categories]);
  const selectedCategoryNames = useMemo(() => {
    return getSelectedCategories(categoryMap, activeRoles);
  }, [categoryMap, activeRoles]);
  const rolesFilterDefault = useMemo(() => {
    if (categories.length > 0) {
      if (ids?.length > 0) {
        const selectedRoles: number[] = [];
        categories.forEach((category) => {
          category.roles?.forEach((role) => {
            if (ids.includes(role.id)) {
              selectedRoles.push(role.id);
            }
          });
        });
        return selectedRoles;
      }
    }
    return [];
  }, [categories, ids]);

  const handleCancel = () => {
    setActiveRoles(() => (ids?.length > 0 ? rolesFilterDefault : []));
    setActiveCategories(() => (ids?.length > 0 ? selectedCategoryNames : []));
    setSelectedCategory(undefined);
    onClose();
  };

  useEffect(() => {
    if (rolesFilterDefault?.length > 0) {
      setActiveRoles(rolesFilterDefault);
    }
  }, [rolesFilterDefault]);
  useEffect(() => {
    if (selectedCategoryNames?.length > 0) {
      setActiveCategories(selectedCategoryNames);
    }
  }, [selectedCategoryNames]);

  const searchState = useAppSelector((state) => state.search.formSearch);

  const calculateItem = (name: string) => {
    const countItems = categoryMap[name]?.filter((roleId) =>
      activeRoles.includes(roleId),
    );
    const length = countItems?.length || null;

    return length ? `(${length})` : null;
  };

  // Example usage of utility functions

  const handleClickCategories = useCallback(
    (item: Category) => {
      const isAlreadyActive = activeCategories.includes(item.name);
      setActiveCategories((prev) => {
        if (prev.includes(item.name)) {
          return prev.filter((name) => name !== item.name);
        }
        return [...prev, item.name];
      });
      setActiveRoles((prev) => {
        const getAllRoleIds = item?.roles?.map((role) => role.id) || [];
        if (isAlreadyActive) {
          return prev.filter((id) => !getAllRoleIds.includes(id));
        }
        return [...prev, ...getAllRoleIds];
      });
    },
    [activeCategories],
  );
  const handleRemoveCheck = useCallback(() => {
    setActiveRoles([]);
    setActiveCategories([]);
    setSelectedCategory(undefined);
  }, []);

  const handleButtonClick = useCallback((role: Role) => {
    setActiveRoles((prev) => {
      const uniqueRoles = new Set(prev);
      if (uniqueRoles.has(role.id)) {
        uniqueRoles.delete(role.id);
      } else {
        uniqueRoles.add(role.id);
      }
      return Array.from(uniqueRoles);
    });
  }, []);

  const handleClickJobs = useCallback(() => {
    onSearchClick({
      ...searchState,
      categories: activeRoles,
    });
  }, [searchState, activeRoles]);

  useEffect(() => {
    if (triggerEvent === CLOSED) {
      setActiveRoles(() => (ids?.length > 0 ? rolesFilterDefault : []));
      setActiveCategories(() => (ids?.length > 0 ? selectedCategoryNames : []));
      setSelectedCategory(undefined);
    }
  }, [triggerEvent, ids, rolesFilterDefault, selectedCategoryNames]);

  return (
    <div
      className={classNames(
        "absolute top-[80px] z-50 rounded bg-white p-3 shadow-sm",
        !isVisible ? "hidden" : "",
      )}
    >
      <div className="flex flex-col gap-4">
        <div className="flex gap-4">
          <div
            className="border-r border-[#FEE6E2] pr-1"
            style={{ width: CATEGORY_MENU_CONFIG.MENU_WIDTH }}
          >
            <ul className="flex flex-col gap-1">
              {categories.map((category) => (
                <li
                  key={category.id}
                  onMouseOver={() => setSelectedCategory(category)}
                  onClick={() => {
                    handleClickCategories(category);
                  }}
                >
                  <CategoryItem
                    key={category.id}
                    category={category}
                    isSelected={categoryMap[category.name]?.some((roleId) =>
                      activeRoles.includes(roleId),
                    )}
                  >
                    {calculateItem(category.name)}
                  </CategoryItem>
                </li>
              ))}
            </ul>
          </div>
          <div style={{ width: CATEGORY_MENU_CONFIG.ROLES_WIDTH }}>
            {selectedCategory?.roles.map((role) => (
              <CategoryRole
                key={role.id}
                role={role}
                isSelected={activeRoles.includes(role.id)}
                onClick={() => handleButtonClick(role)}
              />
            ))}
          </div>
        </div>
        <hr />
        <div className="flex items-center justify-end gap-3">
          <button
            className="bg-none text-xs text-[#DD3F24]"
            onClick={() => {
              handleRemoveCheck();
              dispatch(setCategoriesReducer([]));
            }}
          >
            {t("search_page_clear_all")} ({activeRoles?.length})
          </button>
          <button
            className="rounded-full bg-[#E7E7E7] px-8 py-2 text-xs"
            onClick={() => handleCancel()}
          >
            {t("user_profile_open_to_work_notice_btn_cancel")}
          </button>
          <button
            className="rounded-full bg-[#DD3F24] px-8 py-2 text-xs text-white"
            onClick={() => handleClickJobs()}
          >
            {t("search_page_apply")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default function SearchFormCategorySelect() {
  const [isShowMenu, setIsShowMenu] = useState(false);
  const [triggerEvent, setTriggerEvent] = useState<string | null>(null);

  const handleButtonClick = () => {
    setIsShowMenu(!isShowMenu);
    setTriggerEvent(isShowMenu ? CLOSED : OPENED);
  };

  return (
    <div className="relative">
      <Button onClick={handleButtonClick} />

      <CategoryMenu
        isVisible={isShowMenu}
        onClose={() => setIsShowMenu(false)}
        triggerEvent={triggerEvent}
      />
    </div>
  );
}
