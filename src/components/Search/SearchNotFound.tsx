import { SearchMetaType } from "@/types/search";
import isDevice from "@/utils/device";
import { useTranslations } from "next-intl";
import Image from "next/image";
import React from "react";
type SearchNotFoundProp = {
  meta?: null | SearchMetaType;
  keyword?: string; // Add keyword prop for SSR
};
const SearchNotFound = ({ meta = null, keyword }: SearchNotFoundProp) => {
  const device = isDevice();
  const t = useTranslations();

  // Use keyword prop first, fallback to meta if not available
  const keywordFromMeta = meta?.keyword_display?.replace(/,/g, ", ");
  const output = keyword || keywordFromMeta;
  return (
    <div className="gap flex flex-col items-center justify-center rounded bg-white text-center shadow-sm lg:flex-row lg:justify-normal lg:text-left">
      <div className="p-4">
        <Image
          src="https://cdn.topdev.vn/v4/assets/images/common/not-found.svg"
          width={device === "desktop" ? 178 : 75}
          height={device === "desktop" ? 120 : 54}
          className="h-full w-[4.5rem] max-w-full object-contain lg:w-44"
          alt="Not found"
        />
      </div>
      <div className="flex-1 px-6 py-4">
        <h1 className="font-bold text-primary lg:text-xl">
          {t("search_page_search_not_found", {
            keyword_display: output,
          })}
        </h1>
        <p className="mt-1 text-xs lg:text-base">
          {t("search_page_can_not_found_job")}
        </p>
        <p className="mt-1 text-xs lg:text-base">
          {t("search_page_hint_job_for_user")}
        </p>
      </div>
    </div>
  );
};

export default SearchNotFound;
