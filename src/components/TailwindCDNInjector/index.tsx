"use client";

import { useEffect } from "react";

interface TailwindCDNInjectorProps {
  enabled: boolean;
}

declare global {
  interface Window {
    tailwind?: {
      config?: Record<string, unknown>;
    };
  }
}

const TailwindCDNInjector: React.FC<TailwindCDNInjectorProps> = ({
  enabled,
}) => {
  useEffect(() => {
    if (!enabled) return;

    // Avoid injecting twice
    const existingScript = document.querySelector(
      'script[src="https://cdn.tailwindcss.com"]',
    );
    if (existingScript) return;

    const script = document.createElement("script");
    script.src = "https://cdn.tailwindcss.com";
    script.async = true;
    script.onload = () => {
      setTimeout(() => {
        if (window.tailwind) {
          window.tailwind.config = {
            prefix: "td-",
            // You can add more custom Tailwind config here if needed
          };
        }
      }, 0);
    };
    document.head.appendChild(script);
  }, [enabled]);

  return null;
};

export default TailwindCDNInjector;
