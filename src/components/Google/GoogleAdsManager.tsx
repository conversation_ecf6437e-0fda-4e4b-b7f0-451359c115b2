'use client'

import Script from "next/script";

export default function GoogleAdsManager() {
  if (typeof window == 'undefined') return '';

  const urlParams = new URLSearchParams(window.location.search);
  const isDisabledGTM = urlParams.get('disabledGTM');

  return !isDisabledGTM ? (
    <>
      <Script
        async
        src={"https://securepubads.g.doubleclick.net/tag/js/gpt.js"}
      ></Script>
      <Script id={"google-ad-manager"}>
        {`
          window.googletag = window.googletag || {cmd: []};
          googletag.cmd.push(function() {
            googletag.defineSlot('/22203236202/beta_homepage_916113', [1260, 120], 'div-gpt-ad-1696507101343-0').addService(googletag.pubads());
            googletag.defineSlot('/22203236202/topdev_right_20231006', [404, 320], 'div-gpt-ad-1696568371509-0').addService(googletag.pubads());
            googletag.defineSlot('/22203236202/topdev_search_middle', [832, 102], 'div-gpt-ad-1698056646871-0').addService(googletag.pubads());
            googletag.pubads().enableSingleRequest();
            googletag.enableServices();
          });
        `}
      </Script>
    </>
  ) : (
    <Script id={"google-ad-manager"}>
      {`
        window.googletag = window.googletag || {cmd: []};
      `}
    </Script>
  );
}
