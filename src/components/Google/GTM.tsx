'use client'

/** @ts-ignore */
import { GoogleTagManager } from "@next/third-parties/google";

export default function GTM() {
  if (typeof window == 'undefined') return '';

  const urlParams = new URLSearchParams(window.location.search);
  const isDisabledGTM = urlParams.get('disabledGTM');

  return process.env.NEXT_PUBLIC_GTM_ID && !isDisabledGTM ? (
    <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID} />
  ) : null;
}
