import { useFormik } from "formik";
import { HiShoppingCart } from "react-icons/hi2";
import { useTranslations } from "next-intl";
import TheCheckoutPageInformationOrder from "@/components/Cart/TheCheckoutPageInformationOrder";
import TheCheckoutPageUserInformation from "@/components/Cart/TheCheckoutPageUserInformation";
import TheCheckoutPageCompanyInformation from "@/components/Cart/TheCheckoutPageCompanyInformation";
import TheCheckoutPageMethod from "@/components/Cart/TheCheckoutPageMethod";
import ToastNotification from "@/components/Swal/ToastNotification";
import { PaymentProps, postPaymentOrderProps } from "@/types/cart";
import { postPaymentOrder } from "@/services/cartAPI";
import schemaFormPayment from "@/schemas/schemaFormPayment";
import useCart from "@/hooks/useCart";
import useSetDataCheckout from "@/hooks/useSetDataCheckout";
import useStep from "@/hooks/useStep";
import { CART_STEPS } from "@/contansts/payment";

export default function TheCheckoutPageForm() {
  const [cart, setCartData, recallCart] = useCart();
  const [step, setStepCart] = useStep();
  const [dataPayment, setDataCheckout] = useSetDataCheckout();
  const t = useTranslations();

  //Define formik
  const { handleSubmit, setFieldValue, errors, touched, values } =
    useFormik<PaymentProps>({
      initialValues: {
        buyer_name: "",
        buyer_phone: "",
        company_business_name: "",
        company_phone: "",
        crm_company_id: 0,
        discount_total: 0,
        item_total: 0,
        order_total: 0,
        payment_method_id: 0,
        quantity: 0,
        term_of_use: false,
        company_tax_number: "",
      },
      validationSchema: schemaFormPayment(t),
      onSubmit: async (values: PaymentProps) => {
        const isWarning = await recallCart();
        if (!Boolean(isWarning)) return;

        const newValues = {
          ...values,
          ...{
            discount_total: cart.discount_total,
            item_total: cart.price_total,
            order_total: cart.order_total,
            quantity: cart.quantity,
          },
        };

        postPaymentOrder(newValues)
          .then(({ data }: { data: postPaymentOrderProps }) => {
            if (data.error === false) {
              setDataCheckout(data.data);
              setStepCart(CART_STEPS.THREE);
            }
          })
          .catch((error) => {
            if (!!error?.response?.data?.errors) {
              ToastNotification({
                icon: "error",
                title: error.response.data.errors.cart?.[0],
              });
              return;
            }

            ToastNotification({
              icon: "error",
              title: error.message,
            });
          });
      },
    });
  //End Define formik

  return (
    <>
      <div className="w-full">
        <form
          onSubmit={handleSubmit}
          className="flex flex-col items-start gap-6 md:flex-row"
        >
          <div className="flex w-full flex-col gap-6 md:w-2/3">
            <h3 className="font-bold md:text-3xl">
              {t("carts_payment_billing_information")}
            </h3>
            <div className="rounded-xl bg-white shadow-sm">
              <h3 className="align-self-stretch flex items-center gap-2 border-b border-gray-200 px-6 py-4">
                <HiShoppingCart className="h-5 w-5" />
                <span className="text-xl font-bold text-gray-600">
                  {t("carts_payment_information_line")}
                </span>
              </h3>
              <TheCheckoutPageUserInformation
                setFieldValue={setFieldValue}
                errors={errors}
                touched={touched}
                values={values}
              />
              <TheCheckoutPageCompanyInformation
                setFieldValue={setFieldValue}
                errors={errors}
                touched={touched}
                values={values}
              />
            </div>
            <TheCheckoutPageMethod
              setFieldValue={setFieldValue}
              values={values}
            />
          </div>
          <TheCheckoutPageInformationOrder setFieldValue={setFieldValue} />
        </form>
      </div>
    </>
  );
}
