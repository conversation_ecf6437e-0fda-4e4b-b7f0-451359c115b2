import { classNames } from "@/utils";
import Image from "next/image";
export default function PaymentMethodItem({
  id,
  name,
  description,
  type,
  valueCheck,
  setFieldValue,
}: {
  id: number;
  name: String;
  description: String;
  type: String;
  valueCheck: number;
  setFieldValue(
    field: string,
    value: any,
    shouldValidate?: boolean | undefined,
  ): void;
}) {
  const renderDescription = () => {
    if (type == "credit_card") {
      return "Chấp nhận thanh toán bằng các thẻ Visa/Mastercard/JCB.";
    }
    if (type == "momo_wallet") {
      return "Thanh toán qua ví điện tử Momo.";
    }
    return "Thanh toán bằng thẻ ghi nợ nội địa (NAPAS).";
  };

  return (
    <div
      className={classNames(
        valueCheck == id ? "bg-gray-100" : "",
        "method-item flex cursor-pointer items-center gap-4 rounded-xl p-4 transition-all delay-100 hover:bg-gray-100",
      )}
    >
      <input
        className="h-5 w-5 rounded-full border-gray-300 checked:bg-primary focus:!bg-primary"
        type="radio"
        name="payment_method_id"
        id={`method${id}`}
        value={id}
        checked={valueCheck == id}
        onChange={(e) => {
          setFieldValue("payment_method_id", Number(e.target.value));
        }}
      />
      <label htmlFor={`method${id}`} className="flex items-center w-full gap-4">
        <Image
          className="h-[32px] w-[50px] object-contain"
          src={`/v4/assets/images/online-payment/${type}.png`}
          alt="Flag"
          width={50}
          height={32}
        />
        <div className="flex flex-1 cursor-pointer flex-col">
          <h3 className="font-bold text-gray-600">{name}</h3>
          <p className="min-h-[22px] text-gray-400">
            {!!description && description != ""
              ? description
              : renderDescription()}
          </p>
        </div>
      </label>
    </div>
  );
}
