import { useTranslations } from "next-intl";
import TotalOrder from "@/components/Cart/TotalOrder";
import useCart from "@/hooks/useCart";
import useStep from "@/hooks/useStep";
import { Button } from "@/components/Button";
import { CART_STEPS } from "@/contansts/payment";
import { useAppSelector } from "@/store";

export default function TheCartPageInformationOrder() {
  const t = useTranslations();
  const [cart, setCartData, recallCart] = useCart();
  const [setp, setStepCart] = useStep();

  const handleConfirmOrder = async () => {
    const isWarning = await recallCart();
    if (Boolean(isWarning)) setStepCart(Number(CART_STEPS.TWO));
  };

  return (
    <div className="w-full md:mt-[60px] md:w-1/3">
      <div className="rounded-xl bg-white shadow-sm">
        <h3 className="gap-2 border-b border-gray-200 p-4 text-center text-xl font-bold text-gray-600">
          {t("carts_order")}
        </h3>
        <TotalOrder
          total_tax={cart.total_tax}
          discount_total={cart.discount_total}
          order_total={cart.order_total}
          price_total={cart.price_total}
          tax_name={cart.tax_name}
        />
        <div className="gap-2 border-t border-gray-200 p-4">
          <Button onClick={() => handleConfirmOrder()} accent="primary" isBlock>
            {t("carts_order_confirmation")}
          </Button>
        </div>
      </div>
    </div>
  );
}
