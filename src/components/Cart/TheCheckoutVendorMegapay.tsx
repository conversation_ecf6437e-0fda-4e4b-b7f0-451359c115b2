"use client";
import Script from "next/script";
import { useEffect } from "react";
import useSetDataCheckout from "@/hooks/useSetDataCheckout";

export default function TheCheckoutVendorMegapay() {
  const [dataPayment, setDataCheckout] = useSetDataCheckout();

  useEffect(() => {
    const callFunction = setTimeout(() => {
      const buttonPayNow = document.getElementById("payNow");
      buttonPayNow?.click();
    }, 700);

    return () => {
      clearTimeout(callFunction);
    };
  }, []);

  return (
    <>
      <Script
        id="init-openPayment"
        dangerouslySetInnerHTML={{
          __html:
            "var btn = document.getElementById('payNow'); btn.addEventListener('click', function() {openPayment(1, '"+process.env.NEXT_PUBLIC_BASE_URL_MEGAPAY_PAYMENT+"');});",
        }}
      />
      <div id="wrapper">
        <div className="content">
          <form id="megapayForm" name="megapayForm" method="POST">
            <input type="hidden" name="merId" value={dataPayment.merId} />
            <input type="hidden" name="currency" value={dataPayment.currency} />
            <input type="hidden" name="amount" value={dataPayment.amount} />
            <input
              type="hidden"
              name="invoiceNo"
              value={dataPayment.invoiceNo}
            />
            <input type="hidden" name="goodsNm" value={dataPayment.goodsNm} />
            <input
              type="hidden"
              name="description"
              value={dataPayment.description}
            />
            <input type="hidden" name="payType" value={dataPayment.payType} />
            <input
              type="hidden"
              name="callBackUrl"
              value={dataPayment.callBackUrl}
            />
            <input type="hidden" name="notiUrl" value={dataPayment.notiUrl} />
            <input
              type="hidden"
              name="reqDomain"
              value={dataPayment.reqDomain}
            />
            <input
              type="hidden"
              name="merchantToken"
              value={dataPayment.merchantToken}
            />
            <input
              type="hidden"
              name="userLanguage"
              value={dataPayment.userLanguage}
            />
            <input
              type="hidden"
              name="timeStamp"
              value={dataPayment.timeStamp}
            />
            <input type="hidden" name="merTrxId" value={dataPayment.merTrxId} />
            <input
              type="hidden"
              name="windowColor"
              value={dataPayment.windowColor}
            />
            <input type="hidden" name="windowType" value={dataPayment.windowType} />
            <input
              type="hidden"
              name="bankCode"
              value={dataPayment?.bankCode}
            />
          </form>
          <button className="hidden" id="payNow">
            Pay now
          </button>
        </div>
      </div>
    </>
  );
}
