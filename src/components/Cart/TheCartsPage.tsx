"use client";

import dynamic from "next/dynamic";
import { useState, useEffect } from "react";
import useCart from "@/hooks/useCart";
import useStep from "@/hooks/useStep";
import { CART_STEPS } from "@/contansts/payment";
import TheCartPageEmpty from "@/components/Cart/TheCartPageEmpty";
import TheCartPageInformationOrder from "@/components/Cart/TheCartPageInformationOrder";
import TheCartPageHeading from "@/components/Cart/TheCartPageHeading";
import TheCartPageShopping from "@/components/Cart/TheCartPageShopping";
import TheCartPageStepNavigation from "@/components/Cart/TheCartPageStepNavigation";
import TheCheckoutPageForm from "@/components/Cart/TheCheckoutPageForm";
import TheCheckoutVendor from "@/components/Cart/TheCheckoutVendor";
import LoadingCart from "@/components/Cart/LoadingCart";
import { useTranslations } from "next-intl";
import Script from "next/script";

const PopupLimitQuantity = dynamic(
  () => import("@/components/Product/PopupLimitQuantity"),
  { ssr: false },
);

export default function TheCartsPage() {
  const t = useTranslations();
  const [loading, setLoading] = useState<boolean>(false);
  const [cart] = useCart();
  const [step, setStepCart] = useStep();

  useEffect(() => {
    setLoading(true);
  }, []);

  return (
    <>
      <link
        rel="stylesheet"
        href={`${process.env.NEXT_PUBLIC_BASE_URL_MEGAPAY_PAYMENT}/pg_was/css/payment/layer/paymentClient.css`}
      ></link>
      <Script
        src="https://code.jquery.com/jquery-2.2.4.min.js"
        integrity="sha256-BbhdlvQf/xTY9gja0Dq3HiwQF8LaCRTXxZKRutelT44="
        rel="preload"
        crossOrigin="anonymous"
        id="jquery-min"
      />
      <Script
        id="payment-client"
        src={`${process.env.NEXT_PUBLIC_BASE_URL_API}/payment/paymentClient.js`}
      />

      <PopupLimitQuantity />
      <div className="bg-gray-100">
        <TheCartPageHeading step={step} setStepCart={setStepCart} />
        <TheCartPageStepNavigation />
        <div className="container">
          <div className="flex flex-col gap-6 pb-5 md:flex-row md:pb-40">
            {loading ? (
              <>
                {step === CART_STEPS.ONE && (
                  <>
                    {cart?.items?.length === 0 ? (
                      <TheCartPageEmpty />
                    ) : (
                      <>
                        <TheCartPageShopping />
                        <TheCartPageInformationOrder />
                      </>
                    )}
                  </>
                )}

                {step === CART_STEPS.TWO && <TheCheckoutPageForm />}
                {step === CART_STEPS.THREE && (
                  <div className="w-full">
                    <TheCheckoutVendor />
                    <div className="w-full flex justify-center">
                      <div className="flex items-center">
                        <svg
                          className="-ml-1 mr-3 h-6 w-h-6 animate-spin text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25 text-[#757575]"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            stroke-width="4"
                          ></circle>
                          <path
                            className="opacity-75 text-primary"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                       <span className="text-gray-500">{t("carts_loading_payment")}</span>
                      </div>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <LoadingCart />
            )}
          </div>
        </div>
      </div>
    </>
  );
}
