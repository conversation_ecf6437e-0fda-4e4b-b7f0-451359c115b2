import { useTranslations } from "next-intl";
import { CART_STEPS } from "@/contansts/payment";
import StepItem from "@/components/Cart/StepItem";
import useStep from "@/hooks/useStep";

export default function TheCartPageStepNavigation() {
  const t = useTranslations();
  const [step, setStepCart] = useStep();

  return (
    <div className="flex justify-center px-3 py-5 md:pb-12 md:pt-6">
      <div className="flex w-full max-w-[862px] flex-row justify-between">
        <StepItem
          step={step}
          stepValue={CART_STEPS.ONE}
          title={t("carts_check_cart")}
        />
        <div className="-mx-10 hidden h-[32px] w-full max-w-[277px] items-center md:flex">
          <div className="h-[1px] w-full bg-gray-500"></div>
        </div>
        <StepItem
          step={step}
          stepValue={CART_STEPS.TWO}
          title={t("carts_cart_information")}
        />
        <div className="-mx-10 hidden h-[32px] w-full max-w-[277px] items-center md:flex">
          <div className="h-[1px] w-full bg-gray-500"></div>
        </div>
        <StepItem
          step={step}
          stepValue={CART_STEPS.THREE}
          title={t("carts_make_payments")}
        />
      </div>
    </div>
  );
}
