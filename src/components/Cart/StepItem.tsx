import { classNames } from "@/utils";
export default function StepItem({
  step,
  stepValue,
  title,
}: {
  step: Number;
  stepValue: number;
  title: string;
}) {

  return (
    <div
      data-cy="Step-Item"
      className="flex w-[135px] cursor-pointer select-none flex-col items-center justify-end gap-[10px]"
    >
      <div
        className={classNames(
          step === stepValue
            ? "border-dashed border-primary bg-transparent"
            : "border-gray-500 bg-white",
          "box-border flex h-[32px] w-[32px] items-center justify-center rounded-full border",
        )}
      >
        <span
          className={classNames(
            step === stepValue
              ? "border-primary bg-primary-100 font-bold text-primary"
              : "border-transparent",
            "box-border h-[26px] w-[26px] rounded-full border  text-center text-sm leading-6",
          )}
        >
          0{stepValue}
        </span>
      </div>
      <span
        className={classNames(
          step === stepValue ? "font-bold text-primary" : "",
          "hidden whitespace-nowrap text-sm text-gray-500 md:block",
        )}
      >
        {title}
      </span>
    </div>
  );
}
