import { useTranslations } from "next-intl";
import { OrderProp } from "@/types/cart";
import CartItem from "@/components/Cart/CartItem";
import useCart from "@/hooks/useCart";

export default function TheCartPageShopping() {
  const t = useTranslations();
  const [cart] = useCart();
  const productsPackage =
    !!cart && cart.items.filter((items) => items.category_code === "package");
  const productsCredit =
    !!cart && cart.items.filter((items) => items.category_code === "credit");

  return (
    <div className="w-full md:w-2/3">
      <h3 className="mb-4 font-bold text-black md:mb-6 md:text-3xl">
        {t("carts_your_cart")}
      </h3>
      <div className="overflow-x-auto md:overflow-visible">
        <div className="rounded-xl min-w-[620px] bg-white shadow-sm">
          <div className="flex items-center justify-between border-b border-gray-200">
            <div className="w-2/5 px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
              {t("carts_product")}
            </div>
            <div className="w-1/6 px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
              {t("carts_unit_price")}
            </div>
            <div className="w-1/6 whitespace-nowrap px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
              {t("carts_quantity")}
            </div>
            <div className="w-1/6 whitespace-nowrap px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
              {t("carts_provisional")}
            </div>
            <div className="w-1/12 px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
              {t("carts_delete")}
            </div>
          </div>

          {!!productsPackage && productsPackage.length > 0 && (
            <>
              <div className="flex items-center gap-2 bg-gray-100 px-6 py-2">
                <span className="font-bold text-gray-600">
                  {t("carts_recruitment")}
                </span>
                <span className="text-gray-400">
                  ({productsPackage.length})
                </span>
              </div>
              {productsPackage.map((order: OrderProp, index: number) => {
                return (
                  <CartItem
                    key={index}
                    name={order.name}
                    price={order.price}
                    product_id={order.product_id}
                    quantity={order.quantity}
                    total_anchoring_price={order.total_anchoring_price}
                    total_price={order.total_price}
                    anchoring_price={order.anchoring_price}
                    type="package"
                  />
                );
              })}
            </>
          )}

          {!!productsCredit && productsCredit.length > 0 && (
            <>
              <div className="flex items-center gap-2 bg-gray-100 px-6 py-2">
                <span className="font-bold text-gray-600">
                  {t("carts_find_candidate_cv")}
                </span>
                <span className="text-gray-400">({productsCredit.length})</span>
              </div>
              {productsCredit.map((order: OrderProp, index: number) => {
                return (
                  <CartItem
                    key={index}
                    name={order.name}
                    price={order.price}
                    product_id={order.product_id}
                    quantity={order.quantity}
                    total_anchoring_price={order.total_anchoring_price}
                    total_price={order.total_price}
                    anchoring_price={order.anchoring_price}
                    type="credit"
                  />
                );
              })}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
