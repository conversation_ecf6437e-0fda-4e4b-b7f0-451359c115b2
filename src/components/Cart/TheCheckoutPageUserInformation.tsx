import { FormikErrors, FormikTouched } from "formik";
import { useTranslations } from "next-intl";
import { MdError } from "react-icons/md";
import { useAppSelector } from "@/store";
import { PaymentProps } from "@/types/cart";
import { classNames } from "@/utils";
import { useEffect } from "react";

export default function TheCheckoutPageUserInformation({
  values,
  errors,
  touched,
  setFieldValue,
}: {
  values: PaymentProps;
  errors: FormikErrors<PaymentProps>;
  touched: FormikTouched<PaymentProps>;
  setFieldValue(
    field: string,
    value: any,
    shouldValidate?: boolean | undefined,
  ): void;
}) {
  const user = useAppSelector((state) => state?.user?.user);
  const t = useTranslations();

  useEffect(() => {
    setFieldValue("buyer_name", user?.display_name ?? "");
    setFieldValue("buyer_phone", user?.phone ?? "");
  }, [user, setFieldValue]);

  return (
    <div className="checkout-user-information flex flex-col gap-4 p-6">
      <h3 className="text-lg font-semibold text-gray-600">
        {t("carts_payment_buyer_information")}
      </h3>
      <div className="flex flex-col items-start gap-1">
        <label className="font-bold text-gray-500" htmlFor="email">
          Email
        </label>
        <div className="relative w-full">
          <input
            className="h-[52px] w-full rounded border-gray-200 bg-white px-5 py-4 placeholder:text-gray-300 disabled:bg-gray-100"
            type="email"
            name="email"
            id="email"
            disabled={true}
            value={user?.email}
            placeholder="<EMAIL>"
          />
        </div>
      </div>
      <div className="flex flex-col items-start gap-1">
        <label className="font-bold text-gray-500" htmlFor="buyer_name">
          {t("carts_payment_fullname")} <sup className="text-primary">*</sup>
        </label>
        <div className="relative w-full">
          <input
            className={classNames(
              !!errors["buyer_name"] && !!touched["buyer_name"]
                ? "border-primary"
                : "border-gray-200",
              "h-[52px] w-full rounded  bg-white px-5 py-4 placeholder:text-gray-300 disabled:bg-gray-100",
            )}
            type="text"
            name="buyer_name"
            id="buyer_name"
            placeholder={t("carts_payment_placeholder_fullname")}
            onChange={(e) => setFieldValue("buyer_name", e.target.value)}
            value={values?.buyer_name ?? ""}
          />

          {!!errors["buyer_name"] && !!touched["buyer_name"] && (
            <>
              <div className="absolute right-5 top-4 z-10">
                <MdError className="h-5 w-5 text-primary" />
              </div>
              <span className="mt-2 block text-xs text-primary">
                {errors["buyer_name"]}
              </span>
            </>
          )}
        </div>
      </div>
      <div className="flex flex-col items-start gap-1">
        <label className="font-bold text-gray-500" htmlFor="buyer_phone">
          {t("carts_payment_phone_number")}{" "}
          <sup className="text-primary">*</sup>
        </label>
        <div className="relative w-full">
          <input
            className={classNames(
              !!errors["buyer_phone"] && !!touched["buyer_phone"]
                ? "border-primary"
                : "border-gray-200",
              "h-[52px] w-full rounded  bg-white px-5 py-4 placeholder:text-gray-300 disabled:bg-gray-100",
            )}
            type="text"
            name="buyer_phone"
            id="buyer_phone"
            onChange={(e) => setFieldValue("buyer_phone", e.target.value)}
            placeholder={t("carts_payment_placeholder_phone")}
            value={values?.buyer_phone ?? ""}
          />
          <div className="absolute right-5 top-4 z-10 hidden">
            <MdError className="h-5 w-5 text-primary" />
          </div>
          {!!errors["buyer_phone"] && !!touched["buyer_phone"] && (
            <>
              <div className="absolute right-5 top-4 z-10">
                <MdError className="h-5 w-5 text-primary" />
              </div>
              <span className="mt-2 block text-xs text-primary">
                {errors["buyer_phone"]}
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
