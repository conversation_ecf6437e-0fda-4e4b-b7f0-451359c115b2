import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { HiCreditCard } from "react-icons/hi2";
import PaymentMethodItem from "@/components/Cart/PaymentMethodItem";
import { getMothodPayment } from "@/services/cartAPI";
import { MethodProps, PaymentProps } from "@/types/cart";

export default function TheCheckoutPageMethod({
  values,
  setFieldValue,
}: {
  values: PaymentProps;
  setFieldValue(
    field: string,
    value: any,
    shouldValidate?: boolean | undefined,
  ): void;
}) {
  const [methods, setMethods] = useState<Array<MethodProps>>([]);
  const t = useTranslations();

  useEffect(() => {
    getMothodPayment()
      .then((data) => {
        if (data.data.error == false) {
          setMethods(data?.data?.data);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  useEffect(() => {
    if (methods.length === 0) return;

    setFieldValue("payment_method_id", Number(methods?.[0]?.id));
  }, [methods, setFieldValue]);

  return (
    <div className="checkout-method flex flex-col rounded-xl bg-white shadow-sm">
      <h3 className="align-self-stretch flex items-center gap-2 border-b border-gray-200 px-6 py-4">
        <HiCreditCard className="h-5 w-5" />
        <span className="text-xl font-bold text-gray-600">
          {t("carts_payment_payment_methods")}
        </span>
      </h3>
      <div className="p-6">
        {!!methods &&
          methods.length > 0 &&
          methods.map((method: MethodProps, index) => {
            return (
              <PaymentMethodItem
                key={index}
                name={method.name}
                description=""
                type={method.type}
                id={method.id}
                setFieldValue={setFieldValue}
                valueCheck={values.payment_method_id}
              />
            );
          })}
      </div>
    </div>
  );
}
