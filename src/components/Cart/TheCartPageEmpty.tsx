import { useTranslations } from "next-intl";
import Image from "next/image";
import Button from "@/components/Button/Button";
export default function TheCartPageEmpty() {
  const t = useTranslations();

  return (
    <div data-cy="The-Cart-Page-Empty" className="flex justify-center mx-auto py-5 md:py-36">
      <div className="flex flex-col items-center justify-center gap-4">
        <Image
          src="/v4/assets/images/online-payment/empty-cart.png"
          alt="Empty cart"
          width={197}
          height={146}
        />
        <h3 className="mb-0 text-2xl font-semibold text-black">
          {t("carts_title_cart_empty")}
        </h3>
        <p className="max-w-[465px] text-center text-gray-500">
          {t("carts_description_cart_empty")}
        </p>
        <Button
          onClick={() => (window.location.href = "/products")}
          accent="primary"
        >
          {t("carts_go_to_product_page")}
        </Button>
      </div>
    </div>
  );
}
