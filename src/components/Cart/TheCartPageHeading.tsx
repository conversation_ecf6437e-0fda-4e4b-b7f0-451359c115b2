"use client";

import { useTranslations } from "next-intl";
import { HiChevronLeft } from "react-icons/hi2";
import { CART_STEPS } from "@/contansts/payment";

export default function TheCartPageHeading({
  step,
  setStepCart,
}: {
  step: Number;
  setStepCart(step: Number): void;
}) {
  const t = useTranslations();

  const handleNavigation = () => {
    setStepCart(Number(step) - 1);
    if (step === 1) {
      window.location.href = "/products";
    }
  };

  const convertTitleNavigation = () => {
    if (step == CART_STEPS.TWO) {
      return t("carts_return_to_cart");
    }

    if (step == CART_STEPS.THREE) {
      return "";
      // return t("carts_return_to_infomation");
    }

    return t("carts_back_to_product");
  };

  return (
    <>
      <div
        data-cy="The-Cart-Page-Heading"
        className="border-bottom border-gray-200 bg-white py-4"
      >
        <div className="container">
          <div className="flex flex-col gap-2 md:flex-row">
            {step !== 0 && (
              <button
                onClick={handleNavigation}
                className="group flex cursor-pointer items-center gap-2 px-2 py-3 transition-all hover:bg-gray-200"
              >
                {step !== CART_STEPS.THREE && (
                  <HiChevronLeft className="h-5 w-5" />
                )}

                <span className="whitespace-nowrap font-semibold text-gray-600 transition-all">
                  {convertTitleNavigation()}
                </span>
              </button>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
