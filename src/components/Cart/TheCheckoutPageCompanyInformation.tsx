import { FormikErrors, FormikTouched } from "formik";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { MdError } from "react-icons/md";
import { PaymentProps, CompanyProps } from "@/types/cart";
import { classNames } from "@/utils";
import { getCompaniesPayment } from "@/services/cartAPI";
import Select from "react-select";

export default function TheCheckoutPageCompanyInformation({
  errors,
  touched,
  values,
  setFieldValue,
}: {
  errors: FormikErrors<PaymentProps>;
  touched: FormikTouched<PaymentProps>;
  values: PaymentProps;
  setFieldValue(
    field: string,
    value: any,
    shouldValidate?: boolean | undefined,
  ): void;
}) {
  const [companies, setCompanies] = useState<Array<CompanyProps>>([]);
  const [companiesOptions, setCompaniesOptions] = useState<
    { value: number; label: string }[]
  >([]);
  const [companiesOptionValue, setCompaniesOptionValue] = useState<{
    value: number;
    label: string;
  }>({
    label: "",
    value: 0,
  });
  const t = useTranslations();

  useEffect(() => {
    getCompaniesPayment()
      .then((data) => {
        if (data.status == 200) {
          setCompanies(data?.data?.data);
          setCompaniesOptions(
            data?.data?.data.map((compnay: CompanyProps) => {
              return {
                label: compnay.tax_number,
                value: compnay.id,
              };
            }),
          );
        }
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  useEffect(() => {
    setFieldValue("crm_company_id", Number(companies?.[0]?.id));
    setFieldValue("company_business_name", companies?.[0]?.business_name);
    setFieldValue("company_phone", companies?.[0]?.phone_number);
    setFieldValue("company_tax_number", companies?.[0]?.tax_number);

    if (companies && companies?.[0]?.id && companies?.[0]?.tax_number)
      setCompaniesOptionValue({
        value: companies?.[0]?.id,
        label: companies?.[0]?.tax_number,
      });

  }, [companies, setFieldValue, setCompaniesOptionValue]);

  const handleChangeValueCompany = (compnayId: number) => {
    const getCompay = companies.find(
      (compnay: CompanyProps) => compnay.id === compnayId,
    );

    setFieldValue("company_business_name", getCompay?.business_name);
    setFieldValue("company_phone", getCompay?.phone_number);
    setFieldValue("company_tax_number", getCompay?.tax_number);
    if (getCompay)
      setCompaniesOptionValue({
        value: getCompay.id,
        label: getCompay?.tax_number,
      });
  };

  return (
    <div className="checkout-company-informationtion flex flex-col gap-4 p-6">
      <h3 className="text-lg font-semibold text-gray-600">
        {t("carts_payment_company_information")}
      </h3>
      {companies.length === 0 && (
        <div className="flex flex-row gap-4 rounded bg-primary-100 p-4">
          <div className="h-[30px] w-[30px]">
            <MdError className="h-[30px] w-[30px] text-primary" />
          </div>
          <div className="text-sm">
            <h3 className="font-bold ">
              {t("carts_payment_unable_to_query_company_information")}
            </h3>
            <p>
              {t.rich("carts_payment_alert_content", {
                phone: (chunks) => (
                  <a className="underline" href={`tel:${chunks}`}>
                    {chunks}
                  </a>
                ),
                email: (chunks) => (
                  <a className="underline" href={`mailto:${chunks}`}>
                    {chunks}
                  </a>
                ),
              })}
            </p>
          </div>
        </div>
      )}

      <div className="flex flex-col items-start gap-1">
        <label className="font-bold text-gray-500" htmlFor="crm_company_id">
          {t("carts_payment_tax_code")} <sup className="text-primary">*</sup>
        </label>
        <div className="custom-react-select relative w-full">
          <Select
            classNamePrefix="select"
            isSearchable={true}
            name="crm_company_id"
            id="crm_company_id"
            options={companiesOptions as any}
            value={companiesOptionValue}
            onChange={(value: any) => {
              setFieldValue("crm_company_id", Number(value?.value));
              handleChangeValueCompany(Number(value?.value));
            }}
            placeholder={t("carts_payment_placeholder_tax_code")}
            className="h-full w-full rounded border-gray-200 bg-white placeholder:text-gray-300"
          />
          <div className="absolute right-5 top-4 z-10 hidden">
            <MdError className="h-5 w-5 text-primary" />
          </div>
        </div>
      </div>
      <div className="flex flex-col items-start gap-1">
        <label
          className="font-bold text-gray-500"
          htmlFor="company_business_name"
        >
          {t("carts_payment_name_of_business_registration_company")}{" "}
          <sup className="text-primary">*</sup>
        </label>
        <div className="relative w-full">
          <input
            className={classNames(
              !!errors["company_business_name"] &&
                !!touched["company_business_name"]
                ? "border-primary"
                : "border-gray-200",
              "h-[52px] w-full rounded  bg-white px-5 py-4 placeholder:text-gray-300 disabled:bg-gray-100",
            )}
            type="text"
            name="company_business_name"
            id="company_business_name"
            placeholder={t("carts_payment_placeholder_company_business_name")}
            onChange={(e) =>
              setFieldValue("company_business_name", e.target.value)
            }
            value={values.company_business_name ?? ""}
          />
          {!!errors["company_business_name"] &&
            !!touched["company_business_name"] && (
              <>
                <div className="absolute right-5 top-4 z-10">
                  <MdError className="h-5 w-5 text-primary" />
                </div>
                <span className="mt-2 block text-xs text-primary">
                  {errors["company_business_name"]}
                </span>
              </>
            )}
        </div>
      </div>
      <div className="flex flex-col items-start gap-1">
        <label className="font-bold text-gray-500" htmlFor="company_phone">
          {t("carts_payment_phone_number")}{" "}
        </label>
        <div className="relative w-full">
          <input
            className={classNames(
              !!errors["company_phone"] && !!touched["company_phone"]
                ? "border-primary"
                : "border-gray-200",
              "h-[52px] w-full rounded  bg-white px-5 py-4 placeholder:text-gray-300 disabled:bg-gray-100",
            )}
            type="text"
            name="company_phone"
            id="company_phone"
            onChange={(e) => setFieldValue("company_phone", e.target.value)}
            placeholder={t("carts_payment_placeholder_phone")}
            value={values.company_phone ?? ""}
          />
          {!!errors["company_phone"] && !!touched["company_phone"] && (
            <>
              <div className="absolute right-5 top-4 z-10">
                <MdError className="h-5 w-5 text-primary" />
              </div>
              <span className="mt-2 block text-xs text-primary">
                {errors["company_phone"]}
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
