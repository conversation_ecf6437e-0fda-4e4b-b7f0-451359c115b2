import { Button } from "@/components/Button";
import { useTranslations } from "next-intl";

export default function LoadingCart() {
  const t = useTranslations();
  const data = [0, 1, 2];

  const ItemCartLoading = () => {
    return (
      <div key={1} className="flex items-center justify-between py-2 md:py-4">
        <div className="flex w-2/5 items-center gap-4 px-4">
          <span className="flex h-6 w-6 animate-pulse items-center justify-center rounded-xl border border-gray-200 bg-gray-100 p-[4.356px] md:h-12 md:w-12"></span>
          <div className="h-5 w-2/3 animate-pulse rounded bg-slate-200"></div>
        </div>
        <div className="flex w-1/5 flex-col px-4 md:w-1/6">
          <div className="h-5 w-full animate-pulse rounded bg-slate-200"></div>
        </div>
        <div className="w-1/6 md:px-4">
          <div className="flex items-center">
            <div className="h-5 w-full animate-pulse rounded bg-slate-200"></div>
          </div>
        </div>
        <div className="w-1/6 px-4">
          <div className="h-5 w-full animate-pulse rounded bg-slate-200"></div>
        </div>
        <div className="w-1/12 px-4 text-center">
          <div className="h-5 w-5 animate-pulse rounded bg-slate-200"></div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="w-full md:w-2/3">
        <h3 className="mb-4 font-bold text-black md:mb-6 md:text-3xl">
          {t("carts_your_cart")}
        </h3>
        <div className="overflow-x-auto">
          <div className="min-w-[620px] rounded-xl bg-white shadow-sm">
            <div className="flex items-center justify-between">
              <div className="w-2/5 px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
                {t("carts_product")}
              </div>
              <div className="w-1/6 px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
                {t("carts_unit_price")}
              </div>
              <div className="w-1/6 whitespace-nowrap px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
                {t("carts_quantity")}
              </div>
              <div className="w-1/6 whitespace-nowrap px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
                {t("carts_provisional")}
              </div>
              <div className="w-1/12 px-4 py-4 text-sm font-semibold text-gray-600 md:text-lg">
                {t("carts_delete")}
              </div>
            </div>

            <>
              <div className="flex items-center gap-2 bg-gray-100 px-6 py-2">
                <span className="font-bold text-gray-600">
                  {t("carts_find_candidate_cv")}
                </span>
                <div className="h-5 w-5 animate-pulse rounded bg-slate-200"></div>
              </div>
              <ItemCartLoading />
              <ItemCartLoading />
              <ItemCartLoading />
            </>
          </div>
        </div>
      </div>
      <div className="w-full md:mt-[60px] md:w-1/3">
        <div className="rounded-xl bg-white shadow-sm">
          <h3 className="gap-2 border-b border-gray-200 p-4 text-center text-xl font-bold text-gray-600">
            <span className="inline-block h-5 w-1/3 animate-pulse rounded bg-slate-200"></span>
          </h3>
          <div className="align-self-stretch flex flex-col gap-5 px-6 py-8">
            <div className="align-self-stretch flex flex-col items-start gap-2 border-b border-dashed border-gray-200 pb-4">
              <div className="flex w-full flex-wrap items-start justify-between">
                <span className="font-bold text-gray-600">
                  {t("carts_total_order_value")}
                </span>
                <div className="h-5 w-1/3 animate-pulse rounded bg-slate-200"></div>
              </div>
              <div className="hidden w-full flex-wrap items-start justify-between">
                <span className="font-bold text-gray-600">
                  {t("carts_discount_order")}
                </span>
                <div className="h-5 w-1/3 animate-pulse rounded bg-slate-200"></div>
              </div>
              <div className="flex w-full flex-wrap items-start justify-between">
                <div className="h-5 w-1/3 animate-pulse rounded bg-slate-200"></div>
                <div className="h-5 w-1/3 animate-pulse rounded bg-slate-200"></div>
              </div>
            </div>
            <div className="flex w-full flex-wrap items-start justify-between">
              <div className="flex flex-col">
                <span className="font-bold text-gray-600">
                  {t("carts_total_payment")}
                </span>
                <span className="text-sm text-gray-400">
                  ({t("carts_vat_included")})
                </span>
              </div>
              <div className="h-5 w-1/3 animate-pulse rounded bg-slate-200"></div>
            </div>
          </div>
          <div className="animate-pulse gap-2 border-t border-gray-200 p-4">
            <Button accent="primary" isBlock>
              ...
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
