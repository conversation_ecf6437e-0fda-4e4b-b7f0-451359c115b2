import TheCheckoutVendorMegapay from "@/components/Cart/TheCheckoutVendorMegapay";
import TheCheckoutVendorMomo from "@/components/Cart/TheCheckoutVendorMomo";
import useSetDataCheckout from "@/hooks/useSetDataCheckout";

export default function TheCheckoutVendor() {
  const [dataPayment, setDataCheckout] = useSetDataCheckout();

  switch (dataPayment?.payment_vendor) {
    case "megapay":
      return <TheCheckoutVendorMegapay />;
    case "momo":
      return <TheCheckoutVendorMomo />;
    default:
      return <></>;
  }
}
