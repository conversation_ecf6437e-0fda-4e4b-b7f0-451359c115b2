import Image from "next/image";
import { useState } from "react";
import { useTranslations } from "next-intl";
import { HiOutlineMinusCircle, HiPlusCircle } from "react-icons/hi2";
import { OrderProp } from "@/types/cart";
import { formatPriceVND } from "@/utils";
import DeleteCart from "@/components/Button/DeleteCart";
import { ButtonUpdateToCartProp } from "@/types/products";
import Swal from "sweetalert2";
import { deleteToCart, updateToCart } from "@/services/cartAPI";
import useCart from "@/hooks/useCart";
import { setShowPopupLimitQuantity } from "@/store/slices/cartSlide";
import { useAppDispatch } from "@/store";

interface CartItemProp extends OrderProp {
  type: "package" | "credit";
}

export default function CartItem(props: CartItemProp) {
  const {
    name,
    price,
    product_id,
    quantity,
    total_anchoring_price,
    total_price,
    anchoring_price,
    type,
  } = props;
  const dispatch = useAppDispatch();
  const t = useTranslations();
  const [loadingCart, setLoadingCart] = useState<boolean>(false);
  const [cart, setCartData, recallCart] = useCart();

  const handleUpdateCart = async ({
    product_id,
    update_type,
  }: ButtonUpdateToCartProp) => {
    updateToCart({ product_id, update_type })
      .catch((error) => {
        //Notify the user when max quantity products
        if (
          update_type !== "decrement" &&
          Object.keys(error?.response?.data?.errors)?.length > 0
        ) {
          if (
            error?.response?.data?.errors?.quantity_max?.length > 0 ||
            error?.response?.data?.errors?.quantity?.length > 0
          ) {
            dispatch(setShowPopupLimitQuantity(true));
            return;
          }
        }

        //When quantity = 0 then delete product in carts
        if (
          update_type === "decrement" &&
          quantity == 1 &&
          error.response.data.message == "Unable to update cart"
        ) {
          Swal.fire({
            title: t("carts_toast_delete_cart_title"),
            icon: "question",
            showCancelButton: true,
            confirmButtonText: t("carts_toast_delete_cart_confirm"),
            confirmButtonColor: "#dd3f24",
            cancelButtonText: t("carts_toast_delete_cart_cancel"),
          }).then(async (result) => {
            if (result.isConfirmed) {
              await deleteToCart(product_id);
              await recallCart();
            }
          });
        }
      })
      .finally(() => {
        setLoadingCart(false);
        recallCart();
      });
  };

  return (
    <div
      data-cy="Cart-Item"
      className="flex items-center justify-between py-2 md:py-4"
    >
      <div className="flex w-2/5 items-center gap-4 px-4">
        <span className="flex h-6 w-6 items-center justify-center rounded-xl border border-gray-200 bg-gray-100 p-[4.356px] md:h-12 md:w-12">
          {type === "credit" ? (
            <Image
              src={"/v4/assets/images/online-payment/circle-stack.png"}
              alt="circle stack"
              width={31}
              height={31}
              className="h-[15px] w-[15px] md:h-[31px] md:w-[31px]"
            />
          ) : (
            <Image
              src={"/v4/assets/images/online-payment/documents.png"}
              alt="documents"
              width={31}
              height={31}
              className="h-[15px] w-[15px] md:h-[31px] md:w-[31px]"
            />
          )}
        </span>
        <span
          data-cy="name"
          className="text-sm inline-block w-2/3 font-bold text-gray-600 md:text-xl"
        >
          {name}
        </span>
      </div>
      <div className="flex w-1/5 flex-col px-4 md:w-1/6">
        {!!anchoring_price && anchoring_price > 0 && (
          <span data-cy="discount-price" className="text-gray-300 line-through">
            {formatPriceVND(anchoring_price)}
          </span>
        )}
        <span data-cy="price" className="font-semibold text-gray-600">
          {formatPriceVND(price)}
        </span>
      </div>
      <div className="w-1/6 md:px-4">
        <div className="flex items-center">
          <button
            data-cy="onclick-decrement"
            disabled={loadingCart}
            onClick={() =>
              handleUpdateCart({ product_id, update_type: "decrement" })
            }
            className="h-6 w-6 cursor-pointer disabled:opacity-80"
          >
            <HiOutlineMinusCircle className="h-full w-full" />
          </button>
          <input
            className="input-cart h-7 w-7 !border-none p-0 text-center !outline-none"
            min={0}
            minLength={1}
            value={quantity}
            readOnly
          />
          <button
            data-cy="onclick-increment"
            disabled={loadingCart}
            onClick={() =>
              handleUpdateCart({ product_id, update_type: "increment" })
            }
            className="h-6 w-6 cursor-pointer disabled:opacity-80"
          >
            <HiPlusCircle className="h-full w-full text-primary" />
          </button>
        </div>
      </div>
      <div className="w-1/6 px-4">
        <span
          data-cy="total-price"
          className="text-sm font-semibold text-primary md:text-base"
        >
          {!!total_price && formatPriceVND(total_price)}
        </span>
      </div>
      <div className="w-1/12 px-4 text-center">
        <DeleteCart productId={product_id} />
      </div>
    </div>
  );
}
