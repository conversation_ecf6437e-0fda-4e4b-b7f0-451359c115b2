import { useState } from "react";
import { useTranslations } from "next-intl";
import TotalOrder from "@/components/Cart/TotalOrder";
import useCart from "@/hooks/useCart";
import Link from "@/components/Link/Link";

export default function TheCheckoutPageInformationOrder({
  setFieldValue,
}: {
  setFieldValue(
    field: string,
    value: any,
    shouldValidate?: boolean | undefined,
  ): void;
}) {
  const t = useTranslations();
  const [cart] = useCart();
  const [disiable, setDisiable] = useState<boolean>(true);
  
  return (
    <div
      data-cy="The-Checkout-Page-Information-Order"
      className="w-full sticky top-0 md:mt-[60px] md:w-1/3"
    >
      <div className="rounded-xl bg-white shadow-sm">
        <h3 className="gap-2 border-b border-gray-200 p-4 text-center text-xl font-bold text-gray-600">
          {t("carts_order")}
        </h3>
        <TotalOrder
          total_tax={cart.total_tax}
          discount_total={cart.discount_total}
          order_total={cart.order_total}
          price_total={cart.price_total}
          tax_name={cart.tax_name}
        />
        <div className="gap-2 border-t border-gray-200 p-4">
          <div className="align-self-stretch mb-2 flex items-start gap-2">
            <input
              className="mt-1 h-5 w-5 cursor-pointer rounded border border-gray-300 checked:bg-primary"
              type="checkbox"
              id="term_of_use"
              name="term_of_use"
              onClick={(e) => {
                setDisiable((prev) => !prev);
                setFieldValue("term_of_use", (e.target as any).checked);
              }}
            />
            <label className="text-[15px]">
              {t.rich("carts_terms_policy", {
                terms: (chunks) => (
                  <Link
                    className="font-bold hover:text-primary"
                    href={"/term-of-services"}
                    target="_blank"
                  >
                    {chunks}
                  </Link>
                ),
                policy: (chunks) => (
                  <Link
                    className="font-bold hover:text-primary"
                    href={"/privacy-policy"}
                    target="_blank"
                  >
                    {chunks}
                  </Link>
                ),
              })}
            </label>
          </div>
          <button
            className="inline-flex h-9 w-full items-center justify-center gap-1 rounded border border-solid border-primary bg-primary px-4 text-sm font-semibold text-white transition-all hover:border-primary-400 hover:bg-primary-400 disabled:cursor-not-allowed disabled:border-gray-200 disabled:bg-gray-200 disabled:text-gray-100 lg:h-12 lg:gap-3 lg:px-6 lg:text-base"
            disabled={disiable}
            type="submit"
          >
            {t("carts_button_checkout_form")}
          </button>
        </div>
      </div>
    </div>
  );
}
