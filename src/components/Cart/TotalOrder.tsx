import { useTranslations } from "next-intl";
import { TextInput } from "flowbite-react";
import { HiOutlineTicket } from "react-icons/hi2";
import { TotalOrderProp } from "@/types/cart";
import { useState } from "react";
import { formatPriceVND } from "@/utils";

export default function TotalOrder({
  price_total,
  discount_total,
  order_total,
  tax_name,
  total_tax,
}: TotalOrderProp) {
  const t = useTranslations();
  const [show, setShow] = useState<boolean>(false);

  return (
    <div
      data-cy="Total-Order"
      className="align-self-stretch flex flex-col gap-5 px-6 py-8"
    >
      <div className="align-self-stretch border-b-1 hidden flex-wrap items-start gap-2 border-b border-dashed border-gray-200 pb-4">
        <div className="flex w-full items-start justify-between gap-2">
          <span className="flex items-center gap-1 font-bold text-gray-600">
            <HiOutlineTicket className="h-6 w-6" /> TopDev voucher
          </span>
          <span
            onClick={() => false && setShow((prev) => !prev)}
            className="cursor-pointer select-none font-semibold text-primary underline"
          >
            {t("carts_select_promotional_code")}
          </span>
        </div>
        {show && (
          <div className="w-full">
            <TextInput placeholder="Enter code" />
          </div>
        )}
      </div>
      <div className="align-self-stretch flex flex-col items-start gap-2 border-b border-dashed border-gray-200 pb-4">
        <div className="flex w-full flex-wrap items-start justify-between">
          <span className="font-bold text-gray-600">
            {t("carts_total_order_value")}
          </span>
          <span data-cy="price-total" className="font-semibold text-gray-600">
            {!!price_total && formatPriceVND(price_total ?? 0)}
          </span>
        </div>
        <div className="hidden w-full flex-wrap items-start justify-between">
          <span className="font-bold text-gray-600">
            {t("carts_discount_order")}
          </span>
          <span
            data-cy="discount-total"
            className="font-semibold text-gray-600"
          >
            {formatPriceVND(discount_total ?? 0)}
          </span>
        </div>
        <div className="flex w-full flex-wrap items-start justify-between">
          <span className="font-bold text-gray-600">
            {!!tax_name ? tax_name : t("carts_title_tax")}
          </span>
          <span data-cy="tax-total" className="font-semibold text-gray-600">
            {formatPriceVND(total_tax ?? 0)}
          </span>
        </div>
      </div>
      <div className="flex w-full flex-wrap items-start justify-between">
        <div className="flex flex-col">
          <span className="font-bold text-gray-600">
            {t("carts_total_payment")}
          </span>
          <span className="text-sm text-gray-400">
            ({t("carts_vat_included")})
          </span>
        </div>
        <span
          data-cy="order-total"
          className="text-xl font-semibold text-primary"
        >
          {!!order_total && formatPriceVND(order_total ?? 0)}
        </span>
      </div>
    </div>
  );
}
