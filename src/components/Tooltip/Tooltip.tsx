import { FC, ReactNode, useState } from "react";

const Tooltip: FC<{
  tooltip: string;
  children: ReactNode;
}> = ({ tooltip, children }) => {
  const [show, setShow] = useState(false);

  return (
    <>
      {children}
      {show ? (
        <div
          id="tooltip-default"
          role="tooltip"
          className="tooltip invisible absolute z-10 inline-block rounded-lg bg-gray-900 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-sm transition-opacity duration-300 dark:bg-gray-700"
        >
          {tooltip}
          <div className="tooltip-arrow"></div>
        </div>
      ) : (
        ""
      )}
    </>
  );
};

export default Tooltip;
