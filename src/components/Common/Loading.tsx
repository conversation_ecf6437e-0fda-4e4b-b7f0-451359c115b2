import { classNames } from "@/utils";
import React, { FC } from "react";

interface Props {
  size?: "sm" | "md";
}
const Loading: FC<Props> = ({ size = "md" }) => {
  return (
    <div className="flex items-center justify-center">
      <div
        className={classNames(
          size === "md" ? "h-10 w-10" : "",
          size === "sm" ? "h-6 w-6" : "",
          "inline-block animate-spin rounded-full border-4 border-solid border-black border-t-transparent",
        )}
      ></div>
    </div>
  );
};

export default React.memo(Loading);
