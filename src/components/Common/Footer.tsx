"use client";

import React from "react";
import Image from "next/image";
import Link from "@/components/Link/Link";
import { FaSquareYoutube } from "react-icons/fa6";
import { FaFacebookSquare, FaLinkedin } from "react-icons/fa";
import { useTranslations } from "next-intl";
import { TRACKER_UTM_TOPDEV_MAINMENU } from "@/utils/enums";
import ScrollTopButton from "../Button/ScrollTopButton";
import useCheckPageTechcombank from "@/hooks/useCheckPageTechcombank";
import FooterV2 from "../v2/shared/footer/page";

const Footer = () => {
  const t = useTranslations();
  const checkPageTechcombank = useCheckPageTechcombank();

  //Check page in group Ex: "/group/techcombank"
  if (checkPageTechcombank) {
    return <></>;
  }
  // if (process.env.RENEW_TOPDEV_2025 === "true") {
  return <FooterV2 />;
  // }

  // return (
  //   <footer>
  //     <div className="bg-gray-200 p-4 py-16 text-base text-gray-600">
  //       <div className="container">
  //         <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-6">
  //           <div className="col-span-1 md:col-span-2">
  //             <Link href="/" className="mb-4 inline-block max-w-[152px]">
  //               <Image
  //                 src="https://cdn.topdev.vn/v4/assets/images/td-logo.png"
  //                 alt="TopDev"
  //                 className="h-[30px] w-[152px]"
  //                 loading="lazy"
  //                 width="152"
  //                 height="30"
  //                 unoptimized
  //               />
  //             </Link>
  //             <ul className="mb-4">
  //               <li>{t("footer_address")}</li>
  //               <li className="mt-2">
  //                 {t("footer_tel")}:{" "}
  //                 <Link
  //                   href="tel:0888155500"
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   0888 1555 00
  //                 </Link>{" "}
  //                 -{" "}
  //                 <Link
  //                   href="mailto:<EMAIL>"
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   <EMAIL>
  //                 </Link>
  //               </li>
  //             </ul>
  //             <p className="mb-2 font-bold text-black lg:mb-2">
  //               {t("footer_verified_by")}
  //             </p>
  //             <Link
  //               className="mb-3 inline-block"
  //               href="http://online.gov.vn/HomePage/WebsiteDisplay.aspx?DocId=24677"
  //             >
  //               <Image
  //                 src="https://accounts.topdev.vn/asset/images/logo_bocongthuong.jpgx"
  //                 alt="Bo Cong Thuong Logo"
  //                 className="h-[60px] w-[155px] max-w-full"
  //                 loading="lazy"
  //                 width="155"
  //                 height="60"
  //                 unoptimized
  //               />
  //             </Link>
  //           </div>
  //           <div className="col-span-1">
  //             <p className="mb-2 font-bold text-black lg:mb-4">
  //               {t("footer_about_topdev")}
  //             </p>
  //             <ul className="grid gap-2">
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/about-us`}
  //                   title={t("footer_list_about_us")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_about_us")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/contact`}
  //                   title={t("footer_list_contact_us")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_contact_us")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/term-of-services`}
  //                   title={t("footer_list_terms_of_service")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_terms_of_service")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/topdev-buddies`}
  //                   title={t("footer_list_career_at_topdev")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_career_at_topdev")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`}
  //                   title={t("footer_list_privacy_policy")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_privacy_policy")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/operation-regulation`}
  //                   title={t("footer_list_operation_regulation_of_topdev")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_operation_regulation_of_topdev")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/resolve-complaints`}
  //                   title={t("footer_list_resolve_complaints")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_resolve_complaints")}
  //                 </Link>
  //               </li>
  //             </ul>
  //           </div>
  //           <div className="col-span-1">
  //             <p className="mb-2 font-bold text-black lg:mb-4 ">
  //               {t("footer_for_jobseekers")}
  //             </p>
  //             <ul className="grid gap-2">
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/tool/tinh-luong-gross-net`}
  //                   title={t("footer_list_salary_calculation_gross_net")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_salary_calculation_gross_net")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/tao-cv-online`}
  //                   title={t("footer_list_create_cv")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_create_cv")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/it-jobs`}
  //                   title={t("footer_list_browse_all_it_jobs")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_browse_all_it_jobs")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/trac-nghiem-tinh-cach`}
  //                   title={t("footer_list_personality_test")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_personality_test")}
  //                 </Link>
  //               </li>
  //             </ul>
  //           </div>
  //           <div className="col-span-1">
  //             <p className="mb-2 font-bold text-black lg:mb-4 ">
  //               {t("footer_for_employers")}
  //             </p>
  //             <ul className="grid gap-2">
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/recruit`}
  //                   title={t("footer_list_post_a_job")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_post_a_job")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/products`}
  //                   title={t("footer_list_talent_solutions")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_talent_solutions")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/bao-cao-it-viet-nam${TRACKER_UTM_TOPDEV_MAINMENU}`}
  //                   title={t("footer_list_it_market_report")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_it_market_report")}
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href="https://accounts.topdev.vn"
  //                   title={t("footer_list_create_account")}
  //                   className="transition-all hover:text-primary"
  //                 >
  //                   {t("footer_list_create_account")}
  //                 </Link>
  //               </li>
  //             </ul>
  //           </div>
  //           <div className="col-span-1">
  //             <p className="mb-2 font-bold text-black lg:mb-4 ">
  //               {t("footer_subscrible_us_on")}
  //             </p>
  //             <ul className=" mx-0 mb-4 flex items-center gap-4 text-4xl lg:mx-2">
  //               <li>
  //                 <Link
  //                   href="https://www.facebook.com/topdevvietnam"
  //                   className="text-black transition-all hover:text-primary"
  //                   aria-label="Click to go to TopDev's Facebook page"
  //                 >
  //                   <FaFacebookSquare />
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href="https://www.linkedin.com/company/topdev-vn/"
  //                   className="text-black transition-all hover:text-primary"
  //                   aria-label="Click to go to TopDev's Linkedin page"
  //                 >
  //                   <FaLinkedin />
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link
  //                   href="https://www.youtube.com/channel/UCZedbcmUtab8Y7DEt4ZZv7Q"
  //                   className="text-black transition-all hover:text-primary"
  //                   aria-label="Click to go to TopDev's Youtube channel"
  //                 >
  //                   <FaSquareYoutube />
  //                 </Link>
  //               </li>
  //             </ul>
  //             <p className="mb-2 font-bold text-black lg:mb-4 ">
  //               {t("footer_download_app_here")}
  //             </p>
  //             <ul className="mb-4 flex gap-1">
  //               <li>
  //                 <Link href="https://topdev.vn/s/MrEHZlyk">
  //                   <Image
  //                     src="https://cdn.topdev.vn/v4/assets/images/promote_app/app_store_img.png"
  //                     className="h-[28px] w-[94px] max-w-full"
  //                     alt="TopDev in app store"
  //                     loading="lazy"
  //                     width="94"
  //                     height="28"
  //                     unoptimized
  //                   />
  //                 </Link>
  //               </li>
  //               <li>
  //                 <Link href="https://play.google.com/store/apps/details?id=it.jobs.topdev&referrer=utm_source%3Dtopdev%26utm_medium%3Dfooter_default">
  //                   <Image
  //                     src="https://cdn.topdev.vn/v4/assets/images/promote_app/google_play_img.png"
  //                     className="h-auto max-w-full"
  //                     alt="TopDev in app store"
  //                     loading="lazy"
  //                     width="94"
  //                     height="28"
  //                     unoptimized
  //                   />
  //                 </Link>
  //               </li>
  //             </ul>
  //           </div>
  //         </div>
  //         <div className="pt-16 text-center text-gray-400">
  //           Copyright &copy; CÔNG TY CỔ PHẦN APPLANCER /{" "}
  //           {t("footer_business_registrantion")}: 031 303 2338 -{" "}
  //           {t("footer_issues_on")}: 27/11/2014
  //         </div>
  //         <ScrollTopButton />
  //       </div>
  //     </div>
  //   </footer>
  // );
};

export default Footer;
