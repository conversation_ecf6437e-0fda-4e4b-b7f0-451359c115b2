"use client";

import { usePathname, useRouter } from "@/navigation";
import { Lang } from "@/types/page";
import { classNames } from "@/utils";
import Cookies from "js-cookie";
import { useLocale } from "next-intl";
import { useParams } from "next/navigation";

const SwitchLocale = () => {
  const params = useParams();
  const pathname = usePathname();
  const locale = useLocale();
  const router = useRouter();

  // handle Change Locale
  const handleChangeLocale = async (lang: Lang) => {
    if (lang == locale) {
      console.log("NICE TO SEE YOU HERE ^^");
      return;
    }

    Cookies.set("topdev_locale", lang, {
      expires: 365,
      domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
    });

    router.replace(
      {
        pathname,
        params: params as any,
      },
      { locale: lang },
    );
  };

  // Start template
  return (
    <div className="flex items-center divide-x">
      <button
        onClick={() => handleChangeLocale("en")}
        type="button"
        className={classNames(
          locale == "en" ? "font-bold" : "text-gray-400",
          "px-2 uppercase transition-all hover:text-primary",
        )}
      >
        En
      </button>
      <button
        onClick={() => handleChangeLocale("vi")}
        type="button"
        className={classNames(
          locale == "vi" ? "font-bold" : "text-gray-400",
          "px-2 uppercase transition-all hover:text-primary",
        )}
      >
        Vi
      </button>
    </div>
  );
};

export default SwitchLocale;
