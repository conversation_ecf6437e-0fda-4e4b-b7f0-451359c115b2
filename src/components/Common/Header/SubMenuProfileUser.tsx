import Link from "@/components/Link/Link";
import { useAppSelector } from "@/store";
import { listMenu } from "@/utils";
import Cook<PERSON> from "js-cookie";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import React from "react";
import { useEffect, useState } from "react";

interface PropsSubmenu {
  page?: string | "products";
  position?: string; // Class name position
}

type RoleType = keyof typeof listMenu.account;

// Sub menu Profile user
const SubMenuProfileUser = ({ page = "", position }: PropsSubmenu) => {
  const t = useTranslations();
  const router = useRouter();
  const user = useAppSelector((state) => state?.user?.user);
  const [currentRole, setCurrentRole] = useState<RoleType | undefined>(
    undefined,
  );

  useEffect(() => {
    if (!user?.roles) return;
    let role = user.roles[0];
    if (!!page) {
      role = role + "_" + page;
    }

    setCurrentRole(role as RoleType);
  }, [user?.roles, page]);

  const handleLogout = (redirectURL: string) => {
    window.location.href = process.env.NEXT_PUBLIC_OAUTH2_URL_LOGOUT + "";
  };

  if (!currentRole) {
    return null;
  }

  return (
    <ul
      className={`absolute opacity-0 ${position} invisible top-[110%] -z-50 min-w-[220px] rounded bg-white shadow-sm  group-hover:visible group-hover:top-full group-hover:z-[25] group-hover:opacity-100 lg:min-w-[315px]`}
    >
      {listMenu.account?.[currentRole].map((value, index) => {
        return (
          <li key={index}>
            <Link
              href={value.href ?? ""}
              title={t(value.title)}
              className="group/link-sub flex items-center p-1 text-sm text-gray-600  hover:bg-gray-100 lg:p-3 lg:text-base"
            >
              <span className="mr-2 rounded bg-gray-100 p-2 text-sm transition duration-300 group-hover/link-sub:bg-white group-hover/link-sub:text-primary-300 lg:text-2xl">
                {React.createElement(value.icon)}
              </span>
              {t(value.title)}
            </Link>
          </li>
        );
      })}
    </ul>
  );
};

export default SubMenuProfileUser;
