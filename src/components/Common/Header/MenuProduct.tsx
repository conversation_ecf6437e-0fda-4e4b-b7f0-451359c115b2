"use client";

import SwitchLocale from "@/components/Common/Header/SwitchLocale";
import Link from "@/components/Link/Link";
import useAddToCart from "@/hooks/useAddToCart";
import useCart from "@/hooks/useCart";
import { useAppSelector } from "@/store";
import { classNames, listMenu, openLoginPopup } from "@/utils";
import { Tooltip } from "flowbite-react";
import { useTranslations } from "next-intl";
import Image from "next/image";
import { createElement, useEffect, useRef, useState } from "react";
import { GoChevronDown } from "react-icons/go";
import { HiPhone } from "react-icons/hi";
import { HiOutlineShoppingCart } from "react-icons/hi2";
import { MdAccountCircle } from "react-icons/md";
import SubMenuProfileUser from "./SubMenuProfileUser";

export default function MenuProduct() {
  const [countCart, setCountCart] = useState<number>(0);
  const [cart, setCartData, recallCart] = useCart();
  const { checkFlowCartForUser } = useAddToCart();
  const user = useAppSelector((state) => state?.user?.user);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const t = useTranslations();
  const header = useRef<HTMLDivElement>(null);
  const headerDefault = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setCountCart(cart.items_count);
  }, [cart]);

  useEffect(() => {
    recallCart();
  }, []);

  //Click in cart
  const handleCartHeaderClick = () => checkFlowCartForUser("onlyCheck");
  //End click in cart

  // Template Not Login
  const TemplateNotLogin = () => {
    return (
      <div className="flex items-center">
        <button
          type="button"
          id="btn-login-header"
          className="my-1 inline-block whitespace-nowrap rounded border border-primary-300 bg-primary-300 font-semibold text-white transition-all hover:bg-primary-400 sm:px-1 sm:py-2 sm:text-xs md:px-4 lg:px-6 lg:py-3 lg:text-base"
          role="navigation"
          title={t("header_login")}
          onClick={() => openLoginPopup([{ name: "isEmployer", value: true }])}
        >
          {t("header_login")}
        </button>
      </div>
    );
  };
  // End Template Not Login

  // Template LoggedIn
  const TemplateLoggedIn = () => {
    return (
      <div className="flex items-center">
        <div className="group relative py-4 lg:py-5">
          <button className="group/button flex max-w-[100px] rounded transition-all hover:bg-gray-100 lg:max-w-none lg:px-6 lg:py-3">
            <MdAccountCircle className="hidden h-5 w-5 lg:block" />
            <span className="mx-1 line-clamp-1 text-xs font-semibold text-gray-600 lg:mx-5 lg:text-sm">
              {user?.display_name}
            </span>
            <GoChevronDown className="transition duration-500 group-hover/button:rotate-180 lg:h-5 lg:w-5" />
          </button>
          <SubMenuProfileUser position="right-0" page="products" />
        </div>
      </div>
    );
  };
  // End Template LoggedIn

  return (
      <header
        ref={header}
        id="header"
        className="sticky top-0 z-20 bg-white shadow-sm"
      >
        <div className="container lg:max-w-[1748px]">
          <div className="flex h-[5.25rem] items-center justify-between">
            <div className="flex items-center justify-between">
              {/* Logo header */}
              <div className="flex flex-col items-start gap-3 md:w-[188px] lg:flex-row lg:items-end">
                <Link
                  href="/"
                  className="inline-block w-full py-2 sm:p-0 lg:w-[188px]"
                >
                  <Image
                    src="https://cdn.topdev.vn/v4/assets/images/td-logo.png"
                    alt="TopDev"
                    width={188}
                    height={36}
                    loading="lazy"
                  />
                </Link>
              </div>
              {/* End Logo header */}

              {/* menu header */}
              <ul className="hidden pl-1 sm:flex lg:pl-4">
                <li className="group relative">
                  <Link
                    className="flex px-8 py-[1.95rem] text-base font-bold capitalize text-black group-hover:text-primary"
                    title="product"
                    href="/products"
                  >
                    {t("products_title_menu")}
                    <GoChevronDown className="ml-[10px] text-xl transition duration-500 group-hover:rotate-180 lg:block" />
                  </Link>
                  <ul className="invisible absolute top-[110%] -z-50 min-w-[220px] rounded bg-white opacity-0 shadow-sm transition-all duration-200 ease-out group-hover:visible group-hover:top-full group-hover:z-[25] group-hover:opacity-100 lg:min-w-[315px]">
                    {listMenu.products.map((value, index) => {
                      return (
                        <li key={index}>
                          <Link
                            href={value.href}
                            title={t(value.title)}
                            className="group/link-sub flex items-center p-3 text-base text-gray-600 transition-all hover:bg-gray-100"
                          >
                            <span className="mr-2 h-10 w-10 rounded bg-gray-100 p-2 text-2xl transition-all group-hover/link-sub:bg-white group-hover/link-sub:text-primary-300">
                              {value.icon === "image" ? (
                                <Image
                                  src={
                                    "/v4/assets/images/online-payment/diamond-outlined.png"
                                  }
                                  alt="Diamond Outlined"
                                  width="24"
                                  height="24"
                                  loading="lazy"
                                />
                              ) : (
                                createElement(value.icon)
                              )}
                            </span>
                            {t(value.title)}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                </li>
              </ul>
              {/* End menu header */}
            </div>
            <div
              id="headerRightDefault"
              ref={headerDefault}
              className="flex items-center justify-end"
            >
              <Link
                href="tel:0888 1555 00"
                className="mr-5 inline-flex gap-2 font-semibold text-gray-600 transition-all hover:text-primary"
              >
                <span className="inline-flex h-5 w-5 items-center justify-center text-xl">
                  <HiPhone />
                </span>
                <span>0888 1555 00</span>
              </Link>
              <Tooltip
                content={t("products_tooltip_cart_header")}
                placement="bottom"
              >
                <div
                  className="relative flex h-12 w-12 cursor-pointer items-center justify-center rounded text-center hover:bg-gray-200"
                  onClick={() => handleCartHeaderClick()}
                >
                  {countCart > 0 ? (
                    <span className="absolute right-[3px] top-[5px] inline-block h-5 w-5 rounded-full bg-primary-100 text-center text-xs font-bold leading-5 text-primary">
                      {countCart}
                    </span>
                  ) : (
                    ""
                  )}
                  <HiOutlineShoppingCart className="h-6 w-6" />
                </div>
              </Tooltip>
              {/* Info login header */}
              <div
                className={classNames(
                  isLoggedIn ? "" : "ml-6",
                  "header-login hidden pr-0 sm:block",
                )}
              >
                {isLoggedIn ? TemplateLoggedIn() : TemplateNotLogin()}
              </div>
              {/* End Info login header */}
              <div className="ml-4 text-xs lg:text-base">
                <SwitchLocale />
              </div>
            </div>
          </div>
        </div>
      </header>
  );
}
