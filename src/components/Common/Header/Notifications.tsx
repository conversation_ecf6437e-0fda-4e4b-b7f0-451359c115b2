"use client";

import { useEffect, useState, useRef } from "react";
import { useTranslations } from "next-intl";
import { IoIosNotifications } from "react-icons/io";
import {
  fetchMaskAsSeen,
  fetchNotificationApi,
  requestMaskAsRead,
} from "@/services/userAPI";
import { Notification } from "@/types/user";
import classNames from "@/utils/classNames";
import useJustOnce from "@/hooks/useJustOnce";
import { useIntersectionObserver, useOnClickOutside } from "usehooks-ts";
import dayjs from "dayjs";
import Loading from "../Loading";
import { useAppSelector } from "@/store";

const Notifications = () => {
  const t = useTranslations();
  const notificationRef = useRef(null);
  const [isShowNotification, setIsShowNotification] = useState<boolean>(false);
  const [nextPage, setNextPage] = useState<number>(1);
  const [hasValue, setHasValue] = useState<boolean>(true);
  const [dataNoti, setDataNoti] = useState<Array<Notification>>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { isValid: isFirstRender, handleChangeValue } = useJustOnce();
  const loadMoreRef = useRef<HTMLLIElement | null>(null);
  const entry = useIntersectionObserver(loadMoreRef, {});
  const isVisible = !!entry?.isIntersecting;
  const user = useAppSelector((state) => state?.user?.user);

  useEffect(() => {
    if (isFirstRender) {
      return;
    }

    if (!hasValue) {
      return;
    }

    if (isVisible && hasValue) {
      Promise.all([fetchSeenAction(), fetchNotification()]);
    }
  }, [isVisible]);

  //mask As Read
  const maskAsRead = (id: number | undefined, url: string | undefined) => {
    if (dataNoti.length === 0) return;
    requestMaskAsRead(id)
      .then(() => {
        if (url) {
          const actionUrl = new URL(url);
          const actionUrlWithParam = new URL(
            actionUrl.origin + actionUrl.pathname,
          );
          actionUrlWithParam.searchParams.append("src", "web_push");
          if (actionUrl.searchParams.has("medium")) {
            let medium = actionUrl.searchParams.get("medium");
            if (medium && medium !== null) {
              actionUrlWithParam.searchParams.append("medium", medium!.trim());
            }
          }
          const newDataNotify = dataNoti.map((item) =>
            item.id === id
              ? { ...item, read_at: new Date().toISOString() }
              : item,
          );
          setDataNoti(newDataNotify);
          window.open(actionUrlWithParam.href, "_blank");
        } else {
          const newDataNotify = dataNoti.map((item) => ({
            ...item,
            read_at: new Date().toISOString(),
          }));
          setDataNoti(newDataNotify);
          handleMaskReadAll();
        }
      })
      .catch((error: string) => {
        console.error(error);
      });
  };
  // End mask As Read

  // Mask read all
  const handleMaskReadAll = () => {
    try {
      const date = dayjs().format("DD-MM-YYYY");
      const newArr = dataNoti.map((item) => {
        return { ...item, read_at: date };
      });
      setDataNoti(newArr);
    } catch (error) {}
  };
  // End Mask read all

  const fetchSeenAction = async () => {
    await fetchMaskAsSeen()
      .then(() => {})
      .catch((error: any) => {
        console.error(error);
      });
  };

  const fetchNotification = async () => {
    setIsLoading(true);
    try {
      const { data } = await fetchNotificationApi(nextPage);
      setDataNoti([...dataNoti, ...data.data]);
      if (data.meta.current_page >= data.meta.last_page) {
        setHasValue(false);
      } else {
        let NewNextPage = nextPage + 1;
        setNextPage(NewNextPage);
      }
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      throw err;
    }
  };

  const handleToggleNotification = async () => {
    setIsShowNotification(!isShowNotification);
    if (isFirstRender) {
      handleChangeValue();
      Promise.all([fetchSeenAction(), fetchNotification()]);
    }
  };

  const handleClickContext = () => {
    setIsShowNotification(false);
  };

  useOnClickOutside(notificationRef, handleClickContext);

  if (user && user?.roles?.[0] === "employer") {
    return null;
  }

  //Start template
  return (
    <div
      className="group/notification relative py-4 lg:py-5"
      ref={notificationRef}
    >
      <button
        onClick={handleToggleNotification}
        className="h-5 w-5 rounded text-center transition-all hover:bg-gray-200 focus:bg-gray-200 lg:mr-2 lg:h-11 lg:w-11"
      >
        <IoIosNotifications className="inline-block h-5 w-5" />
      </button>
      <div
        className={classNames(
          !!isShowNotification
            ? "opacity-full visible top-full z-auto"
            : "invisible top-[110%] -z-50 opacity-0",
          "absolute right-0 min-w-[450px] rounded bg-white shadow-md transition-all duration-200 ease-out",
        )}
      >
        <div className="header-notification flex items-center justify-between border-b border-gray-200 p-4">
          <h3 className="text-sm font-bold text-gray-600 lg:text-base">
            {t("header_notification")}
          </h3>
          {dataNoti.length > 0 && (
            <button
              role="button"
              onClick={() => maskAsRead(undefined, undefined)}
              className="bg-gray-200 px-4 py-[6px] text-xs font-semibold text-gray-600 transition-all hover:bg-primary hover:text-white lg:text-sm"
            >
              {t("header_mark_all_as_read")}
            </button>
          )}
        </div>
        <ul className="main-notification max-h-[570px] overflow-y-auto overscroll-contain">
          {dataNoti.length > 0 &&
            dataNoti?.map((value) => {
              return (
                <li
                  key={value.id}
                  onClick={() => maskAsRead(value.id, value.action_url)}
                  className={classNames(
                    !value.read_at ? "bg-gray-100" : "",
                    "cursor-pointer p-2 transition-all hover:bg-gray-100 lg:gap-2 lg:p-4",
                  )}
                >
                  <p className="line-clamp-1 text-sm font-bold text-gray-600 lg:text-base">
                    {value.title}
                  </p>
                  <div className="flex items-center justify-between">
                    <p className="line-clamp-1 w-[91%] text-sm text-gray-600 lg:text-base">
                      {value.body}
                    </p>
                    {!value.read_at && (
                      <span className="inline-block h-[7px] w-[7px] rounded-full bg-primary-300 lg:h-[12px] lg:w-[12px]"></span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400 lg:text-base">
                    {value.created_at}
                  </p>
                </li>
              );
            })}
          {dataNoti.length === 0 && !isLoading && (
            <li className="p-4 text-base font-bold text-gray-500">
              {t("header_no_notification")}
            </li>
          )}
          <li
            ref={loadMoreRef}
            className={classNames("py-2.5", hasValue ? "block" : "hidden")}
          >
            <Loading size="sm" />
          </li>
        </ul>
      </div>
    </div>
  );
};

export default Notifications;
