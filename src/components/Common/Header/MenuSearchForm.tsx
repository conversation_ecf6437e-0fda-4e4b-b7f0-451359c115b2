import { AiOutlineSearch } from "react-icons/ai";
import { HiUser } from "react-icons/hi";
import { useRouter } from "next/navigation";
import { JobSearch } from "@/types/job";
import { ChangeEvent, FormEvent, useState } from "react";
import { searchByKeyword } from "@/services/jobAPI";
import { useTranslations } from "next-intl";
import { useAppSelector } from "@/store";
import { openLoginPopup, classNames } from "@/utils";
import Link from "@/components/Link/Link";
import SubMenuProfileUser from "./SubMenuProfileUser";

export default function MenuSearchForm() {
  const router = useRouter();
  const t = useTranslations();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const [dataSearchJobs, setDataSearchJobs] = useState<Array<JobSearch>>([]);
  const [keyword, setKeyword] = useState("");

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (keyword === "") return;
    const getJobFocused = dataSearchJobs.filter(
      (value) => value.keyword.toLocaleLowerCase() === keyword,
    );
    if (getJobFocused.length > 0) {
      const job = getJobFocused[0];
      router.push(`/${t("slug_it_jobs")}/${job.slug}-kt${job.id}`);
      setDataSearchJobs([]);
      return;
    }
    setDataSearchJobs([]);
    router.push(`/${t("slug_it_jobs")}`);
    setKeyword("");
  };

  // Search Jobs
  const searchJobs = async (event: ChangeEvent<HTMLInputElement>) => {
    setKeyword(event.target.value);
    if (!event.target.value) return setDataSearchJobs([]);
    try {
      const { data } = await searchByKeyword(event.target.value);
      setDataSearchJobs(data.data);
    } catch (error) {
      throw error;
    }
  };
  // End Search Jobs

  return (
    <form
      onSubmit={handleSubmit}
      id="headerRightSroll"
      className="flex w-full items-center justify-end"
    >
      <div className="group relative w-full max-w-[462px] rounded-[4px] bg-gray-100">
        <div className="absolute inset-y-0 flex h-[34px] cursor-pointer items-center pl-3 lg:h-[40px]">
          <AiOutlineSearch
            className="h-5 w-5 text-primary-300"
            aria-hidden="true"
          />
          <span className="line-search ml-2 inline-block h-[20px] w-[1px] bg-gray-300"></span>
        </div>
        <input
          type="text"
          name="keyword"
          id="keyword"
          className="rounded-md focus:trasition-all block w-full border-0 bg-transparent py-[5px] pl-12 pr-5 text-base text-gray-900 placeholder:text-base placeholder:text-gray-300 focus:rounded focus:ring-1 focus:ring-inset focus:ring-primary-300 sm:leading-6 lg:py-[10px]"
          placeholder={t("header_search_placeholder")}
          value={keyword}
          onChange={searchJobs}
          autoComplete="off"
        />
        {dataSearchJobs?.length > 0 && (
          <ul
            className={classNames(
              dataSearchJobs?.length
                ? "visible top-full z-auto opacity-100"
                : "invisible top-[110%] -z-50 opacity-0",
              "absolute w-full rounded bg-white shadow-sm transition-all duration-200 ease-out",
            )}
          >
            {dataSearchJobs?.map((value) => {
              return (
                <li key={value.id}>
                  <Link
                    href={`${process.env.NEXT_PUBLIC_BASE_URL}/${t(
                      "slug_it_jobs",
                    )}/${value.slug}-kt${value.id}`}
                    title={value.keyword}
                    className="group/link-sub flex items-center p-1 text-sm text-gray-600 transition-all hover:bg-gray-100 lg:p-3 lg:text-base"
                  >
                    {value.keyword}
                  </Link>
                </li>
              );
            })}
          </ul>
        )}
      </div>
      <div className="group relative">
        <button
          className="ml-2 box-border rounded border-2 border-primary-300 bg-primary-300 p-[5px] text-xl text-white transition-all hover:border-primary-200 hover:bg-primary-400 lg:p-[10px]"
          role="button"
          type="button"
          onClick={() => !isLoggedIn && openLoginPopup()}
        >
          <HiUser />
        </button>
        <SubMenuProfileUser position="right-0" />
      </div>
    </form>
  );
}
