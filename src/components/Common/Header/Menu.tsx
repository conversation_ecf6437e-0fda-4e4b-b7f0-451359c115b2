"use client";

import Link from "@/components/Link/Link";
import { useEffect, useRef } from "react";
import { useTranslations } from "next-intl";
import { HiChevronRight, HiPhone } from "react-icons/hi";
import { GoChevronDown } from "react-icons/go";
import { MdAccountCircle } from "react-icons/md";

import { TRACKER_UTM_TOPDEV_MAINMENU } from "@/utils/enums";
import { chunkSubMenu, listMenu, openLoginPopup } from "@/utils";
import { getCookie } from "@/utils/cookies";
import { SubMenu } from "@/types/page";
import { useAppSelector } from "@/store";
import SubMenuProfileUser from "./SubMenuProfileUser";
import SwitchLocale from "./SwitchLocale";
import Notifications from "./Notifications";
import useCheckPageTechcombank from "@/hooks/useCheckPageTechcombank";
import React from "react";
import MenuItems from "@/assets/json/menu.json";
import MenuSearchForm from "./MenuSearchForm";
import Logo from "./Logo";

export default function Menu() {
  const t = useTranslations();
  const header = useRef<HTMLDivElement>(null);
  const headerDefault = useRef<HTMLDivElement>(null);
  const headerSroll = useRef<HTMLDivElement>(null);
  const user = useAppSelector((state) => state?.user?.user);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const subMenu =
    getCookie("topdev_locale") == "en" ? MenuItems.en : MenuItems.vi;

  /**
   * Handles the scroll event and updates the header visibility based on the scroll position.
   * If current page is detail job page, the header will not be sticky.
   */
  const handleScroll = () => {
    const pageDetail = document.getElementById("detailJobPage");
    if (!!pageDetail) {
      header?.current?.classList?.remove("sticky");
      header?.current?.classList?.add("relative");
    } else {
      header?.current?.classList?.add("sticky");
      header?.current?.classList?.remove("relative");
    }

    headerDefault?.current?.classList.replace("hidden", "flex");
    headerSroll?.current?.classList.add("hidden");

    if (Number(window.scrollY) > Number(header?.current?.clientHeight)) {
      headerDefault?.current?.classList.add("hidden");
      headerSroll?.current?.classList.replace("hidden", "flex");
    }
  };

  /**
   * Register the scroll event listener when the component is mounted.
   */
  useEffect(() => {
    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  /**
   * Adjusts the height of the submenu based on the height of its parent element.
   * @param event - The event object triggered by the user action.
   */
  const fixedHeightMenuFollowSubmenu = (event: any) => {
    const targetParentElement = event.target?.parentElement;
    const subMenu = document.getElementById("subMenuItJobs");
    if (subMenu) subMenu.style.height = "";
    const subMenuHeight = subMenu?.offsetHeight;
    const child = targetParentElement?.children;
    if (child?.length < 2) return;

    const childElement = child?.[1];
    childElement.classList.remove("h-full");
    const childHeight = childElement?.offsetHeight;
    if (Number(subMenuHeight) >= Number(childHeight)) {
      childElement.classList.add("h-full");
    }

    if (subMenu) {
      if (Number(subMenuHeight) <= Number(childHeight)) {
        subMenu.style.height = `${childHeight}px`;
      }
    }
  };

  /**
   * Renders the template for the header when the user is not logged in.
   * @returns JSX.Element representing the header template for non-logged in users.
   */
  const TemplateNotLogin = () => {
    return (
      <div className="flex items-center">
        <Link
          href="tel:0888 1555 00"
          className="mr-4 inline-flex gap-2 font-semibold text-gray-600 transition-all hover:text-primary"
        >
          <span className="inline-flex h-5 w-5 items-center justify-center text-xl">
            <HiPhone />
          </span>
          <span>0888 1555 00</span>
        </Link>
        <Link
          className="my-1 mr-2 inline-block whitespace-nowrap rounded border border-primary-300 text-xs font-semibold text-primary-300 transition-all hover:bg-primary-100 sm:px-1 sm:py-2 md:px-4 lg:px-6 lg:py-3 lg:text-base"
          role="navigation"
          title={t("header_login_employer")}
          href={`${process.env.NEXT_PUBLIC_BASE_URL}/recruit${TRACKER_UTM_TOPDEV_MAINMENU}`}
        >
          {t("header_login_employer")}
        </Link>
        <button
          type="button"
          id="btn-login-header"
          className="my-1 inline-block whitespace-nowrap rounded border border-primary-300 bg-primary-300 font-semibold text-white transition-all hover:bg-primary-400 sm:px-1 sm:py-2 sm:text-xs md:px-4 lg:px-6 lg:py-3 lg:text-base"
          role="navigation"
          title={t("header_login")}
          onClick={() => {
            const params = [];

            if (window.location.href.includes('/viec-lam/') || window.location.href.includes('/detail-jobs/')) {
              params.push({
                name: "referring_name",
                value: "header"
              });
            }

            openLoginPopup(params);
          }}
        >
          {t("header_login")}
        </button>
      </div>
    );
  };

  /**
   * Renders the template for the logged-in user.
   * @returns JSX.Element representing the template for the logged-in user.
   */
  const TemplateLoggedIn = () => {
    return (
      <div className="flex items-center">
        <Notifications />
        <div className="group relative py-4 lg:py-5">
          <button className="group/button flex max-w-[100px] rounded transition-all hover:bg-gray-100 lg:max-w-none lg:px-6 lg:py-3">
            <MdAccountCircle className="hidden h-5 w-5 lg:block" />
            <span className="mx-1 line-clamp-1 text-xs font-semibold text-gray-600 lg:mx-5 lg:text-sm">
              {user?.display_name}
            </span>
            <GoChevronDown className="transition duration-500 group-hover/button:rotate-180 lg:h-5 lg:w-5" />
          </button>
          <SubMenuProfileUser position="lg:left-0 right-0" />
        </div>
      </div>
    );
  };

  // Check page in group Ex: "/group/techcombank"
  if (useCheckPageTechcombank()) {
    return <></>;
  }

  return (
    <header
      ref={header}
      id="header"
      className="sticky top-0 z-20 bg-white shadow-sm"
    >
      <div className="container flex h-[5.25rem] items-center justify-between lg:max-w-[1748px]">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Logo />
          {/* End Logo */}

          {/* Menu */}
          <ul className="hidden pl-1 sm:flex lg:pl-4">
            <li className="group relative">
              <Link
                className="group flex items-center font-bold capitalize text-black group-hover:text-primary sm:px-[0.4rem] sm:py-2 sm:text-xs md:px-3 lg:px-8 lg:py-[1.95rem] lg:text-base"
                title={t("header_it_jobs")}
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/${
                  t("slug_it_jobs") + TRACKER_UTM_TOPDEV_MAINMENU
                }`}
              >
                {t("header_it_jobs")}
                <GoChevronDown className="ml-[10px] hidden text-xl transition duration-300 group-hover:rotate-180 lg:block" />
              </Link>

              {/* sub menu header */}
              {subMenu?.length && (
                <ul
                  id="subMenuItJobs"
                  className="invisible absolute top-[110%] -z-50 min-w-[200px] bg-white opacity-0 shadow-sm  group-hover:visible group-hover:top-full group-hover:z-[25] group-hover:opacity-100 lg:min-w-[285px]"
                >
                  {subMenu?.map((value, index: number) => {
                    let chunkValueSubMenu = chunkSubMenu(
                      value.sub as Array<SubMenu>,
                    );
                    return (
                      <li
                        key={`${value.type}-${index}`}
                        className="group/link-sub block hover:bg-gray-100"
                      >
                        <Link
                          className="flex w-full items-center justify-between p-1 text-sm text-gray-600 lg:p-4 lg:text-base"
                          href={value.url ?? ""}
                          title={value.title}
                          onMouseOver={fixedHeightMenuFollowSubmenu}
                        >
                          {value.title}
                          <HiChevronRight
                            className="h-[20px] w-[20px]"
                            onMouseOver={(event: any) =>
                              event.stopPropagation()
                            }
                          />
                        </Link>
                        {value?.sub?.length && chunkValueSubMenu.length > 0 && (
                          <ul className="invisible absolute left-full top-[10px] -z-50 flex w-[300px] flex-wrap bg-white opacity-0 shadow-sm group-hover/link-sub:visible group-hover/link-sub:top-0 group-hover/link-sub:z-auto group-hover/link-sub:opacity-100 lg:w-[560px]">
                            {chunkValueSubMenu?.map(
                              (
                                valueSubChunk: Array<SubMenu>,
                                indexSubChunk: number,
                              ) => {
                                return (
                                  <ul
                                    key={`chunk-${indexSubChunk}`}
                                    className="flex flex-1 flex-col"
                                  >
                                    {valueSubChunk.map(
                                      (valueSub: SubMenu, indexSub: number) => {
                                        return (
                                          <li
                                            key={`sub-in-sub-${indexSubChunk}-${indexSub}`}
                                          >
                                            <Link
                                              href={valueSub.url ?? ""}
                                              title={valueSub.title}
                                              className="block p-1 text-sm text-gray-600 hover:bg-gray-100 lg:p-4 lg:text-base"
                                            >
                                              {valueSub.title}
                                            </Link>
                                          </li>
                                        );
                                      },
                                    )}
                                  </ul>
                                );
                              },
                            )}
                          </ul>
                        )}
                      </li>
                    );
                  })}
                </ul>
              )}
              {/* End sub menu header */}
            </li>
            <li>
              <Link
                className="block font-bold capitalize text-black hover:text-primary sm:px-[0.4rem] sm:py-2 sm:text-xs md:px-3 lg:px-8 lg:py-[1.95rem] lg:text-base"
                title={t("header_it_companies")}
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/${
                  t("slug_it_companies") + TRACKER_UTM_TOPDEV_MAINMENU
                }`}
              >
                {t("header_it_companies")}
              </Link>
            </li>
            <li className="group relative">
              <Link
                className="flex font-bold capitalize text-black group-hover:text-primary sm:px-[0.4rem] sm:py-2 sm:text-xs md:px-3 lg:px-8 lg:py-[1.95rem] lg:text-base"
                title={t("header_tools")}
                href="#"
              >
                {t("header_tools")}
                <span className="ml-2 rounded-lg border border-primary bg-primary-100 px-[7px] py-[2px] text-[12px] font-bold uppercase leading-[14px] text-primary">
                  {t("common_new")}
                </span>
                <GoChevronDown className="ml-[10px] hidden text-xl transition duration-300 group-hover:rotate-180 lg:block" />
              </Link>
              <ul className="invisible absolute top-[110%] -z-50 min-w-[220px] rounded bg-white opacity-0 shadow-sm group-hover:visible group-hover:top-full group-hover:z-[25] group-hover:opacity-100 lg:min-w-[315px]">
                {listMenu.tools.map((value, index) => {
                  if (value.title === "header_blog_it") {
                    return null;
                  }
                  return (
                    <li key={index}>
                      <Link
                        href={value.href}
                        title={t(value.title)}
                        className="group/link-sub flex items-center p-1 text-sm text-gray-600 hover:bg-gray-100 lg:p-3 lg:text-base"
                      >
                        <span className="mr-2 rounded bg-gray-100 p-2 text-sm transition-all group-hover/link-sub:bg-white group-hover/link-sub:text-primary-300 lg:text-2xl">
                          {React.createElement(value.icon)}
                        </span>
                        {t(value.title)}
                        {value.title === "home_convert_cv" && (
                          <span className="ml-2 rounded-lg border border-primary bg-primary-100 px-[7px] py-[2px] text-[12px] font-bold uppercase leading-[14px] text-primary">
                            {t("common_new")}
                          </span>
                        )}
                      </Link>
                    </li>
                  );
                })}
              </ul>
            </li>
            <li>
              <Link
                className="block font-bold capitalize text-black hover:text-primary sm:px-[0.4rem] sm:py-2 sm:text-xs md:px-3 lg:px-8 lg:py-[1.95rem] lg:text-base"
                title={t("header_blog_it")}
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/blog${TRACKER_UTM_TOPDEV_MAINMENU}`}
              >
                {t("header_blog_it")}
              </Link>
            </li>
          </ul>
          {/* End menu */}
        </div>

        {/* Search form */}
        <div
          ref={headerSroll}
          className="my-1 hidden w-[35%] lg:my-0 lg:w-[30%]"
        >
          <MenuSearchForm />
        </div>
        {/* End Search form */}

        <div
          id="headerRightDefault"
          ref={headerDefault}
          className="flex items-center justify-end"
        >
          {/* Info login */}
          <div className="header-login hidden pr-0 sm:block sm:pr-2 lg:pr-4">
            {isLoggedIn ? <TemplateLoggedIn /> : <TemplateNotLogin />}
          </div>
          {/* End Info login */}

          {/* Switch Locale */}
          <div className="text-xs lg:text-base">
            <SwitchLocale />
          </div>
          {/* End Switch Locale */}
        </div>
      </div>
    </header>
  );
}
