"use client";
import { usePathname } from "next/navigation";
import HeaderV2 from "../v2/shared/header/page";
import MenuProduct from "./Header/MenuProduct";
import { URL_JOB_SEEKER } from "@/contansts/auth";

export default function Header() {
  const pathname = usePathname();

  const routerPayment = ["/products", "/carts"];
  const checkShowPayment =
    pathname != "/" &&
    (routerPayment.includes(pathname) || pathname.includes("/result"));
  const noLayoutRoutes = [
    "/nha-tuyen-dung/to-chuc/korean-it-companies-83771",
    "/companies/group/korean-it-companies-83771",
    URL_JOB_SEEKER,
  ];
  const isNoLayout = noLayoutRoutes.includes(pathname);
  if (isNoLayout) return <></>;
  if (checkShowPayment) return <MenuProduct />;

  // Only render HeaderPackage when CSS is loaded
  // return isCSSLoaded ? <HeaderPackage language={locale as Lang} /> : null;
  // if (process.env.RENEW_TOPDEV_2025 === "true") {
  return <HeaderV2 />;
  // }
  // return isCSSLoaded ? <HeaderPackage language={locale as Lang} /> : null;
}
