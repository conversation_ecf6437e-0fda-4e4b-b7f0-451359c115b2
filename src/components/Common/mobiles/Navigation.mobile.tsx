"use client";

import React, { Dispatch, SetStateAction } from "react";
import Link from "@/components/Link/Link";
import Image from "next/image";
import { MdClear } from "react-icons/md";
import dynamic from "next/dynamic";

const MenuMobile = dynamic(
  () => import("@/components/Common/mobiles/Navigation/MenuMobile"),
);

const NavigationNotification = dynamic(
  () => import("@/components/Common/mobiles/Navigation/Notification"),
);

const NavigationMobile = ({
  isMenu,
  setIsShow,
}: {
  isMenu: boolean;
  setIsShow: Dispatch<SetStateAction<boolean>>;
}) => {
  return (
    <div
      className={`transiton-all fixed top-0 ${
        isMenu ? "z-50" : "z-40"
      } h-full w-full bg-black/[0.15] duration-200 ease-in-out`}
      id="NavigationMobile"
    >
      <div
        className={`${
          isMenu ? "max-w-[355px]" : ""
        } h-full w-full overflow-y-auto bg-white px-5 py-4`}
      >
        <div className="flex flex-1 items-center justify-between gap-2">
          <Link href="/" className="block h-9 w-28">
            <Image
              src="https://cdn.topdev.vn/v4/assets/images/common/logo-mobile-2.svg"
              width={114}
              height={22}
              loading="lazy"
              className="h-full w-full max-w-full object-contain"
              alt="TopDev"
            />
          </Link>
          <button role="button" onClick={() => setIsShow(false)}>
            <MdClear className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default NavigationMobile;
