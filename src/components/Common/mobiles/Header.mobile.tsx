"use client";

import { usePathname, useSelectedLayoutSegment } from "next/navigation";
import HeaderMobileV2 from "@/components/v2/shared/header-mobile/page";
import { StickyBar } from "@/components/v2/shared/header-mobile/StickyBar";

const HeaderMobile = () => {
  const selectedLayoutSegment = useSelectedLayoutSegment();
  const pathname = usePathname();

  // Detect if it's a detail jobs page
  // const isPageDetailJobs =
  //   selectedLayoutSegment !== "detail-jobs" && !!selectedLayoutSegment;

  // Static routes you want to hide header
  const noLayoutRoutes = ["/nha-tuyen-dung/to-chuc/korean-it-companies-83771"];

  // Dynamic routes you want to hide header (prefix check)
  const noLayoutPatterns = [/^\/companies\//]; // matches companies/[slug]

  const isNoLayout =
    noLayoutRoutes.includes(pathname) ||
    noLayoutPatterns.some((pattern) => pattern.test(pathname));

  if (isNoLayout) return <StickyBar />;

  // if (isPageDetailJobs && selectedLayoutSegment !== "(landing-page)") {
  //   return null;
  // }

  return (
    <>
      <HeaderMobileV2 />
      <StickyBar />
    </>
  );
};

export default HeaderMobile;
