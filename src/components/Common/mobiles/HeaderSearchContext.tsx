"use client";
import { LocationType } from "@/types/search";
import { TaxonomyType } from "@/types/taxonomy";
import { LIST_LOCATIONS } from "@/utils/enums";
import {
  Dispatch,
  FC,
  ReactNode,
  createContext,
  useContext,
  useReducer,
} from "react";

interface LocationStateType {
  value: LocationType;
  options: LocationType[];
  isOpen: boolean;
}

interface TaxonomyStateType {
  value: TaxonomyType[];
  options: TaxonomyType[];
  isOpen: boolean;
}

interface FilterGroupType {
  isOpen: boolean;
}
export interface FilterStateType {
  locationState: LocationStateType;
  jobLevelState: TaxonomyStateType;
  jobTypeState: TaxonomyStateType;
  contractTypeState: TaxonomyStateType;
  filterGroup: FilterGroupType;
  filterClosed: boolean;
  isFireSubmit: boolean;
}

export const initialState: FilterStateType = {
  locationState: {
    isOpen: false,
    options: [],
    value: LIST_LOCATIONS[0],
  },
  jobLevelState: {
    isOpen: false,
    options: [],
    value: [],
  },
  jobTypeState: {
    isOpen: false,
    options: [],
    value: [],
  },
  contractTypeState: {
    isOpen: false,
    options: [],
    value: [],
  },
  filterGroup: {
    isOpen: false,
  },
  filterClosed: true,
  isFireSubmit: false,
};

type FilterActionType =
  | {
      type: "UPDATE_LOCATION";
      payload: LocationStateType;
    }
  | {
      type: "UPDATE_JOB_LEVEL";
      payload: TaxonomyStateType;
    }
  | {
      type: "UPDATE_JOB_TYPE";
      payload: TaxonomyStateType;
    }
  | {
      type: "UPDATE_CONTRACT_TYPE";
      payload: TaxonomyStateType;
    }
  | {
      type: "CLEAR_FILTER";
    }
  | {
      type: "UPDATE_FILTER_GROUP";
      payload: FilterGroupType;
    }
  | {
      type: "CLOSE_FILTER";
    }
  | {
      type: "UPDATE_FIRE_SUBMIT";
      payload: boolean;
    };

export const HeaderContext = createContext<
  | {
      state: FilterStateType;
      dispatch: Dispatch<FilterActionType>;
    }
  | undefined
>({ state: initialState, dispatch: () => {} });

export const HeaderReducer = (
  state: FilterStateType = initialState,
  action: FilterActionType,
): FilterStateType => {
  switch (action.type) {
    case "UPDATE_LOCATION": {
      return {
        ...state,
        locationState: action.payload,
        filterClosed: action.payload.isOpen,
      };
    }
    case "UPDATE_JOB_TYPE": {
      return {
        ...state,
        jobTypeState: action.payload,
        filterClosed: action.payload.isOpen,
      };
    }
    case "UPDATE_JOB_LEVEL": {
      return {
        ...state,
        jobLevelState: action.payload,
        filterClosed: action.payload.isOpen,
      };
    }
    case "UPDATE_CONTRACT_TYPE": {
      return {
        ...state,
        contractTypeState: action.payload,
        filterClosed: action.payload.isOpen,
      };
    }
    case "CLEAR_FILTER": {
      return initialState;
    }
    case "UPDATE_FILTER_GROUP": {
      return {
        ...state,
        filterGroup: action.payload,
        filterClosed: true,
      };
    }
    case "CLOSE_FILTER": {
      return {
        ...state,
        contractTypeState: { ...state.contractTypeState, isOpen: false },
        jobLevelState: { ...state.jobLevelState, isOpen: false },
        locationState: { ...state.locationState, isOpen: false },
        jobTypeState: { ...state.jobTypeState, isOpen: false },
        filterClosed: true,
      };
    }
    case "UPDATE_FIRE_SUBMIT": {
      return {
        ...state,
        isFireSubmit: action.payload,
      };
    }
    default:
      return state;
  }
};

export const HeaderProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(HeaderReducer, initialState);
  return (
    <HeaderContext.Provider value={{ state, dispatch }}>
      {children}
    </HeaderContext.Provider>
  );
};

export const useHeaderContext = () => {
  const context = useContext(HeaderContext);
  if (context === undefined) {
    throw Error("Can not use useHeaderContext now!");
  }
  return context;
};
