"use client";

import Link from "@/components/Link/Link";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import { MouseEvent, useEffect, useRef, useState } from "react";
import { HiMiniChevronLeft, HiMiniChevronRight } from "react-icons/hi2";
import { Lang, PropsMenu, SubMenu } from "@/types/page";
import { chunkSubMenu, classNames, getSubMenu, listMenu } from "@/utils";
import { getCookie } from "@/utils/cookies";
import { TRACKER_UTM_TOPDEV_MAINMENU } from "@/utils/enums";
import Image from "next/image";
import { HiOutlineMenu } from "react-icons/hi";
import { MdClear } from "react-icons/md";
import { useLockedBody, useOnClickOutside } from "usehooks-ts";
import React from "react";
import MenuItems from "@/assets/json/menu.json";

const SwitchLocale = dynamic(
  () => import("@/components/Common/Header/SwitchLocale"),
);

const MenuMobile = () => {
  const t = useTranslations();
  const [isShow, setIsShow] = useState(false);
  const menuRef = useRef(null);
  const [, setLockedBody] = useLockedBody(false, "root");
  const subMenu =
    getCookie("topdev_locale") == "en" ? MenuItems.en : MenuItems.vi;

  useEffect(() => {
    if (isShow) {
      setLockedBody(true);
    } else {
      setLockedBody(false);
    }
  }, [isShow]);

  const handleShowSubMenu = (event: MouseEvent<HTMLLIElement>) => {
    (event.target as any)?.lastChild?.classList?.replace("left-full", "left-0");
  };

  const handleCloseSubMenu = (event: MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    (event.target as any)?.offsetParent?.classList?.replace(
      "left-0",
      "left-full",
    );
  };

  const handleClickContext = (event: any) => {
    setIsShow(false);
  };

  useOnClickOutside(menuRef, handleClickContext);

  return (
    <div id="NavigationMenu" className="relative overflow-hidden" ref={menuRef}>
      <p className="hidden" id="labelmenu">
        Label menu
      </p>
      <button
        onClick={() => setIsShow(true)}
        className="h-11 w-11 p-3 text-center"
        role="button"
        aria-label="Menu"
        aria-labelledby="labelmenu"
        tabIndex={-1}
      >
        <HiOutlineMenu className="h-5 w-5" />
      </button>
      <div
        className={classNames(
          "fixed left-0 top-0 z-30 h-full w-full max-w-[355px] overflow-y-auto overflow-x-hidden bg-white transition-all",
          isShow
            ? "opacity-1 visible -translate-x-0"
            : "invisible -translate-x-full opacity-0",
        )}
      >
        <div className="relative p-4 pb-20">
          <div className="flex flex-1 items-center justify-between gap-2">
            <Link href="/" className="block h-9 w-28">
              <Image
                src="/v4/assets/images/common/logo-mobile-2.svg"
                width={114}
                height={22}
                loading="lazy"
                className="h-full w-full max-w-full object-contain"
                alt="TopDev"
              />
            </Link>
            <button role="button" onClick={() => setIsShow(false)}>
              <MdClear className="h-5 w-5" />
            </button>
          </div>
          <div className="items-sub-menu">
            <p className="border-b border-gray-300 p-2 pt-4 text-xs font-semibold uppercase text-gray-500">
              {t("left_menu_it_jobs")}
            </p>
            {subMenu?.length && (
              <ul id="subMenuItJobs">
                {subMenu?.map((value, index: number) => {
                  let chunkValueSubMenu = chunkSubMenu(
                    value.sub as Array<SubMenu>,
                    18,
                  );
                  if (value && value.type === "topdev") {
                    return null;
                  }
                  return (
                    <li
                      key={`${value.type}-${index}`}
                      className="flex items-center justify-between p-2 text-sm text-black"
                      onClick={handleShowSubMenu}
                    >
                      {value.title}
                      <HiMiniChevronRight className="relative z-0 h-4 w-4" />
                      {value?.sub?.length && chunkValueSubMenu.length > 0 && (
                        <div className="transiton-all absolute left-full top-0 z-10 h-full w-full bg-white duration-200 ease-in-out">
                          <p className="border-b border-gray-300 p-2 pt-4 text-xs font-semibold uppercase text-gray-500">
                            {t("left_menu_it_jobs")}
                          </p>
                          <button
                            className="my-2 flex w-full items-center bg-gray-100 p-2 text-sm font-bold text-black"
                            role="button"
                            onClick={handleCloseSubMenu}
                          >
                            <HiMiniChevronLeft className="mr-2 h-4 w-4" />
                            {value.title}
                          </button>
                          <ul className="flex">
                            {chunkValueSubMenu?.map(
                              (
                                valueSubChunk: Array<SubMenu>,
                                indexSubChunk: number,
                              ) => {
                                return (
                                  <ul
                                    key={`chunk-${indexSubChunk}`}
                                    className="flex flex-1 flex-col"
                                  >
                                    {valueSubChunk.map(
                                      (valueSub: SubMenu, indexSub: number) => {
                                        return (
                                          <li
                                            key={`sub-in-sub-${indexSubChunk}-${indexSub}`}
                                          >
                                            <Link
                                              href={valueSub.url ?? ""}
                                              title={valueSub.title}
                                              className="block p-2 text-xs capitalize text-black hover:bg-gray-100"
                                            >
                                              {valueSub.title}
                                            </Link>
                                          </li>
                                        );
                                      },
                                    )}
                                  </ul>
                                );
                              },
                            )}
                          </ul>
                        </div>
                      )}
                    </li>
                  );
                })}
              </ul>
            )}
          </div>
          <div className="items-sub-menu">
            <p className="border-b border-gray-300 p-2 pt-4 text-xs font-semibold uppercase text-gray-500">
              {t("left_menu_tools")}
            </p>
            <ul>
              {listMenu.tools.map((value, index) => {
                return (
                  <li key={index} onClick={() => setIsShow(false)}>
                    <Link
                      className="flex items-center p-2 text-sm text-black"
                      href={value.href}
                      title={t(value.title)}
                    >
                      <span className="mr-2 h-4 w-4 text-base">
                        {React.createElement(value.icon)}
                      </span>
                      {t(value.title)}
                      {value.title === "home_convert_cv" && (
                        <span className="ml-2 rounded-lg border border-primary bg-primary-100 px-[6px] py-[1px] text-[10px] font-bold uppercase leading-[15px] text-primary">
                          {t("common_new")}
                        </span>
                      )}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>
          <div className="items-sub-menu">
            <p className="border-b border-gray-300 p-2 pt-4 text-xs font-semibold uppercase text-gray-500">
              TOPDEV
            </p>
            <ul>
              <li>
                <Link
                  title={t("left_menu_job_posting")}
                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/products${TRACKER_UTM_TOPDEV_MAINMENU}#section-recruitment`}
                  className="flex p-2 text-sm text-black"
                  target="_blank"
                >
                  {t("left_menu_job_posting")}
                </Link>
              </li>
              <li>
                <Link
                  title={t("left_menu_search_cv")}
                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/products${TRACKER_UTM_TOPDEV_MAINMENU}#section-candidate-cv`}
                  className="flex p-2 text-sm text-black"
                  target="_blank"
                >
                  {t("left_menu_search_cv")}
                </Link>
              </li>
              <li>
                <Link
                  title={t("left_menu_employer_branding")}
                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/products${TRACKER_UTM_TOPDEV_MAINMENU}#section-topDev-rewards`}
                  className="flex p-2 text-sm text-black"
                  target="_blank"
                >
                  {t("left_menu_employer_branding")}
                </Link>
              </li>
              <li>
                <Link
                  title="IT Headhunt"
                  href={`https://talentsuccess.vn${TRACKER_UTM_TOPDEV_MAINMENU}`}
                  className="flex p-2 text-sm text-black"
                  target="_blank"
                >
                  IT Headhunt
                </Link>
              </li>
              <li>
                <Link
                  title={t("left_menu_it_market_report")}
                  href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/bao-cao-it-viet-nam${TRACKER_UTM_TOPDEV_MAINMENU}`}
                  className="flex p-2 text-sm text-black"
                  scroll={false}
                  target="_blank"
                >
                  {t("left_menu_it_market_report")}
                </Link>
              </li>
            </ul>
          </div>
          <div className="items-sub-menu mt-3 mb-3 flex items-center justify-between border-b border-t border-gray-200 p-2 text-sm lg:text-base">
            <span>{t("left_menu_language")}</span>
            <div className="text-base uppercase">
              <SwitchLocale />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MenuMobile;
