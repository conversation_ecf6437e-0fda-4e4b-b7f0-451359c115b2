"use client";

import { useEffect, useRef, useState } from "react";
import Image from "next/image";
import { useTranslations } from "next-intl";
import {
  fetchMaskAsSeen,
  fetchNotificationApi,
  requestMaskAsRead,
} from "@/services/userAPI";
import { Notification } from "@/types/user";
import { useAppSelector } from "@/store";
import { classNames, openLoginPopup } from "@/utils";
import { useIntersectionObserver, useOnClickOutside } from "usehooks-ts";
import useJustOnce from "@/hooks/useJustOnce";
import dayjs from "dayjs";

const NavigationNotification = () => {
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const t = useTranslations();
  const notificationRef = useRef(null);
  const [isShowNotification, setIsShowNotification] = useState<boolean>(false);
  const [nextPage, setNextPage] = useState<number>(1);
  const [hasValue, setHasValue] = useState<boolean>(true);
  const [dataNoti, setDataNoti] = useState<Array<Notification>>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { isValid: isFirstRender, handleChangeValue } = useJustOnce();
  const listRef = useRef<HTMLUListElement | null>(null);
  const loadMoreRef = useRef<HTMLLIElement | null>(null);
  const entry = useIntersectionObserver(loadMoreRef, {});
  const isVisible = !!entry?.isIntersecting;

  useEffect(() => {
    console.table({ isLoggedIn, isFirstRender, hasValue });
    if (!isLoggedIn) {
      return;
    }
    if (isFirstRender) {
      handleChangeValue()
      return;
    }
    if (!hasValue) {
      return;
    }

    if (isVisible && hasValue) {
      Promise.all([fetchSeenAction(), fetchNotification()]);
    }
  }, [isVisible]);

  const maskAsRead = (id: number | undefined, url: string | undefined) => {
    requestMaskAsRead(id)
      .then(() => {
        if (url) {
          const actionUrl = new URL(url);
          const actionUrlWithParam = new URL(
            actionUrl.origin + actionUrl.pathname,
          );
          actionUrlWithParam.searchParams.append("src", "web_push");
          if (actionUrl.searchParams.has("medium")) {
            let medium = actionUrl.searchParams.get("medium");
            if (medium && medium !== null) {
              actionUrlWithParam.searchParams.append("medium", medium!.trim());
            }
          }
          const newDataNotify = dataNoti.map((item) =>
            item.id === id
              ? { ...item, read_at: new Date().toISOString() }
              : item,
          );
          setDataNoti(newDataNotify);
          window.open(actionUrlWithParam.href, "_blank");
        } else {
          const newDataNotify = dataNoti.map((item) => ({
            ...item,
            read_at: new Date().toISOString(),
          }));
          setDataNoti(newDataNotify);
          handleMaskReadAll();
        }
      })
      .catch((error: string) => {
        console.error(error);
      });
  };

  // Mask read all
  const handleMaskReadAll = () => {
    try {
      const date = dayjs().format("DD-MM-YYYY");
      const newArr = dataNoti.map((item) => {
        return { ...item, read_at: date };
      });
      setDataNoti(newArr);
    } catch (error) {}
  };
  // End Mask read all

  const fetchSeenAction = async () => {
    await fetchMaskAsSeen()
      .then(() => {})
      .catch((error: any) => {
        console.error(error);
      });
  };

  const fetchNotification = async () => {
    setIsLoading(true);
    try {
      const { data } = await fetchNotificationApi(nextPage);
      setDataNoti([...dataNoti, ...data.data]);
      if (data.meta.current_page >= data.meta.last_page) {
        setHasValue(false);
      } else {
        let NewNextPage = nextPage + 1;
        setNextPage(NewNextPage);
      }
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      throw err;
    }
  };

  const handleToggleNotification = async () => {
    setIsShowNotification(!isShowNotification);
    if (isFirstRender) {
      handleChangeValue();
      Promise.all([fetchSeenAction(), fetchNotification()]);
    }
  };

  const handleClickContext = () => {
    setIsShowNotification(false);
  };

  useOnClickOutside(notificationRef, handleClickContext);

  if (!isLoggedIn) {
    return (
      <div>
        <div className="header-notification border-b border-gray-200 p-4 pl-0">
          <h3 className="text-base font-bold text-gray-600">
            {t("left_menu_notification")}
          </h3>
        </div>
        <div className="py-14 text-center">
          <Image
            src="https://cdn.topdev.vn/v4/assets/images/common/empty-notification-icon.svg"
            alt="TopDev"
            className="inline-block h-auto w-[120px] max-w-[108px]"
            width="120"
            height="108"
            loading="lazy"
          />
          <div className="mb-2 mt-4 text-sm font-bold text-gray-600">
            {t("notification_not_login_content")}
          </div>
          <button
            onClick={() => openLoginPopup()}
            className="rounded bg-primary-300 px-6 py-3 text-base font-semibold text-white"
            type="button"
          >
            {t("notification_not_login_button")}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div id="NavigationNotification" ref={notificationRef}>
      <div className="header-notification flex items-center justify-between border-b border-gray-200 py-4">
        <h3 className="text-base font-bold text-gray-600">
          {t("left_menu_notification")}
        </h3>
        <button
          role="button"
          onClick={() => maskAsRead(undefined, undefined)}
          className="rounded-sm bg-gray-200 px-4 py-[6px] text-sm font-semibold text-gray-600 transition-all hover:bg-primary hover:text-white"
        >
          {t("left_menu_mark_all_as_read")}
        </button>
      </div>
      {dataNoti.length > 0 ? (
        <>
          <ul className="main-notification mt-2 max-h-[570px] overflow-y-auto">
            {dataNoti?.map((value) => {
              return (
                <li
                  key={value.id}
                  onClick={() => maskAsRead(value.id, value.action_url)}
                  className={classNames(
                    !value.read_at ? "bg-gray-100" : "",
                    "flex cursor-pointer flex-wrap gap-2 border-b border-gray-200 px-4 py-2 transition-all hover:bg-gray-100",
                  )}
                >
                  <p className="line-clamp-1 text-sm font-bold text-gray-600">
                    {value.title}
                  </p>
                  <div className="flex items-center justify-between">
                    <p className="line-clamp-1 w-[91%] text-sm text-gray-600">
                      {value.body}
                    </p>
                    {!value.read_at && (
                      <span className="inline-block h-[12px] w-[12px] rounded-full bg-primary-300"></span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400">{value.created_at}</p>
                </li>
              );
            })}
          </ul>
        </>
      ) : (
        <div className="pt-4 text-base font-bold">
          {t("header_no_notification")}
        </div>
      )}
    </div>
  );
};

export default NavigationNotification;
