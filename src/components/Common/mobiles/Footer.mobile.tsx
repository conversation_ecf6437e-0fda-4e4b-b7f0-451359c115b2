"use client";
import React from "react";
import Link from "@/components/Link/Link";
import Image from "next/image";
import { FaSquareYoutube } from "react-icons/fa6";
import { FaFacebookSquare, FaLinkedin } from "react-icons/fa";
import { useTranslations } from "next-intl";
import { TRACKER_UTM_TOPDEV_MAINMENU } from "@/utils/enums";
import ScrollTopButton from "@/components/Button/ScrollTopButton";
import useCheckPageTechcombank from "@/hooks/useCheckPageTechcombank";

const FooterMobile = () => {
  const t = useTranslations();
  const checkPageTechcombank = useCheckPageTechcombank();

  //Check page in group Ex: "/group/techcombank"
  if (checkPageTechcombank) {
    return <></>;
  }

  const handleShowTab = (event: any) => {
    const tabs = document.getElementById("tabs");
    for (let i = 0; i < Number(tabs?.children?.length); i++) {
      tabs?.children?.[i]?.classList?.remove("font-bold");
    }
    const tab_content = document.getElementsByClassName("tab_content");
    event.target?.classList.add("font-bold");
    const index = event.target?.tabIndex as number;
    for (let i = 0; i < tab_content.length; i++) {
      tab_content?.[i]?.classList.add("hidden");
      if (index === i + 1) {
        tab_content?.[i]?.classList.remove("hidden");
      }
    }
  };

  return (
    <footer className="pt-6 mb-10 text-sm text-gray-600 bg-gray-200">
      <ScrollTopButton />
      <div className="px-2 py-4 pt-0 footer-info sm:px-4">
        <p className="mb-2 text-base font-bold text-black lg:mb-2">
          {t("footer_contact_us")}
        </p>
        <ul className="grid gap-2 mb-3 tracking-wide">
          <li>Applancer JSC - TopDev.vn</li>
          <li>
            <Link
              href="mailto:<EMAIL>"
              className="block py-1 hover:text-primary"
            >
              <EMAIL>
            </Link>
          </li>
          <li>
            <Link href="tel:0888155500" className="block py-1 hover:text-primary">
              0888 1555 00
            </Link>
          </li>
          <li>{t("footer_address")}</li>
        </ul>
        <p className="mb-2 text-base font-bold text-black lg:mb-2">
          {t("footer_subscrible_us_on")}
        </p>
        <ul className="flex items-center gap-3 mx-0 mb-4 text-4xl lg:mx-2">
          <li>
            <Link
              href="https://www.facebook.com/topdevvietnam"
              aria-label="https://www.facebook.com/topdevvietnam"
              className="text-gray-600 transition-all hover:text-primary"
            >
              <FaFacebookSquare />
            </Link>
          </li>
          <li>
            <Link
              href="https://www.linkedin.com/company/topdev-vn/"
              aria-label="https://www.linkedin.com/company/topdev-vn/"
              className="text-gray-600 transition-all hover:text-primary"
            >
              <FaLinkedin />
            </Link>
          </li>
          <li>
            <Link
              href="https://www.youtube.com/channel/UCZedbcmUtab8Y7DEt4ZZv7Q"
              aria-label="https://www.youtube.com/channel/UCZedbcmUtab8Y7DEt4ZZv7Q"
              className="text-gray-600 transition-all hover:text-primary"
            >
              <FaSquareYoutube />
            </Link>
          </li>
        </ul>
        <p className="mb-2 text-base font-bold text-black lg:mb-2">
          {t("footer_download_app_here")}
        </p>
        <ul className="flex justify-start gap-1">
          <li>
            <Link href="https://topdev.vn/s/MrEHZlyk">
              <Image
                src="https://cdn.topdev.vn/v4/assets/images/promote_app/app_store_img.png"
                className="h-auto w-[114px]"
                alt="TopDev in app store"
                loading="lazy"
                width="114"
                height="33"
              />
            </Link>
          </li>
          <li>
            <Link href="https://play.google.com/store/apps/details?id=it.jobs.topdev&referrer=utm_source%3Dtopdev%26utm_medium%3Dfooter_default">
              <Image
                src="https://cdn.topdev.vn/v4/assets/images/promote_app/google_play_img.png"
                className="h-[33px] w-[114px]"
                alt="TopDev in app store"
                loading="lazy"
                width="114"
                height="33"
              />
            </Link>
          </li>
        </ul>
      </div>
      <p id="tabstopdev" className="hidden" role="label">
        Topdev
      </p>
      <div
        id="tabs"
        className="flex px-2 py-3 border-t border-b border-gray-600 tabs-footer sm:px-4"
        aria-label="Tag"
        role="listbox"
        tabIndex={0}

        aria-labelledby="tabstopdev"
        aria-activedescendant="tabstopdev-1"
      >
        <button
          tabIndex={1}
          role="tablist"
          type="button"
          className="flex-1 p-0 text-sm capitalize"
          onClick={handleShowTab}
          id="tabstopdev-1"
        >
          {t("footer_about_topdev")}
        </button>
        <button
          tabIndex={2}
          role="tablist"
          type="button"
          className="flex-1 p-0 text-sm capitalize"
          onClick={handleShowTab}
          id="tabstopdev-2"
        >
          {t("footer_for_jobseekers")}
        </button>
        <button
          tabIndex={3}
          role="tablist"
          type="button"
          className="flex-1 p-0 text-sm capitalize"
          onClick={handleShowTab}
          id="tabstopdev-3"
        >
          {t("footer_for_employers")}
        </button>
      </div>
      <div id="tabs_content">
        <div className="hidden p-6 text-center border-b border-gray-600 tab_content">
          <ul className="grid gap-6">
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/about-us`}
                title={t("footer_list_about_us")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_about_us")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/contact`}
                title={t("footer_list_contact_us")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_contact_us")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/term-of-services`}
                title={t("footer_list_terms_of_service")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_terms_of_service")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/topdev-buddies`}
                title={t("footer_list_career_at_topdev")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_career_at_topdev")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/privacy-policy`}
                title={t("footer_list_privacy_policy")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_privacy_policy")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/operation-regulation`}
                title={t("footer_list_operation_regulation_of_topdev")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_operation_regulation_of_topdev")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/resolve-complaints`}
                title={t("footer_list_resolve_complaints")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_resolve_complaints")}
              </Link>
            </li>
          </ul>
        </div>
        <div className="hidden p-6 text-center border-b border-gray-600 tab_content">
          <ul className="grid gap-6">
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/tool/tinh-luong-gross-net`}
                title={t("footer_list_salary_calculation_gross_net")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_salary_calculation_gross_net")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/tao-cv-online`}
                title={t("footer_list_create_cv")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_create_cv")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/it-jobs`}
                title={t("footer_list_browse_all_it_jobs")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_browse_all_it_jobs")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/trac-nghiem-tinh-cach`}
                title={t("footer_list_personality_test")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_personality_test")}
              </Link>
            </li>
          </ul>
        </div>
        <div className="hidden p-6 text-center border-b border-gray-600 tab_content">
          <ul className="grid gap-6">
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/recruit`}
                title={t("footer_list_post_a_job")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_post_a_job")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/products`}
                title={t("footer_list_talent_solutions")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_talent_solutions")}
              </Link>
            </li>
            <li>
              <Link
                href={`${process.env.NEXT_PUBLIC_BASE_URL}/page/bao-cao-it-viet-nam${TRACKER_UTM_TOPDEV_MAINMENU}`}
                title={t("footer_list_it_market_report")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_it_market_report")}
              </Link>
            </li>
            <li>
              <Link
                href="https://accounts.topdev.vn"
                title={t("footer_list_create_account")}
                className="transition-all hover:text-primary"
              >
                {t("footer_list_create_account")}
              </Link>
            </li>
          </ul>
        </div>
      </div>
      <div className="px-2 py-4 copyright sm:px-4">@ 2019 Applancer</div>
    </footer>
  );
};

export default FooterMobile;
