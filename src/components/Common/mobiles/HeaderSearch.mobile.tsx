"use client";
import { getSuggestedKeywords } from "@/services/searchAPI";
import {
  FilterParams,
  KeywordSearchType,
  LocationType,
  SearchFiltersType,
  SearchMetaType,
  SuggestedType,
} from "@/types/search";
import { TaxonomiesType, TaxonomyType } from "@/types/taxonomy";
import { LIST_LOCATIONS } from "@/utils/enums";
import {
  fakeIdNotInTaxonomies,
  getResultSearch,
  slugify,
  toVnSlug,
} from "@/utils/search";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "@/components/Link/Link";
import {
  ChangeEvent,
  FC,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { HiOutlineSearch } from "react-icons/hi";
import { HiOutlineBell, HiXMark } from "react-icons/hi2";
import { MdOutlineClear } from "react-icons/md";
import { useDebounce } from "usehooks-ts";
import { useHeaderContext } from "./HeaderSearchContext";
import NavigationMobile from "./Navigation.mobile";
import { useAppDispatch, useAppSelector } from "@/store";
import { setKeyword } from "@/store/slices/searchSlice";

const ChipTag = dynamic(() => import("@/components/Tag/ChipTag"));

interface MainSearchProps {
  filters?: SearchFiltersType;
  meta?: SearchMetaType;
  taxonomies: TaxonomiesType;
}

const HeaderSearchMobile: FC<MainSearchProps> = (props) => {
  const t = useTranslations();
  const { filters, meta, taxonomies } = props;
  const [keywordSearched, setKeywordSearched] = useState<KeywordSearchType[]>(
    [],
  );
  const searchRef = useRef<HTMLInputElement>(null);
  const [search, setSearch] = useState("");
  const [suggestData, setSuggestData] = useState<SuggestedType[]>([]);
  const suggestRef = useRef<HTMLDivElement>(null);
  const [isOpenSuggest, setIsOpenSuggest] = useState(false);
  const debouncedSuggest = useDebounce<string>(search, 500);
  const { state, dispatch } = useHeaderContext();
  const [isShow, setIsShow] = useState<boolean>(false);
  const [isMenu, setIsMenu] = useState<boolean>(false);
  const keywordState = useAppSelector(
    (state) => state.search.formSearch.keyword,
  );
  const appDispatch = useAppDispatch();

  useEffect(() => {
    if (typeof window !== "undefined") {
      if (!!isShow) document.body.classList.add("overflow-y-hidden");
      else document.body.classList.remove("overflow-y-hidden");
    }
  }, [isShow]);

  useEffect(() => {
    if (filters && meta) {
      let searchedArr: KeywordSearchType[] = [];
      const taxonomiesSkill: TaxonomyType[] = meta.skills || [];
      try {
        // init keyword searched in taxonomies
        if (taxonomiesSkill && taxonomiesSkill.length > 0) {
          searchedArr = taxonomiesSkill
            .filter((item) => item.taxonomy === "skills")
            .map((item) => ({
              id: item.id,
              slug: item.slug,
              keyword: item.text,
              taxonomy: item.taxonomy,
            }));
        }
        // init location
        if (filters.region_ids) {
          dispatch({
            type: "UPDATE_LOCATION",
            payload: {
              ...state.locationState,
              value: LIST_LOCATIONS.find(
                (item) => item.id === filters.region_ids,
              ) as LocationType,
            },
          });
        }
        // init job type
        if (filters.job_types_ids) {
          const jobTypeData = filters.job_types_ids.reduce(
            (result: TaxonomyType[], item) => {
              const value = taxonomies.job_types.find((it) => it.id === item);
              if (value) {
                return [...result, value];
              }
              return result;
            },
            [],
          );
          // setJobType(jobTypeData);
          dispatch({
            type: "UPDATE_JOB_TYPE",
            payload: {
              ...state.jobTypeState,
              value: jobTypeData,
            },
          });
        }
        // init job level
        if (filters.job_levels_ids) {
          const jobLevelData = filters.job_levels_ids.reduce(
            (result: TaxonomyType[], item) => {
              const value = taxonomies.job_levels.find((it) => it.id === item);
              if (value) {
                return [...result, value];
              }
              return result;
            },
            [],
          );
          // setJobLevels(jobLevelData);
          dispatch({
            type: "UPDATE_JOB_LEVEL",
            payload: {
              ...state.jobLevelState,
              value: jobLevelData,
            },
          });
        }
        //init contract type
        if (filters.contract_types_ids) {
          const jobContractData = filters.contract_types_ids.reduce(
            (result: TaxonomyType[], item) => {
              const value = taxonomies.contract_types.find(
                (it) => it.id === item,
              );
              if (value) {
                return [...result, value];
              }
              return result;
            },
            [],
          );
          // setContractType(jobContractData);
          dispatch({
            type: "UPDATE_CONTRACT_TYPE",
            payload: {
              ...state.contractTypeState,
              value: jobContractData,
            },
          });
        }
        // init keyword searched not in taxonomies
        if (filters.keyword && filters.keyword.trim()) {
          const keywordArr = filters.keyword.split(",");
          keywordArr.forEach((item) => {
            const removeSlug = item.replace("-", " ");
            searchedArr = [
              ...searchedArr,
              {
                id: fakeIdNotInTaxonomies(),
                slug: toVnSlug(item),
                keyword: removeSlug,
                taxonomy: "skills",
              },
            ];
          });
        }
        setKeywordSearched(searchedArr);
      } catch (error) {
        console.log(error);
      }
    }
  }, [filters, meta]);

  useEffect(() => {
    document.addEventListener("click", handleClickDocument);
    return () => {
      document.removeEventListener("click", handleClickDocument);
    };
  }, [isOpenSuggest]);

  useEffect(() => {
    handleSuggestedKeywords();
  }, [debouncedSuggest]);

  useEffect(() => {
    if (state.isFireSubmit) {
      handleSubmit();
      dispatch({
        type: "UPDATE_FIRE_SUBMIT",
        payload: false,
      });
    }
  }, [state.isFireSubmit]);

  const handleClickDocument = (event: globalThis.MouseEvent) => {
    if (
      isOpenSuggest &&
      suggestRef.current &&
      !suggestRef.current.contains(event.target as Node)
    ) {
      setIsOpenSuggest((prev) => !prev);
    }
  };

  const findTaxonomyKeyword = useCallback(
    (slug: string) => {
      return taxonomies["skills"].find((item) => item.slug === slug);
    },
    [taxonomies],
  );

  const handleSuggestedKeywords = async () => {
    if (search) {
      try {
        const data = await getSuggestedKeywords(search);
        setSuggestData(data);
        setIsOpenSuggest(true);
      } catch (error) {
        throw Error("Can not get suggested keywords!");
      }
    } else {
      setIsOpenSuggest(false);
      setSuggestData([]);
    }
  };

  const handleChangeSearch = async (event: ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    appDispatch(setKeyword(event.target.value));
  };

  const handleInputData = async (
    taxonomy?: Pick<TaxonomyType, "id" | "text" | "slug" | "taxonomy">,
  ) => {
    if (taxonomy) {
      const searchKeywords: KeywordSearchType[] = [
        ...keywordSearched,
        {
          id: taxonomy.id,
          keyword: taxonomy.text,
          slug: taxonomy.slug,
          taxonomy: taxonomy.taxonomy,
        },
      ];
      setKeywordSearched(searchKeywords);
      return searchKeywords;
    }
    if (search && search.trim()) {
      let dataAppend: KeywordSearchType = {
        id: null,
        keyword: "",
        slug: "",
        taxonomy: "skills",
      };
      const keywordData = await findTaxonomyKeyword(slugify(search));
      if (keywordData) {
        dataAppend = {
          ...dataAppend,
          id: keywordData.id,
          keyword: keywordData.text,
          slug: keywordData.slug,
        };
      } else {
        const searchToAppend = toVnSlug(search);
        dataAppend = {
          ...dataAppend,
          id: fakeIdNotInTaxonomies(),
          keyword: search,
          slug: searchToAppend,
        };
      }
      const searchKeywords: KeywordSearchType[] = [
        ...keywordSearched,
        dataAppend,
      ];
      setKeywordSearched(searchKeywords);
      return searchKeywords;
    }
    return keywordSearched;
  };

  const handleSubmit = async (taxonomy?: TaxonomyType) => {
    try {
      const searchKeywords = await handleInputData(taxonomy);
      setSearch("");
      const searchFilters: FilterParams = {
        job_salary: [],
        job_type: state.jobTypeState.value,
        job_level: state.jobLevelState.value,
        // job_industry: filters?.industries_ids || [],
        job_industry: [],
        job_contract: state.contractTypeState.value,
      };
      const searchSlug = getResultSearch(
        searchKeywords,
        state.locationState.value,
        searchFilters,
      );
      const href = `${process.env.NEXT_PUBLIC_BASE_URL}/${t(
        "slug_it_jobs",
      )}/${searchSlug}`;
      document.location.href = href;
    } catch (error) {
      console.log("error: ", error);
    }
  };

  const removeKeywordSearched = (id: string | number) => {
    const { contractTypeState, jobLevelState, jobTypeState } = state;
    try {
      setKeywordSearched((prev) => prev.filter((item) => item.id !== id));
      if (contractTypeState.value.length) {
        dispatch({
          type: "UPDATE_CONTRACT_TYPE",
          payload: {
            ...contractTypeState,
            value: contractTypeState.value.filter((item) => item.id !== id),
          },
        });
      }
      if (jobLevelState.value.length) {
        dispatch({
          type: "UPDATE_JOB_LEVEL",
          payload: {
            ...jobLevelState,
            value: jobLevelState.value.filter((item) => item.id !== id),
          },
        });
      }
      if (jobTypeState.value.length) {
        dispatch({
          type: "UPDATE_JOB_TYPE",
          payload: {
            ...jobTypeState,
            value: jobTypeState.value.filter((item) => item.id !== id),
          },
        });
      }
    } catch (error) {
      console.log("error: ", error);
    }
  };

  const handleClickInputSearch = () => {
    if (suggestData.length && !isOpenSuggest) {
      setIsOpenSuggest(true);
    }
  };

  const handleOpenLeftMenu = (menu: boolean = false) => {
    setIsShow(!isShow);
    setIsMenu(menu);
  };

  const handleRemoveKeywords = () => {
    setKeywordSearched([]);
  };

  return (
    <header className="sticky top-0 z-[999] w-full bg-white">
      <div className="relative shadow-sm">
        <div className="container">
          <div className="flex items-center gap-2 py-2">
            <Link href="/" className="block h-9 w-9 p-1">
              <Image
                src="https://cdn.topdev.vn/v4/assets/images/common/logo-mobile.svg"
                width={30}
                height={30}
                loading="lazy"
                className="h-full w-full max-w-full object-contain"
                alt="TopDev"
              />
            </Link>
            <form
              onSubmit={(event) => {
                event.preventDefault();
                handleSubmit();
              }}
              autoComplete="off"
              className="flex h-11 flex-1 items-center gap-2 overflow-hidden bg-gray-light p-2"
            >
              <div className="border-r border-solid border-gray-300 pr-2">
                <div
                  className="flex h-5 w-5 items-center justify-center text-xl text-primary"
                  onClick={() => handleSubmit()}
                >
                  <HiOutlineSearch />
                </div>
              </div>
              <div className="relative flex-1 overflow-hidden">
                <div className="absolute right-0 top-0 h-full w-5 bg-gradient-to-l from-gray-light to-transparent"></div>
                <div className="overflow-hidden">
                  <div className="flex items-center gap-2 overflow-x-auto scrollbar-none">
                    <ul className="flex flex-nowrap items-center gap-2">
                      {keywordSearched.map((item, index) => {
                        return (
                          <li key={index} className="whitespace-nowrap">
                            <ChipTag
                              accent="neutral-solid"
                              title={item.keyword}
                              size="xs"
                              tailingIcon={<HiXMark />}
                              isShowMobile
                              onClickIcon={() => {
                                if (item.id) removeKeywordSearched(item.id);
                              }}
                            />
                          </li>
                        );
                      })}
                    </ul>
                    <input
                      type="text"
                      name="search"
                      ref={searchRef}
                      id="search"
                      className="w-auto border-none bg-transparent p-0 text-xs outline-none focus:outline-none focus:ring-0"
                      placeholder="Search jobs by keywords"
                      onChange={handleChangeSearch}
                      value={keywordState}
                      onClick={handleClickInputSearch}
                    />
                  </div>
                </div>
              </div>
              <div>
                <button
                  onClick={handleRemoveKeywords}
                  type="button"
                  className="flex h-5 w-5 items-center justify-center text-sm text-gray-500"
                >
                  <MdOutlineClear />
                </button>
              </div>
            </form>
            <div>
              <button
                onClick={() => handleOpenLeftMenu()}
                type="button"
                className="flex h-10 w-10 items-center justify-center text-2xl"
              >
                <HiOutlineBell />
              </button>
            </div>
          </div>
        </div>
      </div>
      {!!isShow && <NavigationMobile isMenu={isMenu} setIsShow={setIsShow} />}
    </header>
  );
};

export default HeaderSearchMobile;
