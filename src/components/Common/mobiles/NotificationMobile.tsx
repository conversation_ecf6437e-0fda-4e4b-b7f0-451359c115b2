import useJustOnce from "@/hooks/useJustOnce";
import {
  fetchMaskAsSeen,
  fetchNotificationApi,
  requestMaskAsRead,
} from "@/services/userAPI";
import { useAppSelector } from "@/store";
import { Notification } from "@/types/user";
import { classNames, openLoginPopup } from "@/utils";
import dayjs from "dayjs";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import {
  Dispatch,
  FC,
  SetStateAction,
  useEffect,
  useRef,
  useState
} from "react";
import { HiOutlineBell } from "react-icons/hi2";
import { MdClear } from "react-icons/md";
import {
  useIntersectionObserver,
  useLockedBody,
  useOnClickOutside,
} from "usehooks-ts";
import Loading from "../Loading";

const NotificationSection: FC<{
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
}> = ({ isOpen, setIsOpen }) => {
  const loadMoreRef = useRef<HTMLLIElement | null>(null);
  const entry = useIntersectionObserver(loadMoreRef, {});
  const isVisible = !!entry?.isIntersecting;
  const [nextPage, setNextPage] = useState<number>(1);
  const [hasValue, setHasValue] = useState<boolean>(true);
  const [dataNoti, setDataNoti] = useState<Array<Notification>>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const notificationRef = useRef(null);
  const { isValid: isFirstRender, handleChangeValue } = useJustOnce();
  const t = useTranslations();

  useEffect(() => {
    if (isFirstRender) {
      return;
    }

    if (!hasValue) {
      return;
    }

    if (isVisible && hasValue) {
      Promise.all([fetchSeenAction(), fetchNotification()]);
    }
  }, [isVisible, isFirstRender]);

  useEffect(() => {
    if (isFirstRender) {
      handleChangeValue();
      Promise.all([fetchSeenAction(), fetchNotification()]);
    }
  }, [isFirstRender]);

  const maskAsRead = (id: number | undefined, url: string | undefined) => {
    if (dataNoti.length === 0) return;
    requestMaskAsRead(id)
      .then(() => {
        if (url) {
          const actionUrl = new URL(url);
          const actionUrlWithParam = new URL(
            actionUrl.origin + actionUrl.pathname,
          );
          actionUrlWithParam.searchParams.append("src", "web_push");
          if (actionUrl.searchParams.has("medium")) {
            let medium = actionUrl.searchParams.get("medium");
            if (medium && medium !== null) {
              actionUrlWithParam.searchParams.append("medium", medium!.trim());
            }
          }
          const newDataNotify = dataNoti.map((item) =>
            item.id === id
              ? { ...item, read_at: new Date().toISOString() }
              : item,
          );
          setDataNoti(newDataNotify);
          window.open(actionUrlWithParam.href, "_blank");
        } else {
          const newDataNotify = dataNoti.map((item) => ({
            ...item,
            read_at: new Date().toISOString(),
          }));
          setDataNoti(newDataNotify);
          handleMaskReadAll();
        }
      })
      .catch((error: string) => {
        console.error(error);
      });
  };

  const handleMaskReadAll = () => {
    try {
      const date = dayjs().format("DD-MM-YYYY");
      const newArr = dataNoti.map((item) => {
        return { ...item, read_at: date };
      });
      setDataNoti(newArr);
    } catch (error) {}
  };

  const fetchSeenAction = async () => {
    await fetchMaskAsSeen()
      .then(() => {})
      .catch((error: any) => {
        console.error(error);
      });
  };

  const fetchNotification = async () => {
    setIsLoading(true);
    try {
      const { data } = await fetchNotificationApi(nextPage);
      setDataNoti([...dataNoti, ...data.data]);
      if (data.meta.current_page >= data.meta.last_page) {
        setHasValue(false);
      } else {
        let NewNextPage = nextPage + 1;
        setNextPage(NewNextPage);
      }
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      throw err;
    }
  };

  const handleClickContext = () => {
    setIsOpen(false);
  };

  useOnClickOutside(notificationRef, handleClickContext);

  return (
    <div>
      <>
        <div className="header-notification flex items-center justify-between border-b border-gray-200 py-4">
          <h3 className="text-base font-bold text-gray-600">
            {t("left_menu_notification")}
          </h3>
          <button
            role="button"
            onClick={() => maskAsRead(undefined, undefined)}
            className="rounded-sm bg-gray-200 px-4 py-[6px] text-sm font-semibold text-gray-600 transition-all hover:bg-primary hover:text-white"
          >
            {t("left_menu_mark_all_as_read")}
          </button>
        </div>
        <ul className="main-notification mt-2 max-h-[500px] overflow-y-auto">
          {dataNoti?.map((value) => {
            return (
              <li
                key={value.id}
                onClick={() => maskAsRead(value.id, value.action_url)}
                className={classNames(
                  !value.read_at ? "bg-gray-100" : "",
                  "flex cursor-pointer flex-wrap gap-2 border-b border-gray-200 px-4 py-2 transition-all hover:bg-gray-100",
                )}
              >
                <p className="line-clamp-1 text-sm font-bold text-gray-600">
                  {value.title}
                </p>
                <div className="flex items-center justify-between">
                  <p className="line-clamp-1 w-[91%] text-sm text-gray-600">
                    {value.body}
                  </p>
                  {!value.read_at && (
                    <span className="inline-block h-[12px] w-[12px] rounded-full bg-primary-300"></span>
                  )}
                </div>
                <p className="text-sm text-gray-400">{value.created_at}</p>
              </li>
            );
          })}
          {dataNoti.length === 0 && !isLoading && (
            <li className="p-4 text-base font-bold text-gray-500">
              {t("header_no_notification")}
            </li>
          )}
          <li
            ref={loadMoreRef}
            className={classNames("py-2.5", hasValue ? "block" : "hidden")}
          >
            <Loading size="sm" />
          </li>
        </ul>
      </>
    </div>
  );
};

const NotificationMobile = () => {
  const t = useTranslations();
  const [isShowNotification, setIsShowNotification] = useState<boolean>(false);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const [, setLockedBody] = useLockedBody(false, "root");
  const user = useAppSelector((state) => state?.user?.user);

  useEffect(() => {
    if (isShowNotification) {
      setLockedBody(true);
    } else {
      setLockedBody(false);
    }
  }, [isShowNotification]);

  const handleToggleNotification = async () => {
    setIsShowNotification((prev) => !prev);
  };

  if (user && user?.roles?.[0] === "employer") {
    return null;
  }

  return (
    <div id="notification-mobile">
      <button
        onClick={() => handleToggleNotification()}
        type="button"
        className="flex h-10 w-10 items-center justify-center text-2xl"
        aria-label="Notification mobile"
        aria-busy="true"
      >
        <HiOutlineBell />
      </button>
      <div
        className={classNames(
          "fixed left-0 top-0 z-30 h-screen w-full bg-white",
          isShowNotification ? "block" : "hidden",
        )}
      >
        <div className="relative p-4">
          <div className="flex flex-1 items-center justify-between gap-2">
            <Link href="/" className="block h-9 w-28">
              <Image
                src="/v4/assets/images/common/logo-mobile-2.svg"
                width={114}
                height={22}
                loading="lazy"
                className="h-full w-full max-w-full object-contain"
                alt="TopDev"
              />
            </Link>
            <button role="button" onClick={() => handleToggleNotification()}>
              <MdClear className="h-5 w-5" />
            </button>
          </div>
          {isLoggedIn ? (
            <>
              <NotificationSection
                isOpen={isShowNotification}
                setIsOpen={setIsShowNotification}
              />
            </>
          ) : (
            <>
              <div className="py-14 text-center">
                <Image
                  src="/v4/assets/images/common/empty-notification-icon.svg"
                  alt="TopDev"
                  className="inline-block h-auto w-[120px] max-w-[108px]"
                  width="120"
                  height="108"
                  loading="lazy"
                />
                <div className="mb-2 mt-4 text-sm font-bold text-gray-600">
                  {t("notification_not_login_content")}
                </div>
                <button
                  onClick={() => openLoginPopup()}
                  className="rounded bg-primary-300 px-6 py-3 text-base font-semibold text-white"
                  type="button"
                >
                  {t("notification_not_login_button")}
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationMobile;
