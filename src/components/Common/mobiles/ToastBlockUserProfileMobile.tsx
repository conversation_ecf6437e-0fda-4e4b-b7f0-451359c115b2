"use client";

import { useTranslations } from "next-intl";
import Image from "next/image";
import { useEffect, useState } from "react";
const ToastBlockUserProfileMobile = ({
  isShowToast,
  setIsShowToast,
  eventClosed,
}: {
  isShowToast: boolean;
  setIsShowToast?(isShowToast: boolean): void;
  eventClosed: boolean;
}) => {
  const t = useTranslations();
  const [isClient, setIsClient] = useState<boolean>(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isShowToast || !isClient) return <div></div>;

  return (
    <div className="fixed left-0 top-0 z-[51] flex h-full w-full items-center justify-center bg-black/[0.25]">
      <div className="flex w-full max-w-[350px] flex-wrap justify-center gap-4 bg-white px-6 py-9 text-center">
        <Image
          width="85"
          height="84"
          loading="lazy"
          src="https://cdn.topdev.vn/v4/assets/images/common/image-toast.svg"
          alt="image toast"
          className="h-[84px] w-[85px]"
        />
        <h3 className="text-base font-bold text-black">
          {t("toast_notification_content")}
        </h3>
        <button
          onClick={() => !!setIsShowToast && setIsShowToast(false)}
          role="button"
          className="rounded border border-primary-300 px-6 py-3 text-base font-semibold text-primary-300 transition-all hover:bg-primary-300 hover:text-white"
        >
          {t("toast_notification_confirm")}
        </button>
      </div>
    </div>
  );
};

export default ToastBlockUserProfileMobile;
