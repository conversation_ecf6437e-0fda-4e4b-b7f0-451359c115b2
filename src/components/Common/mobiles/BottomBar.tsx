"use client";

import Link from "@/components/Link/Link";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";
import {
  HiBriefcase,
  HiOutlineBriefcase,
  HiOutlineHome,
  HiO<PERSON>lineUser,
  HiUser,
} from "react-icons/hi";
import {
  HiBuildingOffice2,
  HiHome,
  HiMiniSquaresPlus,
  HiOutlineBuildingOffice2,
  HiOutlineSquaresPlus,
} from "react-icons/hi2";

import { usePathname } from "@/navigation";
import { useRouter } from "next/navigation";
import { useAppSelector } from "@/store";
import { listMenu, classNames } from "@/utils";
import useCheckPageTechcombank from "@/hooks/useCheckPageTechcombank";
import React from "react";
import { URL_JOB_SEEKER } from "@/contansts/auth";

const BottomBar = () => {
  const checkPageTechcombank = useCheckPageTechcombank();

  const t = useTranslations();
  const subContent = useRef(null);
  const [isShowTools, setIsShowTools] = useState<boolean>(false);
  const [isShowAccounts, setIsShowAccounts] = useState<boolean>(false);
  const [isShowToast, setIsShowToast] = useState<boolean>(false);
  const user = useAppSelector((state) => state?.user?.user);
  const pathName = usePathname();
  const router = useRouter();
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const activePath = (slug: string): boolean => pathName == slug;
  let newRole: keyof typeof listMenu.account = user?.roles?.join(
    ",",
  ) as keyof typeof listMenu.account;
  const menuAccount = !!newRole && listMenu.account?.[newRole];
  const LIST_INACTIVE_BOTTOM_BAR = ["slug_it_jobs", "slug_it_jobs_detail"];
  const handleClickContext = (event: any) => {
    if (
      !!subContent?.current &&
      !(subContent.current as any).contains(event.target)
    ) {
      setIsShowTools(false);
      setIsShowAccounts(false);
    }
  };

  if (typeof window !== "undefined")
    document.addEventListener("click", handleClickContext);

  const handleClickToastEmployer = () => {
    setIsShowAccounts(false);
  };

  if (LIST_INACTIVE_BOTTOM_BAR.find((item) => pathName.includes(t(item)))) {
    return null;
  }

  //Check page in group Ex: "/group/techcombank"
  if (checkPageTechcombank) {
    return <></>;
  }

  const handleOpenWindow = () => {
    if (isLoggedIn) return;
    const url = `${URL_JOB_SEEKER}?redirect_uri=${window.location.href}`;
    router.push(url);
  };
  const handleLogout = () => {
    const logoutUrl = process.env.NEXT_PUBLIC_OAUTH2_URL_LOGOUT;
    if (logoutUrl) {
      router.push(logoutUrl + "");
    } else {
      console.error("Logout URL is not defined.");
    }
  };
  //Check page in group Ex: "/group/techcombank"
  if (checkPageTechcombank) {
    return <></>;
  }

  return (
    <>
      <div
        className={classNames(
          isShowTools || isShowAccounts ? "h-full" : "",
          "fixed bottom-0 left-0 z-40 flex w-full items-end bg-black/[0.25]",
        )}
      >
        <div ref={subContent} className="relative z-10 w-full bg-white">
          <div className="grid grid-flow-col grid-cols-5 gap-1 text-center">
            <div className="item-tabs">
              <a
                href="/"
                title={t("common_home")}
                className="flex flex-1 flex-col items-center justify-center gap-1 pb-4 pt-2 hover:text-primary-300"
              >
                <div className="relative h-5 w-5">
                  {activePath("/") ? (
                    <HiHome className="absolute h-full w-full text-primary-300" />
                  ) : (
                    <HiOutlineHome className="absolute h-full w-full" />
                  )}
                </div>
                <span
                  className={classNames(
                    activePath("/") ? "font-bold text-primary-300" : "",
                    "text-xs capitalize text-gray-500",
                  )}
                >
                  {t("common_home")}
                </span>
              </a>
            </div>
            <div className="item-tabs">
              <a
                href={
                  process.env.NEXT_PUBLIC_BASE_URL + "/" + t("slug_it_jobs")
                }
                title={t("header_it_jobs")}
                className="flex flex-1 flex-col items-center justify-center gap-1 pb-4 pt-2 hover:text-primary-300"
              >
                <div className="relative h-5 w-5">
                  {activePath("/" + t("slug_it_jobs")) ? (
                    <HiBriefcase className="absolute h-full w-full text-primary-300" />
                  ) : (
                    <HiOutlineBriefcase className="absolute h-full w-full" />
                  )}
                </div>
                <span
                  className={classNames(
                    activePath("/" + t("slug_it_jobs"))
                      ? "font-bold text-primary-300"
                      : "",
                    "line-clamp-1 text-xs capitalize text-gray-500",
                  )}
                >
                  {t("header_it_jobs")}
                </span>
              </a>
            </div>
            <div className="item-tabs">
              <a
                href={
                  process.env.NEXT_PUBLIC_BASE_URL +
                  "/" +
                  t("slug_it_companies")
                }
                title={t("header_it_companies_mobile")}
                className="flex flex-1 flex-col items-center justify-center gap-1 pb-4 pt-2 hover:text-primary-300"
              >
                <div className="relative h-5 w-5">
                  {activePath("/" + t("slug_it_companies")) ? (
                    <HiBuildingOffice2 className="absolute h-full w-full text-primary-300" />
                  ) : (
                    <HiOutlineBuildingOffice2 className="absolute h-full w-full" />
                  )}
                </div>
                <span
                  className={classNames(
                    activePath("/" + t("slug_it_companies"))
                      ? "font-bold text-primary-300"
                      : "",
                    "text-xs capitalize text-gray-500",
                  )}
                >
                  {t("header_it_companies_mobile")}
                </span>
              </a>
            </div>
            <div className="item-tabs">
              <div
                className="relative"
                onClick={() => {
                  setIsShowTools(!isShowTools);
                  setIsShowAccounts(false);
                }}
              >
                <div className="flex flex-1 flex-col items-center justify-center gap-1 pb-4 pt-2">
                  <div className="relative -z-10 h-5 w-5">
                    {isShowTools ? (
                      <HiMiniSquaresPlus className="absolute h-full w-full text-primary-300" />
                    ) : (
                      <HiOutlineSquaresPlus className="absolute h-full w-full" />
                    )}
                  </div>
                  <span
                    className={classNames(
                      isShowTools ? "text-primary-300" : "",
                      "text-xs capitalize text-gray-500",
                    )}
                  >
                    {t("header_tools")}
                  </span>
                </div>
              </div>
              <div
                className={classNames(
                  isShowTools
                    ? "visible bottom-full z-auto opacity-100"
                    : "invisible bottom-0 -z-50 opacity-0",
                  "box-content-bottom-bar absolute left-0 w-full rounded-tl-[16px] rounded-tr-[16px] border-b border-gray-300 bg-white transition-all duration-200 ease-linear",
                )}
              >
                <div className="flex w-full justify-center p-4">
                  <span className="rouded-[100px] h-[4px] w-[32px] bg-gray-300 opacity-40"></span>
                </div>
                <div className="pb-4 pl-5 pr-5">
                  <ul>
                    {listMenu.tools.map((value, index) => {
                      return (
                        <li key={index}>
                          <a
                            className="flex px-2 py-4 text-sm capitalize text-gray-600"
                            href={value.bottomBarHref}
                            title={t(value.title)}
                          >
                            <span className="mr-2 h-5 w-5 text-xl">
                              {React.createElement(value.icon)}
                            </span>
                            {t(value.title)}
                            {value.title === "home_convert_cv" && (
                              <span className="ml-2 rounded-lg border border-primary bg-primary-100 px-[6px] py-[1px] text-[10px] font-bold uppercase leading-[15px] text-primary">
                                {t("common_new")}
                              </span>
                            )}
                          </a>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              </div>
            </div>
            <div className="item-tabs">
              <div
                className="relative z-10"
                onClick={() => {
                  if (!!user?.id) {
                    setIsShowTools(false);
                    setIsShowAccounts(!isShowAccounts);
                  }
                }}
              >
                <button
                  title={t("bottom_bar_account")}
                  className="flex w-full flex-1 flex-col items-center justify-center gap-1 pb-4 pt-2"
                  onClick={() => handleOpenWindow()}
                >
                  <div className="relative -z-10 h-5 w-5">
                    {isShowAccounts ? (
                      <HiUser className="absolute h-full w-full text-primary-300" />
                    ) : (
                      <HiOutlineUser className="absolute h-full w-full" />
                    )}
                  </div>
                  <span
                    className={classNames(
                      isShowAccounts ? "text-primary-300" : "",
                      "text-xs capitalize text-gray-500",
                    )}
                  >
                    {t("bottom_bar_account")}
                  </span>
                </button>
              </div>
              <div
                className={classNames(
                  isShowAccounts
                    ? "visible bottom-full z-auto opacity-100"
                    : "invisible bottom-0 -z-50 opacity-0",
                  "box-content-bottom-bar absolute left-0 w-full rounded-tl-[16px] rounded-tr-[16px] border-b border-gray-300 bg-white text-left transition-all duration-200 ease-linear",
                )}
              >
                <div className="flex w-full justify-center p-4">
                  <span className="h-[4px] w-[32px] rounded-[100px] bg-gray-300 opacity-40"></span>
                </div>
                <div className="pb-4 pl-5 pr-5">
                  <div className="info border-b border-gray-200 pb-4">
                    <span className="text-sm text-gray-500">
                      {t("bottom_bar_hello")},
                    </span>
                    <h3 className="text-xl font-bold capitalize text-black">
                      {user?.full_name ?? "Updating..."}
                    </h3>
                    <span className="text-sm text-gray-500">
                      {user?.email ?? "Updating..."}
                    </span>
                  </div>
                  <ul>
                    {!!menuAccount &&
                      menuAccount
                        .filter(
                          (item) =>
                            item.title !== "header_recruitment_services",
                        )
                        .map((value, index) => {
                          if (
                            [
                              "header_employer_dashboard",
                              "header_dashboard",
                            ].includes(value.title)
                          ) {
                            return (
                              <li
                                key={index}
                                className="last:border-t last:border-gray-200"
                                onClick={() => {
                                  handleClickToastEmployer();
                                }}
                              >
                                <p
                                  className="flex px-2 py-4 text-sm capitalize text-gray-600"
                                  title={t(value.title)}
                                >
                                  <span className="mr-2 h-5 w-5 text-xl">
                                    {React.createElement(value.icon)}
                                  </span>
                                  {t(value.title)}
                                </p>
                              </li>
                            );
                          }
                          return (
                            <li
                              key={index}
                              className="last:border-t last:border-gray-200"
                              onClick={() => {
                                handleClickToastEmployer();
                              }}
                            >
                              <Link
                                className="flex px-2 py-4 text-sm capitalize text-gray-600"
                                href={
                                  [
                                    "header_employer_dashboard",
                                    "header_dashboard",
                                  ].includes(value.title)
                                    ? "#"
                                    : value.href ?? ""
                                }
                                title={t(value.title)}
                              >
                                <span className="mr-2 h-5 w-5 text-xl">
                                  {React.createElement(value.icon)}
                                </span>
                                {t(value.title)}
                              </Link>
                            </li>
                          );
                        })}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
export default BottomBar;
