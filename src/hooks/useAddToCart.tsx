import Swal from "sweetalert2";
import { useTranslations } from "next-intl";
import { useAppSelector, useAppDispatch } from "@/store";
import { useEffect, useState } from "react";
import { ButtonAddToCartProp } from "@/types/products";
import { OrderProp } from "@/types/cart";
import { addToCart, updateToCart } from "@/services/cartAPI";
import ToastNotification from "@/components/Swal/ToastNotification";
import useCart from "@/hooks/useCart";
import useProduct from "@/hooks/useProduct";
import openLoginPopup from "@/utils/openLoginPopup";
import { setShowPopupLimitQuantity } from "@/store/slices/cartSlide";
import { setCookie } from "@/utils/cookies";

const useAddToCart = () => {
  const dispatch = useAppDispatch();
  const [cart, setCartData, recallCart] = useCart();
  const [checkProductChange] = useProduct();
  const [loadingCart, setLoadingCart] = useState<boolean>(false);
  const [listCart, setListCart] = useState<Array<OrderProp>>([]);
  const user = useAppSelector((state) => state?.user?.user);
  const isLoggedIn = useAppSelector((state) => state?.user?.isLoggedIn);
  const t = useTranslations();

  useEffect(() => {
    if (!!cart && cart.uuid && cart.items.length > 0) {
      setListCart(cart.items);
    }
  }, [cart]);

  const checkFlowCartForUser = (
    kind: "buyNow" | "add" | "onlyCheck" = "buyNow",
  ) => {
    if (!isLoggedIn) {
      openLoginPopup([{ name: "isEmployer", value: true }]);
      return false;
    }

    // Maintainace scripts
    Swal.fire({
      title: t("products_title_toast_maintaining"),
      text: t("products_description_toast_maintaining"),
      showCancelButton: true,
      showConfirmButton: false,
      icon: "error",
      cancelButtonColor: "#DD3F24",
      cancelButtonText: t("products_confirm_toast_for_resume"),
      customClass: {
        title: "text-2xl text-gray-600",
        container: "custom-toast-container",
      },
    });

    return false;
  };

  const handleAddToCart = async ({
    product_id,
    kind,
    price,
    anchoring_price,
  }: ButtonAddToCartProp) => {
    // Maintainace scripts
    Swal.fire({
      title: t("products_title_toast_maintaining"),
      text: t("products_description_toast_maintaining"),
      showCancelButton: true,
      showConfirmButton: false,
      icon: "error",
      cancelButtonColor: "#DD3F24",
      cancelButtonText: t("products_confirm_toast_for_resume"),
      customClass: {
        title: "text-2xl text-gray-600",
        container: "custom-toast-container",
      },
    });

    return false;
  };

  return { loadingCart, checkFlowCartForUser, handleAddToCart };
};

export default useAddToCart;
