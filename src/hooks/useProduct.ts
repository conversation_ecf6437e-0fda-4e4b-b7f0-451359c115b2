import { getProductDetail } from "@/services/productsAPI";
import { useTranslations } from "next-intl";
import Swal from "sweetalert2";

export default function useProduct(): [
  (
    product_id: number,
    price: number,
    anchoring_price?: number,
  ) => Promise<boolean>,
] {
  const t = useTranslations();

  const checkProductChange = async (
    product_id: number,
    price: number,
    anchoring_price?: number,
  ) => {
    if (!product_id) return false;

    //Get details of product
    const detailProduct = await getProductDetail(product_id);

    if (!detailProduct && detailProduct.error) return false;

    const isChangePrice =
      detailProduct.data.price !== price ||
      detailProduct.data.anchoring_price !== (anchoring_price ?? 0);
    const isInactive = !detailProduct.data.is_active;

    //Notice when change price and status in carts
    if (isChangePrice && isInactive) {
      Swal.fire({
        title: t("common_notification"),
        html: t("products_toast_update_cart"),
        showCancelButton: true,
        showConfirmButton: false,
        icon: "warning",
        cancelButtonColor: "#DD3F24",
        cancelButtonText: t("toast_notification_confirm"),
      }).then(() => {
        Swal.fire({
          title: t("common_sorry"),
          html: t.rich("products_toast_product_inactive", {
            b: (chunks) => `<b>${chunks}</b>`,
            productName: detailProduct?.data?.name,
          }) as string,
          showCancelButton: true,
          showConfirmButton: false,
          icon: "warning",
          cancelButtonColor: "#DD3F24",
          cancelButtonText: t("toast_notification_confirm"),
        });
      });

      return true;
    }

    //Notice when change price in carts
    if (isChangePrice) {
      Swal.fire({
        title: t("common_notification"),
        html: t("products_toast_update_cart"),
        showCancelButton: true,
        showConfirmButton: false,
        icon: "warning",
        cancelButtonColor: "#DD3F24",
        cancelButtonText: t("toast_notification_confirm"),
      });
      return true;
    }

    //Notice when change status in carts
    if (isInactive) {
      Swal.fire({
        title: t("common_sorry"),
        html: t.rich("products_toast_product_inactive", {
          b: (chunks) => `<b>${chunks}</b>`,
          productName: detailProduct?.data?.name,
        }) as string,
        showCancelButton: true,
        showConfirmButton: false,
        icon: "warning",
        cancelButtonColor: "#DD3F24",
        cancelButtonText: t("toast_notification_confirm"),
      });

      return true;
    }

    return false;
  };

  return [checkProductChange];
}
