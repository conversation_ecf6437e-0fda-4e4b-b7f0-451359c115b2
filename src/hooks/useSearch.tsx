import { SearchJobParamV2 } from "@/services/searchAPI";
import { getCurrentLocale } from "@/utils/locale";
import _ from "lodash";

export const onSearchClick = (searchState: any, locale?: string) => {
  const filterParams: SearchJobParamV2 = {
    keyword: searchState.keyword as string,
    ordering: searchState.sort_by as string,
    region_ids: searchState.locations.join(","),
    job_categories_ids: searchState.categories.join(","),
    // job_roles_ids: searchState.job_roles_ids as string,
    salary_min: searchState.salary_min,
    salary_max: searchState.salary_max,
    benefit_ids: searchState.benefits.join(","),
    // skills_id: searchState.skills_id as string,
    job_levels_ids: searchState.experience_levels.join(","),
    job_types_ids: searchState.work_types.join(","),
    contract_types_ids: searchState.contract_types.join(","),
    company_size_ids: searchState.company_sizes.join(","),
    company_industry_ids: searchState.company_industries.join(","),
  };

  // Step 1: Remove undefined and NaN values
  const cleanedParams = _.pickBy(
    filterParams,
    (v) => v !== undefined && !_.isNaN(v) && !_.isNull(v),
  );

  // Step 2: Create new search params
  const searchParams = new URLSearchParams();
  for (const [key, value] of Object.entries(cleanedParams)) {
    if (value !== "") {
      searchParams.set(key, String(value));
    }
  }

  // Step 3: Replace current URL
  const lang = locale ?? getCurrentLocale();
  const language = lang === "vi" ? "/viec-lam/tim-kiem" : "/jobs/search";
  const url = new URL(process.env.NEXT_PUBLIC_BASE_URL + language);
  url.search = searchParams.toString();

  window.location.href = url.toString();
};
