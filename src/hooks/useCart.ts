import { useTranslations } from "next-intl";
import Swal from "sweetalert2";
import { getCarts } from "@/services/cartAPI";
import { useAppDispatch, useAppSelector } from "@/store";
import { setCart } from "@/store/slices/cartSlide";
import { CartProp } from "@/types/cart";
import { getCookie, setCookie } from "@/utils/cookies";

export default function useCart(): [
  CartProp,
  (cart: CartProp) => void,
  () => void,
] {
  const t = useTranslations();
  const dispatch = useAppDispatch();
  const cart = useAppSelector((state) => state.cart.cart);

  function setCartData(cart: CartProp) {
    dispatch(setCart(cart));
  }

  async function recallCart() {
    const result = await getCarts().then((response) => {
      setCartData(response?.data);

      //Run when click add to cart product page
      //Use not check when load page
      const checkedWhenOnClick = getCookie("isChangeProductOnClick") ?? false;
      if (checkedWhenOnClick) {
        setCookie("isChangeProductOnClick", "false", 0);
        return true;
      }

      const isChangeStatus = response?.data?.warnings?.status.length > 0;
      const isChangePrice =
        response?.data?.warnings?.price.length > 0 ||
        response?.data?.warnings?.anchoring_price.length > 0;

      //Notice when warning price and status in carts
      if (isChangePrice && isChangeStatus) {
        Swal.fire({
          title: t("common_notification"),
          html: t("products_toast_update_cart"),
          showCancelButton: true,
          showConfirmButton: false,
          icon: "warning",
          cancelButtonColor: "#DD3F24",
          cancelButtonText: t("toast_notification_confirm"),
        }).then(() => {
          Swal.fire({
            title: t("common_sorry"),
            html: t.rich("products_toast_product_inactive", {
              b: (chunks) => `<b>${chunks}</b>`,
              productName: response?.data?.warnings.status.toString(),
            }) as string,
            showCancelButton: true,
            showConfirmButton: false,
            icon: "warning",
            cancelButtonColor: "#DD3F24",
            cancelButtonText: t("toast_notification_confirm"),
          });
        });

        return false;
      }

      //Notice when warning price in carts
      if (isChangePrice) {
        Swal.fire({
          title: t("common_notification"),
          html: t("products_toast_update_cart"),
          showCancelButton: true,
          showConfirmButton: false,
          icon: "warning",
          cancelButtonColor: "#DD3F24",
          cancelButtonText: t("toast_notification_confirm"),
        });
        return false;
      }

      //Notice when warning status in carts
      if (isChangeStatus) {
        Swal.fire({
          title: t("common_sorry"),
          html: t.rich("products_toast_product_inactive", {
            b: (chunks) => `<b>${chunks}</b>`,
            productName: response?.data?.warnings?.status.toString(),
          }) as string,
          showCancelButton: true,
          showConfirmButton: false,
          icon: "warning",
          cancelButtonColor: "#DD3F24",
          cancelButtonText: t("toast_notification_confirm"),
        });

        return false;
      }

      return true;
    });

    return result;
  }

  return [cart, setCartData, recallCart];
}
