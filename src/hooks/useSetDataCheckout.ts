import { useState } from "react";
import { DataPayment } from "@/types/cart";
import { useAppDispatch, useAppSelector } from "@/store";
import { setDataResponseCheckout } from "@/store/slices/cartSlide";

export default function useSetDataCheckout(): [
  DataPayment,
  (response: DataPayment) => void,
] {
  const dispatch = useAppDispatch();
  const dataPayment = useAppSelector(
    (state) => state.cart.dataResponseCheckout,
  );

  const setDataCheckout = (response: DataPayment) => {
    dispatch(setDataResponseCheckout(response));
  };
  return [dataPayment, setDataCheckout];
}
