// lib/useIsMobileBrowser.js
'use client'
import { useEffect, useState } from 'react';

const useIsMobileBrowser = () => {
  const [isMobileBrowser, setIsMobileBrowser] = useState<boolean>(false);

  useEffect(() => {
    // Ensure this runs only on the client side
    if (typeof window !== 'undefined') {
      const userAgent = window.navigator.userAgent.toLowerCase();

      // Basic heuristic to detect WebView vs Browser
      const isWebView = /webview|wv/i.test(userAgent); // Common WebView indicators
      const isMobileDevice = /android|iphone|ipad|ipod/i.test(userAgent);
      const isKnownBrowser = /chrome|safari|firefox|edge|ucbrowser|opera/i.test(userAgent);

      // If it's a mobile device and not a WebView, assume it's a browser
      if (isMobileDevice && !isWebView && isKnownBrowser) {
        setIsMobileBrowser(true);
      } else {
        setIsMobileBrowser(false);
      }
    }
  }, []);

  return isMobileBrowser;
};

export default useIsMobileBrowser;