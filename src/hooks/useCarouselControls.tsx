"use client";
import { CarouselApi } from "@/components/v2/ui/carousel";
import { useState, useEffect } from "react";

export function useCarouselControls() {
  const [api, setApi] = useState<CarouselApi | null>(null);
  const [canScrollPrev, setCanScrollPrev] = useState(false);
  const [canScrollNext, setCanScrollNext] = useState(false);

  useEffect(() => {
    if (!api) return;

    const updateButtons = () => {
      setCanScrollPrev(api.canScrollPrev());
      setCanScrollNext(api.canScrollNext());
    };

    updateButtons();
    api.on("select", updateButtons);

    return () => {
      api.off("select", updateButtons);
    };
  }, [api]);

  return { api, setApi, canScrollPrev, canScrollNext };
}
