import { useAppDispatch, useAppSelector } from "@/store";
import { setProfile } from "@/store/slices/userSlice";
import Profile from "@/types/profile";

export default function useUserProfile(): [
  Profile,
  (profile: Profile) => void,
] {
  const userProfile = useAppSelector((state) => state.user.profile);
  const dispatch = useAppDispatch();
  function setUserProfile(profile: Profile) {
    dispatch(setProfile(profile));
  }

  return [userProfile, setUserProfile];
}
