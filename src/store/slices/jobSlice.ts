import { createSlice } from "@reduxjs/toolkit";
import { PayloadAction } from "@reduxjs/toolkit";
import { JobType } from "@/types/job";
import { Addresses } from "../../types/job";
import { CompanyType } from "../../types/company";
interface JobState {
  currentJob: JobType;
  jobs: Array<JobType>;
  jobsForCompany: Array<JobType>;
  isLoading: boolean;
  jobApplied: {
    jobId: number;
    status: boolean;
  };
  detailJob: {
    jobIndex: number;
  };
}

const initAddress: Addresses = {
  address_region_array: [],
  address_region_ids: [],
  address_region_list: "",
  collection_addresses: [
    {
      district: {
        id: "",
        value: "",
      },
      full_address: "",
      id: 0,
      latitude: null,
      longitude: null,
      postal_code: "",
      province: {
        id: "",
        value: "",
      },
      street: "",
      ward: { id: "", value: "" },
    },
  ],
  full_addresses: [],
  sort_addresses: "",
};

const initCompany: CompanyType = {
  description: "",
  nationalities_arr: [],
  company_size: "",
  industries_arr: [],
  id: 0,
  display_name: "",
  image_logo: "",
  slug: "",
  detail_url: "",
  is_followed: false,
  tagline: "",
  website: "",
  image_galleries: [
    {
      id: 0,
      name: "",
      url: "",
      path: "",
      source: "",
      uploaded: {
        date: "",
        datetime: "",
        since: "",
        timestamp: 0,
      },
    },
  ],
  num_job_openings: 0,
  benefits: [],
  recruitment_process: [],
  faqs: "",
  products: [],
  news: [],
  skills_ids: [],
  addresses: initAddress,
  image_cover: "",
  industries_str: "",
  latest_jobs: [],
  skills_arr: [],
  description_str: "",
  industries_ids: [],
  social_network: [],
  num_followeres: 0,
  meta_title: "",
  meta_keywords: "",
  meta_description: "",
  num_employees: 0,
};

const initJob: JobType = {
  responsibilities: "",
  requirements: "",
  slug: "",
  addresses: initAddress,
  applied: null,
  benefits: [],
  benefits_company: [], // <-- Added missing property
  candidate: null,
  company: initCompany,
  benefits_v2: [],
  content: "",
  detail_url: "",
  extra_skills: [],
  features: [],
  id: 0,
  is_applied: false,
  is_blacklisted: false,
  is_followed: false,
  is_remove_cv: false,
  is_salary_visible: false,
  is_viewed: false,
  experiences_ids: [],
  job_levels_arr: [],
  job_levels_ids: [],
  job_levels_str: "",
  job_types_str: "",
  job_types_ids: [],
  contract_types_ids: [],
  job_url: "",
  owned_id: 0,
  packages: [],
  published: {
    date: "",
    datetime: "",
    since: "",
  },
  recalled_at: "",
  refreshed: {
    date: "",
    datetime: "",
    since: "",
  },
  requirements_arr: [],
  salary: {
    currency: "",
    currency_estimate: "",
    is_negotiable: 0,
    max: "",
    max_estimate: 0,
    min: "",
    min_estimate: 0,
    unit: "",
    value: null,
  },
  skills_arr: [],
  skills_ids: [],
  skills_str: "",
  status_display: "",
  title: "",
  recruiment_process: [],
  schema_job_posting: "",
  meta_title: "",
  meta_keywords: "",
  meta_description: "",
  image_thumbnail: "",
  is_basic: false,
  is_basic_plus: false,
  is_distinction: false,
  is_free: false,
  is_content_image: false,
  is_content_image_enabled: false,
  education_arr: [],
  education_str: "",
  education_ids: [],
  education_major_arr: [],
  education_major_str: "",
  education_major_ids: [],
  education_certificate: "",
  months_of_experience: 0,
  min_months_of_experience: 0,
};

const initialState: JobState = {
  currentJob: initJob,
  jobs: [],
  jobsForCompany: [],
  isLoading: true,
  jobApplied: {
    jobId: 0,
    status: false,
  },
  detailJob: {
    jobIndex: 0,
  },
};

const jobSlice = createSlice({
  name: "job",
  initialState,
  reducers: {
    createOrUpdateCurrentJob: (state, action: PayloadAction<JobType>) => {
      state.currentJob = action.payload;
      state.isLoading = false;
    },
    createOrUpdateJobs: (state, action: PayloadAction<Array<JobType>>) => {
      state.jobs = action.payload;
    },
    setActiveAppliedJob: (
      state,
      action: PayloadAction<{
        jobId: number;
        status: boolean;
      }>,
    ) => {
      state.jobApplied = { ...state.jobApplied, ...action.payload };
    },
    setDetailJobIndex: (state, action: PayloadAction<number>) => {
      state.detailJob.jobIndex = action.payload;
    },
  },
});

export const {
  createOrUpdateCurrentJob,
  createOrUpdateJobs,
  setActiveAppliedJob,
  setDetailJobIndex,
} = jobSlice.actions;

export default jobSlice;
