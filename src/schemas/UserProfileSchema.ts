import { string, object, number, date, array, bool } from "yup";
import { phoneRegExp } from '@/utils/enums';

const BasicInformationSchema = (t: any) =>
  object().shape({
    display_name: string()
      .required(t("user_profile_basic_info_validate_fullname_required"))
      .matches(
        /^[^\d!@#$%^&*()_+=[\]{};':"\\|,.<>/?]*$/u,
        t("user_profile_basic_info_validate_fullname_invalid"),
      ),
    birthday: date().max(new Date()).nullable(),
    gender: string().nullable(),
    position: string().required(
      t("user_profile_basic_info_validate_position_required"),
    ),
    years_of_exp: number()
      .integer()
      .required(t("user_profile_basic_info_validate_exp_required")),
    phone: string()
    .required(t("user_profile_basic_info_validate_phone_required"))
    .matches(
      phoneRegExp,
      t("user_profile_basic_info_validate_phone_format"),
    ),
    address: string()
      .max(120, t("user_profile_basic_info_validate_address_max_length"))
      .nullable(),
    province_code: string().required(
      t("user_profile_basic_info_validate_province_required"),
    ),
    linkedin_link: string().url().nullable(),
    github_link: string().url().nullable(),
    technical_skills: array()
    .of(object().shape({
      skill_id: number().required(t("user_profile_skill_validate_technical_skill_required")),
      skill_name: string().required(t("user_profile_skill_validate_technical_skill_required")),
    }))
    .min(1, t("user_profile_skill_validate_technical_skill_required")),
  });

const SummarySchema = (t: any) =>
  object().shape({
    summary: string()
      .required(t("user_profile_summary_validate_required"))
      .max(2500, t("user_profile_summary_validate_max_length")),
  });

const WorkExperienceSchema = (t: any) =>
  object().shape({
    to: string().when("is_working_here", {
      is: false,
      then: (schema) =>
        schema.required(
          t("user_profile_work_experience_validate_enddate_required"),
        ),
    }),
    from: string().required(
      t("user_profile_work_experience_validate_startdate_required"),
    ),
    company: string().required(
      t("user_profile_work_experience_validate_name_required"),
    ),
    position: string().required(
      t("user_profile_work_experience_validate_position_required"),
    ),
    description: string().nullable(),
    is_working_here: bool(),
    skills: array().of(
      object({
        skill_id: number(),
        skill_name: string(),
      }),
    ),
    projects: array().of(
      object({
        description: string(),
        project_name: string().required(
          t("user_profile_work_experience_validate_project_required"),
        ),
        project_time: string().required(
          t("user_profile_work_experience_validate_timeline_required"),
        ),
      }),
    ),
  });

const EducationSchema = (t: any) =>
  array().of(
    object({
      to: date().when("is_studying_here", {
        is: false,
        then: (schema) =>
          schema.required(
            t("user_profile_education_validate_enddate_required"),
          ),
      }),
      from: date().required(
        t("user_profile_education_validate_startdate_required"),
      ),
      degree: string().required(
        t("user_profile_education_validate_startdate_required"),
      ),
      description: string().nullable(),
      school_name: string().required(
        t("user_profile_education_validate_shoolname_required"),
      ),
      is_studying_here: bool(),
    }),
  );

const ProjectSchema = (t: any) =>
  array().of(
    object({
      project_name: string().required(
        t("user_profile_project_validate_name_required"),
      ),
      project_time: string().nullable(),
      position: string().nullable(),
      description: string().nullable(),
    }),
  );

const LanguageSchema = (t: any) =>
  array().of(
    object({
      language: string().required(
        t("user_profile_language_validate_language_required"),
      ),
    }),
  );

const HobbySchema = (t: any) =>
  object().shape({
    interests: array()
      .of(string().required(t("user_profile_hobby_validate_name_required")))
      .min(1, t("user_profile_hobby_validate_name_required")),
  });

const ReferenceSchema = (t: any) =>
  array().of(
    object({
      refer_name: string().required(
        t("user_profile_reference_validate_name_required"),
      ),
      refer_email: string().nullable(),
      refer_phone: string().nullable(),
      refer_profession: string().nullable(),
    }),
  );

const ActivitySchema = (t: any) =>
  array().of(
    object({
      activity_date: string().nullable(),
      achievement: string().nullable(),
      activity: string().required(
        t("user_profile_activity_validate_name_reuiqred"),
      ),
      is_working_here: bool(),
    }),
  );

const CertificateSchema = (t: any) =>
  array().of(
    object({
      name: string().required(
        t("user_profile_certificate_validate_name_required"),
      ),
      date_completed: string().nullable(),
      description: string().nullable(),
    }),
  );

const SkillSchema = (t: any) =>
  object().shape({
    technical_skills: array()
      .of(
        object().shape({
          skill_id: number().required(
            t("user_profile_skill_validate_technical_skill_required"),
          ),
          skill_name: string().required(
            t("user_profile_skill_validate_technical_skill_required"),
          ),
        }),
      )
      .min(1, t("user_profile_skill_validate_technical_skill_required")),
  });

  const AdditionalSchema = (t: any) =>
  array().of(
    object({
      additional: string().required(
        t("user_profile_other_validate_name_required"),
      ),
      AdditionalSchema: string().nullable(),
    }),
  );

export {
  BasicInformationSchema,
  SummarySchema,
  WorkExperienceSchema,
  EducationSchema,
  ProjectSchema,
  LanguageSchema,
  HobbySchema,
  ReferenceSchema,
  ActivitySchema,
  CertificateSchema,
  SkillSchema,
  AdditionalSchema
};
