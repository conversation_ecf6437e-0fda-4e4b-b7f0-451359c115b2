import { string, object } from "yup";

export const SchemaSalaryForm = () => {
  return object().shape({
    salary_number: string()
      .required("<PERSON>ui lòng nhập số thu nhập")
      .min(1, "<PERSON><PERSON> thu nhập phải lớn hơn 0"),
    salary_primary: string().required("Vui lòng nhập lương chính"),
    salary_area: string().required("Vui lòng chọn vùng"),
    salary_person: string()
      .required("Vui lòng chọn người phụ thuộc")
      .min(0, "Số người phụ thuộc là số nguyên dương"),
    salary_option: string().required("Vui lòng chọn quy định"),
  });
};
