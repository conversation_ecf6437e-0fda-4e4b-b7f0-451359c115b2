import { string, object, bool } from "yup";
import { phoneRegExp } from "@/utils/enums";

const schemaFormRecruit = () => {
  return object().shape({
    company: string().required("Tên công ty không được bỏ trống"),
    name: string().required("Tên của bạn không được bỏ trống"),
    position: string().required("Vị trí không được bỏ trống"),
    email: string()
      .email("Vui lòng nhập đúng định dạng")
      .required("Email không được bỏ trống"),
    phone: string().matches(phoneRegExp, "Số điện thoại không hợp lệ"),
    where_referral: string().required("Thông tin không được bỏ trống"),
    description: string().required("Vui lòng thêm miêu tả"),
    policy_required: bool().oneOf(
      [true],
      "Vui lòng đồng ý với điều khoản và điều kiện dịch vụ",
    ),
    notification_required: bool().oneOf(
      [true],
      "Vui lòng đồng ý với điều khoản và điều kiện dịch vụ",
    ),
  });
};

export default schemaFormRecruit;
