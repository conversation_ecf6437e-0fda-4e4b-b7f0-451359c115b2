import { string, object, number } from "yup";
import { phonePaymentRegExp } from "@/utils/enums";

export default function schemaFormPayment(t: any) {
  return object().shape({
    buyer_name: string().required(t("carts_payment_required_fullname")),
    buyer_phone: string()
      .matches(phonePaymentRegExp, t("carts_payment_validation_is_valid_phone"))
      .required(t("carts_payment_required_phone_number"))
      .min(10, t("carts_payment_validate_phone_number_length"))
      .max(15, t("carts_payment_validate_phone_number_length")),
    crm_company_id: number()
      .integer()
      .required(t("carts_payment_required_tax_code")),
    company_business_name: string().required(
      t("carts_payment_required_company_business_name"),
    ),
    company_phone: string()
      .nullable()
      .matches(
        phonePaymentRegExp,
        t("carts_payment_validation_is_valid_phone_infomation_company"),
      )
      .min(10, t("carts_payment_validate_phone_number_length"))
      .max(15, t("carts_payment_validate_phone_number_length")),
  });
}
