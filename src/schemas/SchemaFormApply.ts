import { string, object, bool } from "yup";
import { phoneRegExp } from "@/utils/enums";

const schemaFormApply = (t: any, isLoggedIn: boolean) => {
  return object().shape({
    display_name: string().required(
      t("detail_job_page_apply_validation_is_requried_name"),
    ),
    email: string()
      .email(t("detail_job_page_apply_validation_is_valid_email"))
      .required(t("detail_job_page_apply_validation_is_requied_email")),
    phone: string().matches(
      phoneRegExp,
      t("detail_job_page_apply_validation_is_valid_phone"),
    ),
    tnc: bool().oneOf(
      [!isLoggedIn],
      t("detail_job_page_apply_required_tnc_apply"),
    ),
  });
};

export default schemaFormApply;
