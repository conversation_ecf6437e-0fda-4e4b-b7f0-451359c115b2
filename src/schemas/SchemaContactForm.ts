import { string, object } from "yup";
import { phoneRegExp } from "@/utils/enums";

const SchemaContactForm = (t: any) => {
  return object().shape({
    fullname: string().required(
      t("detail_job_page_apply_placeholder_name"),
    ),
    company: string().required("Please enter your company"),
    email: string()
      .email(t("detail_job_page_apply_validation_is_valid_email"))
      .required(t("detail_job_page_apply_validation_is_requied_email")),
    phone: string().matches(
      phoneRegExp,
      t("detail_job_page_apply_validation_is_valid_phone"),
    ),
    message: string().required("Please enter your message"),
  });
};

export default SchemaContactForm;
