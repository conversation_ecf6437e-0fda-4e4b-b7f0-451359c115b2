{"contract_types": [{"id": 1622, "taxonomy": "contract_types", "text": "Fulltime", "slug": "fulltime", "text_vi": "Fulltime", "text_en": "Fulltime", "sort_order": 0}, {"id": 1624, "taxonomy": "contract_types", "text": "Freelance", "slug": "freelance", "text_vi": "Freelance", "text_en": "Freelance", "sort_order": 0}, {"id": 4621, "taxonomy": "contract_types", "text": "Part-time", "slug": "part-time", "text_vi": "Part-time", "text_en": "Part-time", "sort_order": 0}], "skills": [{"id": 1, "taxonomy": "skills", "text": "PHP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "php", "text_vi": "PHP", "text_en": "PHP", "sort_order": 0, "description": "PHP"}, {"id": 10, "taxonomy": "skills", "text": "Magento", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "magento", "text_vi": "Magento", "text_en": "Magento", "sort_order": 0, "description": "Magento"}, {"id": 100, "taxonomy": "skills", "text": "SEM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sem", "text_vi": "SEM", "text_en": "SEM", "sort_order": 0, "description": "SEM"}, {"id": 101, "taxonomy": "skills", "text": "SEO", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "seo", "text_vi": "SEO", "text_en": "SEO", "sort_order": 0, "description": "SEO"}, {"id": 10102, "taxonomy": "skills", "text": "Django", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "django-1", "text_vi": "Django", "text_en": "Django", "sort_order": 0, "description": "Django Rest Framework"}, {"id": 10132, "taxonomy": "skills", "text": "Sale", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sale", "text_vi": "Sale", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "Sale"}, {"id": 10133, "taxonomy": "skills", "text": "Marketing", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "marketing-1", "text_vi": "Marketing", "text_en": "Marketing", "sort_order": 0, "description": "Marketing"}, {"id": 10135, "taxonomy": "skills", "text": "HR", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hr", "text_vi": "HR", "text_en": "HR", "sort_order": 0, "description": "HR"}, {"id": 10136, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tai-chinh-2", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "Finance", "sort_order": 0, "description": "Finance"}, {"id": 10137, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tuyen-dung-3", "text_vi": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>", "text_en": "Talent Acquisition", "sort_order": 0, "description": "Talent Acquisition"}, {"id": 10138, "taxonomy": "skills", "text": "<PERSON><PERSON> toán", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ke-toan", "text_vi": "<PERSON><PERSON> toán", "text_en": "Accounting", "sort_order": 0, "description": "Accounting"}, {"id": 10139, "taxonomy": "skills", "text": "C&B", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "c-b", "text_vi": "C&B", "text_en": "C&B", "sort_order": 0, "description": "C&B"}, {"id": 10140, "taxonomy": "skills", "text": "Business Development", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "business-development", "text_vi": "Business Development", "text_en": "<PERSON><PERSON><PERSON> triển kinh doanh", "sort_order": 0, "description": "Business Development"}, {"id": 102, "taxonomy": "skills", "text": "MVC5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mvc5", "text_vi": "MVC5", "text_en": "MVC5", "sort_order": 0, "description": "MVC5"}, {"id": 10299, "taxonomy": "skills", "text": "On Premise", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "skill", "slug": "on-premise", "text_vi": "On Premise", "text_en": "On Premise", "sort_order": 0, "description": "On Premise"}, {"id": 103, "taxonomy": "skills", "text": "WPF", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "wpf", "text_vi": "WPF", "text_en": "WPF", "sort_order": 0, "description": "WPF"}, {"id": 10308, "taxonomy": "skills", "text": "<PERSON><PERSON> h<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ngan-hang-1", "text_vi": "<PERSON><PERSON> h<PERSON>", "text_en": "Banking", "sort_order": 0, "description": "Banking"}, {"id": 10389, "taxonomy": "skills", "text": "Research", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "research", "text_vi": "Research", "text_en": "Research", "sort_order": 0, "description": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 10391, "taxonomy": "skills", "text": "Research", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nghien-cuu", "text_vi": "Research", "text_en": "Research", "sort_order": 0, "description": "Conduct thorough research to understand complex technical concepts and processes."}, {"id": 104, "taxonomy": "skills", "text": "Signal R", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "signal-r", "text_vi": "Signal R", "text_en": "Signal R", "sort_order": 0, "description": "Signal R"}, {"id": 1043, "taxonomy": "skills", "text": "ABAP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "abap", "text_vi": "ABAP", "text_en": "ABAP", "sort_order": 0, "description": "ABAP"}, {"id": 105, "taxonomy": "skills", "text": "Knockout JS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "knockout-js", "text_vi": "Knockout JS", "text_en": "Knockout JS", "sort_order": 0, "description": "Knockout JS"}, {"id": 106, "taxonomy": "skills", "text": "REST API", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rest-api", "text_vi": "REST API", "text_en": "REST API", "sort_order": 0, "description": "REST API"}, {"id": 10607, "taxonomy": "skills", "text": "VanillaJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "vanilla<PERSON>s", "text_vi": "VanillaJS", "text_en": "VanillaJS", "sort_order": 0, "description": "VanillaJS"}, {"id": 10608, "taxonomy": "skills", "text": "Social media", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "social-media-1", "text_vi": "Social media", "text_en": "Social media", "sort_order": 0, "description": "Social media"}, {"id": 1061, "taxonomy": "skills", "text": "Video Game Producer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "video-game-producer", "text_vi": "Video Game Producer", "text_en": "Video Game Producer", "sort_order": 0, "description": "Video Game Producer"}, {"id": 10610, "taxonomy": "skills", "text": "Content", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "content", "text_vi": "Content", "text_en": "Content", "sort_order": 0, "description": "Content marketing"}, {"id": 10611, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ngan-hang", "text_vi": "<PERSON><PERSON>", "text_en": "Bank", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 10628, "taxonomy": "skills", "text": "MQL4/MQL5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mql4-mql5", "text_vi": "MQL4/MQL5", "text_en": "MQL4/MQL5", "sort_order": 0, "description": "MQL4/MQL5"}, {"id": 10629, "taxonomy": "skills", "text": "Talent Acquisition", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "talent-acquisition", "text_vi": "Talent Acquisition", "text_en": "Talent Acquisition", "sort_order": 0, "description": "Talent Acquisition"}, {"id": 10630, "taxonomy": "skills", "text": "Revit", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "revit", "text_vi": "Revit", "text_en": "Revit", "sort_order": 0, "description": "Revit"}, {"id": 10631, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "kiem-toan", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 10642, "taxonomy": "skills", "text": "Social Media", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "social-media", "text_vi": "Social Media", "text_en": "Social Media", "sort_order": 0, "description": "Social Media"}, {"id": 10643, "taxonomy": "skills", "text": "Capcut", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "capcut", "text_vi": "Capcut", "text_en": "Capcut", "sort_order": 0, "description": "Capcut"}, {"id": 10644, "taxonomy": "skills", "text": "E-commerce", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "e-commerce", "text_vi": "E-commerce", "text_en": "E-commerce", "sort_order": 0, "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử"}, {"id": 10645, "taxonomy": "skills", "text": "Postman", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "postman-1", "text_vi": "Postman", "text_en": "Postman", "sort_order": 0, "description": "Postman"}, {"id": 10646, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jira-1", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 107, "taxonomy": "skills", "text": "UML", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "uml", "text_vi": "UML", "text_en": "UML", "sort_order": 0, "description": "UML"}, {"id": 108, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "laravel", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 109, "taxonomy": "skills", "text": "FuelPHP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "fuelphp", "text_vi": "FuelPHP", "text_en": "FuelPHP", "sort_order": 0, "description": "FuelPHP"}, {"id": 1099, "taxonomy": "skills", "text": "Business Intelligence", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "business-intelligence", "text_vi": "Business Intelligence", "text_en": "Business Intelligence", "sort_order": 0, "description": "Business Intelligence"}, {"id": 11, "taxonomy": "skills", "text": "OOP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oop", "text_vi": "OOP", "text_en": "OOP", "sort_order": 0, "description": "OOP"}, {"id": 110, "taxonomy": "skills", "text": "FlightPHP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "flightphp", "text_vi": "FlightPHP", "text_en": "FlightPHP", "sort_order": 0, "description": "FlightPHP"}, {"id": 1105, "taxonomy": "skills", "text": "Lifecycle (sdlc)", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lifecycle-sdlc", "text_vi": "Lifecycle (sdlc)", "text_en": "Lifecycle (sdlc)", "sort_order": 0, "description": "Lifecycle (sdlc)"}, {"id": 111, "taxonomy": "skills", "text": "Symfony", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "symfony", "text_vi": "Symfony", "text_en": "Symfony", "sort_order": 0, "description": "Symfony"}, {"id": 1113, "taxonomy": "skills", "text": "CRM (sap", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "crm-sap", "text_vi": "CRM (sap", "text_en": "CRM (sap", "sort_order": 0, "description": "CRM (sap"}, {"id": 112, "taxonomy": "skills", "text": "CodeIgniter", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "codeigniter", "text_vi": "CodeIgniter", "text_en": "CodeIgniter", "sort_order": 0, "description": "CodeIgniter"}, {"id": 113, "taxonomy": "skills", "text": "PhalconPHP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "phalconphp", "text_vi": "PhalconPHP", "text_en": "PhalconPHP", "sort_order": 0, "description": "PhalconPHP"}, {"id": 114, "taxonomy": "skills", "text": "PopPHP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "popphp", "text_vi": "PopPHP", "text_en": "PopPHP", "sort_order": 0, "description": "PopPHP"}, {"id": 115, "taxonomy": "skills", "text": "P<PERSON><PERSON>ie", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "php<PERSON>ie", "text_vi": "P<PERSON><PERSON>ie", "text_en": "P<PERSON><PERSON>ie", "sort_order": 0, "description": "P<PERSON><PERSON>ie"}, {"id": 117, "taxonomy": "skills", "text": "Nette", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nette", "text_vi": "Nette", "text_en": "Nette", "sort_order": 0, "description": "Nette"}, {"id": 119, "taxonomy": "skills", "text": "Ajax", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ajax", "text_vi": "Ajax", "text_en": "Ajax", "sort_order": 0, "description": "Ajax"}, {"id": 1192, "taxonomy": "skills", "text": "Gulp", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "gulp", "text_vi": "Gulp", "text_en": "Gulp", "sort_order": 0, "description": "Gulp"}, {"id": 1193, "taxonomy": "skills", "text": "LESS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "less", "text_vi": "LESS", "text_en": "LESS", "sort_order": 0, "description": "LESS"}, {"id": 12, "taxonomy": "skills", "text": "Unity", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "unity", "text_vi": "Unity", "text_en": "Unity", "sort_order": 0, "description": "Unity"}, {"id": 120, "taxonomy": "skills", "text": "ActionScript", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "actionscript", "text_vi": "ActionScript", "text_en": "ActionScript", "sort_order": 0, "description": "ActionScript"}, {"id": 122, "taxonomy": "skills", "text": "Photoshop", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "photoshop", "text_vi": "Photoshop", "text_en": "Photoshop", "sort_order": 0, "description": "Photoshop"}, {"id": 123, "taxonomy": "skills", "text": "Illustrator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "illustrator", "text_vi": "Illustrator", "text_en": "Illustrator", "sort_order": 0, "description": "Illustrator"}, {"id": 124, "taxonomy": "skills", "text": "Sketch", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sketch", "text_vi": "Sketch", "text_en": "Sketch", "sort_order": 0, "description": "Sketch"}, {"id": 125, "taxonomy": "skills", "text": "Microsoft Access", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "microsoft-access", "text_vi": "Microsoft Access", "text_en": "Microsoft Access", "sort_order": 0, "description": "Microsoft Access"}, {"id": 126, "taxonomy": "skills", "text": "3D Artist", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "3d-artist", "text_vi": "3D Artist", "text_en": "3D Artist", "sort_order": 0, "description": "3D Artist"}, {"id": 127, "taxonomy": "skills", "text": "Objective-C", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "objective-c", "text_vi": "Objective-C", "text_en": "Objective-C", "sort_order": 0, "description": "Objective-C"}, {"id": 129, "taxonomy": "skills", "text": "MariaDB", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ma<PERSON>b", "text_vi": "MariaDB", "text_en": "MariaDB", "sort_order": 0, "description": "MariaDB"}, {"id": 1295, "taxonomy": "skills", "text": "Data Science", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-science", "text_vi": "Data Science", "text_en": "Data Science", "sort_order": 0, "description": "Data Science"}, {"id": 1296, "taxonomy": "skills", "text": "MeteorJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "meteorjs", "text_vi": "MeteorJS", "text_en": "MeteorJS", "sort_order": 0, "description": "MeteorJS"}, {"id": 1297, "taxonomy": "skills", "text": "VueJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "v<PERSON><PERSON><PERSON>", "text_vi": "VueJS", "text_en": "VueJS", "sort_order": 0, "description": "VueJS"}, {"id": 1298, "taxonomy": "skills", "text": "Cloud", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cloud", "text_vi": "Cloud", "text_en": "Cloud", "sort_order": 0, "description": "Cloud"}, {"id": 1299, "taxonomy": "skills", "text": "Sitecore", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sitecore", "text_vi": "Sitecore", "text_en": "Sitecore", "sort_order": 0, "description": "Sitecore"}, {"id": 13, "taxonomy": "skills", "text": "AngularJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "<PERSON><PERSON>s", "text_vi": "AngularJS", "text_en": "AngularJS", "sort_order": 0, "description": "AngularJS"}, {"id": 130, "taxonomy": "skills", "text": " PostgreSQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "postgresql", "text_vi": " PostgreSQL", "text_en": " PostgreSQL", "sort_order": 0, "description": " PostgreSQL"}, {"id": 1300, "taxonomy": "skills", "text": "Redux", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "redux", "text_vi": "Redux", "text_en": "Redux", "sort_order": 0, "description": "Redux"}, {"id": 1302, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "struts", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 1303, "taxonomy": "skills", "text": "Ansible", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ansible", "text_vi": "Ansible", "text_en": "Ansible", "sort_order": 0, "description": "Ansible"}, {"id": 1304, "taxonomy": "skills", "text": "SDN", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sdn", "text_vi": "SDN", "text_en": "SDN", "sort_order": 0, "description": "SDN"}, {"id": 131, "taxonomy": "skills", "text": "SVN", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "svn", "text_vi": "SVN", "text_en": "SVN", "sort_order": 0, "description": "SVN"}, {"id": 1315, "taxonomy": "skills", "text": "Unit Testing", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "unit-testing", "text_vi": "Unit Testing", "text_en": "Unit Testing", "sort_order": 0, "description": "Unit Testing"}, {"id": 132, "taxonomy": "skills", "text": "Git", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "git", "text_vi": "Git", "text_en": "Git", "sort_order": 0, "description": "Git"}, {"id": 133, "taxonomy": "skills", "text": "NoSQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nosql", "text_vi": "NoSQL", "text_en": "NoSQL", "sort_order": 0, "description": "NoSQL"}, {"id": 1332, "taxonomy": "skills", "text": "Performance Testing", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "performance-testing", "text_vi": "Performance Testing", "text_en": "Performance Testing", "sort_order": 0, "description": "Performance Testing"}, {"id": 134, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nginx", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 135, "taxonomy": "skills", "text": " Squid", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "squid", "text_vi": " Squid", "text_en": " Squid", "sort_order": 0, "description": " Squid"}, {"id": 136, "taxonomy": "skills", "text": " Memca<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "memcached", "text_vi": " Memca<PERSON>", "text_en": " Memca<PERSON>", "sort_order": 0, "description": " Memca<PERSON>"}, {"id": 137, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nagios", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 14, "taxonomy": "skills", "text": "Manager", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "manager", "text_vi": "Manager", "text_en": "Manager", "sort_order": 0, "description": "Manager"}, {"id": 140, "taxonomy": "skills", "text": "GlusterFS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "glusterfs", "text_vi": "GlusterFS", "text_en": "GlusterFS", "sort_order": 0, "description": "GlusterFS"}, {"id": 1405, "taxonomy": "skills", "text": "PMO", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "pmo", "text_vi": "PMO", "text_en": "PMO", "sort_order": 0, "description": "PMO"}, {"id": 1406, "taxonomy": "skills", "text": "French", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "french", "text_vi": "French", "text_en": "French", "sort_order": 0, "description": "French"}, {"id": 1410, "taxonomy": "skills", "text": "Technical Support", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "technical-support", "text_vi": "Technical Support", "text_en": "Technical Support", "sort_order": 0, "description": "Technical Support"}, {"id": 1414, "taxonomy": "skills", "text": "API", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "api", "text_vi": "API", "text_en": "API", "sort_order": 0, "description": "API"}, {"id": 1415, "taxonomy": "skills", "text": "NLP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nlp", "text_vi": "NLP", "text_en": "NLP", "sort_order": 0, "description": "NLP"}, {"id": 1416, "taxonomy": "skills", "text": "Blockchain", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "blockchain", "text_vi": "Blockchain", "text_en": "Blockchain", "sort_order": 0, "description": "Blockchain"}, {"id": 1418, "taxonomy": "skills", "text": "JUnit", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "junit", "text_vi": "JUnit", "text_en": "JUnit", "sort_order": 0, "description": "JUnit"}, {"id": 1421, "taxonomy": "skills", "text": "Angular 2", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-2", "text_vi": "Angular 2", "text_en": "Angular 2", "sort_order": 0, "description": "Angular 2"}, {"id": 144, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "perl", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 145, "taxonomy": "skills", "text": "Indesign", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "indesign", "text_vi": "Indesign", "text_en": "Indesign", "sort_order": 0, "description": "Indesign"}, {"id": 1452, "taxonomy": "skills", "text": "Product Owner", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "product-owner", "text_vi": "Product Owner", "text_en": "Product Owner", "sort_order": 0, "description": "Product Owner"}, {"id": 1456, "taxonomy": "skills", "text": "Business Analyst", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "business-analyst", "text_vi": "Business Analyst", "text_en": "Business Analyst", "sort_order": 0, "description": "Business Analyst"}, {"id": 1461, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "kotlin", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 1463, "taxonomy": "skills", "text": "PL/SQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "plsql", "text_vi": "PL/SQL", "text_en": "PL/SQL", "sort_order": 0, "description": "PL/SQL"}, {"id": 1464, "taxonomy": "skills", "text": "Data Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-engineer", "text_vi": "Data Engineer", "text_en": "Data Engineer", "sort_order": 0, "description": "Data Engineer"}, {"id": 1465, "taxonomy": "skills", "text": "Database Administrator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "database-administrator", "text_vi": "Database Administrator", "text_en": "Database Administrator", "sort_order": 0, "description": "Database Administrator"}, {"id": 1466, "taxonomy": "skills", "text": "Network", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "network", "text_vi": "Network", "text_en": "Network", "sort_order": 0, "description": ""}, {"id": 1467, "taxonomy": "skills", "text": "Windows", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "windows", "text_vi": "Windows", "text_en": "Windows", "sort_order": 0, "description": ""}, {"id": 1468, "taxonomy": "skills", "text": "R", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "r", "text_vi": "R", "text_en": "R", "sort_order": 0, "description": "R"}, {"id": 1469, "taxonomy": "skills", "text": "C", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "c", "text_vi": "C", "text_en": "C", "sort_order": 0, "description": "C"}, {"id": 1470, "taxonomy": "skills", "text": "Automation Tester", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "automation-tester", "text_vi": "Automation Tester", "text_en": "Automation Tester", "sort_order": 0, "description": "Automation Tester"}, {"id": 1472, "taxonomy": "skills", "text": "R&D", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rd", "text_vi": "R&D", "text_en": "R&D", "sort_order": 0, "description": "R&D"}, {"id": 1474, "taxonomy": "skills", "text": "Magento2", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "magento2", "text_vi": "Magento2", "text_en": "Magento2", "sort_order": 0, "description": "Magento2"}, {"id": 1477, "taxonomy": "skills", "text": "Salesforce", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "salesforce", "text_vi": "Salesforce", "text_en": "Salesforce", "sort_order": 0, "description": "Salesforce"}, {"id": 1478, "taxonomy": "skills", "text": "Data Mining", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-mining", "text_vi": "Data Mining", "text_en": "Data Mining", "sort_order": 0, "description": "Data Mining"}, {"id": 1480, "taxonomy": "skills", "text": "Angular", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular", "text_vi": "Angular", "text_en": "Angular", "sort_order": 0, "description": "Angular"}, {"id": 1481, "taxonomy": "skills", "text": "Laravel 5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "laravel-5", "text_vi": "Laravel 5", "text_en": "Laravel 5", "sort_order": 0, "description": "Laravel 5"}, {"id": 1483, "taxonomy": "skills", "text": "Angular 5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-5", "text_vi": "Angular 5", "text_en": "Angular 5", "sort_order": 0, "description": "Angular 5"}, {"id": 1485, "taxonomy": "skills", "text": "Angular 4", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-4", "text_vi": "Angular 4", "text_en": "Angular 4", "sort_order": 0, "description": "Angular 4"}, {"id": 1489, "taxonomy": "skills", "text": "Azure", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "azure", "text_vi": "Azure", "text_en": "Azure", "sort_order": 0, "description": "Azure"}, {"id": 1493, "taxonomy": "skills", "text": "WebForm", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "webform", "text_vi": "WebForm", "text_en": "WebForm", "sort_order": 0, "description": "WebForm"}, {"id": 1497, "taxonomy": "skills", "text": "SASS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sass", "text_vi": "SASS", "text_en": "SASS", "sort_order": 0, "description": "SASS"}, {"id": 1498, "taxonomy": "skills", "text": "Angular 6", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-6", "text_vi": "Angular 6", "text_en": "Angular 6", "sort_order": 0, "description": "Angular 6"}, {"id": 15, "taxonomy": "skills", "text": "Oracle", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle", "text_vi": "Oracle", "text_en": "Oracle", "sort_order": 0, "description": "Oracle"}, {"id": 1503, "taxonomy": "skills", "text": "Software Architect", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "software-architect", "text_vi": "Software Architect", "text_en": "Software Architect", "sort_order": 0, "description": "Software Architect"}, {"id": 1504, "taxonomy": "skills", "text": "System Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "system-engineer", "text_vi": "System Engineer", "text_en": "System Engineer", "sort_order": 0, "description": "System Engineer"}, {"id": 1506, "taxonomy": "skills", "text": "Hybrid App", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hybrid-app", "text_vi": "Hybrid App", "text_en": "Hybrid App", "sort_order": 0, "description": "Hybrid App"}, {"id": 1507, "taxonomy": "skills", "text": "2D Artist", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "2d-artist", "text_vi": "2D Artist", "text_en": "2D Artist", "sort_order": 0, "description": ""}, {"id": 1508, "taxonomy": "skills", "text": "NOC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "noc", "text_vi": "NOC", "text_en": "NOC", "sort_order": 0, "description": "NOC"}, {"id": 1510, "taxonomy": "skills", "text": ".NET Core", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "net-core", "text_vi": ".NET Core", "text_en": ".NET Core", "sort_order": 0, "description": ".NET Core"}, {"id": 1515, "taxonomy": "skills", "text": "MyBatis", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mybatis", "text_vi": "MyBatis", "text_en": "MyBatis", "sort_order": 0, "description": "MyBatis"}, {"id": 1518, "taxonomy": "skills", "text": "C Language", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "c-language", "text_vi": "C Language", "text_en": "C Language", "sort_order": 0, "description": "C Language"}, {"id": 1519, "taxonomy": "skills", "text": "Technical Architect", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "technical-architecture", "text_vi": "Technical Architect", "text_en": "Technical Architect", "sort_order": 0, "description": "Technical Architect"}, {"id": 1529, "taxonomy": "skills", "text": "Oracle Database", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-database", "text_vi": "Oracle Database", "text_en": "Oracle Database", "sort_order": 0, "description": "Oracle Database"}, {"id": 1545, "taxonomy": "skills", "text": "Yii2", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "yii2", "text_vi": "Yii2", "text_en": "Yii2", "sort_order": 0, "description": "Yii2"}, {"id": 1546, "taxonomy": "skills", "text": "Flutter", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "flutter", "text_vi": "Flutter", "text_en": "Flutter", "sort_order": 0, "description": ""}, {"id": 1547, "taxonomy": "skills", "text": "Waterfall", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "waterfall", "text_vi": "Waterfall", "text_en": "Waterfall", "sort_order": 0, "description": "Waterfall"}, {"id": 155, "taxonomy": "skills", "text": "Web/ Mobile", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web-mobile", "text_vi": "Web/ Mobile", "text_en": "Web/ Mobile", "sort_order": 0, "description": "Web/ Mobile"}, {"id": 1552, "taxonomy": "skills", "text": "Java Core", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "java-core", "text_vi": "Java Core", "text_en": "Java Core", "sort_order": 0, "description": "Java Core"}, {"id": 1553, "taxonomy": "skills", "text": "IT Manager", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-manager", "text_vi": "IT Manager", "text_en": "IT Manager", "sort_order": 0, "description": "IT Manager"}, {"id": 1555, "taxonomy": "skills", "text": "AI", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ai", "text_vi": "AI", "text_en": "AI", "sort_order": 0, "description": "AI"}, {"id": 1556, "taxonomy": "skills", "text": "IT Comtor", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-comtor", "text_vi": "IT Comtor", "text_en": "IT Comtor", "sort_order": 0, "description": ""}, {"id": 1559, "taxonomy": "skills", "text": "SOAP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "soap", "text_vi": "SOAP", "text_en": "SOAP", "sort_order": 0, "description": "SOAP"}, {"id": 156, "taxonomy": "skills", "text": "UX Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ux-design", "text_vi": "UX Design", "text_en": "UX Design", "sort_order": 0, "description": "UX Design"}, {"id": 1562, "taxonomy": "skills", "text": "Technical Writer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "technical-writer", "text_vi": "Technical Writer", "text_en": "Technical Writer", "sort_order": 0, "description": "Technical Writer"}, {"id": 1565, "taxonomy": "skills", "text": "DMS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "dms", "text_vi": "DMS", "text_en": "DMS", "sort_order": 0, "description": "DMS"}, {"id": 1566, "taxonomy": "skills", "text": "Shopify", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "shopify", "text_vi": "Shopify", "text_en": "Shopify", "sort_order": 0, "description": ""}, {"id": 1571, "taxonomy": "skills", "text": "Automation QA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "automation-qa", "text_vi": "Automation QA", "text_en": "Automation QA", "sort_order": 0, "description": "Automation QA"}, {"id": 1572, "taxonomy": "skills", "text": "Selenium", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "selenium", "text_vi": "Selenium", "text_en": "Selenium", "sort_order": 0, "description": "Selenium"}, {"id": 1573, "taxonomy": "skills", "text": "Video Stream", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "video-stream", "text_vi": "Video Stream", "text_en": "Video Stream", "sort_order": 0, "description": "Video Stream"}, {"id": 1578, "taxonomy": "skills", "text": "TypeScript", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "typescript", "text_vi": "TypeScript", "text_en": "TypeScript", "sort_order": 0, "description": "TypeScript"}, {"id": 1580, "taxonomy": "skills", "text": "FX", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "fx", "text_vi": "FX", "text_en": "FX", "sort_order": 0, "description": "FX"}, {"id": 1586, "taxonomy": "skills", "text": "Core Banking", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "core-banking", "text_vi": "Core Banking", "text_en": "Core Banking", "sort_order": 0, "description": ""}, {"id": 1587, "taxonomy": "skills", "text": "CI/CD", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cicd", "text_vi": "CI/CD", "text_en": "CI/CD", "sort_order": 0, "description": ""}, {"id": 1597, "taxonomy": "skills", "text": "MAC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mac", "text_vi": "MAC", "text_en": "MAC", "sort_order": 0, "description": ""}, {"id": 1598, "taxonomy": "skills", "text": "Product Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "product-design", "text_vi": "Product Design", "text_en": "Product Design", "sort_order": 0, "description": ""}, {"id": 17, "taxonomy": "skills", "text": "ASP.NET", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "aspnet", "text_vi": "ASP.NET", "text_en": "ASP.NET", "sort_order": 0, "description": "ASP.NET"}, {"id": 18, "taxonomy": "skills", "text": "Django", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "django", "text_vi": "Django", "text_en": "Django", "sort_order": 0, "description": "Django"}, {"id": 19, "taxonomy": "skills", "text": "C++", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "c-plus-plus", "text_vi": "C++", "text_en": "C++", "sort_order": 0, "description": "C++"}, {"id": 194, "taxonomy": "skills", "text": "3D Max", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "3d-max", "text_vi": "3D Max", "text_en": "3D Max", "sort_order": 0, "description": "3D Max"}, {"id": 195, "taxonomy": "skills", "text": "Maya", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "maya", "text_vi": "Maya", "text_en": "Maya", "sort_order": 0, "description": "Maya"}, {"id": 196, "taxonomy": "skills", "text": "CorelDRAW", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "<PERSON><PERSON><PERSON>", "text_vi": "CorelDRAW", "text_en": "CorelDRAW", "sort_order": 0, "description": "CorelDRAW"}, {"id": 197, "taxonomy": "skills", "text": "AutoCAD", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "autocad", "text_vi": "AutoCAD", "text_en": "AutoCAD", "sort_order": 0, "description": "AutoCAD"}, {"id": 199, "taxonomy": "skills", "text": "Game Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-design", "text_vi": "Game Design", "text_en": "Game Design", "sort_order": 0, "description": "Game Design"}, {"id": 2, "taxonomy": "skills", "text": "Agile", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "agile", "text_vi": "Agile", "text_en": "Agile", "sort_order": 0, "description": "Agile"}, {"id": 20, "taxonomy": "skills", "text": "J2EE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "j2ee", "text_vi": "J2EE", "text_en": "J2EE", "sort_order": 0, "description": "J2EE"}, {"id": 200, "taxonomy": "skills", "text": "Graphic Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "graphic-design", "text_vi": "Graphic Design", "text_en": "Graphic Design", "sort_order": 0, "description": "Graphic Design"}, {"id": 201, "taxonomy": "skills", "text": "Game Master", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-master", "text_vi": "Game Master", "text_en": "Game Master", "sort_order": 0, "description": "Game Master"}, {"id": 202, "taxonomy": "skills", "text": "Data Analytics", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-analytics", "text_vi": "Data Analytics", "text_en": "Data Analytics", "sort_order": 0, "description": "Data Analytics"}, {"id": 203, "taxonomy": "skills", "text": "Golang", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "golang", "text_vi": "Golang", "text_en": "Golang", "sort_order": 0, "description": "Golang"}, {"id": 204, "taxonomy": "skills", "text": "UI Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ui-design", "text_vi": "UI Design", "text_en": "UI Design", "sort_order": 0, "description": "UI Design"}, {"id": 205, "taxonomy": "skills", "text": "Retouch", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "retouch", "text_vi": "Retouch", "text_en": "Retouch", "sort_order": 0, "description": "Retouch"}, {"id": 206, "taxonomy": "skills", "text": "<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ruby", "text_vi": "<PERSON>", "text_en": "<PERSON>", "sort_order": 0, "description": "<PERSON>"}, {"id": 207, "taxonomy": "skills", "text": "Mobile", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mobile", "text_vi": "Mobile", "text_en": "Mobile", "sort_order": 0, "description": "Mobile"}, {"id": 208, "taxonomy": "skills", "text": "MEAN", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mean", "text_vi": "MEAN", "text_en": "MEAN", "sort_order": 0, "description": "MEAN"}, {"id": 209, "taxonomy": "skills", "text": "Front-End", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "front-end", "text_vi": "Front-End", "text_en": "Front-End", "sort_order": 0, "description": "Front-End"}, {"id": 21, "taxonomy": "skills", "text": "Java", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "java", "text_vi": "Java", "text_en": "Java", "sort_order": 0, "description": "Java"}, {"id": 210, "taxonomy": "skills", "text": "Back-End", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "back-end", "text_vi": "Back-End", "text_en": "Back-End", "sort_order": 0, "description": "Back-End"}, {"id": 211, "taxonomy": "skills", "text": "Full-Stack", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "full-stack", "text_vi": "Full-Stack", "text_en": "Full-Stack", "sort_order": 0, "description": "Full-Stack"}, {"id": 213, "taxonomy": "skills", "text": "DevOps", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "devops", "text_vi": "DevOps", "text_en": "DevOps", "sort_order": 0, "description": "DevOps"}, {"id": 214, "taxonomy": "skills", "text": "ReactJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "reactjs", "text_vi": "ReactJS", "text_en": "ReactJS", "sort_order": 0, "description": "ReactJS"}, {"id": 215, "taxonomy": "skills", "text": "xCode", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "xcode", "text_vi": "xCode", "text_en": "xCode", "sort_order": 0, "description": "xCode"}, {"id": 216, "taxonomy": "skills", "text": "CSS3", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "css3", "text_vi": "CSS3", "text_en": "CSS3", "sort_order": 0, "description": "CSS3"}, {"id": 217, "taxonomy": "skills", "text": "Visual Studio", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "visual-studio", "text_vi": "Visual Studio", "text_en": "Visual Studio", "sort_order": 0, "description": "Visual Studio"}, {"id": 219, "taxonomy": "skills", "text": "Product", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "product", "text_vi": "Product", "text_en": "Product", "sort_order": 0, "description": "Product"}, {"id": 22, "taxonomy": "skills", "text": "JavaScript", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "javascript", "text_vi": "JavaScript", "text_en": "JavaScript", "sort_order": 0, "description": "JavaScript"}, {"id": 220, "taxonomy": "skills", "text": "Cocoa", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cocoa", "text_vi": "Cocoa", "text_en": "Cocoa", "sort_order": 0, "description": "Cocoa"}, {"id": 221, "taxonomy": "skills", "text": "Cocos2D-x", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cocos2d-x", "text_vi": "Cocos2D-x", "text_en": "Cocos2D-x", "sort_order": 0, "description": "Cocos2D-x"}, {"id": 222, "taxonomy": "skills", "text": "ExpressJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "expressjs", "text_vi": "ExpressJS", "text_en": "ExpressJS", "sort_order": 0, "description": "ExpressJS"}, {"id": 223, "taxonomy": "skills", "text": "Web", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web", "text_vi": "Web", "text_en": "Web", "sort_order": 0, "description": "Web"}, {"id": 224, "taxonomy": "skills", "text": "Server", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "server", "text_vi": "Server", "text_en": "Server", "sort_order": 0, "description": "Server"}, {"id": 225, "taxonomy": "skills", "text": "Scala", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "scala", "text_vi": "Scala", "text_en": "Scala", "sort_order": 0, "description": ""}, {"id": 226, "taxonomy": "skills", "text": "VB.NET", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "vbnet", "text_vi": "VB.NET", "text_en": "VB.NET", "sort_order": 0, "description": "VB.NET"}, {"id": 227, "taxonomy": "skills", "text": "Big Data", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "big-data", "text_vi": "Big Data", "text_en": "Big Data", "sort_order": 0, "description": "Big Data"}, {"id": 228, "taxonomy": "skills", "text": "Design Patterns", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "design-patterns", "text_vi": "Design Patterns", "text_en": "Design Patterns", "sort_order": 0, "description": "Design Patterns"}, {"id": 23, "taxonomy": "skills", "text": "Visual Basic", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "visual-basic", "text_vi": "Visual Basic", "text_en": "Visual Basic", "sort_order": 0, "description": "Visual Basic"}, {"id": 230, "taxonomy": "skills", "text": "<PERSON>er", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "docker", "text_vi": "<PERSON>er", "text_en": "<PERSON>er", "sort_order": 0, "description": "<PERSON>er"}, {"id": 231, "taxonomy": "skills", "text": "React Native", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "react-native", "text_vi": "React Native", "text_en": "React Native", "sort_order": 0, "description": "React Native"}, {"id": 233, "taxonomy": "skills", "text": "WebRTC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "webrtc", "text_vi": "WebRTC", "text_en": "WebRTC", "sort_order": 0, "description": "WebRTC"}, {"id": 234, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ionic", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 236, "taxonomy": "skills", "text": "Web Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web-design", "text_vi": "Web Design", "text_en": "Web Design", "sort_order": 0, "description": "Web Design"}, {"id": 237, "taxonomy": "skills", "text": "Motion Graphic Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "motion-graphic-design", "text_vi": "Motion Graphic Design", "text_en": "Motion Graphic Design", "sort_order": 0, "description": ""}, {"id": 24, "taxonomy": "skills", "text": "C#", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "c-sharp", "text_vi": "C#", "text_en": "C#", "sort_order": 0, "description": "C#"}, {"id": 240, "taxonomy": "skills", "text": "Winform", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "winform", "text_vi": "Winform", "text_en": "Winform", "sort_order": 0, "description": "Winform"}, {"id": 241, "taxonomy": "skills", "text": "BackboneJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "<PERSON><PERSON><PERSON>", "text_vi": "BackboneJS", "text_en": "BackboneJS", "sort_order": 0, "description": "BackboneJS"}, {"id": 242, "taxonomy": "skills", "text": "KnockOut", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "knockout", "text_vi": "KnockOut", "text_en": "KnockOut", "sort_order": 0, "description": "KnockOut"}, {"id": 243, "taxonomy": "skills", "text": "After Effect", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "after-effect", "text_vi": "After Effect", "text_en": "After Effect", "sort_order": 0, "description": "After Effect"}, {"id": 244, "taxonomy": "skills", "text": "Hibernate", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hibernate", "text_vi": "Hibernate", "text_en": "Hibernate", "sort_order": 0, "description": "Hibernate"}, {"id": 245, "taxonomy": "skills", "text": "Spring", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "spring", "text_vi": "Spring", "text_en": "Spring", "sort_order": 0, "description": "Spring"}, {"id": 246, "taxonomy": "skills", "text": "Japanese - N5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "japanese-n5", "text_vi": "Japanese - N5", "text_en": "Japanese - N5", "sort_order": 0, "description": "Japanese - N5"}, {"id": 247, "taxonomy": "skills", "text": "Japanese - N4", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "japanese-n4", "text_vi": "Japanese - N4", "text_en": "Japanese - N4", "sort_order": 0, "description": "Japanese - N4"}, {"id": 248, "taxonomy": "skills", "text": "Japanese - N3", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "japanese-n3", "text_vi": "Japanese - N3", "text_en": "Japanese - N3", "sort_order": 0, "description": "Japanese - N3"}, {"id": 249, "taxonomy": "skills", "text": "Japanese - N2", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "japanese-n2", "text_vi": "Japanese - N2", "text_en": "Japanese - N2", "sort_order": 0, "description": "Japanese - N2"}, {"id": 25, "taxonomy": "skills", "text": "Ruby on Rails", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ruby-on-rails", "text_vi": "Ruby on Rails", "text_en": "Ruby on Rails", "sort_order": 0, "description": "Ruby on Rails"}, {"id": 250, "taxonomy": "skills", "text": "Japanese", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "japanese", "text_vi": "Japanese", "text_en": "Japanese", "sort_order": 0, "description": "Japanese"}, {"id": 251, "taxonomy": "skills", "text": "Database", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "database", "text_vi": "Database", "text_en": "Database", "sort_order": 0, "description": "Database"}, {"id": 254, "taxonomy": "skills", "text": "Japanese - N1", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "japanese-n1", "text_vi": "Japanese - N1", "text_en": "Japanese - N1", "sort_order": 0, "description": "Japanese - N1"}, {"id": 26, "taxonomy": "skills", "text": "MVC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mvc", "text_vi": "MVC", "text_en": "MVC", "sort_order": 0, "description": "MVC"}, {"id": 268, "taxonomy": "skills", "text": "Bridge SE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bridge-se", "text_vi": "Bridge SE", "text_en": "Bridge SE", "sort_order": 0, "description": "Bridge SE"}, {"id": 271, "taxonomy": "skills", "text": "BSE Leader", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bse-leader", "text_vi": "BSE Leader", "text_en": "BSE Leader", "sort_order": 0, "description": "BSE Leader"}, {"id": 273, "taxonomy": "skills", "text": "Fintech", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "fintech", "text_vi": "Fintech", "text_en": "Fintech", "sort_order": 0, "description": "Fintech"}, {"id": 28, "taxonomy": "skills", "text": "SQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sql", "text_vi": "SQL", "text_en": "SQL", "sort_order": 0, "description": "SQL"}, {"id": 280, "taxonomy": "skills", "text": "Programming Experience", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "programming-experience", "text_vi": "Programming Experience", "text_en": "Programming Experience", "sort_order": 0, "description": "Programming Experience"}, {"id": 283, "taxonomy": "skills", "text": "Openstack Development", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "openstack-development", "text_vi": "Openstack Development", "text_en": "Openstack Development", "sort_order": 0, "description": "Openstack Development"}, {"id": 287, "taxonomy": "skills", "text": "Japanese ( N1 Or N2)", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "japanese-n1-or-n2", "text_vi": "Japanese ( N1 Or N2)", "text_en": "Japanese ( N1 Or N2)", "sort_order": 0, "description": "Japanese ( N1 Or N2)"}, {"id": 29, "taxonomy": "skills", "text": "MySQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mysql", "text_vi": "MySQL", "text_en": "MySQL", "sort_order": 0, "description": "MySQL"}, {"id": 3, "taxonomy": "skills", "text": "CSS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "css", "text_vi": "CSS", "text_en": "CSS", "sort_order": 0, "description": "CSS"}, {"id": 30, "taxonomy": "skills", "text": "Product Manager", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "product-manager", "text_vi": "Product Manager", "text_en": "Product Manager", "sort_order": 0, "description": "Product Manager"}, {"id": 308, "taxonomy": "skills", "text": "Premier", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "premier", "text_vi": "Premier", "text_en": "Premier", "sort_order": 0, "description": "Premier"}, {"id": 309, "taxonomy": "skills", "text": "Autodesk", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "autodesk", "text_vi": "Autodesk", "text_en": "Autodesk", "sort_order": 0, "description": "Autodesk"}, {"id": 31, "taxonomy": "skills", "text": "System Admin", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "system-admin", "text_vi": "System Admin", "text_en": "System Admin", "sort_order": 0, "description": "System Admin"}, {"id": 310, "taxonomy": "skills", "text": "Lightroom", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lightroom", "text_vi": "Lightroom", "text_en": "Lightroom", "sort_order": 0, "description": "Lightroom"}, {"id": 311, "taxonomy": "skills", "text": "Photography", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "photography", "text_vi": "Photography", "text_en": "Photography", "sort_order": 0, "description": "Photography"}, {"id": 312, "taxonomy": "skills", "text": "Animation 2D", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "animation-2d", "text_vi": "Animation 2D", "text_en": "Animation 2D", "sort_order": 0, "description": "Animation 2D"}, {"id": 313, "taxonomy": "skills", "text": "Animation 3D", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "animation-3d", "text_vi": "Animation 3D", "text_en": "Animation 3D", "sort_order": 0, "description": "Animation 3D"}, {"id": 316, "taxonomy": "skills", "text": "Embedded System", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "embedded-system", "text_vi": "Embedded System", "text_en": "Embedded System", "sort_order": 0, "description": "Embedded System"}, {"id": 317, "taxonomy": "skills", "text": "Machine Learning", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "machine-learning", "text_vi": "Machine Learning", "text_en": "Machine Learning", "sort_order": 0, "description": "Machine Learning"}, {"id": 318, "taxonomy": "skills", "text": "IT Communicator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-communicator", "text_vi": "IT Communicator", "text_en": "IT Communicator", "sort_order": 0, "description": "IT Communicator"}, {"id": 32, "taxonomy": "skills", "text": "Project Manager", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "project-manager", "text_vi": "Project Manager", "text_en": "Project Manager", "sort_order": 0, "description": "Project Manager"}, {"id": 322, "taxonomy": "skills", "text": "Project Leader", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "project-leader", "text_vi": "Project Leader", "text_en": "Project Leader", "sort_order": 0, "description": "Project Leader"}, {"id": 327, "taxonomy": "skills", "text": "Web Developer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web-developer", "text_vi": "Web Developer", "text_en": "Web Developer", "sort_order": 0, "description": "Web Developer"}, {"id": 33, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "j<PERSON>y", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 34, "taxonomy": "skills", "text": "Python", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "python", "text_vi": "Python", "text_en": "Python", "sort_order": 0, "description": "Python"}, {"id": 345, "taxonomy": "skills", "text": "IT Support", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-support", "text_vi": "IT Support", "text_en": "IT Support", "sort_order": 0, "description": "IT Support"}, {"id": 346, "taxonomy": "skills", "text": "Technical Leader", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "technical-leader", "text_vi": "Technical Leader", "text_en": "Technical Leader", "sort_order": 0, "description": "Technical Leader"}, {"id": 347, "taxonomy": "skills", "text": "AWS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "aws", "text_vi": "AWS", "text_en": "AWS", "sort_order": 0, "description": "AWS"}, {"id": 35, "taxonomy": "skills", "text": "JSON", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "json", "text_vi": "JSON", "text_en": "JSON", "sort_order": 0, "description": "JSON"}, {"id": 353, "taxonomy": "skills", "text": "HTML & CSS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "html-css", "text_vi": "HTML & CSS", "text_en": "HTML & CSS", "sort_order": 0, "description": "HTML & CSS"}, {"id": 36, "taxonomy": "skills", "text": "NodeJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nodejs", "text_vi": "NodeJS", "text_en": "NodeJS", "sort_order": 0, "description": "NodeJS"}, {"id": 373, "taxonomy": "skills", "text": "Scrum", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "scrum", "text_vi": "Scrum", "text_en": "Scrum", "sort_order": 0, "description": "Scrum"}, {"id": 377, "taxonomy": "skills", "text": "Problem Solving Skill", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "problem-solving-skill", "text_vi": "Problem Solving Skill", "text_en": "Problem Solving Skill", "sort_order": 0, "description": "Problem Solving Skill"}, {"id": 38, "taxonomy": "skills", "text": "Game", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game", "text_vi": "Game", "text_en": "Game", "sort_order": 0, "description": "Game"}, {"id": 382, "taxonomy": "skills", "text": "Software Developer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "software-developer", "text_vi": "Software Developer", "text_en": "Software Developer", "sort_order": 0, "description": "Software Developer"}, {"id": 384, "taxonomy": "skills", "text": "Flash", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "flash", "text_vi": "Flash", "text_en": "Flash", "sort_order": 0, "description": "Flash"}, {"id": 385, "taxonomy": "skills", "text": "My SQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "my-sql", "text_vi": "My SQL", "text_en": "My SQL", "sort_order": 0, "description": "My SQL"}, {"id": 39, "taxonomy": "skills", "text": "Game Artist", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-artist", "text_vi": "Game Artist", "text_en": "Game Artist", "sort_order": 0, "description": "Game Artist"}, {"id": 391, "taxonomy": "skills", "text": "Agile & Scrum", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "agile-scrum", "text_vi": "Agile & Scrum", "text_en": "Agile & Scrum", "sort_order": 0, "description": "Agile & Scrum"}, {"id": 395, "taxonomy": "skills", "text": "C#/ Oop/ Net Framwork", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "c-oop-net-framwork", "text_vi": "C#/ Oop/ Net Framwork", "text_en": "C#/ Oop/ Net Framwork", "sort_order": 0, "description": "C#/ Oop/ Net Framwork"}, {"id": 4, "taxonomy": "skills", "text": "HTML5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "html5", "text_vi": "HTML5", "text_en": "HTML5", "sort_order": 0, "description": "HTML5"}, {"id": 401, "taxonomy": "skills", "text": "C/C++", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "c-c", "text_vi": "C/C++", "text_en": "C/C++", "sort_order": 0, "description": "C/C++"}, {"id": 404, "taxonomy": "skills", "text": "Cisco", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cisco", "text_vi": "Cisco", "text_en": "Cisco", "sort_order": 0, "description": "Cisco"}, {"id": 41, "taxonomy": "skills", "text": "Construct 2", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "construct", "text_vi": "Construct 2", "text_en": "Construct 2", "sort_order": 0, "description": "Construct 2"}, {"id": 415, "taxonomy": "skills", "text": "PC Devices", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "pc-devices", "text_vi": "PC Devices", "text_en": "PC Devices", "sort_order": 0, "description": "PC Devices"}, {"id": 422, "taxonomy": "skills", "text": "SAP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sap", "text_vi": "SAP", "text_en": "SAP", "sort_order": 0, "description": "SAP"}, {"id": 4268, "taxonomy": "skills", "text": "IT Service", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-service", "text_vi": "IT Service", "text_en": "IT Service", "sort_order": 0, "description": "IT Service"}, {"id": 4269, "taxonomy": "skills", "text": "IT Security", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-security", "text_vi": "IT Security", "text_en": "IT Security", "sort_order": 0, "description": "IT Security"}, {"id": 427, "taxonomy": "skills", "text": "Motivate Staffs", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "motivate-staffs", "text_vi": "Motivate Staffs", "text_en": "Motivate Staffs", "sort_order": 0, "description": "Motivate Staffs"}, {"id": 4272, "taxonomy": "skills", "text": "COBOL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cobol", "text_vi": "COBOL", "text_en": "COBOL", "sort_order": 0, "description": "COBOL"}, {"id": 4297, "taxonomy": "skills", "text": "ASP.NET Core", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "aspnet-core", "text_vi": "ASP.NET Core", "text_en": "ASP.NET Core", "sort_order": 0, "description": "ASP.NET Core"}, {"id": 4298, "taxonomy": "skills", "text": "Entity", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "entity", "text_vi": "Entity", "text_en": "Entity", "sort_order": 0, "description": "Entity"}, {"id": 431, "taxonomy": "skills", "text": "Web Application", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web-application", "text_vi": "Web Application", "text_en": "Web Application", "sort_order": 0, "description": "Web Application"}, {"id": 4317, "taxonomy": "skills", "text": "Spring Boot", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "spring-boot", "text_vi": "Spring Boot", "text_en": "Spring Boot", "sort_order": 0, "description": "Spring Boot"}, {"id": 432, "taxonomy": "skills", "text": "S3", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "s3", "text_vi": "S3", "text_en": "S3", "sort_order": 0, "description": "S3"}, {"id": 4322, "taxonomy": "skills", "text": "Iot", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "iot", "text_vi": "Iot", "text_en": "Iot", "sort_order": 0, "description": "Iot"}, {"id": 4348, "taxonomy": "skills", "text": "SSIS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ssis", "text_vi": "SSIS", "text_en": "SSIS", "sort_order": 0, "description": "SSIS"}, {"id": 4367, "taxonomy": "skills", "text": "NextJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nextjs", "text_vi": "NextJS", "text_en": "NextJS", "sort_order": 0, "description": "NextJS"}, {"id": 4396, "taxonomy": "skills", "text": "flask", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "flask", "text_vi": "flask", "text_en": "flask", "sort_order": 0, "description": "flask"}, {"id": 442, "taxonomy": "skills", "text": "Json/xml", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jsonxml", "text_vi": "Json/xml", "text_en": "Json/xml", "sort_order": 0, "description": "Json/xml"}, {"id": 4423, "taxonomy": "skills", "text": "OSP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "osp", "text_vi": "OSP", "text_en": "OSP", "sort_order": 0, "description": "OSP"}, {"id": 443, "taxonomy": "skills", "text": "Test Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "test-design", "text_vi": "Test Design", "text_en": "Test Design", "sort_order": 0, "description": "Test Design"}, {"id": 448, "taxonomy": "skills", "text": "Programming Language", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "programming-language", "text_vi": "Programming Language", "text_en": "Programming Language", "sort_order": 0, "description": "Programming Language"}, {"id": 456, "taxonomy": "skills", "text": "Data Structure & Algorithm", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-structure-algorithm", "text_vi": "Data Structure & Algorithm", "text_en": "Data Structure & Algorithm", "sort_order": 0, "description": "Data Structure & Algorithm"}, {"id": 46, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "g<PERSON>os", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 460, "taxonomy": "skills", "text": "English - Good", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "english-good", "text_vi": "English - Good", "text_en": "English - Good", "sort_order": 0, "description": "English - Good"}, {"id": 47, "taxonomy": "skills", "text": "LiveCode", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "livecode", "text_vi": "LiveCode", "text_en": "LiveCode", "sort_order": 0, "description": "LiveCode"}, {"id": 4730, "taxonomy": "skills", "text": "VR/AR", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "vrar", "text_vi": "VR/AR", "text_en": "VR/AR", "sort_order": 0, "description": "VR/AR"}, {"id": 4739, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "keras", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 4740, "taxonomy": "skills", "text": "PyTorch", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "pytorch", "text_vi": "PyTorch", "text_en": "PyTorch", "sort_order": 0, "description": "PyTorch"}, {"id": 4788, "taxonomy": "skills", "text": "Kubernetes", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "kubernetes", "text_vi": "Kubernetes", "text_en": "Kubernetes", "sort_order": 0, "description": "Kubernetes"}, {"id": 48, "taxonomy": "skills", "text": "Game Editor", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-editor", "text_vi": "Game Editor", "text_en": "Game Editor", "sort_order": 0, "description": "Game Editor"}, {"id": 4810, "taxonomy": "skills", "text": "Angular 7", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-7", "text_vi": "Angular 7", "text_en": "Angular 7", "sort_order": 0, "description": "Angular 7"}, {"id": 4842, "taxonomy": "skills", "text": "Angular 2+", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-2", "text_vi": "Angular 2+", "text_en": "Angular 2+", "sort_order": 0, "description": "Angular 2+"}, {"id": 49, "taxonomy": "skills", "text": "Edgelib", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "edgelib", "text_vi": "Edgelib", "text_en": "Edgelib", "sort_order": 0, "description": "Edgelib"}, {"id": 4914, "taxonomy": "skills", "text": "Angular 8+", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-8", "text_vi": "Angular 8+", "text_en": "Angular 8+", "sort_order": 0, "description": "Angular 8+"}, {"id": 4922, "taxonomy": "skills", "text": "Angular 8", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-8", "text_vi": "Angular 8", "text_en": "Angular 8", "sort_order": 0, "description": "Angular 8"}, {"id": 4942, "taxonomy": "skills", "text": "Deep Learning", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "deep-learning", "text_vi": "Deep Learning", "text_en": "Deep Learning", "sort_order": 0, "description": "Deep Learning"}, {"id": 4954, "taxonomy": "skills", "text": "Struct", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "struct", "text_vi": "Struct", "text_en": "Struct", "sort_order": 0, "description": "Struct"}, {"id": 4964, "taxonomy": "skills", "text": "MacOS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "macos", "text_vi": "MacOS", "text_en": "MacOS", "sort_order": 0, "description": "MacOS"}, {"id": 4970, "taxonomy": "skills", "text": "NuxtJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nuxtjs", "text_vi": "NuxtJS", "text_en": "NuxtJS", "sort_order": 0, "description": "NuxtJS"}, {"id": 5, "taxonomy": "skills", "text": "Swift", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "swift", "text_vi": "Swift", "text_en": "Swift", "sort_order": 0, "description": "Swift"}, {"id": 5001, "taxonomy": "skills", "text": "OWASP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "owasp", "text_vi": "OWASP", "text_en": "OWASP", "sort_order": 0, "description": "OWASP"}, {"id": 5013, "taxonomy": "skills", "text": "Web Master", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web-master", "text_vi": "Web Master", "text_en": "Web Master", "sort_order": 0, "description": "Web Master"}, {"id": 5041, "taxonomy": "skills", "text": "LAN", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lan", "text_vi": "LAN", "text_en": "LAN", "sort_order": 0, "description": "LAN"}, {"id": 5042, "taxonomy": "skills", "text": "WAN", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "wan", "text_vi": "WAN", "text_en": "WAN", "sort_order": 0, "description": "WAN"}, {"id": 5064, "taxonomy": "skills", "text": "QA/QC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "qaqc", "text_vi": "QA/QC", "text_en": "QA/QC", "sort_order": 0, "description": "QA/QC"}, {"id": 5070, "taxonomy": "skills", "text": "NestJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "<PERSON><PERSON><PERSON>", "text_vi": "NestJS", "text_en": "NestJS", "sort_order": 0, "description": "NestJS"}, {"id": 5090, "taxonomy": "skills", "text": "Hybrid", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hybrid", "text_vi": "Hybrid", "text_en": "Hybrid", "sort_order": 0, "description": "Hybrid"}, {"id": 5105, "taxonomy": "skills", "text": "CSDL Oracle", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "csdl-oracle", "text_vi": "CSDL Oracle", "text_en": "CSDL Oracle", "sort_order": 0, "description": "CSDL Oracle"}, {"id": 5125, "taxonomy": "skills", "text": "MS Dynamic", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ms-dynamic", "text_vi": "MS Dynamic", "text_en": "MS Dynamic", "sort_order": 0, "description": "MS Dynamic"}, {"id": 5126, "taxonomy": "skills", "text": "CRM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "crm", "text_vi": "CRM", "text_en": "CRM", "sort_order": 0, "description": "CRM"}, {"id": 5132, "taxonomy": "skills", "text": "Spss", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "spss", "text_vi": "Spss", "text_en": "Spss", "sort_order": 0, "description": "Spss"}, {"id": 5146, "taxonomy": "skills", "text": "Algorithm", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "algorithm", "text_vi": "Algorithm", "text_en": "Algorithm", "sort_order": 0, "description": "Algorithm"}, {"id": 5153, "taxonomy": "skills", "text": "LEAN", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lean", "text_vi": "LEAN", "text_en": "LEAN", "sort_order": 0, "description": "LEAN"}, {"id": 5156, "taxonomy": "skills", "text": "IT Operations", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-operations", "text_vi": "IT Operations", "text_en": "IT Operations", "sort_order": 0, "description": "IT Operations"}, {"id": 5162, "taxonomy": "skills", "text": "Visual Effects", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "visual-effects", "text_vi": "Visual Effects", "text_en": "Visual Effects", "sort_order": 0, "description": "Visual Effects"}, {"id": 5170, "taxonomy": "skills", "text": "Mobile Video Games", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mobile-video-games", "text_vi": "Mobile Video Games", "text_en": "Mobile Video Games", "sort_order": 0, "description": "Mobile Video Games"}, {"id": 5184, "taxonomy": "skills", "text": "IoT Development", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "iot-development", "text_vi": "IoT Development", "text_en": "IoT Development", "sort_order": 0, "description": "IoT Development"}, {"id": 5193, "taxonomy": "skills", "text": "Technical Director", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "technical-director", "text_vi": "Technical Director", "text_en": "Technical Director", "sort_order": 0, "description": "Technical Director"}, {"id": 5194, "taxonomy": "skills", "text": "Solution Architect", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "solution-architect", "text_vi": "Solution Architect", "text_en": "Solution Architect", "sort_order": 0, "description": "Solution Architect"}, {"id": 5203, "taxonomy": "skills", "text": "Spark", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "spark", "text_vi": "Spark", "text_en": "Spark", "sort_order": 0, "description": "Spark"}, {"id": 5204, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hadoop", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 5205, "taxonomy": "skills", "text": "DNS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "dns", "text_vi": "DNS", "text_en": "DNS", "sort_order": 0, "description": "DNS"}, {"id": 5209, "taxonomy": "skills", "text": "IOT Integration", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "iot-integration", "text_vi": "IOT Integration", "text_en": "IOT Integration", "sort_order": 0, "description": "IOT Integration"}, {"id": 5226, "taxonomy": "skills", "text": "MCSA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mcsa", "text_vi": "MCSA", "text_en": "MCSA", "sort_order": 0, "description": "MCSA"}, {"id": 5232, "taxonomy": "skills", "text": "German", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "german", "text_vi": "German", "text_en": "German", "sort_order": 0, "description": "German"}, {"id": 5242, "taxonomy": "skills", "text": "Automotive", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "automotive", "text_vi": "Automotive", "text_en": "Automotive", "sort_order": 0, "description": "Automotive"}, {"id": 5252, "taxonomy": "skills", "text": "Product Planning", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "product-planning", "text_vi": "Product Planning", "text_en": "Product Planning", "sort_order": 0, "description": "Product Planning"}, {"id": 5260, "taxonomy": "skills", "text": "PMP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "pmp", "text_vi": "PMP", "text_en": "PMP", "sort_order": 0, "description": "PMP"}, {"id": 5266, "taxonomy": "skills", "text": "Data Warehouse", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-warehouse", "text_vi": "Data Warehouse", "text_en": "Data Warehouse", "sort_order": 0, "description": "Data Warehouse"}, {"id": 53, "taxonomy": "skills", "text": "Esenthel Engine", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "esenthel-engine", "text_vi": "Esenthel Engine", "text_en": "Esenthel Engine", "sort_order": 0, "description": "Esenthel Engine"}, {"id": 5317, "taxonomy": "skills", "text": "3D Modelling", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "3d-modelling", "text_vi": "3D Modelling", "text_en": "3D Modelling", "sort_order": 0, "description": "3D Modelling"}, {"id": 5334, "taxonomy": "skills", "text": "Firebase", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "firebase", "text_vi": "Firebase", "text_en": "Firebase", "sort_order": 0, "description": "Firebase"}, {"id": 5337, "taxonomy": "skills", "text": "Opencv", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "opencv", "text_vi": "Opencv", "text_en": "Opencv", "sort_order": 0, "description": "Opencv"}, {"id": 5362, "taxonomy": "skills", "text": "SSRS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ssrs", "text_vi": "SSRS", "text_en": "SSRS", "sort_order": 0, "description": "SSRS"}, {"id": 5368, "taxonomy": "skills", "text": "Product Operation", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "product-operation", "text_vi": "Product Operation", "text_en": "Product Operation", "sort_order": 0, "description": "Product Operation"}, {"id": 5379, "taxonomy": "skills", "text": "MCSE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mcse", "text_vi": "MCSE", "text_en": "MCSE", "sort_order": 0, "description": "MCSE"}, {"id": 5396, "taxonomy": "skills", "text": "Chinese", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "chinese", "text_vi": "Chinese", "text_en": "Chinese", "sort_order": 0, "description": "Chinese"}, {"id": 5446, "taxonomy": "skills", "text": "Oracle Ebs", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-ebs", "text_vi": "Oracle Ebs", "text_en": "Oracle Ebs", "sort_order": 0, "description": "Oracle Ebs"}, {"id": 5482, "taxonomy": "skills", "text": "Verilog", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "verilog", "text_vi": "Verilog", "text_en": "Verilog", "sort_order": 0, "description": "Verilog"}, {"id": 5488, "taxonomy": "skills", "text": "HMI Application", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hmi-application", "text_vi": "HMI Application", "text_en": "HMI Application", "sort_order": 0, "description": "HMI Application"}, {"id": 5489, "taxonomy": "skills", "text": "HMI Frameworks", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hmi-frameworks", "text_vi": "HMI Frameworks", "text_en": "HMI Frameworks", "sort_order": 0, "description": "HMI Frameworks"}, {"id": 55, "taxonomy": "skills", "text": "Corona", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "corona", "text_vi": "Corona", "text_en": "Corona", "sort_order": 0, "description": "Corona"}, {"id": 5562, "taxonomy": "skills", "text": "Microsoft Dynamics", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "microsoft-dynamics", "text_vi": "Microsoft Dynamics", "text_en": "Microsoft Dynamics", "sort_order": 0, "description": "Microsoft Dynamics"}, {"id": 5568, "taxonomy": "skills", "text": "Oracle EBS R12", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-ebs-r12", "text_vi": "Oracle EBS R12", "text_en": "Oracle EBS R12", "sort_order": 0, "description": "Oracle EBS R12"}, {"id": 5570, "taxonomy": "skills", "text": "Oracle ERP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-erp", "text_vi": "Oracle ERP", "text_en": "Oracle ERP", "sort_order": 0, "description": "Oracle ERP"}, {"id": 5587, "taxonomy": "skills", "text": "QT Framework", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "qt-framework", "text_vi": "QT Framework", "text_en": "QT Framework", "sort_order": 0, "description": "QT Framework"}, {"id": 56, "taxonomy": "skills", "text": "MonoGame ", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "monogame", "text_vi": "MonoGame ", "text_en": "MonoGame ", "sort_order": 0, "description": "MonoGame "}, {"id": 5629, "taxonomy": "skills", "text": "Supporting End Users", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "supporting-end-users", "text_vi": "Supporting End Users", "text_en": "Supporting End Users", "sort_order": 0, "description": "Supporting End Users"}, {"id": 5630, "taxonomy": "skills", "text": "<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jenkins-ci", "text_vi": "<PERSON>", "text_en": "<PERSON>", "sort_order": 0, "description": "<PERSON>"}, {"id": 5642, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ott", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 5645, "taxonomy": "skills", "text": "Microsoft", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "microsoft", "text_vi": "Microsoft", "text_en": "Microsoft", "sort_order": 0, "description": "Microsoft"}, {"id": 5646, "taxonomy": "skills", "text": "Virtualization", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "virtualization", "text_vi": "Virtualization", "text_en": "Virtualization", "sort_order": 0, "description": "Virtualization"}, {"id": 5659, "taxonomy": "skills", "text": "IT Presales", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-presales", "text_vi": "IT Presales", "text_en": "IT Presales", "sort_order": 0, "description": "IT Presales"}, {"id": 5661, "taxonomy": "skills", "text": "Integration Test", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "integration-test", "text_vi": "Integration Test", "text_en": "Integration Test", "sort_order": 0, "description": "Integration Test"}, {"id": 5662, "taxonomy": "skills", "text": "Matlab", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "matlab", "text_vi": "Matlab", "text_en": "Matlab", "sort_order": 0, "description": "Matlab"}, {"id": 5679, "taxonomy": "skills", "text": "Oracle Middleware", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-middleware", "text_vi": "Oracle Middleware", "text_en": "Oracle Middleware", "sort_order": 0, "description": "Oracle Middleware"}, {"id": 584, "taxonomy": "skills", "text": "Oracle And/or Postgresql", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-andor-postgresql", "text_vi": "Oracle And/or Postgresql", "text_en": "Oracle And/or Postgresql", "sort_order": 0, "description": "Oracle And/or Postgresql"}, {"id": 5864, "taxonomy": "skills", "text": "Visual Foxpro", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "visual-foxpro", "text_vi": "Visual Foxpro", "text_en": "Visual Foxpro", "sort_order": 0, "description": "Visual Foxpro"}, {"id": 5893, "taxonomy": "skills", "text": "Soc Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "soc-design", "text_vi": "Soc Design", "text_en": "Soc Design", "sort_order": 0, "description": "Soc Design"}, {"id": 5899, "taxonomy": "skills", "text": "RTL Verification", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rtl-verification", "text_vi": "RTL Verification", "text_en": "RTL Verification", "sort_order": 0, "description": "RTL Verification"}, {"id": 5905, "taxonomy": "skills", "text": "Data Visualization", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-visualization", "text_vi": "Data Visualization", "text_en": "Data Visualization", "sort_order": 0, "description": "Data Visualization"}, {"id": 5915, "taxonomy": "skills", "text": "IOT Engineering", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "iot-engineering", "text_vi": "IOT Engineering", "text_en": "IOT Engineering", "sort_order": 0, "description": "IOT Engineering"}, {"id": 5928, "taxonomy": "skills", "text": "Videos Editor", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "videos-editor", "text_vi": "Videos Editor", "text_en": "Videos Editor", "sort_order": 0, "description": "Videos Editor"}, {"id": 5930, "taxonomy": "skills", "text": "Oracle ERP Cloud", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-erp-cloud", "text_vi": "Oracle ERP Cloud", "text_en": "Oracle ERP Cloud", "sort_order": 0, "description": "Oracle ERP Cloud"}, {"id": 5958, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "kernel", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 5974, "taxonomy": "skills", "text": "Oracle SQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-sql", "text_vi": "Oracle SQL", "text_en": "Oracle SQL", "sort_order": 0, "description": "Oracle SQL"}, {"id": 6, "taxonomy": "skills", "text": "Linux", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "linux", "text_vi": "Linux", "text_en": "Linux", "sort_order": 0, "description": "Linux"}, {"id": 6001, "taxonomy": "skills", "text": "Ceh-V5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ceh-v5", "text_vi": "Ceh-V5", "text_en": "Ceh-V5", "sort_order": 0, "description": "Ceh-V5"}, {"id": 6023, "taxonomy": "skills", "text": "VoIP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "voip", "text_vi": "VoIP", "text_en": "VoIP", "sort_order": 0, "description": "VoIP"}, {"id": 6033, "taxonomy": "skills", "text": "Oracle Storage", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-storage", "text_vi": "Oracle Storage", "text_en": "Oracle Storage", "sort_order": 0, "description": "Oracle Storage"}, {"id": 6034, "taxonomy": "skills", "text": "Oracle & Plsql", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-plsql", "text_vi": "Oracle & Plsql", "text_en": "Oracle & Plsql", "sort_order": 0, "description": "Oracle & Plsql"}, {"id": 6039, "taxonomy": "skills", "text": "Internet of Things", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "internet-of-things", "text_vi": "Internet of Things", "text_en": "Internet of Things", "sort_order": 0, "description": "Internet of Things"}, {"id": 6135, "taxonomy": "skills", "text": "MS Visio", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ms-visio", "text_vi": "MS Visio", "text_en": "MS Visio", "sort_order": 0, "description": "MS Visio"}, {"id": 6136, "taxonomy": "skills", "text": "Axure Rp", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "axure-rp", "text_vi": "Axure Rp", "text_en": "Axure Rp", "sort_order": 0, "description": "Axure Rp"}, {"id": 6168, "taxonomy": "skills", "text": "Plc", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "plc", "text_vi": "Plc", "text_en": "Plc", "sort_order": 0, "description": "Plc"}, {"id": 629, "taxonomy": "skills", "text": "Jsp/servlet", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jspservlet", "text_vi": "Jsp/servlet", "text_en": "Jsp/servlet", "sort_order": 0, "description": "Jsp/servlet"}, {"id": 6390, "taxonomy": "skills", "text": "Project Coordinator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "project-coordinator", "text_vi": "Project Coordinator", "text_en": "Project Coordinator", "sort_order": 0, "description": "Project Coordinator"}, {"id": 6395, "taxonomy": "skills", "text": "NUnit", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nunit", "text_vi": "NUnit", "text_en": "NUnit", "sort_order": 0, "description": "NUnit"}, {"id": 67, "taxonomy": "skills", "text": "Cocos2D", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cocos2d", "text_vi": "Cocos2D", "text_en": "Cocos2D", "sort_order": 0, "description": "Cocos2D"}, {"id": 675, "taxonomy": "skills", "text": "Unix", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "unix", "text_vi": "Unix", "text_en": "Unix", "sort_order": 0, "description": "Unix"}, {"id": 6837, "taxonomy": "skills", "text": "ETL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "etl", "text_vi": "ETL", "text_en": "ETL", "sort_order": 0, "description": "ETL"}, {"id": 6856, "taxonomy": "skills", "text": "Microsoft Dynamics AX", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "microsoft-dynamics-ax", "text_vi": "Microsoft Dynamics AX", "text_en": "Microsoft Dynamics AX", "sort_order": 0, "description": "Microsoft Dynamics AX"}, {"id": 6884, "taxonomy": "skills", "text": "Game Tester", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-tester", "text_vi": "Game Tester", "text_en": "Game Tester", "sort_order": 0, "description": "Game Tester"}, {"id": 6891, "taxonomy": "skills", "text": "CRM Integration", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "crm-integration", "text_vi": "CRM Integration", "text_en": "CRM Integration", "sort_order": 0, "description": "CRM Integration"}, {"id": 6898, "taxonomy": "skills", "text": "Monitor Card Transactions", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "monitor-card-transactions", "text_vi": "Monitor Card Transactions", "text_en": "Monitor Card Transactions", "sort_order": 0, "description": "Monitor Card Transactions"}, {"id": 7, "taxonomy": "skills", "text": "UX/UI Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "uxui-design", "text_vi": "UX/UI Design", "text_en": "UX/UI Design", "sort_order": 0, "description": "UI-UX"}, {"id": 70, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lua", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 7139, "taxonomy": "skills", "text": "Game Operator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-operator", "text_vi": "Game Operator", "text_en": "Game Operator", "sort_order": 0, "description": "Game Operator"}, {"id": 7157, "taxonomy": "skills", "text": "BSI", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bsi", "text_vi": "BSI", "text_en": "BSI", "sort_order": 0, "description": "BSI"}, {"id": 72, "taxonomy": "skills", "text": "Sharepoint", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sharepoint", "text_vi": "Sharepoint", "text_en": "Sharepoint", "sort_order": 0, "description": "Sharepoint"}, {"id": 7202, "taxonomy": "skills", "text": "SRE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sre", "text_vi": "SRE", "text_en": "SRE", "sort_order": 0, "description": "SRE"}, {"id": 7225, "taxonomy": "skills", "text": "Erl<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "erlang", "text_vi": "Erl<PERSON>", "text_en": "Erl<PERSON>", "sort_order": 0, "description": "Erl<PERSON>"}, {"id": 7253, "taxonomy": "skills", "text": "BabylonJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "<PERSON><PERSON><PERSON><PERSON>", "text_vi": "BabylonJS", "text_en": "BabylonJS", "sort_order": 0, "description": "BabylonJS"}, {"id": 7254, "taxonomy": "skills", "text": "Coffeescript", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "coffeescript", "text_vi": "Coffeescript", "text_en": "Coffeescript", "sort_order": 0, "description": "Coffeescript"}, {"id": 7287, "taxonomy": "skills", "text": "Dart", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "dart", "text_vi": "Dart", "text_en": "Dart", "sort_order": 0, "description": "Dart"}, {"id": 7298, "taxonomy": "skills", "text": "Operator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "operator", "text_vi": "Operator", "text_en": "Operator", "sort_order": 0, "description": "Operator"}, {"id": 7299, "taxonomy": "skills", "text": "Security Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "security-engineer", "text_vi": "Security Engineer", "text_en": "Security Engineer", "sort_order": 0, "description": "Security Engineer"}, {"id": 73, "taxonomy": "skills", "text": ".NET", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "net", "text_vi": ".NET", "text_en": ".NET", "sort_order": 0, "description": ".NET"}, {"id": 7308, "taxonomy": "skills", "text": "Financial", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "financial", "text_vi": "Financial", "text_en": "Financial", "sort_order": 0, "description": "Financial"}, {"id": 7309, "taxonomy": "skills", "text": "SwiftUI", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "<PERSON><PERSON>", "text_vi": "SwiftUI", "text_en": "SwiftUI", "sort_order": 0, "description": "SwiftUI"}, {"id": 7310, "taxonomy": "skills", "text": "UIkit", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "uikit", "text_vi": "UIkit", "text_en": "UIkit", "sort_order": 0, "description": "UIkit"}, {"id": 7314, "taxonomy": "skills", "text": "Manual", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "manual", "text_vi": "Manual", "text_en": "Manual", "sort_order": 0, "description": "Manual"}, {"id": 7323, "taxonomy": "skills", "text": "Core Java 8", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "core-java-8", "text_vi": "Core Java 8", "text_en": "Core Java 8", "sort_order": 0, "description": "Core Java 8"}, {"id": 7336, "taxonomy": "skills", "text": "Java 8", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "java-8", "text_vi": "Java 8", "text_en": "Java 8", "sort_order": 0, "description": "Java 8"}, {"id": 7339, "taxonomy": "skills", "text": "Embedded", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "embedded", "text_vi": "Embedded", "text_en": "Embedded", "sort_order": 0, "description": "Embedded"}, {"id": 7347, "taxonomy": "skills", "text": "VFX", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "vfx", "text_vi": "VFX", "text_en": "VFX", "sort_order": 0, "description": "VFX"}, {"id": 7348, "taxonomy": "skills", "text": "FastAPI", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "<PERSON><PERSON><PERSON>", "text_vi": "FastAPI", "text_en": "FastAPI", "sort_order": 0, "description": "FastAPI"}, {"id": 7350, "taxonomy": "skills", "text": "RxJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rxjs", "text_vi": "RxJS", "text_en": "RxJS", "sort_order": 0, "description": "RxJS"}, {"id": 7351, "taxonomy": "skills", "text": "Korean", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "korean", "text_vi": "Korean", "text_en": "Korean", "sort_order": 0, "description": "Korean"}, {"id": 7352, "taxonomy": "skills", "text": "Marketing", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "marketing", "text_vi": "Marketing", "text_en": "Marketing", "sort_order": 0, "description": "Marketing"}, {"id": 7354, "taxonomy": "skills", "text": "CCNA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ccna", "text_vi": "CCNA", "text_en": "CCNA", "sort_order": 0, "description": "CCNA"}, {"id": 7355, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "moodle", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 7360, "taxonomy": "skills", "text": "Quality Analyst", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "quality-analyst", "text_vi": "Quality Analyst", "text_en": "Quality Analyst", "sort_order": 0, "description": "Quality Analyst"}, {"id": 7361, "taxonomy": "skills", "text": "BrSE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "brse", "text_vi": "BrSE", "text_en": "BrSE", "sort_order": 0, "description": "BrSE"}, {"id": 7363, "taxonomy": "skills", "text": "CRM Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "crm-consultant", "text_vi": "CRM Consultant", "text_en": "CRM Consultant", "sort_order": 0, "description": "CRM Consultant"}, {"id": 7364, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tableau", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 7366, "taxonomy": "skills", "text": "Penetration Test", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "penetration-test", "text_vi": "Penetration Test", "text_en": "Penetration Test", "sort_order": 0, "description": "Penetration Test"}, {"id": 7367, "taxonomy": "skills", "text": "React", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "react", "text_vi": "React", "text_en": "React", "sort_order": 0, "description": "React"}, {"id": 74, "taxonomy": "skills", "text": "ERP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "erp", "text_vi": "ERP", "text_en": "ERP", "sort_order": 0, "description": "ERP"}, {"id": 7406, "taxonomy": "skills", "text": "System", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "system", "text_vi": "System", "text_en": "System", "sort_order": 0, "description": "System"}, {"id": 7408, "taxonomy": "skills", "text": "<PERSON>gger", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "dagger", "text_vi": "<PERSON>gger", "text_en": "<PERSON>gger", "sort_order": 0, "description": "<PERSON>gger"}, {"id": 7409, "taxonomy": "skills", "text": "MVVM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mvvm", "text_vi": "MVVM", "text_en": "MVVM", "sort_order": 0, "description": "MVVM"}, {"id": 7411, "taxonomy": "skills", "text": "Oracle PL/SQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-pl-sql", "text_vi": "Oracle PL/SQL", "text_en": "Oracle PL/SQL", "sort_order": 0, "description": "Oracle PL/SQL"}, {"id": 7413, "taxonomy": "skills", "text": "Designer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "designer", "text_vi": "Designer", "text_en": "Designer", "sort_order": 0, "description": "Designer"}, {"id": 7417, "taxonomy": "skills", "text": "Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "consultant", "text_vi": "Consultant", "text_en": "Consultant", "sort_order": 0, "description": "Consultant"}, {"id": 7420, "taxonomy": "skills", "text": "Appium", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "appium", "text_vi": "Appium", "text_en": "Appium", "sort_order": 0, "description": "Appium"}, {"id": 7421, "taxonomy": "skills", "text": "Serenity BDD", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "serenity-bdd", "text_vi": "Serenity BDD", "text_en": "Serenity BDD", "sort_order": 0, "description": "Serenity BDD"}, {"id": 7422, "taxonomy": "skills", "text": "Jmeter", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jmeter", "text_vi": "Jmeter", "text_en": "Jmeter", "sort_order": 0, "description": "Jmeter"}, {"id": 7430, "taxonomy": "skills", "text": "Manual Tester", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "manual-tester", "text_vi": "Manual Tester", "text_en": "Manual Tester", "sort_order": 0, "description": "Manual Tester"}, {"id": 7443, "taxonomy": "skills", "text": "Embedded Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "embedded-engineer", "text_vi": "Embedded Engineer", "text_en": "Embedded Engineer", "sort_order": 0, "description": "Embedded Engineer"}, {"id": 75, "taxonomy": "skills", "text": "HTML", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "html", "text_vi": "HTML", "text_en": "HTML", "sort_order": 0, "description": "HTML"}, {"id": 76, "taxonomy": "skills", "text": "QA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "qa", "text_vi": "QA", "text_en": "QA", "sort_order": 0, "description": "QA"}, {"id": 77, "taxonomy": "skills", "text": "QC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "qc", "text_vi": "QC", "text_en": "QC", "sort_order": 0, "description": "QC"}, {"id": 78, "taxonomy": "skills", "text": "Tester", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tester", "text_vi": "Tester", "text_en": "Tester", "sort_order": 0, "description": "Tester"}, {"id": 79, "taxonomy": "skills", "text": "MS SQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ms-sql", "text_vi": "MS SQL", "text_en": "MS SQL", "sort_order": 0, "description": "MS SQL"}, {"id": 7957, "taxonomy": "skills", "text": "Vmware", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "vmware", "text_vi": "Vmware", "text_en": "Vmware", "sort_order": 0, "description": "Vmware"}, {"id": 7959, "taxonomy": "skills", "text": "RxSwift", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rxswift", "text_vi": "RxSwift", "text_en": "RxSwift", "sort_order": 0, "description": "RxSwift"}, {"id": 7960, "taxonomy": "skills", "text": "Combine", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "combine", "text_vi": "Combine", "text_en": "Combine", "sort_order": 0, "description": "Combine"}, {"id": 7963, "taxonomy": "skills", "text": "POD", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "pod", "text_vi": "POD", "text_en": "POD", "sort_order": 0, "description": "POD"}, {"id": 7964, "taxonomy": "skills", "text": "Java Spring Boot", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "java-spring-boot", "text_vi": "Java Spring Boot", "text_en": "Java Spring Boot", "sort_order": 0, "description": "Java Spring Boot"}, {"id": 7966, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "trien-khai-phan-mem", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 7968, "taxonomy": "skills", "text": "Tailwind", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tailwind", "text_vi": "Tailwind", "text_en": "Tailwind", "sort_order": 0, "description": "Tailwind"}, {"id": 7969, "taxonomy": "skills", "text": "IT Operation", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-operation", "text_vi": "IT Operation", "text_en": "IT Operation", "sort_order": 0, "description": "IT Operation"}, {"id": 7972, "taxonomy": "skills", "text": "CAD", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cad", "text_vi": "CAD", "text_en": "CAD", "sort_order": 0, "description": "CAD"}, {"id": 7974, "taxonomy": "skills", "text": "Unity3d", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "unity3d", "text_vi": "Unity3d", "text_en": "Unity3d", "sort_order": 0, "description": "Unity3d"}, {"id": 7976, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lumen", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 7977, "taxonomy": "skills", "text": "Video Edit", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "video-edit", "text_vi": "Video Edit", "text_en": "Video Edit", "sort_order": 0, "description": "Video Edit"}, {"id": 7982, "taxonomy": "skills", "text": "Data Analyst", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-analyst", "text_vi": "Data Analyst", "text_en": "Data Analyst", "sort_order": 0, "description": "Data Analyst"}, {"id": 7984, "taxonomy": "skills", "text": "R&D Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "r-d-engineer", "text_vi": "R&D Engineer", "text_en": "R&D Engineer", "sort_order": 0, "description": "R&D Engineer"}, {"id": 7987, "taxonomy": "skills", "text": "Scrum Master", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "scrum-master", "text_vi": "Scrum Master", "text_en": "Scrum Master", "sort_order": 0, "description": "Scrum Master"}, {"id": 7992, "taxonomy": "skills", "text": "FI", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "fi", "text_vi": "FI", "text_en": "FI", "sort_order": 0, "description": "FI"}, {"id": 7995, "taxonomy": "skills", "text": "Java EE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "java-ee", "text_vi": "Java EE", "text_en": "Java EE", "sort_order": 0, "description": "Java EE"}, {"id": 7997, "taxonomy": "skills", "text": "Angular 4+", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-4", "text_vi": "Angular 4+", "text_en": "Angular 4+", "sort_order": 0, "description": "Angular 4+"}, {"id": 7998, "taxonomy": "skills", "text": "Adobe XD", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "adobe-xd", "text_vi": "Adobe XD", "text_en": "Adobe XD", "sort_order": 0, "description": "Adobe XD"}, {"id": 7999, "taxonomy": "skills", "text": "Data Reporting Analyst", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-reporting-analyst", "text_vi": "Data Reporting Analyst", "text_en": "Data Reporting Analyst", "sort_order": 0, "description": "Data Reporting Analyst"}, {"id": 8, "taxonomy": "skills", "text": "Android", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "android", "text_vi": "Android", "text_en": "Android", "sort_order": 0, "description": "Android"}, {"id": 80, "taxonomy": "skills", "text": "CakePHP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cakephp", "text_vi": "CakePHP", "text_en": "CakePHP", "sort_order": 0, "description": "CakePHP"}, {"id": 8000, "taxonomy": "skills", "text": "Unity2D", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "unity2d", "text_vi": "Unity2D", "text_en": "Unity2D", "sort_order": 0, "description": "Unity2D"}, {"id": 8001, "taxonomy": "skills", "text": "English", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "english", "text_vi": "English", "text_en": "English", "sort_order": 0, "description": "English"}, {"id": 8005, "taxonomy": "skills", "text": "Functional Safety", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "functional-safety", "text_vi": "Functional Safety", "text_en": "Functional Safety", "sort_order": 0, "description": "Functional Safety"}, {"id": 8009, "taxonomy": "skills", "text": "INTEGRATION ENGINEER", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "integration-engineer", "text_vi": "INTEGRATION ENGINEER", "text_en": "INTEGRATION ENGINEER", "sort_order": 0, "description": "INTEGRATION ENGINEER"}, {"id": 8010, "taxonomy": "skills", "text": "Integration", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "integration", "text_vi": "Integration", "text_en": "Integration", "sort_order": 0, "description": "Integration"}, {"id": 8031, "taxonomy": "skills", "text": "Prisma2", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "prisma2", "text_vi": "Prisma2", "text_en": "Prisma2", "sort_order": 0, "description": "Prisma2"}, {"id": 8039, "taxonomy": "skills", "text": "audit", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "audit", "text_vi": "audit", "text_en": "audit", "sort_order": 0, "description": "audit"}, {"id": 8041, "taxonomy": "skills", "text": "Software", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "software", "text_vi": "Software", "text_en": "Software", "sort_order": 0, "description": "Software"}, {"id": 8047, "taxonomy": "skills", "text": "Commucation", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "commucation", "text_vi": "Commucation", "text_en": "Commucation", "sort_order": 0, "description": "Commucation"}, {"id": 8048, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "z<PERSON>lin", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8050, "taxonomy": "skills", "text": ".NET MVC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "net-mvc", "text_vi": ".NET MVC", "text_en": ".NET MVC", "sort_order": 0, "description": ".NET MVC"}, {"id": 8051, "taxonomy": "skills", "text": "Angular 10", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-10", "text_vi": "Angular 10", "text_en": "Angular 10", "sort_order": 0, "description": "Angular 10"}, {"id": 8055, "taxonomy": "skills", "text": "Digital Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "digital-design", "text_vi": "Digital Design", "text_en": "Digital Design", "sort_order": 0, "description": "Digital Design"}, {"id": 8056, "taxonomy": "skills", "text": "Art Director", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "art-director", "text_vi": "Art Director", "text_en": "Art Director", "sort_order": 0, "description": "Art Director"}, {"id": 8058, "taxonomy": "skills", "text": "AX", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ax", "text_vi": "AX", "text_en": "AX", "sort_order": 0, "description": "AX"}, {"id": 8059, "taxonomy": "skills", "text": "X++", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "x", "text_vi": "X++", "text_en": "X++", "sort_order": 0, "description": "X++"}, {"id": 8060, "taxonomy": "skills", "text": "Project Assistant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "project-assistant", "text_vi": "Project Assistant", "text_en": "Project Assistant", "sort_order": 0, "description": "Project Assistant"}, {"id": 8061, "taxonomy": "skills", "text": "ASP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "asp", "text_vi": "ASP", "text_en": "ASP", "sort_order": 0, "description": "ASP"}, {"id": 8065, "taxonomy": "skills", "text": "CMS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cms", "text_vi": "CMS", "text_en": "CMS", "sort_order": 0, "description": "CMS"}, {"id": 8067, "taxonomy": "skills", "text": "GIS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "gis", "text_vi": "GIS", "text_en": "GIS", "sort_order": 0, "description": "GIS"}, {"id": 8069, "taxonomy": "skills", "text": "Cybersecurity", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cybersecurity", "text_vi": "Cybersecurity", "text_en": "Cybersecurity", "sort_order": 0, "description": "Cybersecurity"}, {"id": 8071, "taxonomy": "skills", "text": "Mobile Tester", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mobile-tester", "text_vi": "Mobile Tester", "text_en": "Mobile Tester", "sort_order": 0, "description": "Mobile Tester"}, {"id": 8072, "taxonomy": "skills", "text": "Web Tester", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web-tester", "text_vi": "Web Tester", "text_en": "Web Tester", "sort_order": 0, "description": "Web Tester"}, {"id": 8077, "taxonomy": "skills", "text": "Angular 5+", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-5", "text_vi": "Angular 5+", "text_en": "Angular 5+", "sort_order": 0, "description": "Angular 5+"}, {"id": 8082, "taxonomy": "skills", "text": "JPA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jpa", "text_vi": "JPA", "text_en": "JPA", "sort_order": 0, "description": "JPA"}, {"id": 8083, "taxonomy": "skills", "text": "Microsoft 365", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "microsoft-365", "text_vi": "Microsoft 365", "text_en": "Microsoft 365", "sort_order": 0, "description": "Microsoft 365"}, {"id": 8087, "taxonomy": "skills", "text": "Crawling", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "crawling", "text_vi": "Crawling", "text_en": "Crawling", "sort_order": 0, "description": "Crawling"}, {"id": 8089, "taxonomy": "skills", "text": "hub", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hub", "text_vi": "hub", "text_en": "hub", "sort_order": 0, "description": "hub"}, {"id": 8094, "taxonomy": "skills", "text": "Corecard", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "corecard", "text_vi": "Corecard", "text_en": "Corecard", "sort_order": 0, "description": "Corecard"}, {"id": 81, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "yii", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 8108, "taxonomy": "skills", "text": "Infrastructure", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "infrastructure", "text_vi": "Infrastructure", "text_en": "Infrastructure", "sort_order": 0, "description": "Infrastructure"}, {"id": 8114, "taxonomy": "skills", "text": "CSDL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "csdl", "text_vi": "CSDL", "text_en": "CSDL", "sort_order": 0, "description": "CSDL"}, {"id": 8115, "taxonomy": "skills", "text": "ESB", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "esb", "text_vi": "ESB", "text_en": "ESB", "sort_order": 0, "description": "ESB"}, {"id": 8116, "taxonomy": "skills", "text": "IT Coordinator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-coordinator", "text_vi": "IT Coordinator", "text_en": "IT Coordinator", "sort_order": 0, "description": "IT Coordinator"}, {"id": 8120, "taxonomy": "skills", "text": "MSA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "msa", "text_vi": "MSA", "text_en": "MSA", "sort_order": 0, "description": "MSA"}, {"id": 8122, "taxonomy": "skills", "text": "SSR", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ssr", "text_vi": "SSR", "text_en": "SSR", "sort_order": 0, "description": "SSR"}, {"id": 8125, "taxonomy": "skills", "text": "oss", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oss", "text_vi": "oss", "text_en": "oss", "sort_order": 0, "description": "oss"}, {"id": 8127, "taxonomy": "skills", "text": "Laravel 6", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "laravel-6", "text_vi": "Laravel 6", "text_en": "Laravel 6", "sort_order": 0, "description": "Laravel 6"}, {"id": 8129, "taxonomy": "skills", "text": "Woocommerce", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "woocommerce", "text_vi": "Woocommerce", "text_en": "Woocommerce", "sort_order": 0, "description": "Woocommerce"}, {"id": 8136, "taxonomy": "skills", "text": "backlog", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "backlog", "text_vi": "backlog", "text_en": "backlog", "sort_order": 0, "description": "backlog"}, {"id": 8141, "taxonomy": "skills", "text": "Invision", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "invision", "text_vi": "Invision", "text_en": "Invision", "sort_order": 0, "description": "Invision"}, {"id": 8142, "taxonomy": "skills", "text": "Ka<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "kanban", "text_vi": "Ka<PERSON><PERSON>", "text_en": "Ka<PERSON><PERSON>", "sort_order": 0, "description": "Ka<PERSON><PERSON>"}, {"id": 8145, "taxonomy": "skills", "text": "mcv", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mcv", "text_vi": "mcv", "text_en": "mcv", "sort_order": 0, "description": "mcv"}, {"id": 8147, "taxonomy": "skills", "text": "NAS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nas", "text_vi": "NAS", "text_en": "NAS", "sort_order": 0, "description": "NAS"}, {"id": 8148, "taxonomy": "skills", "text": "Rust", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rust", "text_vi": "Rust", "text_en": "Rust", "sort_order": 0, "description": "Rust"}, {"id": 8151, "taxonomy": "skills", "text": "Site Reliability", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "site-reliability", "text_vi": "Site Reliability", "text_en": "Site Reliability", "sort_order": 0, "description": "Site Reliability"}, {"id": 8152, "taxonomy": "skills", "text": "DOM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "dom", "text_vi": "DOM", "text_en": "DOM", "sort_order": 0, "description": "DOM"}, {"id": 8153, "taxonomy": "skills", "text": "IT Staff", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-staff", "text_vi": "IT Staff", "text_en": "IT Staff", "sort_order": 0, "description": "IT Staff"}, {"id": 8156, "taxonomy": "skills", "text": "CRM System", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "crm-system", "text_vi": "CRM System", "text_en": "CRM System", "sort_order": 0, "description": "CRM System"}, {"id": 8157, "taxonomy": "skills", "text": "ASP.NET MVC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "aspnet-mvc", "text_vi": "ASP.NET MVC", "text_en": "ASP.NET MVC", "sort_order": 0, "description": "ASP.NET MVC"}, {"id": 8158, "taxonomy": "skills", "text": "MDM design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mdm-design", "text_vi": "MDM design", "text_en": "MDM design", "sort_order": 0, "description": "MDM design"}, {"id": 8161, "taxonomy": "skills", "text": "Test", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "test", "text_vi": "Test", "text_en": "Test", "sort_order": 0, "description": "Test"}, {"id": 8164, "taxonomy": "skills", "text": "Game Programmer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-programmer", "text_vi": "Game Programmer", "text_en": "Game Programmer", "sort_order": 0, "description": "Game Programmer"}, {"id": 8165, "taxonomy": "skills", "text": "Solid", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "solid", "text_vi": "Solid", "text_en": "Solid", "sort_order": 0, "description": "Solid"}, {"id": 8167, "taxonomy": "skills", "text": "framework", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "framework", "text_vi": "framework", "text_en": "framework", "sort_order": 0, "description": "framework"}, {"id": 8168, "taxonomy": "skills", "text": "SCSS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "scss", "text_vi": "SCSS", "text_en": "SCSS", "sort_order": 0, "description": "SCSS"}, {"id": 8172, "taxonomy": "skills", "text": "Technical Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "technical-consultant", "text_vi": "Technical Consultant", "text_en": "Technical Consultant", "sort_order": 0, "description": "Technical Consultant"}, {"id": 8173, "taxonomy": "skills", "text": "Android Unit Test", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "android-unit-test", "text_vi": "Android Unit Test", "text_en": "Android Unit Test", "sort_order": 0, "description": "Android Unit Test"}, {"id": 8174, "taxonomy": "skills", "text": "Apache", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "apache", "text_vi": "Apache", "text_en": "Apache", "sort_order": 0, "description": "Apache"}, {"id": 8179, "taxonomy": "skills", "text": "PQA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "pqa", "text_vi": "PQA", "text_en": "PQA", "sort_order": 0, "description": "PQA"}, {"id": 8180, "taxonomy": "skills", "text": "Process Quality Assurance", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "process-quality-assurance", "text_vi": "Process Quality Assurance", "text_en": "Process Quality Assurance", "sort_order": 0, "description": "Process Quality Assurance"}, {"id": 8183, "taxonomy": "skills", "text": "Elastic Path", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "elastic-path", "text_vi": "Elastic Path", "text_en": "Elastic Path", "sort_order": 0, "description": "Elastic Path"}, {"id": 8188, "taxonomy": "skills", "text": "Quantitative Researcher", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "quantitative-researcher", "text_vi": "Quantitative Researcher", "text_en": "Quantitative Researcher", "sort_order": 0, "description": "Quantitative Researcher"}, {"id": 8193, "taxonomy": "skills", "text": "Firmware", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "firmware", "text_vi": "Firmware", "text_en": "Firmware", "sort_order": 0, "description": "Firmware"}, {"id": 8194, "taxonomy": "skills", "text": "AVR", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "avr", "text_vi": "AVR", "text_en": "AVR", "sort_order": 0, "description": "AVR"}, {"id": 8196, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "a<PERSON><PERSON><PERSON>", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 8197, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "beaglebone", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": 8198, "taxonomy": "skills", "text": "Native", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "native", "text_vi": "Native", "text_en": "Native", "sort_order": 0, "description": "Native"}, {"id": 82, "taxonomy": "skills", "text": "Zend", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "zend", "text_vi": "Zend", "text_en": "Zend", "sort_order": 0, "description": "Zend"}, {"id": 8200, "taxonomy": "skills", "text": "RTOS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rtos", "text_vi": "RTOS", "text_en": "RTOS", "sort_order": 0, "description": "RTOS"}, {"id": 8205, "taxonomy": "skills", "text": "GraphQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "graphql", "text_vi": "GraphQL", "text_en": "GraphQL", "sort_order": 0, "description": "GraphQL"}, {"id": 8206, "taxonomy": "skills", "text": "Prisma", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "prisma", "text_vi": "Prisma", "text_en": "Prisma", "sort_order": 0, "description": "Prisma"}, {"id": 8208, "taxonomy": "skills", "text": "Lambda", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lambda", "text_vi": "Lambda", "text_en": "Lambda", "sort_order": 0, "description": "Lambda"}, {"id": 8211, "taxonomy": "skills", "text": "IPS/IDS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ips-ids", "text_vi": "IPS/IDS", "text_en": "IPS/IDS", "sort_order": 0, "description": "IPS/IDS"}, {"id": 8212, "taxonomy": "skills", "text": "System Security", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "system-security", "text_vi": "System Security", "text_en": "System Security", "sort_order": 0, "description": "System Security"}, {"id": 8216, "taxonomy": "skills", "text": "RabbitMQ", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rabbitmq", "text_vi": "RabbitMQ", "text_en": "RabbitMQ", "sort_order": 0, "description": "RabbitMQ"}, {"id": 8217, "taxonomy": "skills", "text": "Tensorflow", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tensorflow", "text_vi": "Tensorflow", "text_en": "Tensorflow", "sort_order": 0, "description": "Tensorflow"}, {"id": 8219, "taxonomy": "skills", "text": "Game Asset", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-asset", "text_vi": "Game Asset", "text_en": "Game Asset", "sort_order": 0, "description": "Game Asset"}, {"id": 8221, "taxonomy": "skills", "text": "Arabic", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "arabic", "text_vi": "Arabic", "text_en": "Arabic", "sort_order": 0, "description": "Arabic"}, {"id": 8222, "taxonomy": "skills", "text": "REP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rep", "text_vi": "REP", "text_en": "REP", "sort_order": 0, "description": "REP"}, {"id": 8230, "taxonomy": "skills", "text": "MVC 4", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mvc-4", "text_vi": "MVC 4", "text_en": "MVC 4", "sort_order": 0, "description": "MVC 4"}, {"id": 8231, "taxonomy": "skills", "text": "MVC 5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mvc-5", "text_vi": "MVC 5", "text_en": "MVC 5", "sort_order": 0, "description": "MVC 5"}, {"id": 8232, "taxonomy": "skills", "text": "Entity framework 6", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "entity-framework-6", "text_vi": "Entity framework 6", "text_en": "Entity framework 6", "sort_order": 0, "description": "Entity framework 6"}, {"id": 8233, "taxonomy": "skills", "text": "Solidity", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "solidity", "text_vi": "Solidity", "text_en": "Solidity", "sort_order": 0, "description": "Solidity"}, {"id": 8234, "taxonomy": "skills", "text": "GSM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "gsm", "text_vi": "GSM", "text_en": "GSM", "sort_order": 0, "description": "GSM"}, {"id": 8235, "taxonomy": "skills", "text": "3G", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "3g", "text_vi": "3G", "text_en": "3G", "sort_order": 0, "description": "3G"}, {"id": 8236, "taxonomy": "skills", "text": "4G LTE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "4g-lte", "text_vi": "4G LTE", "text_en": "4G LTE", "sort_order": 0, "description": "4G LTE"}, {"id": 8237, "taxonomy": "skills", "text": "MQTT", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mqtt", "text_vi": "MQTT", "text_en": "MQTT", "sort_order": 0, "description": "MQTT"}, {"id": 8238, "taxonomy": "skills", "text": "LoRa", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lora", "text_vi": "LoRa", "text_en": "LoRa", "sort_order": 0, "description": "LoRa"}, {"id": 8239, "taxonomy": "skills", "text": "TCP/IP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tcpip", "text_vi": "TCP/IP", "text_en": "TCP/IP", "sort_order": 0, "description": "TCP/IP"}, {"id": 8240, "taxonomy": "skills", "text": "SSl", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ssl", "text_vi": "SSl", "text_en": "SSl", "sort_order": 0, "description": "SSl"}, {"id": 8253, "taxonomy": "skills", "text": "Centos 7", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "centos-7", "text_vi": "Centos 7", "text_en": "Centos 7", "sort_order": 0, "description": "Centos 7"}, {"id": 8254, "taxonomy": "skills", "text": "MS Office", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ms-office", "text_vi": "MS Office", "text_en": "MS Office", "sort_order": 0, "description": "MS Office"}, {"id": 8255, "taxonomy": "skills", "text": "PySpark", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "pyspark", "text_vi": "PySpark", "text_en": "PySpark", "sort_order": 0, "description": "PySpark"}, {"id": 8259, "taxonomy": "skills", "text": "Emtuty Famework 6", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "emtuty-famework-6", "text_vi": "Emtuty Famework 6", "text_en": "Emtuty Famework 6", "sort_order": 0, "description": "Emtuty Famework 6"}, {"id": 8269, "taxonomy": "skills", "text": "<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "slim", "text_vi": "<PERSON>", "text_en": "<PERSON>", "sort_order": 0, "description": "<PERSON>"}, {"id": 8274, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "o<PERSON>h", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8275, "taxonomy": "skills", "text": "ActiveMQ", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "activemq", "text_vi": "ActiveMQ", "text_en": "ActiveMQ", "sort_order": 0, "description": "ActiveMQ"}, {"id": 8276, "taxonomy": "skills", "text": "elasticsearch", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "elasticsearch", "text_vi": "elasticsearch", "text_en": "elasticsearch", "sort_order": 0, "description": "elasticsearch"}, {"id": 8278, "taxonomy": "skills", "text": "Adobe Creative Suite", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "adobe-creative-suite", "text_vi": "Adobe Creative Suite", "text_en": "Adobe Creative Suite", "sort_order": 0, "description": "Adobe Creative Suite"}, {"id": 8279, "taxonomy": "skills", "text": "Test Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "test-engineer", "text_vi": "Test Engineer", "text_en": "Test Engineer", "sort_order": 0, "description": "Test Engineer"}, {"id": 8284, "taxonomy": "skills", "text": "Angular 11", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-11", "text_vi": "Angular 11", "text_en": "Angular 11", "sort_order": 0, "description": "Angular 11"}, {"id": 8286, "taxonomy": "skills", "text": "English Skills", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "english-skills", "text_vi": "English Skills", "text_en": "English Skills", "sort_order": 0, "description": "English Skills"}, {"id": 8287, "taxonomy": "skills", "text": "GitFlow", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "gitflow", "text_vi": "GitFlow", "text_en": "GitFlow", "sort_order": 0, "description": "GitFlow"}, {"id": 8291, "taxonomy": "skills", "text": "Cocoa Touch", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cocoa-touch", "text_vi": "Cocoa Touch", "text_en": "Cocoa Touch", "sort_order": 0, "description": "Cocoa Touch"}, {"id": 8292, "taxonomy": "skills", "text": "Visual Designer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "visual-designer", "text_vi": "Visual Designer", "text_en": "Visual Designer", "sort_order": 0, "description": "Visual Designer"}, {"id": 8298, "taxonomy": "skills", "text": "ASP.NET MVC 5", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "asp-net-mvc-5", "text_vi": "ASP.NET MVC 5", "text_en": "ASP.NET MVC 5", "sort_order": 0, "description": "ASP.NET MVC 5"}, {"id": 8299, "taxonomy": "skills", "text": "Entity Framework", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "entity-framework", "text_vi": "Entity Framework", "text_en": "Entity Framework", "sort_order": 0, "description": "Entity Framework"}, {"id": 83, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "drupal", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8304, "taxonomy": "skills", "text": "Application Security", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "application-security", "text_vi": "Application Security", "text_en": "Application Security", "sort_order": 0, "description": "Application Security"}, {"id": 8306, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "phan-mem-dich-vu", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8308, "taxonomy": "skills", "text": "Entity Framework Core", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "entity-framework-core", "text_vi": "Entity Framework Core", "text_en": "Entity Framework Core", "sort_order": 0, "description": "Entity Framework Core"}, {"id": 8309, "taxonomy": "skills", "text": "SQA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sqa", "text_vi": "SQA", "text_en": "SQA", "sort_order": 0, "description": "SQA"}, {"id": 8313, "taxonomy": "skills", "text": "UE4", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ue4", "text_vi": "UE4", "text_en": "UE4", "sort_order": 0, "description": "UE4"}, {"id": 8314, "taxonomy": "skills", "text": "Zbrush", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "zbrush", "text_vi": "Zbrush", "text_en": "Zbrush", "sort_order": 0, "description": "Zbrush"}, {"id": 8317, "taxonomy": "skills", "text": "React/Typescript", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "react-typescript", "text_vi": "React/Typescript", "text_en": "React/Typescript", "sort_order": 0, "description": "React/Typescript"}, {"id": 8318, "taxonomy": "skills", "text": "control system", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "control-system", "text_vi": "control system", "text_en": "control system", "sort_order": 0, "description": "control system"}, {"id": 8321, "taxonomy": "skills", "text": "IT Administrator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-administrator", "text_vi": "IT Administrator", "text_en": "IT Administrator", "sort_order": 0, "description": "IT Administrator"}, {"id": 8325, "taxonomy": "skills", "text": "NativeScript", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nativescript", "text_vi": "NativeScript", "text_en": "NativeScript", "sort_order": 0, "description": "NativeScript"}, {"id": 8328, "taxonomy": "skills", "text": "Robot", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "robot", "text_vi": "Robot", "text_en": "Robot", "sort_order": 0, "description": "Robot"}, {"id": 8333, "taxonomy": "skills", "text": "TDD", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "tdd", "text_vi": "TDD", "text_en": "TDD", "sort_order": 0, "description": "TDD"}, {"id": 8338, "taxonomy": "skills", "text": "JavaEE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "javaee", "text_vi": "JavaEE", "text_en": "JavaEE", "sort_order": 0, "description": "JavaEE"}, {"id": 8340, "taxonomy": "skills", "text": "Microsoft Dynamics 365", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "microsoft-dynamics-365", "text_vi": "Microsoft Dynamics 365", "text_en": "Microsoft Dynamics 365", "sort_order": 0, "description": "Microsoft Dynamics 365"}, {"id": 8341, "taxonomy": "skills", "text": "LS Central", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ls-central", "text_vi": "LS Central", "text_en": "LS Central", "sort_order": 0, "description": "LS Central"}, {"id": 8342, "taxonomy": "skills", "text": "Jet Global", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jet-global", "text_vi": "Jet Global", "text_en": "Jet Global", "sort_order": 0, "description": "Jet Global"}, {"id": 8352, "taxonomy": "skills", "text": "IT Specialist", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-specialist", "text_vi": "IT Specialist", "text_en": "IT Specialist", "sort_order": 0, "description": "IT Specialist"}, {"id": 8353, "taxonomy": "skills", "text": "Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "design", "text_vi": "Design", "text_en": "Design", "sort_order": 0, "description": "Design"}, {"id": 8354, "taxonomy": "skills", "text": "React JS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "react-js", "text_vi": "React JS", "text_en": "React JS", "sort_order": 0, "description": "React JS"}, {"id": 8358, "taxonomy": "skills", "text": "De-fi", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "de-fi", "text_vi": "De-fi", "text_en": "De-fi", "sort_order": 0, "description": "De-fi"}, {"id": 8370, "taxonomy": "skills", "text": "SQS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sqs", "text_vi": "SQS", "text_en": "SQS", "sort_order": 0, "description": "SQS"}, {"id": 8375, "taxonomy": "skills", "text": "Memcache", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "memcache", "text_vi": "Memcache", "text_en": "Memcache", "sort_order": 0, "description": "Memcache"}, {"id": 8378, "taxonomy": "skills", "text": "Balsamiq", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "balsamiq", "text_vi": "Balsamiq", "text_en": "Balsamiq", "sort_order": 0, "description": "Balsamiq"}, {"id": 8379, "taxonomy": "skills", "text": "Manual Test", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "manual-test", "text_vi": "Manual Test", "text_en": "Manual Test", "sort_order": 0, "description": "Manual Test"}, {"id": 8384, "taxonomy": "skills", "text": "Angular 7+", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-7-8", "text_vi": "Angular 7+", "text_en": "Angular 7+", "sort_order": 0, "description": "Angular 7+"}, {"id": 8385, "taxonomy": "skills", "text": "Game Mobile", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-mobile", "text_vi": "Game Mobile", "text_en": "Game Mobile", "sort_order": 0, "description": "Game Mobile"}, {"id": 8386, "taxonomy": "skills", "text": "Document Analyst", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "document-analyst", "text_vi": "Document Analyst", "text_en": "Document Analyst", "sort_order": 0, "description": "Document Analyst"}, {"id": 8389, "taxonomy": "skills", "text": "Kafka", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "kafka", "text_vi": "Kafka", "text_en": "Kafka", "sort_order": 0, "description": "Kafka"}, {"id": 8391, "taxonomy": "skills", "text": "LiveOps", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "liveops", "text_vi": "LiveOps", "text_en": "LiveOps", "sort_order": 0, "description": "LiveOps"}, {"id": 8393, "taxonomy": "skills", "text": "3D Designer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "3d-designer", "text_vi": "3D Designer", "text_en": "3D Designer", "sort_order": 0, "description": "- 3D Designer"}, {"id": 8394, "taxonomy": "skills", "text": "Angular 10+", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-10-11", "text_vi": "Angular 10+", "text_en": "Angular 10+", "sort_order": 0, "description": "Angular 10+"}, {"id": 8396, "taxonomy": "skills", "text": "Functional Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "functional-consultant", "text_vi": "Functional Consultant", "text_en": "Functional Consultant", "sort_order": 0, "description": "Functional Consultant"}, {"id": 84, "taxonomy": "skills", "text": "WordPress", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "wordpress", "text_vi": "WordPress", "text_en": "WordPress", "sort_order": 0, "description": "WordPress"}, {"id": 8425, "taxonomy": "skills", "text": "VHDL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "vhdl", "text_vi": "VHDL", "text_en": "VHDL", "sort_order": 0, "description": "VHDL"}, {"id": 8426, "taxonomy": "skills", "text": "EDA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "eda", "text_vi": "EDA", "text_en": "EDA", "sort_order": 0, "description": "EDA"}, {"id": 8427, "taxonomy": "skills", "text": "Power BI", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "power-bi-specialist", "text_vi": "Power BI", "text_en": "Power BI", "sort_order": 0, "description": "Power BI"}, {"id": 8428, "taxonomy": "skills", "text": "Digital Operation Specialist", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "digital-operation-specialist", "text_vi": "Digital Operation Specialist", "text_en": "Digital Operation Specialist", "sort_order": 0, "description": "Digital Operation Specialist"}, {"id": 8436, "taxonomy": "skills", "text": "Data Operation", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-operation", "text_vi": "Data Operation", "text_en": "Data Operation", "sort_order": 0, "description": "Data Operation"}, {"id": 8438, "taxonomy": "skills", "text": "gRPC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "grpc", "text_vi": "gRPC", "text_en": "gRPC", "sort_order": 0, "description": "gRPC"}, {"id": 8440, "taxonomy": "skills", "text": "Firestore", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "firestore", "text_vi": "Firestore", "text_en": "Firestore", "sort_order": 0, "description": "Firestore"}, {"id": 8441, "taxonomy": "skills", "text": "Elixir - Rails", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "elixir-rails", "text_vi": "Elixir - Rails", "text_en": "Elixir - Rails", "sort_order": 0, "description": "Elixir - Rails"}, {"id": 8444, "taxonomy": "skills", "text": "Google Cloud Platform - Rollbar", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "google-cloud-platform-rollbar", "text_vi": "Google Cloud Platform - Rollbar", "text_en": "Google Cloud Platform - Rollbar", "sort_order": 0, "description": "Google Cloud Platform - Rollbar"}, {"id": 8445, "taxonomy": "skills", "text": "NewRelic", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "newrelic", "text_vi": "NewRelic", "text_en": "NewRelic", "sort_order": 0, "description": "NewRelic"}, {"id": 8446, "taxonomy": "skills", "text": "<PERSON><PERSON>ck", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "slack", "text_vi": "<PERSON><PERSON>ck", "text_en": "<PERSON><PERSON>ck", "sort_order": 0, "description": "<PERSON><PERSON>ck"}, {"id": 8447, "taxonomy": "skills", "text": "Trello", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "trello", "text_vi": "Trello", "text_en": "Trello", "sort_order": 0, "description": "Trello"}, {"id": 8448, "taxonomy": "skills", "text": "Technical Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "technical-engineer", "text_vi": "Technical Engineer", "text_en": "Technical Engineer", "sort_order": 0, "description": "Technical Engineer"}, {"id": 8449, "taxonomy": "skills", "text": "Memca<PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "memcached", "text_vi": "Memca<PERSON>", "text_en": "Memca<PERSON>", "sort_order": 0, "description": "Memca<PERSON>"}, {"id": 8451, "taxonomy": "skills", "text": "<PERSON>e", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "jee", "text_vi": "<PERSON>e", "text_en": "<PERSON>e", "sort_order": 0, "description": "<PERSON>e"}, {"id": 8462, "taxonomy": "skills", "text": "3CX", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "3cx", "text_vi": "3CX", "text_en": "3CX", "sort_order": 0, "description": "3CX"}, {"id": 8467, "taxonomy": "skills", "text": "Sharepoint framework", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sharepoint-framework", "text_vi": "Sharepoint framework", "text_en": "Sharepoint framework", "sort_order": 0, "description": "Sharepoint framework"}, {"id": 8469, "taxonomy": "skills", "text": "NPM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "npm", "text_vi": "NPM", "text_en": "NPM", "sort_order": 0, "description": "NPM"}, {"id": 8473, "taxonomy": "skills", "text": "LinQ", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "linq", "text_vi": "LinQ", "text_en": "LinQ", "sort_order": 0, "description": "LinQ"}, {"id": 8474, "taxonomy": "skills", "text": "IT Auditor", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-auditor", "text_vi": "IT Auditor", "text_en": "IT Auditor", "sort_order": 0, "description": "IT Auditor"}, {"id": 8477, "taxonomy": "skills", "text": "MFC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mfc", "text_vi": "MFC", "text_en": "MFC", "sort_order": 0, "description": "MFC"}, {"id": 8480, "taxonomy": "skills", "text": "Java Swing", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "java-swing", "text_vi": "Java Swing", "text_en": "Java Swing", "sort_order": 0, "description": "Java Swing"}, {"id": 8482, "taxonomy": "skills", "text": "SAP Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sap-consultant", "text_vi": "SAP Consultant", "text_en": "SAP Consultant", "sort_order": 0, "description": "SAP Consultant"}, {"id": 8483, "taxonomy": "skills", "text": "ERP Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "erp-consultant", "text_vi": "ERP Consultant", "text_en": "ERP Consultant", "sort_order": 0, "description": "ERP Consultant"}, {"id": 8488, "taxonomy": "skills", "text": "ES", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "es", "text_vi": "ES", "text_en": "ES", "sort_order": 0, "description": "ES"}, {"id": 8489, "taxonomy": "skills", "text": "CCNP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ccnp", "text_vi": "CCNP", "text_en": "CCNP", "sort_order": 0, "description": "CCNP"}, {"id": 8494, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "elixir", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8495, "taxonomy": "skills", "text": "SysOps", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sysops", "text_vi": "SysOps", "text_en": "SysOps", "sort_order": 0, "description": "SysOps"}, {"id": 8496, "taxonomy": "skills", "text": "AR", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ar", "text_vi": "AR", "text_en": "AR", "sort_order": 0, "description": "AR"}, {"id": 8497, "taxonomy": "skills", "text": "IT Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-consultant", "text_vi": "IT Consultant", "text_en": "IT Consultant", "sort_order": 0, "description": "IT Consultant"}, {"id": 8498, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>ber", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cucumber", "text_vi": "<PERSON><PERSON><PERSON>ber", "text_en": "<PERSON><PERSON><PERSON>ber", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>ber"}, {"id": 85, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "j<PERSON><PERSON>", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8503, "taxonomy": "skills", "text": "IT Support Specialist", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-support-specialist", "text_vi": "IT Support Specialist", "text_en": "IT Support Specialist", "sort_order": 0, "description": "IT Support Specialist"}, {"id": 8512, "taxonomy": "skills", "text": "SAP Consultant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sap-consultant-2", "text_vi": "SAP Consultant", "text_en": "SAP Consultant", "sort_order": 0, "description": "SAP Consultant"}, {"id": 8515, "taxonomy": "skills", "text": "Kubernete", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "kubernete", "text_vi": "Kubernete", "text_en": "Kubernete", "sort_order": 0, "description": "Kubernete"}, {"id": 8517, "taxonomy": "skills", "text": "Automation Developer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "automation-developer", "text_vi": "Automation Developer", "text_en": "Automation Developer", "sort_order": 0, "description": "Automation Developer"}, {"id": 8518, "taxonomy": "skills", "text": "5G", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "5g", "text_vi": "5G", "text_en": "5G", "sort_order": 0, "description": "5G"}, {"id": 8521, "taxonomy": "skills", "text": "Game NFT", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-nft", "text_vi": "Game NFT", "text_en": "Game NFT", "sort_order": 0, "description": "Game NFT"}, {"id": 8529, "taxonomy": "skills", "text": "Data Architecture", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-architecture", "text_vi": "Data Architecture", "text_en": "Data Architecture", "sort_order": 0, "description": "Data Architecture"}, {"id": 8530, "taxonomy": "skills", "text": "Computer Science", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "computer-science", "text_vi": "Computer Science", "text_en": "Computer Science", "sort_order": 0, "description": "Computer Science"}, {"id": 8534, "taxonomy": "skills", "text": "Functional Analyst", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "functional-analyst", "text_vi": "Functional Analyst", "text_en": "Functional Analyst", "sort_order": 0, "description": "Functional Analyst"}, {"id": 8536, "taxonomy": "skills", "text": "Liquid", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "liquid", "text_vi": "Liquid", "text_en": "Liquid", "sort_order": 0, "description": "Liquid"}, {"id": 8541, "taxonomy": "skills", "text": "Outsystem", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "outsystem", "text_vi": "Outsystem", "text_en": "Outsystem", "sort_order": 0, "description": "Outsystem"}, {"id": 8547, "taxonomy": "skills", "text": "Erpnext", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "erpnext", "text_vi": "Erpnext", "text_en": "Erpnext", "sort_order": 0, "description": "Erpnext"}, {"id": 8550, "taxonomy": "skills", "text": "BrightScript", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "brightscript", "text_vi": "BrightScript", "text_en": "BrightScript", "sort_order": 0, "description": "BrightScript"}, {"id": 8551, "taxonomy": "skills", "text": "ROKU", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "roku", "text_vi": "ROKU", "text_en": "ROKU", "sort_order": 0, "description": "ROKU"}, {"id": 8554, "taxonomy": "skills", "text": "BPO", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bpo", "text_vi": "BPO", "text_en": "BPO", "sort_order": 0, "description": "BPO"}, {"id": 8556, "taxonomy": "skills", "text": "IT System", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-system", "text_vi": "IT System", "text_en": "IT System", "sort_order": 0, "description": "IT System"}, {"id": 8557, "taxonomy": "skills", "text": "Analyst", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "analyst", "text_vi": "Analyst", "text_en": "Analyst", "sort_order": 0, "description": "Analyst"}, {"id": 8580, "taxonomy": "skills", "text": "Nuxt.js", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nuxt-js", "text_vi": "Nuxt.js", "text_en": "Nuxt.js", "sort_order": 0, "description": "Nuxt.js"}, {"id": 8582, "taxonomy": "skills", "text": "dEPM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "depm", "text_vi": "dEPM", "text_en": "dEPM", "sort_order": 0, "description": "dEPM"}, {"id": 8583, "taxonomy": "skills", "text": "Syteline", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "syteline", "text_vi": "Syteline", "text_en": "Syteline", "sort_order": 0, "description": "Syteline"}, {"id": 8584, "taxonomy": "skills", "text": "Cloud Migration Service (AWS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cloud-migration-service-aws", "text_vi": "Cloud Migration Service (AWS", "text_en": "Cloud Migration Service (AWS", "sort_order": 0, "description": "Cloud Migration Service (AWS"}, {"id": 8588, "taxonomy": "skills", "text": "Automation QC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "automation-qc", "text_vi": "Automation QC", "text_en": "Automation QC", "sort_order": 0, "description": "Automation QC"}, {"id": 8589, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cosos", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8590, "taxonomy": "skills", "text": "Hardware Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "hardware-design", "text_vi": "Hardware Design", "text_en": "Hardware Design", "sort_order": 0, "description": "Hardware Design"}, {"id": 8591, "taxonomy": "skills", "text": "IT Outsource", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-outsource", "text_vi": "IT Outsource", "text_en": "IT Outsource", "sort_order": 0, "description": "IT Outsource"}, {"id": 8592, "taxonomy": "skills", "text": "ITIL/ITSM", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "itil-itsm", "text_vi": "ITIL/ITSM", "text_en": "ITIL/ITSM", "sort_order": 0, "description": "ITIL/ITSM"}, {"id": 8594, "taxonomy": "skills", "text": "ES6", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "es6", "text_vi": "ES6", "text_en": "ES6", "sort_order": 0, "description": "ES6"}, {"id": 8595, "taxonomy": "skills", "text": "XML", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "xml", "text_vi": "XML", "text_en": "XML", "sort_order": 0, "description": "XML"}, {"id": 8596, "taxonomy": "skills", "text": "Web Service", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "web-service", "text_vi": "Web Service", "text_en": "Web Service", "sort_order": 0, "description": "Web Service"}, {"id": 8597, "taxonomy": "skills", "text": "Data Administrator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-administrator", "text_vi": "Data Administrator", "text_en": "Data Administrator", "sort_order": 0, "description": "Data Administrator"}, {"id": 8598, "taxonomy": "skills", "text": "Nest JS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "nest-js", "text_vi": "Nest JS", "text_en": "Nest JS", "sort_order": 0, "description": "Nest JS"}, {"id": 86, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "xamarin", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 8601, "taxonomy": "skills", "text": "2D Animator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "2d-animator", "text_vi": "2D Animator", "text_en": "2D Animator", "sort_order": 0, "description": "2D Animator"}, {"id": 8602, "taxonomy": "skills", "text": "Assistant", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "assistant", "text_vi": "Assistant", "text_en": "Assistant", "sort_order": 0, "description": "Assistant"}, {"id": 8603, "taxonomy": "skills", "text": "RPA using UiPath", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rpa-using-uipath", "text_vi": "RPA using UiPath", "text_en": "RPA using UiPath", "sort_order": 0, "description": "RPA using UiPath"}, {"id": 8604, "taxonomy": "skills", "text": "Character Artist", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "character-artist", "text_vi": "Character Artist", "text_en": "Character Artist", "sort_order": 0, "description": "Character Artist"}, {"id": 8606, "taxonomy": "skills", "text": "Communicator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "communicator", "text_vi": "Communicator", "text_en": "Communicator", "sort_order": 0, "description": "Communicator"}, {"id": 8608, "taxonomy": "skills", "text": "System Test", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "system-test", "text_vi": "System Test", "text_en": "System Test", "sort_order": 0, "description": "System Test"}, {"id": 8610, "taxonomy": "skills", "text": "Angular 6+", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "angular-6", "text_vi": "Angular 6+", "text_en": "Angular 6+", "sort_order": 0, "description": "Angular 6+"}, {"id": 8611, "taxonomy": "skills", "text": "UI/UX", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "uiux", "text_vi": "UI/UX", "text_en": "UI/UX", "sort_order": 0, "description": "UI/UX"}, {"id": 8612, "taxonomy": "skills", "text": "RPA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "rpa", "text_vi": "RPA", "text_en": "RPA", "sort_order": 0, "description": "RPA"}, {"id": 8614, "taxonomy": "skills", "text": "Business Coordinator", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "business-coordinator", "text_vi": "Business Coordinator", "text_en": "Business Coordinator", "sort_order": 0, "description": "Business Coordinator"}, {"id": 8615, "taxonomy": "skills", "text": "Photon", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "photon", "text_vi": "Photon", "text_en": "Photon", "sort_order": 0, "description": "Photon"}, {"id": 8616, "taxonomy": "skills", "text": "Game Developer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "game-developer", "text_vi": "Game Developer", "text_en": "Game Developer", "sort_order": 0, "description": "Game Developer"}, {"id": 8619, "taxonomy": "skills", "text": "AX2012 R2", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ax2012-r2", "text_vi": "AX2012 R2", "text_en": "AX2012 R2", "sort_order": 0, "description": "AX2012 R2"}, {"id": 8620, "taxonomy": "skills", "text": "Microsoft Dynamics AX 2009", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "microsoft-dynamics-ax-2009", "text_vi": "Microsoft Dynamics AX 2009", "text_en": "Microsoft Dynamics AX 2009", "sort_order": 0, "description": "Microsoft Dynamics AX 2009"}, {"id": 8621, "taxonomy": "skills", "text": "D365 FO", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "d365-fo", "text_vi": "D365 FO", "text_en": "D365 FO", "sort_order": 0, "description": "D365 FO"}, {"id": 8622, "taxonomy": "skills", "text": "Swing", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "swing", "text_vi": "Swing", "text_en": "Swing", "sort_order": 0, "description": "Swing"}, {"id": 8623, "taxonomy": "skills", "text": "System Architect", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "system-architect", "text_vi": "System Architect", "text_en": "System Architect", "sort_order": 0, "description": "System Architect"}, {"id": 8624, "taxonomy": "skills", "text": "Java Spring", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "java-spring", "text_vi": "Java Spring", "text_en": "Java Spring", "sort_order": 0, "description": "Java Spring"}, {"id": 8625, "taxonomy": "skills", "text": "Postgre", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "postgre", "text_vi": "Postgre", "text_en": "Postgre", "sort_order": 0, "description": "Postgre"}, {"id": 8626, "taxonomy": "skills", "text": "IT Infrastructure", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-infrastructure", "text_vi": "IT Infrastructure", "text_en": "IT Infrastructure", "sort_order": 0, "description": "IT Infrastructure"}, {"id": 8627, "taxonomy": "skills", "text": "VBA", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "vba", "text_vi": "VBA", "text_en": "VBA", "sort_order": 0, "description": "VBA"}, {"id": 8634, "taxonomy": "skills", "text": "no", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "no", "text_vi": "no", "text_en": "no", "sort_order": 0, "description": "no"}, {"id": 8637, "taxonomy": "skills", "text": "PostgreSql", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "postgresql", "text_vi": "PostgreSql", "text_en": "PostgreSql", "sort_order": 0, "description": "PostgreSql"}, {"id": 8649, "taxonomy": "skills", "text": "Software Engineer", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "software-engineer", "text_vi": "Software Engineer", "text_en": "Software Engineer", "sort_order": 0, "description": "Software Engineer"}, {"id": 8655, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "grpc-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "gRPC"}, {"id": 8685, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "character-artist-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Character Artist"}, {"id": 8689, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-outsource-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "IT Outsource"}, {"id": 87, "taxonomy": "skills", "text": "Titanium", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "titanium", "text_vi": "Titanium", "text_en": "Titanium", "sort_order": 0, "description": "Titanium"}, {"id": 8700, "taxonomy": "skills", "text": "Restful API", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "Restful API", "slug": "restful-api-1", "text_vi": "Restful API", "text_en": "Restful API", "sort_order": 0, "description": "Restful API"}, {"id": 8706, "taxonomy": "skills", "text": "Restful Api", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "restful-api", "text_vi": "Restful Api", "text_en": "Restful Api", "sort_order": 0, "description": "Restful Api"}, {"id": 8707, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "analyst-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Analyst"}, {"id": 8711, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "liquid-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Liquid"}, {"id": 8729, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "system-test-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "System Test"}, {"id": 8732, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "itil-itsm-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "ITIL/ITSM"}, {"id": 8743, "taxonomy": "skills", "text": "Shell", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "shell-1", "text_vi": "Shell", "text_en": "Shell", "sort_order": 0, "description": "Shell"}, {"id": 8744, "taxonomy": "skills", "text": "<PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bash-1", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON>"}, {"id": 8752, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "postgre-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Postgre"}, {"id": 8754, "taxonomy": "skills", "text": "Scikit-learn", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "scikit-learn", "text_vi": "Scikit-learn", "text_en": "Scikit-learn", "sort_order": 0, "description": "Scikit-learn"}, {"id": 8755, "taxonomy": "skills", "text": "bash", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bash", "text_vi": "bash", "text_en": "bash", "sort_order": 0, "description": "bash"}, {"id": 8757, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "business-coordinator-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Business Coordinator"}, {"id": 8761, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-support-specialist-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "IT Support Specialist"}, {"id": 8762, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "data-administrator-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Data Administrator"}, {"id": 8763, "taxonomy": "skills", "text": "shell", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "shell", "text_vi": "shell", "text_en": "shell", "sort_order": 0, "description": "shell"}, {"id": 8766, "taxonomy": "skills", "text": "Terraform", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "terraform", "text_vi": "Terraform", "text_en": "Terraform", "sort_order": 0, "description": "Terraform"}, {"id": 8788, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "linq-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "LinQ"}, {"id": 88, "taxonomy": "skills", "text": "CocoonJS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "cocoonjs", "text_vi": "CocoonJS", "text_en": "CocoonJS", "sort_order": 0, "description": "CocoonJS"}, {"id": 8803, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "network-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Network"}, {"id": 8805, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "windows-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Windows"}, {"id": 8813, "taxonomy": "skills", "text": "UX/UI Design", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ux-ui-design", "text_vi": "UX/UI Design", "text_en": "UX/UI Design", "sort_order": 0, "description": "UX/UI Design"}, {"id": 8824, "taxonomy": "skills", "text": "Laravel 8", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "laravel-8", "text_vi": "Laravel 8", "text_en": "Laravel 8", "sort_order": 0, "description": "Laravel 8"}, {"id": 8827, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "motion-graphic-design-2", "text_vi": "", "text_en": "", "sort_order": 0, "description": "Motion Graphic Design"}, {"id": 8830, "taxonomy": "skills", "text": "", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ci-cd", "text_vi": "", "text_en": "", "sort_order": 0, "description": "CI/CD"}, {"id": 8835, "taxonomy": "skills", "text": "MongoSQL", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mongosql", "text_vi": "MongoSQL", "text_en": "MongoSQL", "sort_order": 0, "description": "MongoSQL"}, {"id": 89, "taxonomy": "skills", "text": "Bootstrap", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bootstrap", "text_vi": "Bootstrap", "text_en": "Bootstrap", "sort_order": 0, "description": "Bootstrap"}, {"id": 9, "taxonomy": "skills", "text": "iOS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ios", "text_vi": "iOS", "text_en": "iOS", "sort_order": 0, "description": "iOS"}, {"id": 90, "taxonomy": "skills", "text": "MongoDB", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "mongodb", "text_vi": "MongoDB", "text_en": "MongoDB", "sort_order": 0, "description": "MongoDB"}, {"id": 91, "taxonomy": "skills", "text": "Redis", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "redis", "text_vi": "Redis", "text_en": "Redis", "sort_order": 0, "description": "Redis"}, {"id": 92, "taxonomy": "skills", "text": "Websocket", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "websocket", "text_vi": "Websocket", "text_en": "Websocket", "sort_order": 0, "description": "Websocket"}, {"id": 9209, "taxonomy": "skills", "text": "Apex", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "apex-1", "text_vi": "Apex", "text_en": "Apex", "sort_order": null, "description": "Apex"}, {"id": 9210, "taxonomy": "skills", "text": "LWC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lwc-1", "text_vi": "LWC", "text_en": "LWC", "sort_order": null, "description": "LWC"}, {"id": 9211, "taxonomy": "skills", "text": "Apex", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "apex", "text_vi": "Apex", "text_en": "Apex", "sort_order": 0, "description": "Apex"}, {"id": 9212, "taxonomy": "skills", "text": "LWC", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "lwc", "text_vi": "LWC", "text_en": "LWC", "sort_order": 0, "description": "LWC"}, {"id": 9214, "taxonomy": "skills", "text": "ASPX", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "aspx", "text_vi": "ASPX", "text_en": "ASPX", "sort_order": 0, "description": "ASPX"}, {"id": 9220, "taxonomy": "skills", "text": "Autosar", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "autosar", "text_vi": "Autosar", "text_en": "Autosar", "sort_order": null, "description": "Autosar"}, {"id": 9223, "taxonomy": "skills", "text": "HTML/CSS", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "htmlcss", "text_vi": "HTML/CSS", "text_en": "HTML/CSS", "sort_order": 0, "description": "HTML/CSS"}, {"id": 9224, "taxonomy": "skills", "text": "Node.Js", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "node-js", "text_vi": "Node.Js", "text_en": "Node.Js", "sort_order": 0, "description": "Node.Js"}, {"id": 9226, "taxonomy": "skills", "text": "WordPress Customize", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "wordpress-customize", "text_vi": "WordPress Customize", "text_en": "WordPress Customize", "sort_order": 0, "description": "WordPress Customize"}, {"id": 93, "taxonomy": "skills", "text": "Socket.io", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "socketio", "text_vi": "Socket.io", "text_en": "Socket.io", "sort_order": 0, "description": "Socket.io"}, {"id": 95, "taxonomy": "skills", "text": "IT Helpdesk", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "it-helpdesk", "text_vi": "IT Helpdesk", "text_en": "IT Helpdesk", "sort_order": 0, "description": "IT Helpdesk"}, {"id": 951, "taxonomy": "skills", "text": "Bitbucket/ Bamboo/ Jenkins", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "bitbucket-bamboo-jenkins", "text_vi": "Bitbucket/ Bamboo/ Jenkins", "text_en": "Bitbucket/ Bamboo/ Jenkins", "sort_order": 0, "description": "Bitbucket/ Bamboo/ Jenkins"}, {"id": 9539, "taxonomy": "skills", "text": "Simulink", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "simulink", "text_vi": "Simulink", "text_en": "Simulink", "sort_order": 0, "description": "Simulink"}, {"id": 96, "taxonomy": "skills", "text": "Odoo", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "odoo", "text_vi": "Odoo", "text_en": "Odoo", "sort_order": 0, "description": "Odoo"}, {"id": 964, "taxonomy": "skills", "text": "Oracle Rac/ SQL Server", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oracle-rac-sql-server", "text_vi": "Oracle Rac/ SQL Server", "text_en": "Oracle Rac/ SQL Server", "sort_order": 0, "description": "Oracle Rac/ SQL Server"}, {"id": 97, "taxonomy": "skills", "text": "<PERSON><PERSON><PERSON>", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "github", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0, "description": "<PERSON><PERSON><PERSON>"}, {"id": 973, "taxonomy": "skills", "text": "Oca/ Ocp", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "oca-ocp", "text_vi": "Oca/ Ocp", "text_en": "Oca/ Ocp", "sort_order": 0, "description": "Oca/ Ocp"}, {"id": 978, "taxonomy": "skills", "text": "Ccna/ CCNP", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "ccna-ccnp", "text_vi": "Ccna/ CCNP", "text_en": "Ccna/ CCNP", "sort_order": 0, "description": "Ccna/ CCNP"}, {"id": 98, "taxonomy": "skills", "text": "WCF", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "wcf", "text_vi": "WCF", "text_en": "WCF", "sort_order": 0, "description": "WCF"}, {"id": 99, "taxonomy": "skills", "text": "SQL Server", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "", "slug": "sql-server", "text_vi": "SQL Server", "text_en": "SQL Server", "sort_order": 0, "description": "SQL Server"}], "salary_range": [{"id": 7393, "taxonomy": "salary_range", "text": "Dưới $300", "slug": "duoi-300", "text_vi": "Dưới $300", "text_en": "Dưới $300", "sort_order": 0}, {"id": 7394, "taxonomy": "salary_range", "text": "$300 - $500", "slug": "300-500", "text_vi": "$300 - $500", "text_en": "$300 - $500", "sort_order": 0}, {"id": 7395, "taxonomy": "salary_range", "text": "$500 - $700", "slug": "500-700", "text_vi": "$500 - $700", "text_en": "$500 - $700", "sort_order": 0}, {"id": 7396, "taxonomy": "salary_range", "text": "$700 - $1000", "slug": "700-1000", "text_vi": "$700 - $1000", "text_en": "$700 - $1000", "sort_order": 0}, {"id": 7397, "taxonomy": "salary_range", "text": "$1000 - $1200", "slug": "1000-1200", "text_vi": "$1000 - $1200", "text_en": "$1000 - $1200", "sort_order": 0}, {"id": 7398, "taxonomy": "salary_range", "text": "$1200 - $1500", "slug": "1200-1500", "text_vi": "$1200 - $1500", "text_en": "$1200 - $1500", "sort_order": 0}, {"id": 7399, "taxonomy": "salary_range", "text": "$1500 - $2000", "slug": "1500-2000", "text_vi": "$1500 - $2000", "text_en": "$1500 - $2000", "sort_order": 0}, {"id": 7400, "taxonomy": "salary_range", "text": "Trên $2000", "slug": "tren-2000", "text_vi": "Trên $2000", "text_en": "Trên $2000", "sort_order": 0}], "job_levels": [{"id": 1616, "taxonomy": "job_levels", "text": "Intern", "slug": "intern", "text_vi": "Intern", "text_en": "Intern", "sort_order": 0}, {"id": 1617, "taxonomy": "job_levels", "text": "Fresher", "slug": "fresher", "text_vi": "Fresher", "text_en": "Fresher", "sort_order": 0}, {"id": 8664, "taxonomy": "job_levels", "text": "Junior", "slug": "junior", "text_vi": "Junior", "text_en": "Junior", "sort_order": 0}, {"id": 8665, "taxonomy": "job_levels", "text": "Middle", "slug": "middle", "text_vi": "Middle", "text_en": "Middle", "sort_order": 0}, {"id": 8666, "taxonomy": "job_levels", "text": "Senior", "slug": "senior", "text_vi": "Senior", "text_en": "Senior", "sort_order": 0}, {"id": 7276, "taxonomy": "job_levels", "text": "Trưởng Nhóm", "slug": "truong-nhom", "text_vi": "Trưởng Nhóm", "text_en": "Leader", "sort_order": 9}, {"id": 1620, "taxonomy": "job_levels", "text": "Trưởng phòng", "slug": "truong-phong", "text_vi": "Trưởng phòng", "text_en": "Manager", "sort_order": 10}, {"id": 8765, "taxonomy": "job_levels", "text": "All Levels", "slug": "all-level", "text_vi": "All Levels", "text_en": "All Levels", "sort_order": 12}], "job_types": [{"id": 8792, "taxonomy": "job_types", "text": "In Office", "slug": "in-office", "text_vi": "In Office", "text_en": "In Office", "sort_order": 0}, {"id": 8642, "taxonomy": "job_types", "text": "Hybrid", "slug": "hybrid", "text_vi": "Hybrid", "text_en": "Hybrid", "sort_order": 1}, {"id": 1623, "taxonomy": "job_types", "text": "Remote", "slug": "remote", "text_vi": "Remote", "text_en": "Remote", "sort_order": 3}, {"id": 1625, "taxonomy": "job_types", "text": "Oversea", "slug": "oversea", "text_vi": "Oversea", "text_en": "Oversea", "sort_order": 4}], "num_employees": [{"id": 4278, "taxonomy": "num_employees", "text": "Dưới 10", "slug": "less-than-10", "text_vi": "Nhỏ hơn 10", "text_en": "Less than 10", "sort_order": 0}, {"id": 4279, "taxonomy": "num_employees", "text": "10-24", "slug": "10-24", "text_vi": "10-24", "text_en": "10-24", "sort_order": 0}, {"id": 4280, "taxonomy": "num_employees", "text": "25-99", "slug": "25-99", "text_vi": "25-99", "text_en": "25-99", "sort_order": 0}, {"id": 4282, "taxonomy": "num_employees", "text": "100-499", "slug": "100-499", "text_vi": "100-499", "text_en": "100-499", "sort_order": 0}, {"id": 4283, "taxonomy": "num_employees", "text": "500-999", "slug": "500-999", "text_vi": "500-999", "text_en": "500-999", "sort_order": 0}, {"id": 4284, "taxonomy": "num_employees", "text": "Hơn 1000", "slug": "over-1000", "text_vi": "Hơn 1000", "text_en": "Over 1000", "sort_order": 0}, {"id": 8781, "taxonomy": "num_employees", "text": "5.000-9.999", "slug": "5-000-9-999", "text_vi": "5.000-9.999", "text_en": "5.000-9.999", "sort_order": 0}, {"id": 8782, "taxonomy": "num_employees", "text": "10.000-19.999", "slug": "10-000-19-999", "text_vi": "10.000-19.999", "text_en": "10.000-19.999", "sort_order": 0}, {"id": 8783, "taxonomy": "num_employees", "text": "Over 20.000", "slug": "over-20-000", "text_vi": "Over 20.000", "text_en": "Over 20.000", "sort_order": 0}], "experiences": [{"id": 1640, "taxonomy": "experiences", "text": "<PERSON><PERSON><PERSON><PERSON> yêu c<PERSON>u", "slug": "moi-ra-truong", "text_vi": "<PERSON><PERSON><PERSON><PERSON> yêu c<PERSON>u", "text_en": "Not required", "sort_order": 0}, {"id": 8667, "taxonomy": "experiences", "text": "03 tháng", "slug": "03-thang", "text_vi": "03 tháng", "text_en": "03 months", "sort_order": 1}, {"id": 1641, "taxonomy": "experiences", "text": "06 tháng", "slug": "06-thang", "text_vi": "06 tháng", "text_en": "06 months", "sort_order": 2}, {"id": 1642, "taxonomy": "experiences", "text": "1 năm", "slug": "1-nam", "text_vi": "1 năm", "text_en": "1 year", "sort_order": 3}, {"id": 1643, "taxonomy": "experiences", "text": "2 năm", "slug": "2-nam", "text_vi": "2 năm", "text_en": "2 years", "sort_order": 4}, {"id": 1644, "taxonomy": "experiences", "text": "3 năm", "slug": "3-nam", "text_vi": "3 năm", "text_en": "3 years", "sort_order": 5}, {"id": 1645, "taxonomy": "experiences", "text": "4 năm", "slug": "4-nam", "text_vi": "4 năm", "text_en": "4 years", "sort_order": 6}, {"id": 1646, "taxonomy": "experiences", "text": "5 năm", "slug": "5-nam", "text_vi": "5 năm", "text_en": "5 years", "sort_order": 7}, {"id": 1647, "taxonomy": "experiences", "text": "6 năm", "slug": "6-nam", "text_vi": "6 năm", "text_en": "6 years", "sort_order": 8}, {"id": 1648, "taxonomy": "experiences", "text": "7 năm", "slug": "7-nam", "text_vi": "7 năm", "text_en": "7 years", "sort_order": 9}, {"id": 1649, "taxonomy": "experiences", "text": "8 năm", "slug": "8-nam", "text_vi": "8 năm", "text_en": "8 years", "sort_order": 10}, {"id": 1650, "taxonomy": "experiences", "text": "9 năm", "slug": "9-nam", "text_vi": "9 năm", "text_en": "9 years", "sort_order": 11}, {"id": 8288, "taxonomy": "experiences", "text": "10 năm", "slug": "10-nam", "text_vi": "10 năm", "text_en": "10 years", "sort_order": 12}, {"id": 1651, "taxonomy": "experiences", "text": "Trên 10 năm", "slug": "tren-10-nam", "text_vi": "Trên 10 năm", "text_en": "Above 10 years", "sort_order": 13}, {"id": 4851, "taxonomy": "experiences", "text": "<PERSON><PERSON><PERSON> c<PERSON> kinh nghiệm", "slug": "all-levels", "text_vi": "<PERSON><PERSON><PERSON> c<PERSON> kinh nghiệm", "text_en": "All experiences", "sort_order": 14}], "categories": [{"id": 8794, "taxonomy": "categories", "text": "DevSecOps", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "DevSecOps", "slug": "devsecops", "text_vi": "DevSecOps", "text_en": "DevSecOps", "sort_order": 0}, {"id": 8795, "taxonomy": "categories", "text": "Software Engineering", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "Software Engineering", "slug": "software-engineering", "text_vi": "Software Engineering", "text_en": "Software Engineering", "sort_order": 0}, {"id": 8796, "taxonomy": "categories", "text": "Enterprise Architecture", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "Enterprise Architecture", "slug": "solution-architecture", "text_vi": "Enterprise Architecture", "text_en": "Enterprise Architecture", "sort_order": 0}, {"id": 8797, "taxonomy": "categories", "text": "Cloud Architecture", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "Cloud Architecture", "slug": "cloud-architecture", "text_vi": "Cloud Architecture", "text_en": "Cloud Architecture", "sort_order": 0}, {"id": 8798, "taxonomy": "categories", "text": "Data Science & Analytics, DE", "image": "", "thumbnail_url": "", "banner_url": "", "feature": "Data Science & Analytics, DE", "slug": "data-science-analytics-de", "text_vi": "Data Science & Analytics, DE", "text_en": "Data Science & Analytics, DE", "sort_order": 0}, {"id": 8799, "taxonomy": "categories", "text": "PM/ BA/ Product Management", "slug": "pm-ba-product-management", "text_vi": "PM/ BA/ Product Management", "text_en": "PM/ BA/ Product Management", "sort_order": 0}], "industries": [{"id": 3919, "taxonomy": "industries", "text": "Product", "slug": "product", "text_vi": "Product", "text_en": "Product", "sort_order": 0}, {"id": 8123, "taxonomy": "industries", "text": "Management & digital consulting", "slug": "management-digital-consulting", "text_vi": "Management & digital consulting", "text_en": "Management & digital consulting", "sort_order": 0}, {"id": 8329, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON> kế chế tạo", "slug": "thiet-ke-che-tao", "text_vi": "<PERSON><PERSON><PERSON><PERSON> kế chế tạo", "text_en": "<PERSON><PERSON><PERSON><PERSON> kế chế tạo", "sort_order": 0}, {"id": 8311, "taxonomy": "industries", "text": "MarTech", "slug": "martech", "text_vi": "MarTech", "text_en": "MarTech", "sort_order": 0}, {"id": 8283, "taxonomy": "industries", "text": "Mạng xã hội thời trang", "slug": "mang-xa-hoi-thoi-trang", "text_vi": "Mạng xã hội thời trang", "text_en": "Mạng xã hội thời trang", "sort_order": 0}, {"id": 8187, "taxonomy": "industries", "text": "Technology and Computer Sciences", "slug": "technology-and-computer-sciences", "text_vi": "Technology and Computer Sciences", "text_en": "Technology and Computer Sciences", "sort_order": 0}, {"id": 8146, "taxonomy": "industries", "text": "Construction", "slug": "construction", "text_vi": "Construction", "text_en": "Construction", "sort_order": 0}, {"id": 8144, "taxonomy": "industries", "text": "Software", "slug": "software", "text_vi": "Software", "text_en": "Software", "sort_order": 0}, {"id": 8139, "taxonomy": "industries", "text": "Công nghệ thông tin", "slug": "cong-nghe-thong-tin", "text_vi": "Công nghệ thông tin", "text_en": "Công nghệ thông tin", "sort_order": 0}, {"id": 8121, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> d<PERSON> vụ", "slug": "san-pham-dich-vu", "text_vi": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> d<PERSON> vụ", "text_en": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON> d<PERSON> vụ", "sort_order": 0}, {"id": 8356, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON> bị công nghệ điện tử", "slug": "thiet-bi-cong-nghe-dien-tu", "text_vi": "<PERSON><PERSON><PERSON><PERSON> bị công nghệ điện tử", "text_en": "<PERSON><PERSON><PERSON><PERSON> bị công nghệ điện tử", "sort_order": 0}, {"id": 8103, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> nghiệp nặng", "slug": "cong-nghiep-nang", "text_vi": "<PERSON><PERSON><PERSON> nghiệp nặng", "text_en": "<PERSON><PERSON><PERSON> nghiệp nặng", "sort_order": 0}, {"id": 8053, "taxonomy": "industries", "text": "Sale & Marketing Services", "slug": "sale-marketing-services", "text_vi": "Sale & Marketing Services", "text_en": "Sale & Marketing Services", "sort_order": 0}, {"id": 8037, "taxonomy": "industries", "text": "B2B Services", "slug": "b2b-services", "text_vi": "B2B Services", "text_en": "B2B Services", "sort_order": 0}, {"id": 8032, "taxonomy": "industries", "text": "Security", "slug": "security", "text_vi": "Security", "text_en": "Security", "sort_order": 0}, {"id": 7266, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> mai dịch vụ", "slug": "thuong-mai-dich-vu", "text_vi": "<PERSON><PERSON><PERSON><PERSON><PERSON> mai dịch vụ", "text_en": "<PERSON><PERSON><PERSON><PERSON><PERSON> mai dịch vụ", "sort_order": 0}, {"id": 7148, "taxonomy": "industries", "text": "Human Resource", "slug": "human-resource", "text_vi": "Human Resource", "text_en": "Human Resource", "sort_order": 0}, {"id": 6845, "taxonomy": "industries", "text": "E-commerce Platforms", "slug": "e-commerce-platforms", "text_vi": "E-commerce Platforms", "text_en": "E-commerce Platforms", "sort_order": 0}, {"id": 6842, "taxonomy": "industries", "text": "<PERSON><PERSON>", "slug": "kinh-do<PERSON>h", "text_vi": "<PERSON><PERSON>", "text_en": "<PERSON><PERSON>", "sort_order": 0}, {"id": 8343, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "trien-khai-phan-mem", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 8395, "taxonomy": "industries", "text": "IT Consultant", "slug": "it-consultant", "text_vi": "IT Consultant", "text_en": "IT Consultant", "sort_order": 0}, {"id": 6836, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "bao-hiem", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 8605, "taxonomy": "industries", "text": "SocialTech", "slug": "socialtech", "text_vi": "SocialTech", "text_en": "SocialTech", "sort_order": 0}, {"id": 8831, "taxonomy": "industries", "text": "", "slug": "bao-chi", "text_vi": "", "text_en": "", "sort_order": 0}, {"id": 8753, "taxonomy": "industries", "text": "", "slug": "artificial-intelligence-2", "text_vi": "", "text_en": "", "sort_order": 0}, {"id": 8726, "taxonomy": "industries", "text": "Artificial Intelligence", "slug": "artificial-intelligence-1", "text_vi": "Artificial Intelligence", "text_en": "Artificial Intelligence", "sort_order": 0}, {"id": 8694, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "dau-tu-2", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 8690, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON>", "slug": "tuyen-dung-2", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 8687, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> , t<PERSON><PERSON><PERSON><PERSON> hình", "slug": "bao-chi-t<PERSON>yen-hinh", "text_vi": "<PERSON><PERSON><PERSON> , t<PERSON><PERSON><PERSON><PERSON> hình", "text_en": "<PERSON><PERSON><PERSON> , t<PERSON><PERSON><PERSON><PERSON> hình", "sort_order": 0}, {"id": 8657, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "slug": "thuong-mai-2", "text_vi": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "text_en": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "sort_order": 0}, {"id": 8645, "taxonomy": "industries", "text": "HR-Tech and AI Training Data Solutions", "slug": "hr-tech-and-ai-training-data-solutions-2", "text_vi": "HR-Tech and AI Training Data Solutions", "text_en": "HR-Tech and AI Training Data Solutions", "sort_order": 0}, {"id": 8549, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "dau-tu", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 8400, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> ph<PERSON>t", "slug": "buu-chinh-chuyen-phat", "text_vi": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> ph<PERSON>t", "text_en": "<PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON> ph<PERSON>t", "sort_order": 0}, {"id": 8548, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "slug": "thuong-mai", "text_vi": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "text_en": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "sort_order": 0}, {"id": 8506, "taxonomy": "industries", "text": "HR-Tech and AI Training Data Solutions", "slug": "hr-tech-and-ai-training-data-solutions", "text_vi": "HR-Tech and AI Training Data Solutions", "text_en": "HR-Tech and AI Training Data Solutions", "sort_order": 0}, {"id": 8491, "taxonomy": "industries", "text": "<PERSON><PERSON> vấn gi<PERSON> & <PERSON><PERSON><PERSON><PERSON> lý dự án", "slug": "tu-van-giam-sat-quan-ly-du-an", "text_vi": "<PERSON><PERSON> vấn gi<PERSON> & <PERSON><PERSON><PERSON><PERSON> lý dự án", "text_en": "<PERSON><PERSON> vấn gi<PERSON> & <PERSON><PERSON><PERSON><PERSON> lý dự án", "sort_order": 0}, {"id": 8484, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON>", "slug": "tuyen-dung", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 8478, "taxonomy": "industries", "text": "SUPPLY CHAIN CONSULTING", "slug": "supply-chain-consulting", "text_vi": "SUPPLY CHAIN CONSULTING", "text_en": "SUPPLY CHAIN CONSULTING", "sort_order": 0}, {"id": 8463, "taxonomy": "industries", "text": "Information Technology", "slug": "information-technology", "text_vi": "Information Technology", "text_en": "Information Technology", "sort_order": 0}, {"id": 8460, "taxonomy": "industries", "text": "EdTech", "slug": "edtech", "text_vi": "EdTech", "text_en": "EdTech", "sort_order": 0}, {"id": 8453, "taxonomy": "industries", "text": "Non-governmental Organizations", "slug": "non-governmental-organizations", "text_vi": "Non-governmental Organizations", "text_en": "Non-governmental Organizations", "sort_order": 0}, {"id": 6838, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "tai-chinh", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 6834, "taxonomy": "industries", "text": "<PERSON><PERSON> do<PERSON>h trang sức", "slug": "kinh-doanh-trang-suc", "text_vi": "<PERSON><PERSON> do<PERSON>h trang sức", "text_en": "<PERSON><PERSON> do<PERSON>h trang sức", "sort_order": 0}, {"id": 3929, "taxonomy": "industries", "text": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "slug": "ke-toan-kiem-toan-1", "text_vi": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3939, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "phan-mem", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3946, "taxonomy": "industries", "text": "Sức khỏe/ Làm đẹp", "slug": "suc-khoe-lam-dep", "text_vi": "Sức khỏe/ Làm đẹp", "text_en": "Sức khỏe/ Làm đẹp", "sort_order": 0}, {"id": 3945, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON> phẩm", "slug": "thuc-pham", "text_vi": "<PERSON><PERSON><PERSON><PERSON> phẩm", "text_en": "<PERSON><PERSON><PERSON><PERSON> phẩm", "sort_order": 0}, {"id": 3944, "taxonomy": "industries", "text": "Fintech", "slug": "fintech", "text_vi": "Fintech", "text_en": "Fintech", "sort_order": 0}, {"id": 3943, "taxonomy": "industries", "text": "Giải trí/ Game", "slug": "giai-tri-game", "text_vi": "Giải trí/ Game", "text_en": "Giải trí/ Game", "sort_order": 0}, {"id": 3942, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "nang-luong", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3941, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON>", "slug": "giao-duc", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3940, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử/ <PERSON><PERSON> lẻ", "slug": "thuong-mai-dien-tu-ban-le", "text_vi": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử/ <PERSON><PERSON> lẻ", "text_en": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử/ <PERSON><PERSON> lẻ", "sort_order": 0}, {"id": 3938, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> c<PERSON>", "slug": "phan-cung", "text_vi": "<PERSON><PERSON><PERSON> c<PERSON>", "text_en": "<PERSON><PERSON><PERSON> c<PERSON>", "sort_order": 0}, {"id": 3948, "taxonomy": "industries", "text": "Dịch vụ IT", "slug": "dich-vu-it", "text_vi": "Dịch vụ IT", "text_en": "Dịch vụ IT", "sort_order": 0}, {"id": 3937, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "hoa-duoc", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3936, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> v<PERSON>", "slug": "dich-vu-doanh-nghiep", "text_vi": "<PERSON><PERSON><PERSON> v<PERSON>", "text_en": "<PERSON><PERSON><PERSON> v<PERSON>", "sort_order": 0}, {"id": 3935, "taxonomy": "industries", "text": "<PERSON><PERSON>", "slug": "ngan-hang", "text_vi": "<PERSON><PERSON>", "text_en": "Bank", "sort_order": 0}, {"id": 3934, "taxonomy": "industries", "text": "Hàng không vũ trụ", "slug": "hang-khong-vu-tru", "text_vi": "Hàng không vũ trụ", "text_en": "Hàng không vũ trụ", "sort_order": 0}, {"id": 3933, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> nghiệp ô tô", "slug": "cong-nghiep-o-to", "text_vi": "<PERSON><PERSON><PERSON> nghiệp ô tô", "text_en": "<PERSON><PERSON><PERSON> nghiệp ô tô", "sort_order": 0}, {"id": 3932, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "nong-nghiep", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3931, "taxonomy": "industries", "text": "Kiến trúc/ <PERSON><PERSON><PERSON> d<PERSON>ng", "slug": "kien-truc-xay-dung", "text_vi": "Kiến trúc/ <PERSON><PERSON><PERSON> d<PERSON>ng", "text_en": "Kiến trúc/ <PERSON><PERSON><PERSON> d<PERSON>ng", "sort_order": 0}, {"id": 3930, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON>", "slug": "quang-cao-truyen-thong", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3947, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> sự", "slug": "n<PERSON>-su", "text_vi": "<PERSON><PERSON><PERSON> sự", "text_en": "<PERSON><PERSON><PERSON> sự", "sort_order": 0}, {"id": 3949, "taxonomy": "industries", "text": "Internet", "slug": "internet", "text_vi": "Internet", "text_en": "Internet", "sort_order": 0}, {"id": 6403, "taxonomy": "industries", "text": "Manufacturing", "slug": "manufacturing", "text_vi": "Manufacturing", "text_en": "Manufacturing", "sort_order": 0}, {"id": 4800, "taxonomy": "industries", "text": "Logistics", "slug": "logistics", "text_vi": "Logistics", "text_en": "Logistics", "sort_order": 0}, {"id": 6387, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON>", "slug": "insurtech", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 6385, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> k<PERSON>", "slug": "chung-<PERSON><PERSON>an", "text_vi": "<PERSON><PERSON><PERSON> k<PERSON>", "text_en": "<PERSON><PERSON><PERSON> k<PERSON>", "sort_order": 0}, {"id": 4939, "taxonomy": "industries", "text": "Finance", "slug": "finance", "text_vi": "Finance", "text_en": "Finance", "sort_order": 0}, {"id": 4938, "taxonomy": "industries", "text": "Creative Production", "slug": "creative-production", "text_vi": "Creative Production", "text_en": "Creative Production", "sort_order": 0}, {"id": 4937, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON>", "slug": "thiet-ke", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 4881, "taxonomy": "industries", "text": "Tư vấn và Đào  tạo Internet Marketing", "slug": "tu-van-va-dao-tao-internet-marketing", "text_vi": "Tư vấn và Đào  tạo Internet Marketing", "text_en": "Tư vấn và Đào  tạo Internet Marketing", "sort_order": 0}, {"id": 4812, "taxonomy": "industries", "text": "Consumer Electronics", "slug": "consumer-electronics", "text_vi": "Consumer Electronics", "text_en": "Consumer Electronics", "sort_order": 0}, {"id": 4811, "taxonomy": "industries", "text": "B<PERSON> lẻ", "slug": "ban-le", "text_vi": "B<PERSON> lẻ", "text_en": "B<PERSON> lẻ", "sort_order": 0}, {"id": 4392, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> v<PERSON>", "slug": "dich-vu", "text_vi": "<PERSON><PERSON><PERSON> v<PERSON>", "text_en": "<PERSON><PERSON><PERSON> v<PERSON>", "sort_order": 0}, {"id": 3950, "taxonomy": "industries", "text": "<PERSON><PERSON> h<PERSON>nh p<PERSON><PERSON><PERSON> lu<PERSON>t", "slug": "thi-hanh-phap-luat", "text_vi": "<PERSON><PERSON> h<PERSON>nh p<PERSON><PERSON><PERSON> lu<PERSON>t", "text_en": "<PERSON><PERSON> h<PERSON>nh p<PERSON><PERSON><PERSON> lu<PERSON>t", "sort_order": 0}, {"id": 4276, "taxonomy": "industries", "text": "Outsourcing", "slug": "outsourcing", "text_vi": "Outsourcing", "text_en": "Outsourcing", "sort_order": 0}, {"id": 3957, "taxonomy": "industries", "text": "K<PERSON><PERSON><PERSON>", "slug": "khac", "text_vi": "K<PERSON><PERSON><PERSON>", "text_en": "K<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3956, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON><PERSON>", "slug": "vien-thong", "text_vi": "<PERSON><PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3955, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "slug": "bat-dong-san", "text_vi": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "text_en": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "sort_order": 0}, {"id": 3954, "taxonomy": "industries", "text": "<PERSON><PERSON> công p<PERSON><PERSON><PERSON> mềm", "slug": "gia-cong-phan-mem", "text_vi": "<PERSON><PERSON> công p<PERSON><PERSON><PERSON> mềm", "text_en": "<PERSON><PERSON> công p<PERSON><PERSON><PERSON> mềm", "sort_order": 0}, {"id": 3953, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON>", "slug": "san-xuat", "text_vi": "<PERSON><PERSON><PERSON>", "text_en": "<PERSON><PERSON><PERSON>", "sort_order": 0}, {"id": 3952, "taxonomy": "industries", "text": "<PERSON><PERSON><PERSON> t<PERSON>", "slug": "van-tai", "text_vi": "<PERSON><PERSON><PERSON> t<PERSON>", "text_en": "<PERSON><PERSON><PERSON> t<PERSON>", "sort_order": 0}, {"id": 3951, "taxonomy": "industries", "text": "<PERSON> l<PERSON>ch/ Nghỉ dưỡng", "slug": "du-lich-nghi-duong", "text_vi": "<PERSON> l<PERSON>ch/ Nghỉ dưỡng", "text_en": "<PERSON> l<PERSON>ch/ Nghỉ dưỡng", "sort_order": 0}, {"id": 8832, "taxonomy": "industries", "text": "", "slug": "truyen-hinh", "text_vi": "", "text_en": "", "sort_order": 0}], "benefits": [{"id": 9280, "taxonomy": "benefits", "text": "offered in some technical leadership positions", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-TCXNMA-1737021107.svg", "banner_url": "", "feature": "", "slug": "sign-on-bonuses", "text_vi": "Sign-on Bonuses", "text_en": "offered in some technical leadership positions", "sort_order": 0, "description": "offered in some technical leadership positions"}, {"id": 9281, "taxonomy": "benefits", "text": "Project-Based Bonuses", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-7bb0930c61901467cf5af1cb2e5370d3-**********.svg", "banner_url": "", "feature": "", "slug": "project-based-bonuses", "text_vi": "Project-Based Bonuses", "text_en": "Project-Based Bonuses", "sort_order": 0, "description": "Depending on business performance and revenue."}, {"id": 9282, "taxonomy": "benefits", "text": "Health and Insurance", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-_x31_bD8wA-**********.svg", "banner_url": "", "feature": "", "slug": "health-and-insurance", "text_vi": "Health and Insurance", "text_en": "Health and Insurance", "sort_order": 0, "description": "medical coverage, dental, social insurance, family coverage."}, {"id": 9283, "taxonomy": "benefits", "text": "Additional Allowances", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-Z0Dhxy-**********.svg", "banner_url": "", "feature": "", "slug": "additional-allowances", "text_vi": "Additional Allowances", "text_en": "Additional Allowances", "sort_order": 0, "description": "Support for various occasions (e.g., Children's Day, Mid-Autumn Festival, weddings, maternity)."}, {"id": 9284, "taxonomy": "benefits", "text": "Professional Development", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-McKThx-**********.svg", "banner_url": "", "feature": "", "slug": "professional-development", "text_vi": "Professional Development", "text_en": "Professional Development", "sort_order": 0, "description": "training, conferences, certifications"}, {"id": 9285, "taxonomy": "benefits", "text": "Career Growth Opportunities", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-pgH9aa-1737021237.svg", "banner_url": "", "feature": "", "slug": "career-growth-opportunities", "text_vi": "Career Growth Opportunities", "text_en": "Career Growth Opportunities", "sort_order": 0, "description": "leadership roles, learning new technologies"}, {"id": 9286, "taxonomy": "benefits", "text": "High-profile projects", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-oVM7qD-1737021351.svg", "banner_url": "", "feature": "", "slug": "high-profile-projects", "text_vi": "High-profile projects", "text_en": "High-profile projects", "sort_order": 0, "description": "Opportunities to work on high-profile projects with cutting-edge technologies."}, {"id": 9287, "taxonomy": "benefits", "text": "Flexible Work Arrangements", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-U2XYQn-1737021395.svg", "banner_url": "", "feature": "", "slug": "flexible-work-arrangements", "text_vi": "Flexible Work Arrangements", "text_en": "Flexible Work Arrangements", "sort_order": 0, "description": "remote or hybrid options available"}, {"id": 9288, "taxonomy": "benefits", "text": "Learning opportunities", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-pFnw9M-1737021427.svg", "banner_url": "", "feature": "", "slug": "learning-opportunities", "text_vi": "Learning opportunities", "text_en": "Learning opportunities", "sort_order": 0, "description": "access to courses like Udemy, training programs"}, {"id": 9289, "taxonomy": "benefits", "text": "Social events", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-xcIrzo-(1)-1737021613.svg", "banner_url": "", "feature": "", "slug": "social-events", "text_vi": "Social events", "text_en": "Social events", "sort_order": 0, "description": "company parties, team-building activities"}, {"id": 9291, "taxonomy": "benefits", "text": "Relocation packages", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-ltJu2g-1737021639.svg", "banner_url": "", "feature": "", "slug": "relocation-packages", "text_vi": "Relocation packages", "text_en": "Relocation packages", "sort_order": 0, "description": "Provide all fee for relocation"}, {"id": 9292, "taxonomy": "benefits", "text": "Stock options", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-qKRRHV-1737021687.svg", "banner_url": "", "feature": "", "slug": "stock-options", "text_vi": "Stock options", "text_en": "Stock options", "sort_order": 0, "description": "Stock"}, {"id": 9293, "taxonomy": "benefits", "text": "Employee discounts", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-39643a5ec381f2189084281f493377a6-1737021713.svg", "banner_url": "", "feature": "", "slug": "employee-discounts", "text_vi": "Employee discounts", "text_en": "Employee discounts", "sort_order": 0, "description": "Coupon"}, {"id": 9294, "taxonomy": "benefits", "text": "Travel opportunities and global exposure", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-xcIrzo-(2)-1737021853.svg", "banner_url": "", "feature": "", "slug": "travel-opportunities-and-global-exposure", "text_vi": "Travel opportunities and global exposure", "text_en": "Travel opportunities and global exposure", "sort_order": 0, "description": "Travel"}, {"id": 9375, "taxonomy": "benefits", "text": "Annual Bonus", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-Rgh8lT-(1)-1737021832.svg", "banner_url": "", "feature": "", "slug": "annual-bonus", "text_vi": "Annual Bonus", "text_en": "Annual Bonus", "sort_order": 0, "description": "Annual Bonus"}, {"id": 9376, "taxonomy": "benefits", "text": "Annual Leave", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-gtkU9k-(1)-1737021802.svg", "banner_url": "", "feature": "", "slug": "annual-leave", "text_vi": "Annual Leave", "text_en": "Annual Leave", "sort_order": 0, "description": "15 Annual Leaves, Sick Leave"}, {"id": 9377, "taxonomy": "benefits", "text": "Equipment", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-nMsVaR-(1)-1737021879.svg", "banner_url": "", "feature": "", "slug": "equipment", "text_vi": "Equipment", "text_en": "Equipment", "sort_order": 0, "description": "Be well-equipped with new working equipment"}, {"id": 9378, "taxonomy": "benefits", "text": "SHUI, Insurance", "image": "", "thumbnail_url": "https://assets.topdev.asia/files/2025/01/16/TopDev-_x31_ZaI7o-(1)-1737021931.svg", "banner_url": "", "feature": "", "slug": "shui-insurance", "text_vi": "SHUI, Insurance", "text_en": "SHUI, Insurance", "sort_order": 0, "description": "Full SHUI, Insurance"}]}