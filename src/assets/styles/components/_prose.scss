.prose :where(li):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: auto;
  margin-bottom: auto;
}

.prose :where(p):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  margin-top: auto;
  @apply mb-2;
}

.prose :where(ul):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  @apply mb-2 mt-auto;
}

.prose :where(ol):not(:where([class~="not-prose"], [class~="not-prose"] *)) {
  @apply mt-auto;
}
