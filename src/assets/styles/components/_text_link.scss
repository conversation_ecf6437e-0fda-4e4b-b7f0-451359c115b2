#box-list-link {
	padding: 2rem 0;
	.grid {
		display: grid;
		row-gap: 30px;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		.grid-item {
			padding: 0 15px;
		}
	}
	.title {
		font-size: 16px;
		font-weight: 700;
		text-align: left;
		padding-top: 1rem;
		padding-bottom: 0.25rem;
	}
	.box-link {
		padding: 0 20px 20px 20px;
		background-color: #faece9;
	}
	.list-link {
		list-style: none;
		margin: 0;
		padding: 0;
		max-height: 170px;
		overflow-y: auto;
		&::-webkit-scrollbar {
			width: 5px;
		}
		&::-webkit-scrollbar-track {
			box-shadow: inset 0 0 5px #fff;
			border-radius: 10px;
		}
		&::-webkit-scrollbar-thumb {
			background: #979797;
			border-radius: 10px;
		}
		.text-link {
			color: #000;
			font-size: 14px;
			display: block;
			padding: 0.25rem 0.25rem;
			&:hover {
				color: #d34126;
			}
		}
	}
	&.mobile {
		padding: 2rem 0;
		.card {
			border: none;
			margin: 0;
			padding: 0;
			position: relative;
			border-left: solid rgba(0, 0, 0, 0.0745098039) 1px;
			border-right: solid rgba(0, 0, 0, 0.0745098039) 1px;
			&::after {
				content: "";
				height: 1px;
				background-color: rgba(0, 0, 0, 0.0745098039);
				width: 90%;
				left: 50%;
				bottom: 0;
				position: absolute;
				transform: translateX(-50%);
			}
			&:first-child {
				border-top: solid rgba(0, 0, 0, 0.0745098039) 1px;
				border-top-left-radius: 4px;
				border-top-right-radius: 4px;
			}
			&:last-child {
				border-bottom: solid rgba(0, 0, 0, 0.0745098039) 1px;
				border-bottom-left-radius: 4px;
				border-bottom-right-radius: 4px;
				&::after {
					display: none;
				}
			}
			&.active {
				.box-item {
					.icon {
						transform: translateY(-50%);
					}
				}
			}
			.box-item {
				display: block;
				position: relative;
				.title {
					text-align: left;
					width: 100%;
					display: block;
					color: #d34127;
					background-color: #faece9;
					font-weight: 700;
					padding: 15px 15px;
					font-size: 14px;
					outline: 0;
					border: none;
				}
				.icon {
					position: absolute;
					top: 50%;
					right: 20px;
					transition: all 0.3s ease-in-out;
					transform: translateY(-50%) rotate(180deg);
				}
			}
			.text-link {
				display: block;
				padding: 15px 15px;
				color: #000;
				&:hover {
					color: #d34127;
				}
			}
			.list-item {
				border-top: solid 1px rgba(0, 0, 0, 0.0745098039);
			}
			.list-collapse {
				transition: all 0.3s ease-out;
				margin: 0;
				padding: 0;
				list-style: none;
				overflow: hidden;
				max-height: 0;
				border-left: solid 1px rgba(0, 0, 0, 0.0745098039);
				border-right: solid 1px rgba(0, 0, 0, 0.0745098039);
				border-bottom: solid 1px rgba(0, 0, 0, 0.0745098039);
			}
		}
	}
}
