@import "swiper/scss";
@import "swiper/scss/effect-coverflow";
@import "swiper/scss/pagination";
@import "swiper/scss/grid";
@import "swiper/scss/navigation";

:root {
  --swiper-pagination-bullet-size: 14px;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-color: #dd3f24;

  /** Navigation **/
  --swiper-navigation-size: 12px;
  --swiper-navigation-color: #dd3f24;
}

/**
 * Custom Pagination
 */
.swiper-pagination-bullets.swiper-pagination-horizontal {
  width: auto !important;
  @apply flex items-center;
}
.swiper-pagination-bullet-active {
  position: relative;

  &:before {
    content: "";
    display: block;
    margin: 2px auto;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #ffffff;
  }
}

.pagination__custom{
  .swiper-pagination-bullet{
    @media screen and (max-width: 1280px) {
      width: 10px;
      height: 10px;
    }
  }
  .swiper-pagination-bullet-active {
    position: relative;
    background-color: #A93A28;
    @media screen and (max-width: 1280px) {
      width: 10px;
      height: 10px;
    }
    &:before {
      content: "";
      display: block;
      margin: 2px auto;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background-color: #A93A28;
      transform: translate(-3px,-4px);
      @media screen and (max-width: 1280px) {
        width: 10px;
        height: 10px;
        border: none;
        transform: translate(0,-2px);

      }
    }
  }
}
.pagination__custom__blue{
  .swiper-pagination-bullet{
    @media screen and (max-width: 1280px) {
      width: 10px;
      height: 10px;
    }
  }
  .swiper-pagination-bullet-active {
    position: relative;
    background-color: #1C4587;
    @media screen and (max-width: 1280px) {
      width: 10px;
      height: 10px;
    }
    &:before {
      content: "";
      display: block;
      margin: 2px auto;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background-color: #1C4587;
      transform: translate(-3px,-4px);
      @media screen and (max-width: 1280px) {
        width: 10px;
        height: 10px;
        border: none;
        transform: translate(0,-2px);

      }
    }
  }
}

/**
 * Custom navigation
 */
.swiper-footer {
  @apply mt-0 lg:mt-8;

  .preEl {
    @apply mr-4 cursor-pointer;
  }

  .nextEl {
    @apply ml-4 cursor-pointer;
  }
}


.swiper-slide{
  @apply h-auto;
}
