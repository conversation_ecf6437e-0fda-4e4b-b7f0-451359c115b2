@layer components {
  .scrollbar-primary {
    @apply scrollbar-thin scrollbar-track-gray-light scrollbar-thumb-primary scrollbar-track-rounded-full scrollbar-thumb-rounded-full scrollbar-w-1;
  }
  .scrollbar-primary-gray {
    @apply scrollbar-thin scrollbar-track-gray-light  scrollbar-thumb-gray-300 scrollbar-track-rounded-full scrollbar-thumb-rounded-full scrollbar-w-[2px];
  }
}
.simplebar-vertical {
  background: #dbdbdb !important;
  border-radius: 7px !important;
  width: 3px !important;
}
.scroll-bar-resume-list-user::-webkit-scrollbar-track {
  border-radius: 8px;
  background: var(--grey-200, #c2c2c2); // Default gray color
}

.scroll-bar-resume-list-user::-webkit-scrollbar-track:hover {
  background: var(--grey-200, #dd3f24); // Changes to #dd3f24 on hover
}

.scroll-bar-resume-list-user::-webkit-scrollbar {
  padding-left: 24px;
  width: 4px;
  background: transparent; // It's better to have a transparent background for the scrollbar itself
}
.scroll-bar-resume-list-user::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: var(--grey-500, #dd3f24); // Thumb color
}
.scroll-bar-resume-list-user {
  max-height: 500px; /* Adjust the height as needed */
  overflow-y: auto; /* Keeps the overflow scroll behavior */
}

//List resum CV Scroll
.scroll-bar-list-user-cv::-webkit-scrollbar-track {
  border-radius: 8px;
  background: var(--grey-200, #c2c2c2); // Default gray color
}
.scroll-bar-list-user-cv::-webkit-scrollbar-track:hover {
  background: var(--grey-200, #5d5d5d); // Changes to #dd3f24 on hover
}
.scroll-bar-list-user-cv::-webkit-scrollbar {
  padding-left: 24px;
  width: 4px;
  background: transparent; // It's better to have a transparent background for the scrollbar itself
}
.scroll-bar-list-user-cv::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: var(--grey-500, #5d5d5d); // Thumb color
}
.simplebar-scrollbar {
  &:before {
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background: #dd3f24 !important;
    border-radius: 8px;
  }

  &.simplebar-visible:before {
    opacity: 1 !important;
  }
}
