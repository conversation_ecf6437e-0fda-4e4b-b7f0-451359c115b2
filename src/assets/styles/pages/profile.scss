.bg-new-star-icon {
  border: 1px solid var(--yellow-light, #fdf1d8);
  background: linear-gradient(281deg, #ffa800 3.49%, #ff7a00 117.34%);

  /* Shadow/small */
  box-shadow: 0px 4px 4px 0px rgba(95, 47, 38, 0.15);
}

.bg-standard-icon {
  border: 1px solid var(--green-light, #d9ffdf);
  background: linear-gradient(281deg, #00a79d 3.49%, #60e90c 117.34%);

  /* Shadow/small */
  box-shadow: 0px 4px 4px 0px rgba(95, 47, 38, 0.15);
}

.bg-booster-icon {
  border: 1px solid var(--brand-200, #ffbcb0);
  background: var(
    --decoration-booster,
    linear-gradient(281deg, #ff7b42 3.49%, #d34127 117.34%)
  );

  /* Shadow/small */
  box-shadow: 0px 4px 4px 0px rgba(95, 47, 38, 0.15);
}

.bg-booster-icon.inactive,
.bg-standard-icon.inactive {
  // border-radius: 56px;
  border: 1px solid var(--grey-300, #c2c2c2);
  background: var(--grey-100, #f5f5f5);
}
.bg-new-star-icon.no-box-shadow,
.bg-standard-icon.no-box-shadow,
.bg-booster-icon.no-box-shadow {
  box-shadow: 0 0 #0000;
}
.text-booster {
  background: linear-gradient(281.14deg, #ff7b42 3.49%, #d34127 117.34%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

[type="text"]:focus,
input:where(:not([type])):focus,
[type="email"]:focus,
[type="url"]:focus,
[type="password"]:focus,
[type="number"]:focus,
[type="date"]:focus,
[type="datetime-local"]:focus,
[type="month"]:focus,
[type="search"]:focus,
[type="tel"]:focus,
[type="time"]:focus,
[type="week"]:focus,
[multiple]:focus,
textarea:focus,
select:focus {
  outline-offset: 0;
  box-shadow: none;
  outline: none;
  @apply border-gray-500;
}

#basic-information-form {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
  }

  input[type="number"] {
    -moz-appearance: textfield; /* Firefox */
  }

  .form-group {
    .select-error {
      .select__control {
        --tw-bg-opacity: 1;
        border-color: rgb(240 82 82 / var(--tw-border-opacity));
        --tw-bg-opacity: 1;
        background-color: rgb(253 242 242 / var(--tw-bg-opacity));
      }
    }
  }
}
.list-resumes-cv {
  .item-resume {
    label {
      &:before {
        content: "";
        position: absolute;
        left: 0;
        width: 16px;
        height: 16px;
        border: 1px solid #c2c2c2;
        background-color: #fff;
        border-radius: 100%;
        top: calc(50% - 8px);
      }
    }
    input[type="radio"]:checked ~ label {
      &::before {
        content: "";
        border: 5px solid #dd3f24;
      }
    }
  }
}

.item-resumes {
  .info-resumes {
    width: calc(100% - 32px);
  }

  &.active {
    .dots-resume {
      border: 5px solid #dd3f24;
    }
    .info-resumes {
      border: 1px solid var(--brand-200, #ffbcb0);
      background: var(--brand-100, #feeeeb);
    }
  }
}

.scroll-bar-resume-profile-user::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  background: var(--grey-200, #dbdbdb);
}

.scroll-bar-resume-profile-user::-webkit-scrollbar {
  width: 2px;
  background: var(--grey-200, #dbdbdb);
}

.scroll-bar-resume-profile-user::-webkit-scrollbar-thumb {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  background: var(--grey-500, #5c5b5b);
}

@media (max-width: 62rem) {
  .custom-modal-profile {
    > div > div {
      max-height: 80vh !important;
      border-top-left-radius: 1.5rem; /* Adjust as needed */
      border-top-right-radius: 1.5rem; /* Adjust as needed */
    }
  }
}

.toggle-otw {
  border: none !important;

  &[aria-checked="true"] .toggle-bg {
    border: none !important;
    background-color: #dd3f24 !important;
    color: #dd3f24 !important;
  }

  &[aria-checked="false"] .toggle-bg {
    border: none !important;
    background-color: #b0b0b0 !important;
  }
}

.item-update-profile {
  text-align: center;
  border: 1px solid #DD3F24;
  height: 48px;
  line-height: 48px;
  font-weight: 700;
  color: #DD3F24;
  border-radius: 4px;
}

.mb-5 {
  margin-bottom: 5px;
}