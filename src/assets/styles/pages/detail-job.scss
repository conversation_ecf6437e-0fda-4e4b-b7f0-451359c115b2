@import url("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css");

body.layer-popup {
  position: relative;
  overflow: hidden;
}

body.layer-popup::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  display: block;
  z-index: 51;
  background: rgba(0, 0, 0, 0.7);
  transition: 0.2s all;
  -webkit-transition: 0.2s all;
  -moz-transition: 0.2s all;
  -ms-transition: 0.2s all;
  -o-transition: 0.2s all;
  -webkit-animation-name: fade;
  -webkit-animation-duration: 0.4s;
  animation-name: fade;
  animation-duration: 0.4s;
}

#formApply {
  .boxCheckboxTnc {
    label.custom-control-label {
      &::before {
        content: "";
        width: 20px;
        height: 20px;
        border: 1px solid #757575;
        border-radius: 4px;
        left: 0;
        top: -2px;
        position: absolute;
      }
    }

    input#checkboxTnc:checked ~ label.custom-control-label {
      &::before {
        border: none;
        background: url("https://cdn.topdev.vn/v4/assets/images/apply_job/icon-checked.png")
          no-repeat;
      }
    }
  }
  .tox-tinymce {
    .tox-statusbar {
      border: none;
      text-transform: lowercase;
      padding: 0 20px 15px 0;
      height: 32px;
    }
  }

  .tox {
    .tox-statusbar__text-container {
      display: flex;
      flex: 1 1 auto;
      justify-content: flex-end;
      overflow: hidden;
    }
  }
  .item-resume {
    label {
      &:before {
        content: "";
        position: absolute;
        left: 0;
        width: 16px;
        height: 16px;
        border: 1px solid #c2c2c2;
        background-color: #fff;
        border-radius: 100%;
        top: calc(50% - 8px);
      }
    }
    input[type="radio"]:checked ~ label {
      &::before {
        content: "";
        border: 5px solid #dd3f24;
      }
    }
  }
}

#EventSuggestJobs {
  .checkbox-checked {
    content: "";
    width: 5px;
    height: 12px;
    margin-top: -2px;
    border-right: 2px solid #fff;
    border-bottom: 3px solid #fff;
    border-radius: 2px;
    opacity: 0;
    transform: rotate(44deg);
    transition: 0.2s;
  }
  .checkbox-input:checked + .checkbox-group {
    background-color: #d34127;
    border-color: #d34127;
    .checkbox-checked {
      opacity: 1;
    }
  }
}

@media all and (max-width: 1100px) {
  .box-popup-apply {
    max-height: 600px;
  }
}

#detailJobPage #detailJobSwiperMobile .swiper-wrapper {
  transition-duration: 0ms;
  transform: translate3d(calc((100% - 344px) / 2), 0px, 0px);
  transition-delay: 0ms;
}

// style editor
.editor-content {
  .ck-editor {
    .ck-placeholder::before {
      font-size: 14px;
      color: #b0b0b0;
    }
    .ck-toolbar {
      border-color: #b0b0b0 !important;
      border-top-left-radius: 4px !important;
      border-top-right-radius: 4px !important;
      min-height: 44px;
    }
    .ck-content {
      width: 100%;
      overflow: hidden;
      border-color: #b0b0b0 !important;
      border-bottom-left-radius: 4px !important;
      border-bottom-right-radius: 4px !important;
      padding-left: 1.5rem;
      padding-top: 0.5rem;
      padding-bottom: 2.5rem;
      ul,
      ol {
        margin-left: 1.5rem;
      }
    }
  }
}

.ck-body-wrapper {
  .ck-body {
    display: none;
  }
}
.modal-feedback {
  .max-w-6xl {
    max-width: 67.4rem;
  }
  .tox .tox-edit-area {
    padding-top: 0.5rem;
    padding-left: 0.5rem;
  }
}
