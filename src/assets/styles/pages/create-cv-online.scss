#landing {
    :root {
       --color: #e6eef9;
   }
   .tabs {
       position: relative;
       * {
           z-index: 2;
       }
   }
   input[type="radio"] {
       display: none;
   }
   .tab {
       cursor: pointer;
       transition: color 0.15s ease-in;
       display: flex;
       align-items: center;
       justify-content: center;
       width: min(177px, 50%);
       font-size: 1.25rem;
       font-weight: 500;
       z-index: 9;
   }
   input[type="radio"] {
       &+label {
           color: #5C5B5B;
           z-index: 9;
           margin: 5px
       }
   }
   .glider {
       position: absolute;
       display: flex;
       height: 39px;
       width: min(195px, calc(50% - 8px));
       opacity: 0.8;
       border-radius: 8px;
       transition: 3s ease-out;
       z-index: 2;
       background-color: #fff;
   }
   input[id="createnewcv"] {
       &~#tablandingpage #divcreatenewcv {
           // animation-name: faceOut;
           animation-duration: 3s;
           // opacity: 0;
           // position: relative;
       }
       &:checked {
           &~.--switch .glider {
               transform: translateX(0);
           }
           &~#tablandingpage #divcreatenewcv {
               display: block !important;
               // animation-name: faceIn;
               animation-duration: 3s;
               opacity: 1;
           }
           &~#tablandingpage #divstandardizecv {
               position: relative;
               // animation-name: faceOut;
               animation-duration: 3s;
           }
       }
   }
   input[id="standardizecv"] {
       &~#tablandingpage #divstandardizecv {
           // animation-name: faceOut;
           animation-duration: 3s;
           // opacity: 0;
           // position: relative;
       }
       &:checked {
           &~.--switch .glider {
               transform: translateX(100%);
           }
           &~#tablandingpage #divstandardizecv {
               display: block !important;
               // animation-name: faceIn;
               animation-duration: 3s;
               opacity: 1;
           }
           &~#tablandingpage #divcreatenewcv {
               position: relative;
               // animation-name: faceOut;
               animation-duration: 3s;
           }
       }
   }
   .--switch {
       padding: 8px;
       border-radius: 8px;
       border: 1px solid #dbdbdb;
       gap: 10px;
       height: 55px;
       background-color: #f5f5f5;
       width: min(409px, 100%);
       display: flex;
       justify-content: space-between;
   }
   @keyframes faceIn {
       0% {
           opacity: 0;
           display: none
       }
       50% {
           opacity: 0.5;
       }
       100% {
           opacity: 1;
       }
   }
   @keyframes faceOut {
       0% {
           opacity: 1;
       }
       50% {
           opacity: 0.5;
       }
       100% {
           opacity: 0;
       }
   }
}

.list-resumes-cv {
    .item-resume {
      label {
        &:before {
          content: "";
          position: absolute;
          left: 0;
          width: 16px;
          height: 16px;
          border: 1px solid #c2c2c2;
          background-color: #fff;
          border-radius: 100%;
          top: calc(50% - 8px);
        }
      }
      input[type="radio"]:checked ~ label {
        &::before {
          content: "";
          border: 5px solid #dd3f24;
        }
      }
    }
  }