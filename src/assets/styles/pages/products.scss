#section-hero {
  .banner {
    border-radius: 0px 0px 96px 96px;
    background: url("/v4/assets/images/online-payment/bg-hero-section.webp")
      no-repeat center;
    background-size: cover;
  }
}

.items-product {
  ul {
    li {
      background: url("/v4/assets/images/online-payment/icon-check-list.png")
        no-repeat left top 3px;
    }
  }
}

.items-product-vertical {
  width: calc((100% - 4rem) / 3);
}

#section-topDev-rewards {
  .item-reward {
    width: calc((100% - 1.5rem) / 2);

    &.active {
      @apply border-primary;
      background: var(
        --BackGround,
        radial-gradient(
          281.67% 158.44% at 105.89% -50.76%,
          #ffc7bd 0%,
          #ffe8d8 100%
        )
      );
    }
  }

  .swiper {
    padding-bottom: 20px;
    padding-left: 10px;
    padding-right: 10px;
  }
}

.custom-toast-container {
  .swal2-html-container {
    @apply text-base text-gray-600;
  }

  .swal2-cancel {
    padding: 15px 38px;
  }
}

@media (max-width: 62rem) {
  #section-hero {
    .banner {
      border-radius: unset;
      background: radial-gradient(
        281.67% 158.44% at 105.89% -50.76%,
        #ffc7bd 0%,
        #ffe8d8 100%
      );
    }
  }

  .items-product-vertical {
    width: 100%;
  }

  #section-topDev-rewards {
    .item-reward {
      width: 100%;
    }
  }
}
