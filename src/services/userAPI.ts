import request from "./request";
import ToastNotification from "@/components/Swal/ToastNotification";
import Profile from "@/types/profile";
import { StatusWork } from "@/types/user";
import { getCurrentLocale } from "@/utils/locale";

export const issueToken = () => {
  const params: { auth?: any } = {};

  return request({
    url: "/oauth2/issueToken",
    method: "GET",
    params,
  });
};

// Get info user with token
export const fetchUserDataApi = (params?: any) => {
  return request({
    url: "/users/me/progress",
    method: "GET",
    params,
  });
};

// Get data notification
export const fetchNotificationApi = (page: number = 1) => {
  return request({
    url: `/notifications?fields[notification]=data&page=${page}&page_size=10`,
    method: "GET",
  });
};

// Get data notification as Seen
export const fetchMaskAsSeen = () => {
  return request({
    url: "/notifications/mark-as-seen",
    method: "POST",
    data: { ids: "all" },
  });
};

// Get data notification as Read
export const requestMaskAsRead = (id: number | undefined) => {
  return request({
    url: "/notifications/mark-as-seen",
    method: "POST",
    data: { ids: id ? id : "all" },
  });
};

// Get discovery menu
export const getDiscoveryMenu = async () => {
  const response = await fetch("https://api.topdev.vn/fe/v1/discovery-menu");

  return response.json();
};

export async function getUserProfile() {
  return await request({
    url: "/users/profile",
  }).then((response) => response.data);
}

export async function uploadUserProfileCV(file: File) {
  const formData = new FormData();
  formData.append("files", file);

  return await request({
    url: "/files/profile_upload",
    method: "POST",
    data: formData,
  });
}

export async function downloadUserProfileCv(
  profile: Profile,
  newTab: boolean = false,
) {
  const locale = getCurrentLocale();
  return await request({
    url: "https://api.topdev.vn/export/v2/theme/template1",
    method: "POST",
    responseType: "arraybuffer",
    data: {
      resume: {
        name_resume: profile.display_name,
        lang: "vi",
        template_name: "template1",
        experience_type: 0,
        meta: {
          name: profile.display_name,
          lang: locale,
          theme: "template1",
          color: null,
        },
        summary: [
          {
            summary: profile.summary,
          },
        ],
        personal: [
          {
            name: profile.display_name,
            email: profile.email,
            phone: profile.phone,
            gender: profile.gender,
            images: "",
            address: profile.address,
            profession: profile.position,
            dateofbirth: profile.birthday,
            extra_skills: null,
            provinces_id: profile.province_code,
            province_name: profile.province_name,
          },
        ],
        interests: [
          {
            interest:
              profile.interests && profile.interests.length > 0
                ? profile.interests[0]
                : "",
          },
        ],
        activities: profile.activities?.map((item) => {
          return {
            date_to: item.is_working_here ? null : item.to,
            activity: item.activity,
            date_from: item.from,
            achievement: item.achievement,
          };
        }),
        educations: profile.educations?.map((item) => {
          return {
            degree: item.degree,
            year_to: item.is_studying_here ? null : item.to,
            year_from: item.from,
            schoolname: item.school_name,
            description: item.description,
          };
        }),
        experiences: profile.experiences?.map((item) => {
          return {
            company: item.company,
            year_to: item.is_working_here ? null : item.to,
            position: item.position,
            projects: item?.projects?.map((project) => {
              return {
                project: project.project_name,
                project_time: project.project_time,
                description: project.description,
              };
            }),
            technical: item?.skills
              ?.map((skill) => skill.skill_name)
              ?.join(","),
            year_from: item.from,
            experience: item.description,
          };
        }),
        skill_groups: [
          {
            skills: profile.skills?.technical_skills?.map((item) => {
              return {
                skill: item.skill_name,
                rating: 5,
                skill_id: item.skill_id,
              };
            }),
            group_name: "Technical Skill",
          },
          {
            skills: profile.skills?.soft_skills?.map((item) => {
              return {
                skill: item,
                rating: 5,
                skill_id: item,
              };
            }),
            group_name: "Soft skills",
          },
        ],
        languages: profile.languages?.map((item) => {
          return {
            fluency: item.fluent,
            language: item.language,
          };
        }),
        references: profile.references ?? [],
        additionals: profile.additionals ?? [],
        projects: profile.projects?.map((item) => {
          return {
            project: item.project_name,
            project_time: item.project_time,
            description: item.description,
          };
        }),
        certificates: profile.certificates ?? [],
        sectionsOrder: [],
        sections: [
          {
            key: "personal",
            status: "active",
            order: 1,
          },
          {
            key: "summary",
            status: "active",
            order: 2,
          },
          {
            key: "experiences",
            status: "active",
            order: 3,
          },
          {
            key: "skill_groups",
            status: "active",
            order: 4,
          },
          {
            key: "educations",
            status: "active",
            order: 5,
          },
          {
            key: "languages",
            status: profile.languages?.length ? "active" : "inactive",
            order: 6,
          },
          {
            key: "projects",
            status: profile.projects?.length ? "active" : "inactive",
            order: 7,
          },
          {
            key: "interests",
            status: profile.interests?.length ? "active" : "inactive",
            order: 8,
          },
          {
            key: "references",
            status: profile.references?.length ? "active" : "inactive",
            order: 9,
          },
          {
            key: "activities",
            status: profile.activities?.length ? "active" : "inactive",
            order: 10,
          },
          {
            key: "certificates",
            status: profile.certificates?.length ? "active" : "inactive",
            order: 11,
          },
          {
            key: "additionals",
            status: profile.additionals?.length ? "active" : "inactive",
            order: 12,
          },
        ],
      },
    },
  }).then((response) => {
    const blob = new Blob([response.data], {
      type: "application/pdf",
    });

    const blobUrl = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = blobUrl;
    if (!newTab) {
      a.download = "TopDev-" + profile.display_name + ".pdf";
    }
    if (newTab) {
      a.target = "_blank";
    }
    a.click();
  });
}

export async function parseUserProfileUploadedCv(
  mediaId: number,
  data?: {
    utm_source: string | null;
    utm_medium: string | null;
    utm_campaign: string | null;
    device_type: "mobile" | "PC";
  },
) {
  return await request({
    url: "/users/profile/parse_cv/" + mediaId,
    method: "POST",
    data: data,
  });
}

export async function getUserProfileParsingProcess(mediaId: number) {
  return await request({
    url: "/users/profile/parse_progress/" + mediaId,
    method: "GET",
  });
}

export async function patchUserProfile(data: {}) {
  return await request({
    url: "/users/profile",
    method: "PATCH",
    data: data,
  }).catch((error) => {
    ToastNotification({
      icon: "error",
      title: "Error!",
      description:
        "Error occur: " + JSON.stringify(error.response.data.message),
      timer: 5000,
    });

    throw error;
  });
}

export const getUserData = () => {
  return request({
    url: "/users/me",
    method: "GET",
  });
};

export const updateUserInfoApi = (willing_to_work: boolean) => {
  return request({
    url: "/users/me",
    method: "PATCH",
    params: { willing_to_work: willing_to_work },
  });
};

export const toggleOpenToWork = async (status_works: StatusWork) => {
  return await request({
    url: "/users/me",
    method: "PATCH",
    data: status_works,
  }).catch((error) => {
    ToastNotification({
      icon: "error",
      title: "Error!",
      description: "Error occur: " + JSON.stringify(error.response.data.errors),
      timer: 5000,
    });

    throw error;
  });
};

export async function uploadUserProfileAvatar(formData: FormData) {
  return await request({
    url: "/files/profile_avatar",
    method: "POST",
    data: formData,
  });
}

export async function getBlockedCompaniesUser() {
  return await request({
    url: "/users/blocked-companies",
    method: "GET",
  });
}

export async function blockedCompaniesUser(
  company_ids?: Array<number>,
  emails?: Array<string>,
) {
  return await request({
    url: "/users/blocked-companies",
    method: "POST",
    data: { company_ids, emails },
  });
}

// Get main cv user profile
export async function getMainCVUserProfile() {
  return await request({
    url: "/users/main-cv",
    method: "GET",
  });
}

// Set main cv user profile
export async function setMainCVUserProfile(
  cv_id: number | null,
  cv_type: "topdev_cv" | "cv_builder" | "upload_cv",
) {
  return await request({
    url: "/users/main-cv",
    method: "POST",
    data: { cv_id, cv_type },
  });
}

// Get list cv user profile
export async function getListCVUserProfile() {
  return await request({
    url: "/users/my-cv",
    method: "GET",
  });
}

// Get list employer viewed cv
export async function getEmployerViewedCV(
  page: number = 1,
  page_size: number = 5,
) {
  return await request({
    url: `/users/viewed-companies?page=${page}&page_size=${page_size}`,
    method: "GET",
  });
}
