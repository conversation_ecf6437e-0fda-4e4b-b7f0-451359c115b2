import request from "@/services/request";
import { TaxonomiesType, TaxonomyType } from "@/types/taxonomy";

type TaxonomyFields =
  | "job_levels"
  | "benefits"
  | "num_employees"
  | "job_types"
  | "contract_types"
  | "industries"
  | "skills"
  | "experiences"
  | "categories";

export const getAllTaxonomies = async () => {
  try {
    const response = await request({
      method: "GET",
      url: "/taxonomies",
      params: {
        fields:
          "contract_types,skills,salary_range,job_levels,job_types,num_employees,experiences,benefits",
      },
    });
    const data: { error: boolean; data: TaxonomiesType } = await response.data;

    return data.data;
  } catch (error) {
    // throw Error("get all taxonomies failed!");
  }
};

export const getAllJobsLevel = async () => {
  const fields = "job_levels";

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${fields}`,
    {
      next: { revalidate: 60 * 60 * 24 },
    },
  );

  const data = await response.json();

  return data.data as { job_levels: TaxonomyType[] };
};

export const getAllBenefits = async () => {
  const fields = "benefits";

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${fields}`,
    {
      next: { revalidate: 60 * 60 * 24 },
    },
  );

  const data = await response.json();

  return data.data as { benefits: TaxonomyType[] };
};
export const getCompanySize = async () => {
  const fields = "num_employees";

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${fields}`,
    {
      next: { revalidate: 60 * 60 * 24 },
    },
  );

  const data = await response.json();

  return data.data as { num_employees: TaxonomyType[] };
};
export const getWorkTypes = async () => {
  const fields = "job_types";

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${fields}`,
    {
      next: { revalidate: 60 * 60 * 24 },
    },
  );

  const data = await response.json();

  return data.data as { job_types: TaxonomyType[] };
};
export const getContractTypes = async () => {
  const fields = "contract_types";

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${fields}`,
    {
      next: { revalidate: 60 * 60 * 24 },
    },
  );

  const data = await response.json();

  return data.data as { contract_types: TaxonomyType[] };
};
export const getIndustries = async () => {
  const fields = "industries";

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${fields}`,
    {
      next: { revalidate: 60 * 60 * 24 },
    },
  );

  const data = await response.json();

  return data.data as { industries: TaxonomyType[] };
};

// Generic fetcher that accepts multiple fields
export const getTaxonomiesDynamic = async <T = any>(
  fields: TaxonomyFields[],
) => {
  const query = fields.join(",");

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${query}`,
    {
      next: { revalidate: 60 * 60 * 24 },
    },
  );

  const data = await response.json();

  return data.data as T;
};
export const getTaxonomies = async () => {
  const fields = [
    "skills",
    "contract_types",
    "job_levels",
    "job_types",
    "experiences",
    "categories",
    "num_employees",
    "industries",
  ].join(",");

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL_API}/taxonomies?fields=${fields}`,
    {
      cache: "no-cache",
    },
  );

  const data = await response.json();

  return data.data;
};
