import request from "./request";

// Tracking data
export const fetchJobsOfCategoriesInFeatures = (page_size: number | null) => {
  return request({
    url: "/companies/78588/jobs?fields[job]=title,addresses,detail_url,experiences_ids,expires,job_levels_ids,content_str,expires",
    method: "GET",
    params: {
      page_size,
    },
  });
};

//Fecth jobs of categories
export const fetchJobsOfCategories = (
  categories_ids: number | null,
  page_size: number | null,
) => {
  return request({
    url: `/companies/78588/jobs?fields[job]=title,addresses,detail_url,experiences_ids,expires,job_levels_ids,content_str,expires`,
    method: "GET",
    params: {
      categories_ids,
      page_size,
    },
  });
};

//Fetch jobs with ids
export const fetchJobsForIds = (Ids: string) => {
  return request({
    url: `/jobs?fields[job]=title,addresses,detail_url,experiences_ids,job_levels_ids,image_thumbnail`,
    method: "GET",
    params: {
      ids: Ids,
    },
  });
};

//Fetch blogs
export const fetchBlogs = () => {
  return request({
    url: `/blogs?page_size=60&category=Techcombank`,
    method: "GET"
  });
};
