import { DEFAULT_SEARCH_PARAMS } from "@/contansts/search";
import { SearchResultType } from "@/types/search";
import { notFound } from "next/navigation";
import request from "./request";
import { JobType } from "@/types/job";
import { CompanyType } from "@/types/company";
import { BlogType } from "@/types/blog";

export const getSuggestedKeywords = async (keyword: string) => {
  try {
    const response = await request({
      method: "GET",
      url: `/suggested-units/?keyword=${keyword}`,
    });
    const data = await response.data;
    return data.data;
  } catch (error) {
    throw Error("Can not get suggested keywords!");
  }
};

export const getSearchData = async (slug: string, fullUrl?: string) => {
  return await request({
    method: "GET",
    url: `/jobs/search`,
    params: {
      keyword: slug,
      fullUrl: fullUrl,
      ...DEFAULT_SEARCH_PARAMS,
    },
  })
    .then((response) => {
      return response.data as SearchResultType;
    })
    .catch(() => notFound());
};

export interface SearchJobParamV2 {
  keyword?: string;
  ordering?: string;
  locale?: string;
  region_ids?: string;
  job_categories_ids?: string;
  job_roles_ids?: string;
  salary_min?: number;
  salary_max?: number;
  benefit_ids?: string;
  skills_id?: string;
  job_levels_ids?: string;
  job_types_ids?: string;
  contract_types_ids?: string;
  company_size_ids?: string;
  company_industry_ids?: string;
  page?: number;
  page_size?: number;
  "fields[job]"?: string;
  "fields[company]"?: string;
  _f?: number;
}

export const searchJobV2 = async (params: SearchJobParamV2) => {
  return await request({
    method: "GET",
    url: `/jobs/search/v2`,
    params: params,
  })
    .then((response) => {
      return response.data as {
        aggregations: any;
        data: JobType[];
        links: any;
        meta: any;
        meta_tag: any;
      };
    })
    .catch(() => notFound());
};

export const searchCompanyV2 = async (params: SearchJobParamV2) => {
  return await request({
    method: "GET",
    url: `/companies/search/v2`,
    params: params,
  })
    .then((response) => {
      return response.data as {
        aggregations: any;
        data: CompanyType[];
        links: any;
        meta: any;
        meta_tag: any;
      };
    })
    .catch(() => notFound());
};

export const searchBlog = async (params: { keyword?: string }) => {
  return await request({
    method: "GET",
    url: `/blogs/search`,
    params: params,
  })
    .then((response) => {
      return response.data as {
        aggregations: any;
        data: BlogType[];
        links: any;
        meta: any;
      };
    })
    .catch(() => notFound());
};
