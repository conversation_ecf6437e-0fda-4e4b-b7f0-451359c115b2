import {
  MissingSection,
  MissingSections,
  PopupStateType,
} from "@/types/userProfile";

export const INITIAL_POPUP_STATE: PopupStateType = {
  type: "",
};
export const PROFILE_CONTEXT_TYPE = {
  POPUP_CLOSED: "POPUP_CLOSED",
  POPUP_CONVERT_CV: "POPUP_CONVERT_CV",
  POPUP_BASIC_INFOMATION: "POPUP_BASIC_INFOMATION",
  POPUP_SUMMARY: "POPUP_SUMMARY",
  POPUP_SKILLS: "POPUP_SKILLS",
  POPUP_EXPERIENCES: "POPUP_EXPERIENCES",
  POPUP_EDUCATIONS: "POPUP_EDUCATIONS",
  POPUP_PROJECTS: "POPUP_PROJECTS",
  TOGGLE_SELECT_CONVERT_CV: "TOGGLE_SELECT_CONVERT_CV",
  TOGGLE_OPEN_TO_WORK: "TOGGLE_OPEN_TO_WORK",
  TOGGLE_OPEN_BASIC_INFO: "TOGGLE_OPEN_BASIC_INFO",
  TOGGLE_OPEN_CONVERT_CV: "TOGGLE_OPEN_CONVERT_CV",
};

export const PROFILE_STATUS = {
  new_star: "new_star",
  standard: "standard",
  booster: "booster",
};

export const STATUS_MISSING_SECTIONS: MissingSections = {
  // new_star: [
  //   {
  //     section: "basic_info",
  //     title: "user_profile_basic_info_modal_header_title",
  //     action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
  //   },
  // ] as MissingSection[],
  standard: [
    {
      section: "summary",
      title: "user_profile_summary_title",
      action: PROFILE_CONTEXT_TYPE.POPUP_SUMMARY,
    },
    {
      section: "skills",
      title: "user_profile_skill_title",
      action: PROFILE_CONTEXT_TYPE.POPUP_SKILLS,
    },
    {
      section: "experiences",
      title: "user_profile_work_experience_title",
      action: PROFILE_CONTEXT_TYPE.POPUP_EXPERIENCES,
    },
    {
      section: "educations",
      title: "user_profile_education_title",
      action: PROFILE_CONTEXT_TYPE.POPUP_EDUCATIONS,
    },
    {
      section: "projects",
      title: "user_profile_project_title",
      action: PROFILE_CONTEXT_TYPE.POPUP_PROJECTS,
    },
  ] as MissingSection[],
  new_star: [
    {
      section: "fullName",
      title: "information_missing_fullname",
      action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
    },
    {
      section: "email",
      title: "information_missing_email",
      action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
    },
    {
      section: "phone",
      title: "information_missing_phone_number",
      action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
    },
    {
      section: "cityOrProvince",
      title: "information_missing_city_or_provice",
      action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
    },
    {
      section: "technicalSkills",
      title: "information_missing_technical_skill",
      action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
    },
    {
      section: "yearOfExperience",
      title: "information_missing_year_of_experience",
      action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
    },
    {
      section: "position",
      title: "information_missing_position",
      action: PROFILE_CONTEXT_TYPE.POPUP_BASIC_INFOMATION,
    },
  ] as MissingSection[],
};

export const PROFILE_OPEN_TO_WORK = {
  OPEN_TO_WORK: 7390,
  NOT_OPEN_TO_WORK: 7391,
};

export const IS_SHOW_CONVERT_CV_MODAL = "isShowConvertCVModal";
export const IS_SHOW_BASIC_INFO_MODAL = "isShowBasicInfoModal";
