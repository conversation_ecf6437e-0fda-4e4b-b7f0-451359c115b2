import { TaxonomyType } from "@/types/taxonomy";

export const YOUR_SEARCH_KEYWORDS = [
  "TOP IT Jobs",
  ".NET Developer ",
  "C",
  "C++ Developer  ",
  "Embedded Developer ",
  "Java Developer ",
  "Mobile Developer",
  "Tester",
  "Business Analyst",
  "NodeJS Developer",
  "IT Security ",
  "Python Developer",
  "IT helpdesk ",
  "UI",
  "UX Designer ",
  "PHP Developer",
  "Data Analyst ",
  "Back-end Developer ",
  "DevOps Engineer ",
  "Full-stack Developer",
  "Product Owner",
  "Front-end Developer",
  "System Administrator",
];

export const SUGGESTED_KEYWORDS: TaxonomyType[] = [
  {
    id: 21,
    taxonomy: "skills",
    text: "Java",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "java",
    text_vi: "Java",
    text_en: "Java",
    sort_order: 0,
  },
  {
    id: 19,
    taxonomy: "skills",
    text: "C++",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "c-plus",
    text_vi: "C++",
    text_en: "C++",
    sort_order: 0,
  },
  {
    id: 22,
    taxonomy: "skills",
    text: "JavaScript",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "javascript",
    text_vi: "JavaScript",
    text_en: "JavaScript",
    sort_order: 0,
  },
  {
    id: 8611,
    taxonomy: "skills",
    text: "UI/UX",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "uiux",
    text_vi: "UI/UX",
    text_en: "UI/UX",
    sort_order: 0,
  },
  {
    id: 24,
    taxonomy: "skills",
    text: "C#",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "c-sharp",
    text_vi: "C#",
    text_en: "C#",
    sort_order: 0,
  },
  {
    id: 1617,
    taxonomy: "job_levels",
    text: "Fresher",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "fresher",
    text_vi: "Fresher",
    text_en: "Fresher",
    sort_order: 0,
  },
  {
    id: 34,
    taxonomy: "skills",
    text: "Python",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "python",
    text_vi: "Python",
    text_en: "Python",
    sort_order: 0,
  },
  {
    id: 1,
    taxonomy: "skills",
    text: "PHP",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "php",
    text_vi: "PHP",
    text_en: "PHP",
    sort_order: 0,
  },
  {
    id: 1452,
    taxonomy: "skills",
    text: "Product Owner",
    image: "",
    thumbnail_url: "",
    banner_url: "",
    feature: "",
    slug: "product-owner",
    text_vi: "Product Owner",
    text_en: "Product Owner",
    sort_order: 0,
  },
];
