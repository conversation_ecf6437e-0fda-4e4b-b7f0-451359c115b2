import { SoftSkillType } from "@/types/interview";

export const SOFT_SKILL_DATA: SoftSkillType[] = [
  {
    title: `Bạn hiểu thế nào về điểm mạnh?`,
    answer: `Thuật ngữ điểm mạnh khá gần gũi nhưng trước tiên, cùng xem nó có ý nghĩa thế nào. Điểm mạnh (Strengths) là những thế mạnh của bạn về tố chất, các kỹ năng, kinh nghiệm hoặc trình độ chuyên môn nổi trội của bạn tương ứng trong giới hạn khả năng được vận dụng vào cuộc sống, quá trình thực hiện công việc của bạn.\n\nMỗi người chúng ta đều có những điểm mạnh khác nhau, khó trộn lẫn. Và tùy vào tình huống thực tế, những điểm mạnh sẽ được phát huy, cân bằng một cách tốt nhất. Những điểm mạnh cơ bản mà bạn có thể biết bao gồm:\n\n- Năng lực chuyên môn giỏi \n\n- Tính trung thực cao \n\n-  Có trách nhiệm - ý thức cao trong công việc; nhiệt huyết và niềm đam mê công việc\n\n- Trình độ ngoại ngữ tốt (Đạt tiêu chuẩn giao tiếp quốc tế - Tiếng Anh, Tiếng Nhật, Tiếng Trung,...\n\n- Khả năng học hỏi, ứng biến linh hoạt với môi trường, nhạy bén với cái mới\n\n- Sức sáng tạo\n\n- Tinh thần kỷ luật cao, có đạo đức nghề nghiệp\n\n- Sự kiên nhẫn\n\n - Hòa đồng -  thân thiện với mọi người xung quanh\n\n- Mức độ quyết tâm hoàn thành công việc\n\n- Kỹ năng mềm\n\n- Làm việc có nguyên tắc, đúng giờ, chuyên nghiệp\n\n- Sự năng động\n\n- Kỹ năng lên kế hoạch và giải quyết vấn đề tốt\n\n- Thành thạo kỹ năng tin học\n\n- Sở hữu những năng khiếu về nghệ thuật (ca hát, làm MC. diễn xuất,...)`,
  },
  {
    title: `Vậy thế nào là điểm yếu?`,
    answer: `Điểm yếu (Weaknesses) là những điểm hạn chế, những điểm mà bản thân bạn cảm tấy không tự tin về chúng. Cũng có thể hiểu một phần nó không phải trường chính mà bạn ứng dụng vào công việc lẫn cuộc sống.\n\nĐiểm yếu thường bao gồm:\n- Kỹ năng hay năng lực chuyên môn nghề nghiệp chưa tốt\n\n- Chưa có sự định hướng hay mục tiêu rõ rãng trong công việc\n\n- Trình độ ngoại ngữ chưa đảm bảo tiêu chuẩn cơ bản (Đọc, viết, giao tiếp, nghe)\n\n- Kỹ năng tin học văn phòng chưa tốt\n\n- Kỹ năng giao tiếp chưa hoàn thiện, còn sợ và e dè - không tự tin trước đám đông\n\n- Những thói quen tích cực ảnh hưởng đến các tính trách nhiệm, ý thức tự giác, tình thần làm việc,...`,
  },
  {
    title: `Trình bày ưu nhược điểm trong CV –  Đâu là những điều cần lưu ý?`,
    answer: `Đề làm nổi bật được nội dung điểm mạnh điểm yếu của bản thân trong CV, bạn cần chú ý những vấn đề sau:\n\nTập trung trình bày các ưu điểm gắn với công việc, không nên lan man. Đừng kể lể quá nhiều sẽ làm nhà tuyển dụng cảm thấy bạn không đáng tin. Trình bày ưu điểm với những từ ngữ đơn giản, tránh việc “bày vẽ” quá nhiều thứ sẽ khó tạo được những thiện cảm cho nhà tuyển dụng.\n\nNói như vậy, không cò nghĩa là bạn chị tập trung vào việc trình bày điểm mạnh. Bạn hạy khôn khèo và cân bằng việc đưa các điểm yếu của mình vào CV. Tuy nhiên, hãy gắn nó với những cách thức giúp bạn vượt qua hoặc khắc phục tốt nhất những nhược điểm ấy. Đó là một cách thức thật sự thông minh dành cho bạn.\n\nMột điều quan trọng nữa mà bạn cần lưu tâm chính là phải thật sự trung thực với những gì mình chia sẻ với nhà tuyển dụng. Không nên nói quá nhiều về điểm mạnh. Vì nếu thế, dường như bạn chỉ đang khoe khoang hoặc đang “thùng rỗng kêu to”. Nhà tuyển dụng họ là những người có cái nhìn tổng quan lẫn chi tiết nhất. Vì thế, việc kiểm chứng tính xác thực về những gì bạn trình bày là một điều hoàn toàn dễ dàng dối với họ.  `,
  },
  {
    title: `Những câu hỏi thách thức trong buổi phỏng vấn của bạn`,
    answer: `Thách thức tuyển dụng luôn là điều mà mọi ứng viên cần phải trải qua. Và để có thể đồng hành cùng các doanh nghiệp/tổ chức, bạn cần vượt qua những thách thức ấy trong chính buổi phỏng vấn của mình. Và cụ thể là việc cách bạn trả lời câu hỏi: “Anh chị hãy nói về điểm mạnh và điểm yếu của mình”.\n\nNhiều thách thức được đặt ra trong chính quá trình giới thiệu bản thân khi phỏng vấn. Việc của ứng viên là cần bình tĩnh để xử lý các thách thức. Bạn không nên hoang mang để rồi cảm thấy bị bế tắc trong chính những thách thức ấy. Hãy có cách ứng xử thông mình, hiệu quả phỏng vấn hầu như đạt mức tuyệt đối. Tuy nhiên, nhiều câu trả lời dường như chỉ tập trung vào việc đào sâu các vấn đề nhạy cảm liên quan đến công việc. Cụ thể, nhiều ứng viên sẽ đề cập đến mức lương và các chế độ thuộc về mặt quyền lợi của nhân viên. Từ đây, các ý kiến khác nhau được bàn luận. Hãy cẩn trọng để không phải mắc phải cấm kỵ khi đi phỏng vấn nhé! `,
  },
  {
    title: `Bạn muốn mình là ai và như thế nào trong 5 năm tới?`,
    answer: `Nhiều ứng viên đã trình bày các kế hoạch không một tí liên quan gì đến công việc như: mua nhà, đi du lịch, kinh doanh,…\n\nHoặc thậm chí, ứng viên bộc lộ nhiều sự cường điệu trong cách dẫn dắt câu chuyện. Chẳng hạn như việc chia sẻ rằng họ sẽ là một người có đam mê với công việc nhiều nhất; làm việc chăm chỉ nhất. Tồi tệ hơn là có nhiều ứng viên chỉ cười trừ vì không biết phải trả lời như thế nào. Vậy đâu là lỗi khi đi phỏng vấn, gây ấn tượng lúc phỏng vấn? \n\nBạn phải thật sự cẩn trọng trong cách trả lời của mình. Nếu không, bạn sẽ thất bại về vấn đề tri nhận các yêu cầu phản hồi thông điệp. Dù bạn cố tình hay vô tình (thiếu sự trải nghiệm), bạn cũng không nên khiến mình thiếu chuyên nghiệp.`,
  },
  {
    title: `Em mong muốn mức lương bao nhiêu? Theo em tự đánh giá, với năng lực hiện tại thì mức lương cụ thể nào phù hợp với em? `,
    answer: `Nhiều ứng viên đã đánh giá về mức lương. Họ chê mức lương hoặc đề nghị một mức lương cao hơn. Tất nhiên, câu chuyện về quyền lợi thì bất cứ ứng viên nào cũng đều quan tâm. Nhưng họ cần biết, nếu là một ứng viên thông minh và nhạy bén, ít ai lại “cò kèo” lương với nhà tuyển dụng. Vậy đâu là lỗi khi đi phỏng vấn, gây ấn tượng lúc phỏng vấn? \n\nMột cách phản hồi cấm kỵ khi đi phỏng vấn nữa đó là ứng viên than vãn hoặc đòi hỏi quá nhiều về “giấc mơ doanh nghiệp”. Ví dụ họ muốn có một môi trường làm việc tốt, một người sếp biết quan tâm, các kế hoạch cho sự thăng tiến,… Đây mới thật sự là những gì họ quan tâm.`,
  },
  {
    title: `Đừng lặp lại những thông tin trong CV`,
    answer: `Chắc chắn nhà tuyển dụng sẽ không hề mong muốn nghe lại những chia sẻ của bạn suy quanh chiếc CV. Đơn giản vì họ đã nghe xem xét và nắm bắt những thông tin chung quanh bạn rồi. Bạn có thể đề cập đến thời gian bắt đầu sự nghiệp; một số công việc đã trải nghiệm và vai trò gần đây nhất của bạn. Nhưng việc lặp lại các khía cạnh sâu như những gì họ thấy trong CV là một điều hoàn toàn không nên.`,
  },
  {
    title: `Cách trả lời ưu nhược điểm của bản thân bằng tiếng anh`,
    answer: `Phỏng vấn tiếng anh hiện nay không còn là một cách thức quá xa lạ. Tuy nhiên, bạn sẽ giải quyết thế nào khi phải trả lời câu hỏi: “Điểm mạnh và điểm yếu của em là gì” bằng tiếng anh. Đừng lo lắng vì và hãy thật bình tĩnh nào. Sẽ có hai nhiệm vụ quan trọng mà bạn cần nắm trong trường hợp này. Thứ nhất chính là tư duy thật nhanh về các điểm yếu của mình trước. Sau đó suy nghĩ cách trả lời thật ngắn gọn chúng bằng tiếng anh. \n\nBản thân bạn có thể trả lời câu phỏng vấn với ý khái quát chung, chưa cần quá chi tiết vì đó cũng không phải điều mà nhà tuyển dụng muốn khai thác.\n\nHãy nhớ, sự bình tĩnh trong việc đưa ra sự trình bày trước các câu hỏi tiếng anh là điều quan trọng nhất. Bạn chỉ cần kiểm soát tốt cảm xúc, trình bày rõ ràng, rạch mạch, bạn sẽ thành công. Đừng để bản thân hoảng sợ và mất đi cơ hội. Cố gắng lên nào! Họ không quá chú trọng đến việc đúng ngữ pháp, từ ngữ chuyên môn của bạn. Cái họ quan tâm vẫn là khả năng thích ứng và giải quyết vấn đề thách thức của bạn từ câu hỏi tiếng anh. Vì vậy, hãy cứ trả lời bằng khả năng của chính bạn.`,
  },
  {
    title: `Ai sẽ là người phỏng vấn tôi?`,
    answer: `Nếu may mắn vượt qua ải vòng đầu tiên, việc tiếp theo là chờ đợi email; cuộc gọi từ nhân sự về buổi phỏng vấn quyết định.\n\nBạn cần biết rõ người mà mình sẽ được gặp trong cuộc phỏng vấn là ai. Vì vậy, đừng ngần ngại hỏi nhà tuyển dụng xem ai sẽ là người phỏng vấn bạn tiếp theo. Tùy từng đối tượng mà cách bạn đưa ra câu hỏi cũng khác nhau.\n\nMột vài tip chính là hãy nghiên cứu một số thông tin về họ nếu có thể. Ví dụ như: kinh nghiệm, cá tính, phong cách làm việc,... để làm chủ cuộc phỏng vấn. Điều này cũng thể hiện bạn là ứng viên biết cách tạo ấn tượng với bạn phỏng vấn.`,
  },
  {
    title: `Có thể cho tôi biết rõ hơn về cơ hội phát triển khi tôi làm việc tại đây? Lý do nào nhà tuyển dụng nhận thấy tôi phù hợp?`,
    answer: `Trước khi ứng tuyển vào một vị trí bất kỳ, chí ít bạn đã có những hiểu biết cơ bản về doanh nghiệp của mình. Từ đó, bạn có cơ sở để hoàn thiện bản thân và chứng minh mình là một mảnh ghép phù hợp. Điều đó cho thấy sự tâm huyết và bạn thật sự khao khát vị trí này. \n\nCâu hỏi phỏng vấn này là một thách thức lớn với họ đấy! Bạn sẽ đánh giá được mức độ quan tâm của họ về việc thiết lập lộ trình phát triển năng lực cho nhân viên. Dĩ nhiên, mức độ tốt hay không tốt là do cảm nhận của riêng bạn. Nếu nhà tuyển dụng không thể xác định được điều gì ở bạn phù hợp với công ty hoặc cố tình phớt lờ. Điều đó chứng tỏ họ chưa thật sự xây dựng ra một hệ giá trị để đánh giá ứng viên, phát triển nhân viên. Do vậy, bạn cần phải thận trọng hơn trong mọi quyết định của mình. `,
  },
  {
    title: `Tôi khó khăn trong việc xác định các mục tiêu vì chưa rõ những mong muốn cụ thể từ quý công ty/doanh nghiệp, tôi có thể biết thêm về điều này được không? `,
    answer: `Tất nhiên nhà tuyển dụng sẽ hỏi bạn về mục tiêu và kế hoạch nghề nghiệp của bạn. Để “lật bài ngửa”, bạn hãy khéo léo pha chút “gia vị” để cuộc phỏng vấn thêm thú vị.\n\nCách tốt nhất chính là bạn chủ động yêu cầu họ đưa ra những tiêu chí cụ thể. Đâu là những mong muốn mà họ kỳ vọng mà người làm vị trí này có thể đạt được. \n\nĐiều này rất quan trọng. Vì nó giúp bạn xác định rõ mục đích sau cùng của cuộc tuyển chọn này. Liệu bạn là người họ đang tìm kiếm đồng hành phát triển? Hay bạn chỉ là một giải pháp thay thế tạm thời cho vị trí còn thiếu? Việc xác định rõ giúp bạn có thể hình dung được một phần những gì bạn cần phải làm. Chính điều này khiến bạn biết cách đáp ứng nhu cầu từ nhà tuyển dụng nếu may mắn được chọn lựa. `,
  },
  {
    title: `Người giữ vị trí này trước đây tại sao lại nghỉ việc?`,
    answer: `Câu hỏi này không quá khó khăn để bạn nói với nhà tuyển dụng. Và tất nhiên, họ cũng có quyền tiết lộ hoặc giữ kín. Tuy nhiên, nếu nhận thấy các cá nhân từng giữ vị trí ấy có được một công việc tốt hơn tại nơi khác thì đó là dấu hiệu tốt. Có thể công ty chính là bệ phóng tốt để bạn học hỏi và phát triển.\n\nCâu hỏi này còn có thể giúp bạn mở ra nhiều câu hỏi khác có liên quan. Một số nhà tuyển dụng họ không ngần ngại chia sẻ lý do tại sao các nhân viên trước lại nghỉ việc/chuyển công tác. Từ đó, bạn sẽ đánh giá được mức độ có liên quan đến công việc như khả năng đồng hành, mức độ cạnh tranh, áp lực công việc,...`,
  },
  {
    title: `Thách thức lớn nhất đối với người giữ vai trò này là gì?`,
    answer: `Không vị trí nào là hoàn hảo. Trong thực tế, một số vai trò được tạo ra để gắn với một vấn đề cần phải giải quyết. Đó có thể hiểu là những sức hút mà công việc ảnh hưởng đến bạn.\n\nMột người sử dụng lao động trung thực sẽ cho bạn biết áp lực nào nằm phía trước.  Hoặc ít nhất họ cung cấp cho bạn những mô tả cơ bản về các khó khăn mà bạn cần vượt qua.\n\nBạn cần lắng nghe, đặt câu hỏi và ghi nhận các phản hồi. Đồng thời, chấp nhận các thách thức và trình bày một số ý tưởng về cách bạn sẽ giải quyết những trở ngại. Ban phỏng vấn từ nhà tuyển dụng sẽ ấn tượng về bạn đấy!`,
  },
  {
    title: `1. Hãy kể cho tôi về 1 project mà bạn đã từng làm việc trong 6 tháng qua`,
    answer: `Câu này sẽ khiến người được hỏi kể về những gì đã thực sự xảy ra - chứ không phải mô tả lại 1 tình huống lý tưởng nào đó.\n\nĐừng quên đào sâu vào những khía cạnh khác để bạn hiểu được chính xác những chi tiết nhỏ nhặt nhất.\n\nAi đã đề xuất dự án này?\nLàm sao để dự án được ưu tiên hơn?\nAi là người chịu trách nhiệm cho dự án này và làm cách nào để quyết định được điều đó?\nLàm cách nào để bạn giải quyết những bất đồng trong team project?\nManager của bạn chịu trách nhiệm như thế nào với project?\nQuy trình review dự án như thế nào?\nNếu phải thực hiện lại project lần nữa, bạn sẽ làm khác đi chuyện gì?`,
  },
  {
    title: `2. Các quyết định về sản phẩm được đưa ra như thế nào?`,
    answer: `Đây là 1 câu hỏi mở có chủ đích. Từ đây, bạn có thể khai thác được những điều sau:\n\nhiểu sâu hơn về sứ mệnh và tầm nhìn liên quan đến product của công ty\nliệu sứ mệnh và tầm nhìn đó có thực sự gắng liền với các dự án mà công ty đang triển khai hay không\n\nTheo sau đó, bạn có thể hỏi:\n\nSứ mệnh và năng lực cốt lõi của công ty là gì?\nLàm thế nào để việc ra mắt sản phẩm hiện tại đáp ứng được những điều đó?\nCó những thách thức nào mà công ty cần vượt qua để đạt được sứ mệnh của mình?\nLàm thế nào để công ty ưu tiên các khoản đầu tư kĩ thuật hay các khoản đầu tư sản phẩm?`,
  },
  {
    title: `3. Công ty đã giúp bạn đạt được mục tiêu nghề nghiệp ra sao?`,
    answer: `Công ty mà bạn đang muốn gia nhập chắc chắn phải tương đồng với các dự định nghề nghiệp của bạn. Lý tưởng nhất là khi gia nhập công ty, bạn sẽ làm những loại công việc đáp ứng chính xác kế hoạch, dự định đó.\n\nNhững câu hỏi bạn có thể đặt ra với người phỏng vấn bao gồm:\n\nBạn và quản lý của mình có trao đổi về mục tiêu nghề nghiệp của bạn?\nQuản lý giúp bạn xác định và thực hiện những mục tiêu đó như thế nào?\nĐiều gì sẽ xảy ra khi mục tiêu cá nhân của bạn không phù hợp với công việc mà bạn đang làm mà công việc đó lại có sức ảnh hưởng lớn nhất đến công ty?\nLàm thế nào để mọi người phát triển và tiến bộ trong nghề nghiệp của họ tại công ty?\nNhân viên mới sẽ được giao trách nhiệm ở chừng mực nào?`,
  },
  {
    title: `4. Văn hóa công ty khác gì với các công ty công nghệ khác?`,
    answer: `Rất nhiều công ty Tech có chính sách đãi ngộ tốt, những nhân tài kiệt xuất, văn phòng đẹp, vậy bạn nên xác định được điều gì phân biệt công ty này với các công ty khác.\n\nNhững điểm mà bạn có thể cân nhắc chính là:\n\nTỷ lệ giữa những người có kinh nghiệm với những nhân viên mới tuyển dụng\nMức độ mọi người giao lưu với nhau ngoài công việc\nTính linh hoạt về địa điểm và thời gian làm việc\nMức độ trân trọng sự khác biệt\n\nNhững con người khác biệt sẽ phát triển tốt tại những môi trường và văn hóa làm việc khác biệt, và việc tìm kiếm 1 nơi giúp bạn làm việc năng suất nhất, cảm thấy thoải mái nhất là 1 yếu tố rất quan trọng.`,
  },
  {
    title: `5. Cơ hội nào cho nhân viên để học hỏi những điều mới?`,
    answer: `Học hỏi là 1 trong những thứ có tỷ lệ ROI (Return on Investment) cao nhất về lâu dài. Hầu hết các công ty đều phát triển những quy trình nhập môn và hướng dẫn người mới khá bài bản, nhưng tôi lại đặc biệt đánh giá cao những công ty liên tục đầu tư vào việc phát triển con người trong những tháng đầu tiên.\n\nVí dụ:\n\nLiệu có dễ dàng không nếu mọi người muốn chuyển sang những teams khác và thử nghiệm những vai trò mà họ chưa từng làm trước đây?\nCó cách nào để học hỏi 1 lĩnh vực kĩ thuật khác so với những gì mà bạn đang làm?\nLàm cách nào để mọi người chia sẻ những kiến thức có được từ những projects cũ và giúp đỡ nhau phát triển kiến thức chung?`,
  },
  {
    title: `Glassdoor: Giữa HackerRank, whiteboarding, paired programming v.v Hiện nay các kiểu phỏng vấn rất đa dạng. Theo chị ứng viên thực sự mong muốn điều gì nhất ở một buổi phỏng vấn ?`,
    answer: `McDowell: Một buổi phỏng vấn điển hình bắt đầu với một hoặc hai cuộc phỏng vấn qua điện thoại (ít nhất một trong số đó là về kĩ thuật), tiếp theo sẽ có từ 4 đến 6 buổi phỏng vấn tại công ty. Trong những buổi phỏng vấn tại công ty, một trong những loại câu hỏi phổ biến là câu hỏi về hành vi. Những câu khác sẽ nghiêng về kĩ thuật, thường bao gồm coding / thuật toán, thiết kế hoặc các kiến ​​thức công nghệ chuyên sâu và kĩ năng.\n\nMột buổi phỏng vấn bình thường diễn ra từ 45 đến 60 phút, và bắt đầu với một hoặc hai câu hỏi nhanh về hành vi. Mục đích là để có thêm thông tin về ứng viên và giúp họ thư giãn khi bắt đầu buổi phỏng vấn. Nếu nhà tuyển dụng bước vào và ngay lập tức đưa ra một câu hỏi liên quan đến kĩ thuật, điều này có thể gây ra cảm giác sợ hãi.\n\nỞ những buổi phỏng vấn này, bạn sẽ phải thể hiện kỹ năng của mình qua tấm bảng trắng, chứ không phải trên máy tính xách tay mà bạn hay dùng. Tuy nhiên, một số công ty sẽ offer cho bạn máy tính nếu cần. Mục tiêu của dạng phỏng vấn này là đánh giá các kỹ năng giải quyết vấn đề của ứng viên và xem liệu họ có thể sắp xếp những suy nghĩ của họ thành những dòng code chính xác và có cấu trúc hợp lý hay không. Nhìn chung, nhà tuyển dụng sẽ bỏ qua các vấn đề cú pháp nhỏ (đặc biệt là trên bảng trắng), nhưng tôi vẫn khuyến khích các ứng viên đưa ra câu trả lời tốt nhất có thể. Bạn không cần thiết phải đạt đến mức độ hoàn hảo, nhưng nếu bạn quá cẩu thả hoặc hời hợt với các tiểu tiết, nó có thể ảnh hưởng đến quyết định của nhà tuyển dụng.\n\nMột số công ty cũng sẽ đánh giá qua các buổi test trực tiếp, hoặc sử dụng các công cụ như HackerRank hoặc làm một dự án độc lập. Điều này thường xảy ra ngay trước hoặc sau khi phỏng vấn qua điện thoại, nhưng trong một số trường hợp, cũng có thể xảy ra ngay sau khi phỏng vấn tại công ty. Một số công ty sử dụng nó như cách để thu thập thêm thông tin khi nhà tuyển dụng không thu thập đủ thông tin để đánh giá ứng viên.`,
  },
  {
    title: `Glassdoor: Những nguyên tắc lập trình cơ bản nào mà chị cảm thấy ứng viên cần phải xem lại trước khi tham gia buổi phỏng vấn?`,
    answer: `McDowell: Giả sử công ty sẽ đưa ra một số bài tập về code, nên việc có nền tảng về cấu trúc dữ liệu và các thuật toán thực sự rất quan trọng. Sau đây là những khái niệm cơ bản như binary search tree và breadth-first search. Chúng khá dễ học, nhưng lại có rất nhiều câu hỏi phỏng vấn kiến thức về những chủ đề này. Một ứng viên cũng nên biết cách viết code bằng một hoặc nhiều ngôn ngữ khác nhau. Sẽ không có vấn đề gì nếu bạn quên các parameter chính xác của phương pháp substring, nhưng bạn nên biết cách viết code hợp lý - cho các loops, các hàm, các lớp, v.v ... mà không cần phải dựa vào việc tìm kiếm trên mạng.`,
  },
  {
    title: `Glassdoor: Vì cấu trúc dữ liệu và các thuật toán rất quan trọng, liệu bạn có thật sự cần một tấm bằng CS để có thể làm việc tại một công ty công nghệ hàng đầu hay không?`,
    answer: `McDowell: Không hẳn. Với tấm bằng CS, một ứng viên có thể sẽ biết các cấu trúc dữ liệu cốt lõi và các thuật toán. Nhưng ứng viên chỉ cần vài tuần để học, vì vậy nó không thực sự đem lại cho ứng viên lợi thế lớn.\n\nBất lợi lớn nhất của các ứng viên không có bằng CS là sự thiếu tự tin.\n\nTôi đã thấy nhiều ứng viên không có tấm bằng lận lưng hoảng sợ ngay khi gặp câu hỏi liên quan đến \`học tập\`. Đó là điều mà tôi gọi là \`nỗi sợ không đánh mà đau\`. Khi tôi hỏi một câu hỏi liên quan đến trees hoặc đồ thị, mặc dù họ có kiến thức để giải quyết nó, họ vẫn từ bỏ ngay lập tức. Họ nghĩ mọi người có thứ gì đó \`nhiều hơn\` họ biết, và họ thậm chí không thử.`,
  },
  {
    title: `Glassdoor: Theo chị, cách thực hành một buổi phỏng vấn tốt nhất sẽ như thế nào?`,
    answer: `McDowell: Bạn có thể xem Cracking the Coding Interview như một tài liệu hữu ích cho mình. Nó có tất cả những kiến thức cần thiết và cung cấp nhiều ví dụ thực tiễn khác nhau mà bạn có thể thử qua. Nếu bạn không có bằng CS, nó sẽ giúp bạn tìm hiểu một số nguyên tắc cơ bản. HackerRank cũng có thể cung cấp cho bạn các bài tập thực hành.\n\nTự nghiên cứu rất quan trọng, nhưng hãy nhớ rằng đôi khi bạn tham gia phỏng vấn, họ sẽ không cung máy tính đâu - thay vào đó họ sẽ đưa một cái bảng trắng. Họ làm điều đó một phần là vì phương pháp này khuyến khích mọi người suy nghĩ và truyền đạt nhiều hơn, nhưng đòi hỏi bạn phải biết phải hướng giải quyết trước khi viết thành code. Bạn cần thực hành nhiều trong hoàn cảnh đó, do đó bạn có thể cần tìm một người bạn đồng hành và thực hành phỏng vấn với bạn ấy. Nó không chỉ giúp bạn có nhiều kinh nghiệm hơn trong vị trí ứng viên - nó cũng cho bạn kinh nghiệm như một nhà tuyển dụng. Có nhiều thứ ứng viên không thể hiểu cho tới khi họ có cơ hội trải nghiệm những điều đó.`,
  },
  {
    title: `Glassdoor: Việc luyện tập trước khi tham gia buổi phỏng vấn có ảnh hưởng xấu đến biểu hiện của ứng viên hay không? Mọi người có thường chú ý nhiều đến việc trình bày câu trả lời như nội dung của nó không?`,
    answer: `McDowell: Khi luyện tập hợp lí, các câu hỏi bạn nhận được trong buổi phỏng vấn là những câu bạn chưa bao giờ nghe. Trên thực tế, mục đích của những câu hỏi này là đánh giá cách bạn giải quyết những vấn đề bạn chưa từng gặp. Nếu tôi yêu cầu bạn thiết kế một thành phố hoặc nghĩ một cách mới để làm việc gì đó, bạn không nhất thiết phải có một câu trả lời \`sai\`, nhưng có nhiều cách sai để làm điều đó. Như bạn biết, đôi khi bạn sẽ gặp các công ty tìm kiếm và hỏi các câu hỏi nổi tiếng của Google, điều đó sẽ dẫn đến việc chọn nhầm những ứng viên luyện tập quá kĩ. Nhưng nhìn chung đó là những công ty không biết họ đang làm gì. Những nhà tuyển dụng có thể phần nào đánh giá bạn thông qua cách bạn giao tiếp, nhưng thường họ sẽ \` dễ dãi \` ở điểm này - trừ khi có một điểm cấm kị như kiểu tự tin thái quá. Điều quan trọng nhất vẫn là khả năng giải quyết vấn đề hiệu quả với nền tảng vững chắc.`,
  },
  {
    title: `Glassdoor: Theo chị, việc ứng viên không trả lời được câu hỏi trong buổi phỏng vấn có ảnh hưởng đến kết quả hay không? Nếu ứng viên không biết câu trả lời, cách phản hồi tốt nhất là gì?`,
    answer: `McDowell: Hoàn toàn bình thường! Tôi cho rằng việc bạn không biết trả lời một câu hỏi là chuyện rất bình thường. Tôi khuyên bạn nên dành một chút thời gian suy nghĩ và đảm bảo rằng hiểu rõ tất cả các chi tiết của vấn đề một cách chính xác. Sau đó sử dụng bảng ghi chép lại các chi tiết và đưa ra các ví dụ để tìm ra cách giải quyết của vấn đề. Hãy tưởng tượng bạn hỏi những người không biết gì về code các câu hỏi chuyên sâu về mảng này, chắc chắn họ sẽ không biết câu trả lời - nhưng nếu bạn nói với họ rằng hãy đọc tài liệu và tìm thử đi, thì tỷ lệ cao là họ sẽ làm được. Vì vậy, hãy xem xét kĩ các thông tin trong câu hỏi để tìm ra đáp án hợp lí nhất. Khi bạn đã có một số hướng giải quyết, ngay cả khi chúng không phải là cách tốt nhất, hãy nghĩ về những trường hợp tệ nhất và cách tối ưu hóa chúng. Nếu hướng giải quyết của bạn không thành công, hãy suy nghĩ lại thật kĩ để xác định lí do thất bại.\n\nKhi đã tìm ra giải pháp, đừng vội vàng code liền tay. Bạn có biết rằng, một trong những điều làm con người chậm lại là tính hấp tấp - họ có một ý tưởng cơ bản về cách giải quyết, ngay lập tức họ lao đầu vào viết code cho nó. Lấy một ví dụ tương tự, hãy tưởng tượng bạn muốn lái xe đến một nơi cách đây hai dặm. Bạn mường tượng sơ sơ được đường đi đến đó, nhưng bạn có nên khởi hành ngay lúc đó? Hoàn toàn có thể, nhưng bạn có thể sẽ quẹo sai đường, đi sai hướng rất nhiều lần. Thay vào đó, 3 phút tìm hiểu hướng đi trước khi di chuyển sẽ giúp bạn hoàn thành chuyến đi dễ hơn rất nhiều.`,
  },
  {
    title: `Glassdoor: Các ứng viên cần chuẩn bị gì cho buổi phỏng vấn không thuần về data structure hay các câu hỏi thuật toán chuyên sâu?`,
    answer: `McDowell: Developers cũng nên có khả năng trình bày background của mình. Không cần lâu - chỉ cần 30 giây đến một phút thôi đã là quá đủ. Mục đích là để nhà tuyển dụng có cái nhìn tổng quan về kinh nghiệm của bạn.\n\nNgoài ra, các developers nên chuẩn bị để có thể trình bày từ 3 đến 5 dự án. Họ phải trình bày được tổng quan cách tổ chức của các dự án, và bạn có thể đề cập đến những ca khó nhất mà bạn đã trải qua. Vì vậy, khi tôi hỏi các developers về những thách thức trong một dự án, câu trả lời của họ sẽ đại loại kiểu \` tôi phải học bao nhiêu là thứ.\` Với tư cách là một developer, tôi hiểu rằng làm việc với các công nghệ không quen thuộc là một thách thức. Nhưng nó là thử thách đối với để phân biệt một developer giỏi và tệ, nên điều này không gây ấn tượng với nhà tuyển dụng. Thay vào đó, câu trả lời hay nên nhắc đến các vấn đề kỹ thuật - một công cụ tối ưu hóa, một trình chữa bug, một thuật toán mới. Những điều này chứng minh bạn là một kĩ sư tuyệt vời.`,
  },
  {
    title: `Glassdoor: Bạn sẽ làm gì nếu gặp khó khăn trong buổi phỏng vấn đầu tiên?`,
    answer: `McDowell: Bên cạnh Cracking the Coding Interview, tôi có một cuốn sách tên Cracking the Tech Career, dành cho những người nghĩ rằng \` Trời ơi, tôi là một lập trình viên của một công ty bảo hiểm nhưng tôi rất muốn làm việc cho Google, tôi làm điều đó như thế nào? \` Hoặc \` Tôi đang làm trong mảng marketing, tôi có thể chuyển sang làm việc tại một công ty công nghệ như thế nào?\`. Một trong những điểm tôi đưa ra trong cuốn sách đó là, để tham gia một buổi phỏng vấn, bạn không chỉ muốn chứng minh rằng bạn là một developer giỏi, mà bạn còn là người có kiến ​​thức cơ bản để vượt qua các buổi phỏng vấn.\n\nBằng CS chắc chắn chứng minh được bạn là người có kiến ​​thức tốt. Nhưng nếu bạn không có bằng CS, tham gia các lớp data structure và các thuật toán trên Coursera sẽ giúp ích. Bạn cũng có thể lập trình ít cạnh tranh vì nó đòi hỏi phải lập trình rất cạnh tranh. Tôi cũng thấy những người cung cấp những liên kết về việc thực hiện các vấn đề của họ từ cuốn sách Cracking the Coding Interview.\n\nĐể chứng tỏ rằng bạn là một nhân viên giỏi, các dự án (open source, independent, hackathons - bất cứ điều gì!) có thể giúp ích rất nhiều. Bạn nên có ba hoặc bốn dự án lớn trong CV của bạn để chứng minh kỹ năng, niềm đam mê và sáng kiến ​​của bạn.\n\nĐừng ràng buộc bản thân với bất kì ngôn ngữ lập trình nào (điều này xảy ra rất nhiều với boot camps). Bạn không phải là một developer .NET; bạn là một developer đang sử dụng .NET. Bạn có thể học một ngôn ngữ mới và kỹ năng của bạn chắc chắn sẽ nâng cao. Các công ty công nghệ quan tâm đến các kĩ năng cơ bản hơn là việc bạn giỏi một ngôn ngữ cụ thể. Trên thực tế, họ không xem trọng những developer chỉ sử dụng một loại ngôn ngữ, nên việc đa dạng hoá các kỹ năng rất quan trọng.`,
  },
  {
    title: `Glassdoor: Việc tuyển dụng đã thay đổi như thế nào từ khi chị viết cuốn sách cuối cùng? Theo chị, có xu hướng nào chuẩn bị biến mất không?`,
    answer: `McDowell: Phiên bản mới nhất của cuốn sách cuối cùng của tôi đã xuất bản cách đây một năm rưỡi, và phiên bản cũ nhất đã được phát hành vào năm 2011. Nói thật, mặc dù mọi người đang nói về việc các buổi phỏng vấn đang thay đổi, thực sự nó không phải như vậy. Các kiểu câu hỏi về cơ bản không đổi trong 15 năm qua, nhưng có phần tập trung hơn về các hệ thống mở rộng và công nghệ web. Họ muốn biết rằng bạn có thể viết code tốt hay không, bạn có phải là người thông minh hay không, bạn có giỏi giải quyết vấn đề hay không, và bạn có thể làm việc tốt với đồng đội hay không. Cách đánh giá vẫn không thay đổi - chỉ có ngôn ngữ và công nghệ thay đổi.\n\nMặc dù tôi không nghĩ rằng quá trình phỏng vấn thay đổi ở các công ty hàng đầu, nó vẫn khá khác ở công ty kém phát triển hơn. Nhiều người nhận ra rằng: \`Này, nhìn vào Google, Microsoft, Amazon, Uber, Lyft. Những công ty này đang xây dựng một công nghệ mới - họ biết họ đang làm gì. Hãy làm những gì họ làm. \``,
  },
  {
    title: `Glassdoor: Một số người cho rằng buổi phỏng vấn không thành công là do các yếu tố như thiên vị ngầm và thực tế là chúng thường không nắm bắt chính xác loại công việc bạn đang làm trên cơ sở hàng ngày. Chị có đồng ý không?`,
    answer: `McDowell: Đó là một câu hỏi thú vị. Tôi không đồng ý rằng có sự thiên vị đối với những trường hơp thường thấy - nếu họ thấy một người phụ nữ hoặc dân tộc thiểu số, họ sẽ cho rằng những người ấy không phải là dân kĩ thuật. Tuy nhiên, tôi nghĩ rằng các buổi phỏng vấn về code ít thiên vị hơn, vì các tiêu chí đánh giá thường khách quan hơn. Thậm chí một nhà tuyển dụng \` công bằng \` ​​có thể nhận ra khả năng đưa ra giải pháp tối ưu của một ứng viên một cách nhanh chóng.\n\nTôi nghi ngờ các buổi phỏng vấn có tính chất ít khách quan hơn, ví dụ buổi phỏng vấn hành vi, có nhiều yếu tố thiên vị. Phụ nữ, ví dụ, thường không muốn khoe những thành tựu của họ, một phần là vì họ có thể bị phạt nếu làm như thế. Ngay cả khi họ làm như thế, nhà tuyển dụng vẫn có thể hỏi người đó đã làm việc như thế nào hay nhiệm vụ của họ thử thách đến mức nào.\n\nMột số công ty đã thử nghiệm các phương pháp thay thế như phỏng vấn coding trực tiếp, hoặc các dự án làm tại nhà. Nhưng như thế các ứng viên vẫn gặp nhiều vấn đề. Thời gian một bà mẹ hai con có thể làm việc tại nhà là bao lâu?\n\nChúng ta nên chú ý đến sự thiên vị ngầm, nhưng cũng cần chú ý rằng cải cách có thể biến quá trình thiên vị thành một quá trình thiên vị hơn rất nhiều lần.\n\nQuá trình phỏng vấn có thể có sai sót không? Chắc chắn có, trên thực tế có khá nhiều phản ánh chưa chính xác - những ứng viên thể hiện không tốt trong buổi phỏng vấn lại có thể là những developer tuyệt vời. Nhưng tôi sẽ nói điều này: Buổi phỏng vấn, khi được thực hiện hiệu quả, có thể tạo ra những kết quả và tài năng hàng đầu.`,
  },
  {
    title: `Kiến thức Java nền tảng`,
    answer: `Thế nào là lập trình đối tượng? Cho biết các tính chất đặc thù của lập trình hướng đối tượng?\nSự khác nhau giữa While và doWhile?\nCách tổ chức hoạt động của các Collection Framework như List , Map, Set, Queue, Stack,..?\nPhân biệt ArrayList , Linkedlist và Vector?\nSự khác nhau giữa ArrayList - Array, Linkedlist - Arraylist, Set - List, Override - Overload?\nKhái niệm về Generic? Cho ví dụ và lý do sử dụng?\nSự khác nhau giữa Abstract class và Interface?\nKhái niệm tham trị và tham chiếu?\nNgoại lệ (Exception là gì)? Phân biệt Check và Uncheck exception?\nThuật toán tìm kiếm nhị phân và thuật toán sắp xếp?`,
  },
  {
    title: `Một số câu hỏi về Git cần biết `,
    answer: `Git fork là gì? Sự khác nhau giữa git fork, branch và clone?\nSự khác nhau giữa pull request và branch?\nLàm thế nào để revert previous commit trong git?\nGiải thích những ưu điểm of Forking Workflow?\nSự khác nhau giữa HEAD, working tree và index?\nTrình bày quy trình làm việc của Gitflow Workflow?\nKhi nào nên sử dụng git stash?\nLàm thế nào để loại bỏ một tập tin từ git mà không cần loại bỏ nó khỏi file system của bạn?\nKhi nào nên sử dụng git rebase thay vì git merge?`,
  },
  {
    title: `Kiến thức về Framework (ví dụ như học Spring Framework của Java)`,
    answer: `Mô hình MVC là gì? Mô tả luồng đi của một ứng dụng MVC?\nCác khái niệm về Dependency Injection, JPA, ORM mapping, Webservice?\nGiải thích các annotation @Controller , @Service , @Repository , @Autowire?\nChức năng hoạt động của Maven?\nSự khác nhau của Session và Cookie?\nLàm thế nào để bảo mật trong lập trình?`,
  },
  {
    title: `Kiến thức về Database`,
    answer: `Khái niệm Database? Các quan hệ trong database? Các loại Join trong database?\nCác khái niệm về Composite key, Transaction, Unique?\nKhoá chính - khóa ngoại là gì?\nGiải thích các Rule chuẩn hóa dữ liệu?\nSự khác nhau Truncate, Delete , Drop?\nSự khác nhau Having và Where?`,
  },
  {
    title: `Một số câu hỏi “mẹo” về Front-end`,
    answer: `HTML, CSS, Bootstrap dùng để làm gì ?\nPhân biệt Class và Id? Phân biệt \nPhân biệt các thuộc tính Position: Absolute, Fixed, Relative, Fixed, Static\nKhai báo <!DOCTYPE> trong HTML có tác dụng gì?\nPhân biệt Class vs ID như thế nào trong CSS?\nPhân biệt toán tử “==” và “===” trong Javascript?\n\`this\` trong Javascript dùng để làm gì?\nĐánh lừa bằng cách đặt tên function trùng với một function có sẵn của thư viện`,
  },
  {
    title: `Can you introduce yourself? Tell me about yourself (Hãy giới thiệu về bản thân bạn)`,
    answer: `Đối với câu hỏi này, người phỏng vấn chủ yếu muốn ứng viên khái quát một cách tổng quan những thông tin cơ bản của bản thân và thể hiện được mong muốn của mình với công việc. Về cơ bản cách trả lời câu hỏi này cũng tương tự như cách bạn trả lời trong buổi phỏng vấn bằng tiếng Việt. Tuy nhiên, với những ứng viên đã có kinh nghiệm làm việc, ứng viên có thể bổ sung thêm những kinh nghiệm mình đã có và cách bạn có thể áp dụng những điều ấy cho vị trí mà mình đang ứng tuyển.`,
  },
  {
    title: `What are your strengths and weaknesses? (Điểm mạnh và điểm yếu của bạn là gì?)`,
    answer: `Câu hỏi này gần như luôn xuất hiện trong tất cả các buổi phỏng vấn. Mục đích của việc đặt câu hỏi điểm mạnh và điểm yếu là để người phỏng vấn có thể xem xét khả năng tự đánh giá về bản thân của ứng viên như thế nào. Bạn có đủ tự tin với những điểm mạnh của mình không cũng như có dám tiết lộ một cách chân thật điểm yếu của mình không?\r\n\r\nTham khảo:\r\n\r\nI have the ability to adapt very quickly to work and withstand pressure well. Due to the characteristics of working in the IT industry, I often work overtime to solve my assignments, but this has never been a thing that makes me feel depressed.\r\n\r\nHowever, I am still not really satisfied with my professional knowledge. That’s also the reason I signed up for more external courses to upgrade my coding skills.`,
  },
  {
    title: `Why do you want to apply for this position? (Tại sao bạn lại muốn ứng tuyển vào vị trí này?)`,
    answer: `Nếu như ở câu hỏi điểm mạnh, bạn có thể kể một cách thoải mái những điều ưu tú nhất của bản thân, thì với câu hỏi này, ứng viên nên đi sâu vào khai thác điểm mạnh ấy. Với những điểm mạnh ấy, bạn có thể đóng góp được gì cho công việc này. Đó là cách để ứng viên có thể thuyết phục nhà tuyển dụng rằng bạn phù hợp với vị trí này.`,
  },
  {
    title: `4. What are your short term goals in your career path? (Các mục tiêu ngắn hạn của bạn trong lộ trình sự nghiệp của bạn?)`,
    answer: `So với mục tiêu dài hạn, mục tiêu ngắn hạn sẽ cụ thể và thực tế hơn rất nhiều. Nhà tuyển dụng nhờ thế cũng có thể đánh giá ứng viên một cách chính xác hơn, do đó họ thích đặt câu hỏi này hơn so với mục tiêu dài hạn. Vậy nên kể cả khi câu hỏi không nêu cụ thể mục tiêu ngắn hạn hay dài hạn, ứng viên vẫn nên ưu tiên các mục tiêu ngắn hạn để gây ấn tượng với nhà tuyển dụng cũng như có tính thuyết phục cao hơn.\r\n\r\nTham khảo:\r\n\r\nI want to develop to a higher position and improve my knowledge of software programming with Python language. My specialty is computer sciences, I also have knowledge of SQL server databases, Postgres, knowledge of Linux centos operating system, Ubuntu, Odoo Framework and Python programming language. I want to continue to explore new languages ​​at work.`,
  },
  {
    title: `5. What is your expected salary? (Bạn mong đợi mức lương bao nhiêu?)`,
    answer: `Đây là câu hỏi mang tính chủ quan từ phía ứng viên do đó tốt hơn hết ứng viên nên có sự chuẩn bị trước với những câu hỏi như thế này. Bạn nên tham khảo mức lương thị trường với vị trí này để biết năng lực của mình đang ở đâu và có thể thương lượng được mức lương như ý nhất.\r\n\r\nTham khảo:\r\n\r\nMy salary expectations are commensurate with my experience and qualifications. In addition, I have also searched about the market salary for this position as well as the jobs that I can do, so I would like to receive a salary in the range from 12.000.000 VNĐ to 15.000.000 VNĐ.\r\n\r\nHi vọng một số câu hỏi tiếng Anh thường gặp trên đây sẽ giúp ứng viên trang bị thêm cho mình một số kiến thức để chuẩn bị cho những cuộc phỏng vấn quan trọng sắp tới. Đón đọc thêm các bài viết hữu ích khác về công nghệ và nhân sự tại TopDev nhé!`,
  },
];
