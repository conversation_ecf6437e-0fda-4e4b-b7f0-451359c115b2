import type { CustomFlowbiteTheme } from "flowbite-react";
export const TOPDEV_FLOWBITE_THEME: CustomFlowbiteTheme = {
  button: {
    color: {
      primary: "bg-red-500 hover:bg-red-600",
    },
  },
  textInput: {
    field: {
      input: {
        base: "block w-full border disabled:cursor-not-allowed disabled:opacity-50",
        colors: {
          gray: "bg-white placeholder:text-gray-300 border-gray-300 text-gray-500 rounded focus:ring-0 focus:border-gray-300",
        },
        withAddon: {
          off: "rounded",
        },
      },
    },
  },
  textarea: {
    colors: {
      gray: "bg-white placeholder:text-gray-300 border-gray-300 text-gray-500 rounded focus:ring-0 focus:border-gray-300",
    },
  },
  checkbox: {
    root: {
      base: "h-4 w-4 rounded border border-gray-300 dark:border-gray-600 dark:bg-gray-700 bg-gray-100",
      color: {
        default: "text-primary-600",
      },
    },
  },
  pagination: {
    pages: {
      base: "inline-flex items-center gap-1",
      previous: {
        base: "w-8 h-8 flex items-center justify-center rounded text-gray-500 hover:bg-primary hover:text-white disabled:hover:bg-white text-primary disabled:text-gray-500",
        icon: "h-5 w-5",
      },
      next: {
        base: "w-8 h-8 flex items-center justify-center rounded text-gray-500 hover:bg-primary hover:text-white disabled:hover:bg-white text-primary disabled:text-gray-500",
        icon: "h-5 w-5",
      },
      selector: {
        base: "w-8 h-8 flex items-center justify-center rounded text-gray-500 hover:bg-primary hover:text-white",
        active: "bg-primary text-white font-bold",
        disabled: "opacity-50 cursor-normal",
      },
    },
  },
  modal: {
    content: {
      inner: "relative flex max-h-[90dvh] flex-col rounded-lg bg-white shadow dark:bg-gray-700"
    }
  }
};
