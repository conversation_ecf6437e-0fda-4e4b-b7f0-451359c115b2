import {
  Blog,
  BsFillEmojiSmileFill,
  BsFire,
  Business,
  Companies,
  ConvertCv,
  CreateCv,
  Design,
  Fire,
  FresherJob,
  Interview,
  It,
  Jobs,
  Management,
  Manufacturing,
  Salary,
  Service,
  Tools,
  Top100,
} from "@/components/v2/icons";
import Account from "@/components/v2/icons/Account";
import AppliedJobs from "@/components/v2/icons/ApplyJobs";
import CvManagement from "@/components/v2/icons/CvManagement";
import Dashboard from "@/components/v2/icons/Dashboard";
import FollowingJobs from "@/components/v2/icons/FollowingJobs";
import HotJob from "@/components/v2/icons/HotJob";
import House from "@/components/v2/icons/House";
import Logout from "@/components/v2/icons/Logout";
import Setting from "@/components/v2/icons/Setting";
import { MenuItem } from "@/types/job";
import { IoLocationSharp } from "react-icons/io5";
import { background } from "storybook/internal/theming";

export const LIST_MENU = [
  {
    id: 1,
    name: "Hot Jobs",
    color: "#DD3F24",
    icon: <Fire />,
    subMenu: [
      {
        id: "A1",
        name: "Top 100 Highest Salary",
        link: "/jobs/search?ordering=high_low_salary",
      },
      {
        id: "A2",
        name: "By Location",
        subMenu: [
          {
            id: "loc_hcm",
            name: "Ho Chi Minh",
            link: "/jobs/search?region_ids=79",
          },
          {
            id: "loc_hn",
            name: "Ha Noi",
            link: "/jobs/search?region_ids=01",
          },
          {
            id: "loc_dn",
            name: "Da Nang",
            link: "/jobs/search?region_ids=48",
          },
        ],
      },
      // {
      //   id: "A3",
      //   name: "Recommend Jobs",
      //   link: "/",
      //   icon: <BiSolidLike />,
      // },
    ],
  },
  {
    id: 2,
    name: "Jobs",
    bilingualKey: "header_it_jobs",
    link: "/jobs/search",
    subMenu: [
      {
        id: "IT",
        name: "IT",
        link: "/",
        bilingualKey: "header_it_jobs",
      },
      {
        id: "Business & Finance",
        name: "Business & Finance",
        link: "/",
        bilingualKey: "header_it_jobs",
      },
      {
        id: "Management",
        name: "Management",
        link: "/",
        bilingualKey: "header_it_jobs",
      },
      {
        id: "Manufacturing & Engineer",
        name: "Manufacturing & Engineer",
        link: "/",
        bilingualKey: "header_it_jobs",
      },
      {
        id: "Service",
        name: "Service",
        link: "/",
        bilingualKey: "header_it_jobs",
      },
      {
        id: "Design",
        name: "Design",
        link: "/",
        bilingualKey: "header_it_jobs",
      },
    ],
  },
  {
    id: 3,
    name: "Company",
    link: "/companies?src=topdev.vn&medium=mainmenu",
    bilingualKey: "header_it_companies",
  },
  {
    id: 4,
    name: "Tools",
    bilingualKey: "header_tools",
    subMenu: [
      {
        id: "Create CV",
        name: "Create CV",
        link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
        bilingualKey: "header_create_CV",
        icon: <CreateCv />,
      },
      {
        id: "Convert CV",
        name: "Convert CV",
        link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
        bilingualKey: "home_convert_cv",
        icon: <ConvertCv />,
      },
      {
        id: "Personality Test",
        name: "Personality Test",
        link: "/page/trac-nghiem-tinh-cach?src=topdev.vn&medium=mainmenu",
        bilingualKey: "footer_list_personality_test",
        icon: <BsFillEmojiSmileFill />,
      },
      {
        id: "Salary",
        name: "Salary",
        link: "/tool/tinh-luong-gross-net?src=topdev.vn&medium=mainmenu",
        bilingualKey: "footer_list_salary_calculation_gross_net",
        icon: <Salary />,
      },
      {
        id: "Interview",
        name: "Interview",
        link: "/interview?src=topdev.vn&medium=mainmenu",
        bilingualKey: "header_interview_questions",
        icon: <Interview />,
      },
      {
        id: "Blog",
        name: "Blog",
        link: `/blog?src=topdev.vn&medium=mainmenu`,
        bilingualKey: "header_blog_it",
        icon: <Blog />,
      },
    ],
  },
  {
    id: 5,
    name: "Blog",
    link: "/blog?src=topdev.vn&medium=mainmenu",
    bilingualKey: "header_blog_it",
  },
  {
    id: 6,
    name: "Fresher Jobs",
    link: "/top-viec-lam-it-fresher?src=topdev.vn&medium=mainmenu",
    icon: <FresherJob />,
    color: "#4876EF",
  },
  {
    id: 7,
    name: "NIPA",
    color: "#2440D1",
    link: "/nha-tuyen-dung/to-chuc/korean-it-companies-83771",
  },
];
export const LIST_MENU_TOPIC = [
  {
    id: "IT",
    name: "IT",
    link: "/",
    bilingualKey: "header_it_jobs",
    icon: <It />,
  },
  {
    id: "Business & Finance",
    name: "Business & Finance",
    link: "/",
    bilingualKey: "header_it_jobs",
    icon: <Business />,
  },
  {
    id: "Management",
    name: "Management",
    link: "/",
    bilingualKey: "header_it_jobs",
    icon: <Management />,
  },
  {
    id: "Manufacturing & Engineer",
    name: "Manufacturing & Engineer",
    link: "/",
    bilingualKey: "header_it_jobs",
    icon: <Manufacturing />,
  },
  {
    id: "Service",
    name: "Service",
    link: "/",
    bilingualKey: "header_it_jobs",
    icon: <Service />,
  },
  {
    id: "Design",
    name: "Design",
    link: "/",
    bilingualKey: "header_it_jobs",
    icon: <Design />,
  },
];

export const LIST_MENU_MOBILE = [
  {
    id: 1,
    name: "Hot Jobs",
    icon: <BsFire />,
    subMenu: [
      {
        id: "A1",
        name: "Top 100 Highest Salary",
        link: "/jobs/search?ordering=high_low_salary",
        icon: <Top100 />,
      },
      {
        id: "A2",
        name: "By Location",
        link: "/",
        icon: <IoLocationSharp />,
        subMenu: [
          {
            id: "loc_hcm",
            name: "Ho Chi Minh",
            link: "/jobs/search?region_ids=79",
          },
          {
            id: "loc_hn",
            name: "Ha Noi",
            link: "/jobs/search?region_ids=01",
          },
          {
            id: "loc_dn",
            name: "Da Nang",
            link: "/jobs/search?region_ids=48",
          },
        ],
      },
      // {
      //   id: "A3",
      //   name: "Recommend Jobs",
      //   link: "/",
      //   icon: <BiSolidLike />,
      // },
    ],
  },
  {
    id: 2,
    name: "Jobs",
    icon: <Jobs />,
    bilingualKey: "header_it_jobs",
    subMenu: [
      {
        id: "IT",
        name: "IT",
        link: "/",
      },
      {
        id: "Business & Finance",
        name: "Business & Finance",
        link: "/",
      },
      {
        id: "Management",
        name: "Management",
        link: "/",
      },
      {
        id: "Manufacturing & Engineer",
        name: "Manufacturing & Engineer",
        link: "/",
      },
      {
        id: "Service",
        name: "Service",
        link: "/",
      },
      {
        id: "Design",
        name: "Design",
        link: "/",
      },
    ],
  },
  {
    id: 3,
    name: "Company",
    link: "/companies?src=topdev.vn&medium=mainmenu",
    icon: <Companies />,
    bilingualKey: "header_it_companies",
  },
  {
    id: 4,
    name: "Tools",
    icon: <Tools />,
    bilingualKey: "header_tools",
    subMenu: [
      {
        id: "Create CV",
        name: "Create CV",
        link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
        bilingualKey: "header_create_CV",
        icon: <CreateCv />,
      },
      {
        id: "Convert CV",
        name: "Convert CV",
        link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
        bilingualKey: "home_convert_cv",
        icon: <ConvertCv />,
      },
      {
        id: "Personality Test",
        name: "Personality Test",
        link: "/page/trac-nghiem-tinh-cach?src=topdev.vn&medium=mainmenu",
        bilingualKey: "footer_list_personality_test",
        icon: <BsFillEmojiSmileFill />,
      },
      {
        id: "Salary",
        name: "Salary",
        link: "/tool/tinh-luong-gross-net?src=topdev.vn&medium=mainmenu",
        bilingualKey: "footer_list_salary_calculation_gross_net",
        icon: <Salary />,
      },
      {
        id: "Interview",
        name: "Interview",
        link: "/interview?src=topdev.vn&medium=mainmenu",
        bilingualKey: "header_interview_questions",
        icon: <Interview />,
      },
      {
        id: "Blog",
        name: "Blog",
        link: `/blog?src=topdev.vn&medium=mainmenu`,
        bilingualKey: "header_blog_it",
        icon: <Blog />,
      },
    ],
  },
  {
    id: 5,
    name: "Blog",
    link: "/blog?src=topdev.vn&medium=mainmenu",
    icon: <Blog />,
  },
  {
    id: 6,
    name: "Fresher Jobs",
    link: "/top-viec-lam-it-fresher?src=topdev.vn&medium=mainmenu",
    icon: <FresherJob />,
  },
  {
    id: 7,
    name: "NIPA",
    color: "#0B4DA1",
    link: "/nha-tuyen-dung/to-chuc/korean-it-companies-83771",
  },
  // {
  //   id: 8,
  //   name: "TopDev",
  //   icon: <Topdev />,
  //   link: "/",
  // },
];
export const LIST_TOOLS = [
  {
    id: "Create CV",
    name: "Create CV",
    link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
    bilingualKey: "header_create_CV",
    backgroundImage: "https://c.topdevvn.com/uploads/2025/08/13/createCv.jpg",
    icon: <CreateCv />,
  },
  {
    id: "Convert CV",
    name: "Convert CV",
    link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
    bilingualKey: "home_convert_cv",
    backgroundImage: "https://c.topdevvn.com/uploads/2025/08/13/convertCv.png",
    icon: <ConvertCv />,
  },
  {
    id: "Personality Test",
    name: "Personality Test",
    link: "/page/trac-nghiem-tinh-cach?src=topdev.vn&medium=mainmenu",
    bilingualKey: "footer_list_personality_test",
    backgroundImage: "https://c.topdevvn.com/uploads/2025/08/13/test.png",
    icon: <BsFillEmojiSmileFill />,
  },
  {
    id: "Salary",
    name: "Salary",
    link: "/tool/tinh-luong-gross-net?src=topdev.vn&medium=mainmenu",
    backgroundImage: "https://c.topdevvn.com/uploads/2025/08/13/salary.png",
    bilingualKey: "footer_list_salary_calculation_gross_net",
    icon: <Salary />,
  },
  {
    id: "Interview",
    name: "Interview",
    link: "/interview?src=topdev.vn&medium=mainmenu",
    bilingualKey: "header_interview_questions",
    backgroundImage: "https://c.topdevvn.com/uploads/2025/08/13/salary.png",
    icon: <Interview />,
  },
];
export const MENU_LIST: MenuItem[] = [
  {
    id: "home",
    name: "Home",
    link: "/",
    icon: <House />,
    bilingualKey: "common_home",
  },
  {
    id: "hot jobs",
    name: "Hot Jobs",
    icon: <HotJob />,
    subMenu: [
      {
        id: "Top 100 Highest Salary",
        name: "Top 100 Highest Salary",
        link: "/jobs/search?ordering=high_low_salary",
        icon: <Top100 />,
      },
      {
        id: "By Location",
        name: "By Location",
        link: "/",
        icon: <IoLocationSharp />,
        subMenu: [
          {
            id: "loc_hcm",
            name: "Ho Chi Minh",
            link: "/jobs/search?region_ids=79",
          },
          {
            id: "loc_hn",
            name: "Ha Noi",
            link: "/jobs/search?region_ids=01",
          },
          {
            id: "loc_dn",
            name: "Da Nang",
            link: "/jobs/search?region_ids=48",
          },
        ],
      },
      // {
      //   id: "Recommend Jobs",
      //   name: "Recommend Jobs",
      //   link: "/",
      //   icon: <BiSolidLike />,
      // },
    ],
  },
  {
    id: "companies",
    name: "Companies",
    link: "/companies",
    icon: <Companies />,
    bilingualKey: "header_it_companies",
  },
  {
    id: "tools",
    name: "Tools",
    icon: <Tools />,
    bilingualKey: "header_tools",
    subMenu: [
      {
        id: "Create CV",
        name: "Create CV",
        link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
        bilingualKey: "header_create_CV",
        icon: <CreateCv />,
      },
      {
        id: "Convert CV",
        name: "Convert CV",
        link: "/tao-cv-online?src=topdev.vn&medium=mainmenu",
        bilingualKey: "home_convert_cv",
        icon: <ConvertCv />,
      },
      {
        id: "Personality Test",
        name: "Personality Test",
        link: "/page/trac-nghiem-tinh-cach?src=topdev.vn&medium=mainmenu",
        bilingualKey: "footer_list_personality_test",
        icon: <BsFillEmojiSmileFill />,
      },
      {
        id: "Salary",
        name: "Salary",
        link: "/tool/tinh-luong-gross-net?src=topdev.vn&medium=mainmenu",
        bilingualKey: "footer_list_salary_calculation_gross_net",
        icon: <Salary />,
      },
      {
        id: "Interview",
        name: "Interview",
        link: "/interview?src=topdev.vn&medium=mainmenu",
        bilingualKey: "header_interview_questions",
        icon: <Interview />,
      },
      {
        id: "Blog",
        name: "Blog",
        link: `/blog?src=topdev.vn&medium=mainmenu`,
        bilingualKey: "header_blog_it",
        icon: <Blog />,
      },
    ],
  },
  {
    id: "account",
    name: "Account",
    icon: <Account />,
    subMenu: [
      {
        id: "Dashboard",
        name: "Dashboard",
        link: "/users/profile?src=topdev.vn&medium=submenu",
        bilingualKey: "header_dashboard",
        icon: <Dashboard />,
      },
      {
        id: "CvManagement",
        name: "CV Management ",
        bilingualKey: "header_cv_management",
        link: "/users/my-cv?src=topdev.vn&medium=submenu",
        icon: <CvManagement />,
      },
      {
        id: "AppliedJobs",
        name: "Applied Jobs",
        bilingualKey: "header_applied_jobs",
        link: "/users/jobs-applied?src=topdev.vn&medium=submenu",
        icon: <AppliedJobs />,
      },
      {
        id: "FollowingJobs",
        name: "Following Jobs",
        bilingualKey: "header_following_Jobs",
        link: "/users/jobs-followed?src=topdev.vn&medium=submenu",
        icon: <FollowingJobs />,
      },
      {
        id: "Setting",
        name: "Account Setting",
        link: "/users/personality-test?src=topdev.vn&medium=submenu",
        icon: <Setting />,
      },
      {
        id: "Logout",
        name: "Logout",
        bilingualKey: "header_logout",
        link: `${process.env.NEXT_PUBLIC_OAUTH2_URL_LOGOUT}`,
        icon: <Logout />,
      },
    ],
    subMenuEmployer: [
      {
        id: "dashboard",
        link: "https://dash.topdev.vn?src=topdev.vn&medium=submenu",
        name: "Employer Dashboard",
        icon: <Dashboard />,
      },
      {
        id: "products",
        link: "/products",
        name: "Recruitment Services",
        icon: <Jobs />,
      },
      {
        id: "logout",
        link: `${process.env.NEXT_PUBLIC_OAUTH2_URL_LOGOUT}`,
        name: "Logout",
        icon: <Logout />,
      },
    ],
  },
];
