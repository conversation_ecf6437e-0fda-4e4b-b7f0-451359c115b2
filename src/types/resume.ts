//Resumes Props in apply job
export interface UserResumes {
  id?: string | number | null;
  name?: string;
  path?: string;
  source?: string;
  type?: string;
  url?: string;
  created_at?: number | string;
  updated_at?: number | string;
  assets?: Array<{ download_url: string }>;
  applies?: string[];
  features?: FeaturesResume;
  last?: boolean;
}

interface FeaturesResume {
  apply: boolean;
  delete: boolean;
  download: boolean;
  duplicate: boolean;
  edit: boolean;
  view: boolean;
}
