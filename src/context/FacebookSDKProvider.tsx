// contexts/FacebookSDKProvider.tsx
"use client"; // Bắt buộc đối với App Router

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';

// Đ<PERSON><PERSON> nghĩa kiểu cho giá trị của Context
interface FacebookSDKContextType {
  isReady: boolean;
}

const FacebookSDKContext = createContext<FacebookSDKContextType | null>(null);

// Định nghĩa kiểu cho props của Provider
interface FacebookSDKProviderProps {
  children: ReactNode;
}

export const FacebookSDKProvider = ({ children }: FacebookSDKProviderProps) => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Nếu SDK đã tồn tại, chỉ cần set trạng thái sẵn sàng
    if (window.FB) {
      setIsReady(true);
      return;
    }

    // <PERSON><PERSON> hà<PERSON> callback vào đối tượng window
    window.fbAsyncInit = function () {
      if (!process.env.NEXT_PUBLIC_FACEBOOK_APP_ID) {
        console.error("Facebook App ID is not defined. Please set NEXT_PUBLIC_FACEBOOK_APP_ID in your .env.local file.");
        return;
      }
      
      window.FB?.init({
        appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
        cookie: true,
        xfbml: true,
        version: 'v19.0', // Luôn dùng phiên bản mới nhất
      });
      setIsReady(true);
    };

    // Tải SDK
    (function (d, s, id) {
      let js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) { return; }
      js = d.createElement(s) as HTMLScriptElement;
      js.id = id;
      js.src = "https://connect.facebook.net/en_US/sdk.js";
      if (fjs) {
        fjs.parentNode?.insertBefore(js, fjs);
      }
    })(document, 'script', 'facebook-jssdk');

  }, []);

  return (
    <FacebookSDKContext.Provider value={{ isReady }}>
      {children}
    </FacebookSDKContext.Provider>
  );
};

// Custom hook để sử dụng Context một cách an toàn
export const useFacebookSDK = () => {
  const context = useContext(FacebookSDKContext);
  if (!context) {
    throw new Error('useFacebookSDK must be used within a FacebookSDKProvider');
  }
  return context;
};