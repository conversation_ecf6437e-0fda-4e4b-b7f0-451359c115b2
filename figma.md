

candidate list table:
/* Frame 1000005101 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: flex-start;
padding: 0px;

position: absolute;
width: 1592px;
height: 836px;
left: 281px;
top: 291px;



/* Frame 1000005101 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 65px;
height: 836px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Chip 1 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px 16px;
gap: 10px;

width: 65px;
height: 32px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/200 */
border: 0.5px solid #C2D5FB;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Sample */

width: 19px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Chip 3 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px 16px;
gap: 10px;

width: 65px;
height: 184px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0.5px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Sample */

width: 33px;
height: 20px;

/* New-TopDev/title/small/baseline */
font-family: 'Roboto';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
text-align: center;
letter-spacing: 0.1px;

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Chip 4 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px 16px;
gap: 10px;

width: 65px;
height: 256px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0.5px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Sample */

width: 33px;
height: 20px;

/* New-TopDev/title/small/baseline */
font-family: 'Roboto';
font-style: normal;
font-weight: 500;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
text-align: center;
letter-spacing: 0.1px;

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005094 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 292px;
height: 836px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Chip 1 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px 16px;
gap: 10px;

width: 292px;
height: 32px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/200 */
border-width: 0.5px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Sample */

width: 157px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Chip 4 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 12px 20px;
gap: 12px;

width: 292px;
height: 236px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0.5px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005109 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 10px;

width: 43px;
height: 44px;

/* New-TopDev/brand-color/100 */
background: #DCE7FD;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* MA */

width: 27px;
height: 24px;

/* New-TopDev/title/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */
letter-spacing: 0.15px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005110 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 185px;
height: 146px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005163 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 2px;

width: 187px;
height: 74px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Nguyễn Minh Anh */

width: 122px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004528 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 187px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 9.42%;
right: 10.19%;
top: 16.64%;
bottom: 43.14%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Vector */

position: absolute;
left: 5.36%;
right: 5.75%;
top: 20.53%;
bottom: 16.67%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* <EMAIL> */

width: 173px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005088 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 97px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* 0346 433 367 */

width: 83px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 102px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 20.83%;
right: 20.83%;
top: 8.33%;
bottom: 8.34%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* TP.Hồ Chí Minh */

width: 88px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 42 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 2px 6px 2px 8px;
gap: 8px;

width: 94px;
height: 20px;

/* New-TopDev/positive-color/green-100 */
background: #D7FFE3;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004136 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 2px;

width: 80px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Best Match */

width: 66px;
height: 16px;

/* New-TopDev/body/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/positive-color/green-700 */
color: #049132;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Vector */

position: absolute;
left: 0.01%;
right: 0%;
top: 0%;
bottom: 0%;

/* New-TopDev/positive-color/green-700 */
background: #049132;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Frame 1000005203 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

width: 162px;
height: 36px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005164 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 112px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
height: 8.75px;
left: 6.25%;
right: 6.25%;
top: calc(50% - 8.75px/2);

/* New-TopDev/careful-color/orange-500* */
background: #FF821E;


/* Has Cover Letter */

width: 98px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;

/* New-TopDev/careful-color/orange-500* */
color: #FF821E;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 162px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.34%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Applied Date: 15/08/2025 */

width: 148px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Chip 5 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 12px 20px;
gap: 12px;

width: 292px;
height: 284px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0.5px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005109 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 10px;

width: 43px;
height: 44px;

/* New-TopDev/brand-color/100 */
background: #DCE7FD;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* MA */

width: 27px;
height: 24px;

/* New-TopDev/title/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */
letter-spacing: 0.15px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005110 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 185px;
height: 146px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005163 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 2px;

width: 187px;
height: 74px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Nguyễn Minh Anh */

width: 122px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004528 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 187px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 9.42%;
right: 10.19%;
top: 16.64%;
bottom: 43.14%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Vector */

position: absolute;
left: 5.36%;
right: 5.75%;
top: 20.53%;
bottom: 16.67%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* <EMAIL> */

width: 173px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005088 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 97px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* 0346 433 367 */

width: 83px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 102px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 20.83%;
right: 20.83%;
top: 8.33%;
bottom: 8.34%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* TP.Hồ Chí Minh */

width: 88px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 42 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 2px 6px 2px 8px;
gap: 8px;

width: 94px;
height: 20px;

/* New-TopDev/positive-color/green-100 */
background: #D7FFE3;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004136 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 2px;

width: 80px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Best Match */

width: 66px;
height: 16px;

/* New-TopDev/body/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/positive-color/green-700 */
color: #049132;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Vector */

position: absolute;
left: 0.01%;
right: 0%;
top: 0%;
bottom: 0%;

/* New-TopDev/positive-color/green-700 */
background: #049132;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Frame 1000005203 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

width: 162px;
height: 36px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005164 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 112px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
height: 8.75px;
left: 6.25%;
right: 6.25%;
top: calc(50% - 8.75px/2);

/* New-TopDev/careful-color/orange-500* */
background: #FF821E;


/* Has Cover Letter */

width: 98px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;

/* New-TopDev/careful-color/orange-500* */
color: #FF821E;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 162px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.34%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Applied Date: 15/08/2025 */

width: 148px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Chip 6 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 12px 20px;
gap: 12px;

width: 292px;
height: 284px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0.5px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 1;


/* Frame 1000005109 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 10px;

width: 43px;
height: 44px;

/* New-TopDev/brand-color/100 */
background: #DCE7FD;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* MA */

width: 27px;
height: 24px;

/* New-TopDev/title/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 16px;
line-height: 24px;
/* identical to box height, or 150% */
letter-spacing: 0.15px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005110 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 8px;

width: 185px;
height: 146px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005163 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 2px;

width: 187px;
height: 74px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Nguyễn Minh Anh */

width: 122px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004528 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 187px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 9.42%;
right: 10.19%;
top: 16.64%;
bottom: 43.14%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Vector */

position: absolute;
left: 5.36%;
right: 5.75%;
top: 20.53%;
bottom: 16.67%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 0%;
bottom: 0%;



/* <EMAIL> */

width: 173px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005088 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 97px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* 0346 433 367 */

width: 83px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 102px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 20.83%;
right: 20.83%;
top: 8.33%;
bottom: 8.34%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* TP.Hồ Chí Minh */

width: 88px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 42 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 2px 6px 2px 8px;
gap: 8px;

width: 94px;
height: 20px;

/* New-TopDev/positive-color/green-100 */
background: #D7FFE3;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004136 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 2px;

width: 80px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Best Match */

width: 66px;
height: 16px;

/* New-TopDev/body/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/positive-color/green-700 */
color: #049132;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Vector */

position: absolute;
left: 0.01%;
right: 0%;
top: 0%;
bottom: 0%;

/* New-TopDev/positive-color/green-700 */
background: #049132;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Frame 1000005203 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 4px;

width: 162px;
height: 36px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005164 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 112px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
height: 8.75px;
left: 6.25%;
right: 6.25%;
top: calc(50% - 8.75px/2);

/* New-TopDev/careful-color/orange-500* */
background: #FF821E;


/* Has Cover Letter */

width: 98px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;

/* New-TopDev/careful-color/orange-500* */
color: #FF821E;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 162px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 10px;
height: 10px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 8.33%;
bottom: 8.34%;

/* New-TopDev/text-color/500 */
background: #6D6D6D;


/* Applied Date: 15/08/2025 */

width: 148px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
display: flex;
align-items: center;

/* New-TopDev/text-color/500 */
color: #6D6D6D;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005099 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: space-between;
align-items: flex-start;
padding: 0px;

width: 714px;
height: 836px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Chip 1 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px 16px;
gap: 10px;

margin: 0 auto;
width: 714px;
height: 32px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/200 */
border-width: 0.5px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Sample */

width: 152px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005175 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 12px 16px 16px;
gap: 12px;

margin: 0 auto;
width: 714px;
height: 236px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. */

width: 682px;
height: 40px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005177 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 65px;

width: 682px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Skill(s): */

width: 51px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005164 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 166px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 20 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 41px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 25px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 25px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 66 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 76px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 60px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 60px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 67 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 41px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 25px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 25px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005178 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 36px;

width: 682px;
height: 92px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005172 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 2px;

width: 80px;
height: 42px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Experience: */

width: 80px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Component 42 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 2px 6px 2px 8px;
gap: 8px;

width: 50px;
height: 20px;

/* New-TopDev/careful-color/orange-50 */
background: #FFF8ED;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 6 YOE */

width: 36px;
height: 16px;

/* New-TopDev/body/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/careful-color/orange-500* */
color: #FF821E;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005171 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 4px;

width: 332px;
height: 92px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005168 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005170 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005173 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005172 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005171 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

display: none;
width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005169 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

display: none;
width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005179 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 42px;

width: 682px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Education: */

width: 73px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Posts and Telecommunication Institute of Technology - Ho Chi Minh City Campus */

width: 567px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Frame 1000005176 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 12px 16px 16px;
gap: 12px;

margin: 0 auto;
width: 714px;
height: 284px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. */

width: 682px;
height: 40px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005177 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 65px;

width: 682px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Skill(s): */

width: 51px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005164 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 166px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 20 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 41px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 25px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 25px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 66 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 76px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 60px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 60px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 67 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 41px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 25px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 25px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005178 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 36px;

width: 682px;
height: 140px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005172 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 2px;

width: 80px;
height: 42px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Experience: */

width: 80px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Component 42 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 2px 6px 2px 8px;
gap: 8px;

width: 50px;
height: 20px;

/* New-TopDev/careful-color/orange-50 */
background: #FFF8ED;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 6 YOE */

width: 36px;
height: 16px;

/* New-TopDev/body/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/careful-color/orange-500* */
color: #FF821E;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005171 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 4px;

width: 332px;
height: 140px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005168 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005170 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005173 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005172 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005171 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005169 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005179 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 42px;

width: 682px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Education: */

width: 73px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Posts and Telecommunication Institute of Technology - Ho Chi Minh City Campus */

width: 567px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Frame 1000005177 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 12px 16px 16px;
gap: 12px;

margin: 0 auto;
width: 714px;
height: 284px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor. */

width: 682px;
height: 40px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005177 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 65px;

width: 682px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Skill(s): */

width: 51px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005164 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 166px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 20 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 41px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 25px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 25px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 66 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 76px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 60px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 60px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Component 67 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 4px 8px;

width: 41px;
height: 20px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/300 */
border: 0.5px solid #97BBF9;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 25px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Sample */

width: 25px;
height: 16px;

/* New-TopDev/body/small/baseline

0.4
*/
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 12px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005178 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 36px;

width: 682px;
height: 140px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005172 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 2px;

width: 80px;
height: 42px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Experience: */

width: 80px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Component 42 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 2px 6px 2px 8px;
gap: 8px;

width: 50px;
height: 20px;

/* New-TopDev/careful-color/orange-50 */
background: #FFF8ED;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 6 YOE */

width: 36px;
height: 16px;

/* New-TopDev/body/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */

/* New-TopDev/careful-color/orange-500* */
color: #FF821E;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

display: none;
width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005171 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: center;
padding: 0px;
gap: 4px;

width: 332px;
height: 140px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000005168 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005170 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005173 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005172 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005171 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005169 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 8px;

width: 332px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* 06/2018 - 04/2022 */

width: 127px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* | */

width: 5px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/400 */
color: #888888;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Graphic Designer - abcxyz */

width: 180px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frame 1000005179 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 42px;

width: 682px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Education: */

width: 73px;
height: 20px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Posts and Telecommunication Institute of Technology - Ho Chi Minh City Campus */

width: 567px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/700 */
color: #4F4F4F;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 1;


/* Frame 1000005098 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 260.5px;
height: 836px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 1;


/* Chip 1 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px 16px;
gap: 10px;

width: 260.5px;
height: 32px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/200 */
border-width: 0.5px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Sample */

width: 60px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005097 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 10px;

width: 260.5px;
height: 236px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Graphic Designer - abcxyzGraphic Designer - abcxyz */

width: 240.5px;
height: 40px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* or 143% */
text-align: center;

/* New-TopDev/useful-color/blue-500* */
color: #2F6BFF;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005098 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 10px;

width: 260.5px;
height: 284px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 1;


/* Graphic Designer - abcxyzGraphic Designer - abcxyz */

width: 240.5px;
height: 40px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* or 143% */
text-align: center;

/* New-TopDev/useful-color/blue-500* */
color: #2F6BFF;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005099 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px;
gap: 10px;

width: 260.5px;
height: 284px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 1;


/* Graphic Designer - abcxyzGraphic Designer - abcxyz */

width: 240.5px;
height: 40px;

/* New-TopDev/body/medium/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* or 143% */
text-align: center;

/* New-TopDev/useful-color/blue-500* */
color: #2F6BFF;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005100 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 260.5px;
height: 836px;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 1;


/* Chip 1 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 10px 16px;
gap: 10px;

width: 260.5px;
height: 32px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/200 */
border-width: 0.5px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Sample */

width: 45px;
height: 20px;

/* New-TopDev/title/small/emphasis */
font-family: 'Inter';
font-style: normal;
font-weight: 600;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */
letter-spacing: 0.1px;

/* New-TopDev/brand-color/500* */
color: #4876EF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000005094 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 12px;

width: 260.5px;
height: 236px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 16.67%;
right: 12.5%;
top: 15.95%;
bottom: 16.67%;



/* Vector */

position: absolute;
left: 16.67%;
right: 15.95%;
top: 15.95%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 2px solid #4876EF;


/* Vector */

position: absolute;
left: 16.67%;
right: 25%;
top: 25%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;


/* Vector */

position: absolute;
left: 54.17%;
right: 12.5%;
top: 25%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 2px solid #4876EF;


/* Frame 1000005088 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 25%;
bottom: 25%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 25%;
bottom: 25%;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Vector */

position: absolute;
left: 39.58%;
right: 39.58%;
top: 39.58%;
bottom: 39.59%;

/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* icon-set */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;



/* Vector */

box-sizing: border-box;

position: absolute;
left: 29.08%;
right: 29.09%;
top: 12.5%;
bottom: 37.51%;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 12.5%;
right: 12.5%;
top: 54.17%;
bottom: 12.5%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Component 66 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 4px 20px 4px 16px;
gap: 4px;

width: 195px;
height: 40px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border: 0.5px solid #C2D5FB;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 137px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Recruitment Process */

width: 137px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/300 */
color: #B0B0B0;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

width: 10px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

width: 9px;
height: 9px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

width: 9px;
height: 4.91px;

/* New-TopDev/text-color/500 */
background: #6D6D6D;
transform: rotate(-180deg);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005095 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 12px;

width: 260.5px;
height: 284px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 16.67%;
right: 12.5%;
top: 15.95%;
bottom: 16.67%;



/* Vector */

position: absolute;
left: 16.67%;
right: 15.95%;
top: 15.95%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 2px solid #4876EF;


/* Vector */

position: absolute;
left: 16.67%;
right: 25%;
top: 25%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;


/* Vector */

position: absolute;
left: 54.17%;
right: 12.5%;
top: 25%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 2px solid #4876EF;


/* Frame 1000005088 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 25%;
bottom: 25%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 25%;
bottom: 25%;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Vector */

position: absolute;
left: 39.58%;
right: 39.58%;
top: 39.58%;
bottom: 39.59%;

/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* icon-set */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;



/* Vector */

box-sizing: border-box;

position: absolute;
left: 29.08%;
right: 29.09%;
top: 12.5%;
bottom: 37.51%;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 12.5%;
right: 12.5%;
top: 54.17%;
bottom: 12.5%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Component 66 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 4px 20px 4px 16px;
gap: 4px;

width: 195px;
height: 40px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border: 0.5px solid #C2D5FB;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 137px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Recruitment Process */

width: 137px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/300 */
color: #B0B0B0;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

width: 10px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

width: 9px;
height: 9px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

width: 9px;
height: 4.91px;

/* New-TopDev/text-color/500 */
background: #6D6D6D;
transform: rotate(-180deg);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 1000005096 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 12px;

width: 260.5px;
height: 256px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border-width: 0px 0.5px 0.5px 0px;
border-style: solid;
border-color: #C2D5FB;

/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* icon-set */

width: 14px;
height: 14px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 16.67%;
right: 12.5%;
top: 15.95%;
bottom: 16.67%;



/* Vector */

position: absolute;
left: 16.67%;
right: 15.95%;
top: 15.95%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 2px solid #4876EF;


/* Vector */

position: absolute;
left: 16.67%;
right: 25%;
top: 25%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;


/* Vector */

position: absolute;
left: 54.17%;
right: 12.5%;
top: 25%;
bottom: 16.67%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 2px solid #4876EF;


/* Frame 1000005088 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 25%;
bottom: 25%;



/* Vector */

position: absolute;
left: 8.33%;
right: 8.33%;
top: 25%;
bottom: 25%;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Vector */

position: absolute;
left: 39.58%;
right: 39.58%;
top: 39.58%;
bottom: 39.59%;

/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Frame 1000005089 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

width: 31px;
height: 30px;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* icon-set */

width: 16px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Group */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;



/* Vector */

box-sizing: border-box;

position: absolute;
left: 29.08%;
right: 29.09%;
top: 12.5%;
bottom: 37.51%;

/* New-TopDev/brand-color/50 */
background: #F0F5FE;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Vector */

box-sizing: border-box;

position: absolute;
left: 12.5%;
right: 12.5%;
top: 54.17%;
bottom: 12.5%;

/* New-TopDev/brand-color/500* */
background: #4876EF;
/* New-TopDev/brand-color/500* */
border: 1px solid #4876EF;


/* Component 66 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 4px 20px 4px 16px;
gap: 4px;

width: 195px;
height: 40px;

/* New-TopDev/white */
background: #FFFFFF;
/* New-TopDev/brand-color/200 */
border: 0.5px solid #C2D5FB;
border-radius: 4px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 6px;

width: 137px;
height: 20px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 1000004512 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px;
gap: 10px;

display: none;
width: 30px;
height: 30px;

/* New-TopDev/BG-card */
background: #F8FBFF;
border-radius: 64px;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Recruitment Process */

width: 137px;
height: 20px;

/* New-TopDev/body/medium/baseline */
font-family: 'Inter';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;
/* identical to box height, or 143% */

/* New-TopDev/text-color/300 */
color: #B0B0B0;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* icon-set */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

width: 10px;
height: 12px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 1000004665 */

/* Auto layout */
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
padding: 0px;
gap: 10px;

width: 9px;
height: 9px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

width: 9px;
height: 4.91px;

/* New-TopDev/text-color/500 */
background: #6D6D6D;
transform: rotate(-180deg);

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;
